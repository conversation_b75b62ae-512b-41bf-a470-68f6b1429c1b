#!/bin/bash

# An<PERSON> de tudo, fa<PERSON> o docker compose down, execute esse arquivo e só depois faça docker compose up -d

# Para executar este arquivo: bash docker-login.sh
# Realizar login para permitir baixar as imagens do message-broker
# Credenciais configuradas para permitir apenas o pull das imagens
docker login -u axvaplbwrlcl/message-broker-images-pull -p "jq[V5b;oalR98h}trB]u" sa-vinhedo-1.ocir.io
