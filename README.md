# Monorepo Digisac
Bem vindo meu jovem!! Esse projeto consiste na união do back e do front em um único projeto afim de termos um maior controle das versões e praticidade no desenvolvimento de features.
Para ajudar no gerenciamento dos pacotes e versões do monorepo estamos utilizando o [lerna](https://github.com/lerna/lerna)

## Iniciando o projeto
1. Clone o projeto na sua máquina
```bash
<NAME_EMAIL>:digisac/digisac.git
```

2. Instale as dependencias do root
```bash
yarn install
```

3. Instale as dependencias do projeto
```bash
yarn lerna exec --parallel yarn
```

4. Subindo os serviços do back que estão no `docker-compose.yml`
> OBS: É necessário parar os serviços de postgresql, rabbitmq e browserless para que seja possível. 
```bash
bash docker-login.sh

docker-compose up -d
```
Caso o comando dê problema, baixe todos os serviços que foram criados anteriormente.
```bash
docker rm -f postgresql rabbitmq browserless
```

5. Executando as migrations para o banco
> Não esqueça de copiar o `.env.example` para `.env`, alterar a variável de ambiente e iniciar os projetos. Para isso, basta entrar nas pastas e executar o `yarn develop`

```bash
cd packages/back
yarn sequelize db:migrate
yarn app seed:dev
```
## Importando suas branchs
> O script `import-branch.sh` faz todo esse processo de importação. Para isso é só precisa passar o nome da **branch** que você queria trazer para dentro

Antes de importar sua branch para o projeto é preciso adicionar o `back` e o `front` como remotes.

**Adicionando o `back`**

`git remote <NAME_EMAIL>:mandeumzap/mandeumzap-back.git`

**Adicionando o `front`**

`git remote <NAME_EMAIL>:mandeumzap/mandeumzap-front.git`

Feito isso, já podemos iniciar o processo de migração

```bash
$ git fetch --all --no-tags
$ git checkout my_branch
$ git merge --strategy recursive --strategy-option subtree=packages/branch/ branch/my_branch
```

## Informacoes
### Commits para se evitar build
No `.gitlab-ci.yml` foi inserido uma regra commits que iniciam com `[docs]` e `[no-build]` NÃO SERÃO buildadas, para evitar versões vazias e desenecessárias

## Comandos úteis

- Caso seja necessário recriar a base dados novamente, **entre no container da api e rode o comando abaixo:**
```bash
yarn sequelize db:drop; yarn sequelize db:create; yarn sequelize db:migrate; yarn app seed:dev;
```