#! /bin/bash

set -e

BRANCH_TARGET=${1:-master}
ROOT_BRANCH=${2:-master}

if ! git remote get-url front >/dev/null; then
    git remote <NAME_EMAIL>:mandeumzap/mandeumzap-front.git
fi

if ! git remote get-url back >/dev/null; then
    git remote <NAME_EMAIL>:mandeumzap/mandeumzap-api.git
fi

git fetch --all --no-tags

if ! git branch --show-current ; then
    git checkout -b $BRANCH_TARGET $ROOT_BRANCH
fi

git merge --no-edit --strategy recursive --strategy-option subtree=packages/front front/$BRANCH_TARGET
git merge --no-edit --strategy recursive --strategy-option subtree=packages/back back/$BRANCH_TARGET
