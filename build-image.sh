#!/bin/bash

set -e

COMMIT_SHA=$(git rev-parse HEAD)
IMAGE_NAME=${1:-""}
BRANCH_TAG=${2:-${COMMIT_SHA}}

AWS_REGISTRY=${3:-121154950204.dkr.ecr.us-east-1.amazonaws.com}
AWS_REGION=${4:-us-east-1}
AWS_PROFILE=${5:-default}
AWS_REPOSITORY=digisac-isolated

export FULL_AWS_REGISTRY_REPOSITORY_NAME="$REGISTRY/$REPOSITORY"

OCI_REGION=sa-vinhedo-1.ocir.io
OCI_NAMESPACE=axvaplbwrlcl
OCI_REPOSITORY=digisac-isolated

export FULL_OCI_REGISTRY_REPOSITORY_NAME="${OCI_REGION}/${OCI_NAMESPACE}/${OCI_REPOSITORY}"

# Params docker_image_builder 
docker_image_builder() {
    SERVICE=${1:-IMAGE_NAME}
    CONTEXT="packages/${SERVICE}"

    cp .rev yarn.lock $CONTEXT
   
    #Semantic Versionament - https://semver.org/lang/pt-BR/
    SEMANTIC_TAG=$(awk -F\" '/"version":/ { print $4 }' ${CONTEXT}/package.json)

    docker build \
        --progress=plain \
        --tag "$FULL_AWS_REGISTRY_REPOSITORY_NAME/$SERVICE:$BRANCH_TAG" \
        --tag "$FULL_AWS_REGISTRY_REPOSITORY_NAME/$SERVICE:$SEMANTIC_TAG" \
        --tag "$FULL_OCI_REGISTRY_REPOSITORY_NAME/$SERVICE:$BRANCH_TAG" \
        --tag "$FULL_OCI_REGISTRY_REPOSITORY_NAME/$SERVICE:$SEMANTIC_TAG" \
        --file "$CONTEXT/Dockerfile" \
        $CONTEXT

    if [[ ! ${?} ]]; then
        echo "[Error $?] Failed to build an image" 1>&2
        exit 1
    else
        echo "==> Docker Push"
        docker push $FULL_AWS_REGISTRY_REPOSITORY_NAME/$SERVICE:$SEMANTIC_TAG
        docker push $FULL_AWS_REGISTRY_REPOSITORY_NAME/$SERVICE:$BRANCH_TAG
        docker push $FULL_OCI_REGISTRY_REPOSITORY_NAME/$SERVICE:$SEMANTIC_TAG
        docker push $FULL_OCI_REGISTRY_REPOSITORY_NAME/$SERVICE:$BRANCH_TAG
    fi
}

#export DOCKER_BUILDKIT=1
case $IMAGE_NAME in
    back)
        docker_image_builder back
    ;;
    front)
        docker_image_builder front
    ;;
esac
