.ssh-config: &ssh-config
  - which ssh-agent > /dev/null || apk add openssh-client -y
  - eval `ssh-agent`
  - echo "${DEPLOY_SSH_PRIVATE_KEY}" | tr -d '\r' | ssh-add -
  - mkdir -p ~/.ssh
  - chmod 0700 ~/.ssh
  - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" >> ~/.ssh/config
  - git config --global user.email "<EMAIL>"
  - git config --global user.name "Deploy"
  - git remote set-url --push origin ${GIT_PROJECT_URL}
  - git config diff.renames 0

.aws-login: &aws-login
  - aws configure set default.aws_access_key_id ${AWS_ACCESS_KEY_ID_K8S}
  - aws configure set default.aws_secret_access_key ${AWS_SECRET_ACCESS_KEY_K8S}
  - aws configure set default.region ${AWS_DEFAULT_REGION}
  - eval $(aws ecr get-login --no-include-email --region ${AWS_DEFAULT_REGION})

.install-apps: &install-apps
  - node -v
  - yarn -v
  - cd packages/back && yarn install

include:
  - local: "qa-template.yml"

  - project: "application-security/sast"
    file: "application-scanning.yml"

image: ikateclab/ci:0.16

stages:
  - snyk-scan
  - test
  - unit-tests
  - build
  - deploy

variables:
  K8S_INFRA_TRIGGER_PIPILINE_URL: https://gitlab.ikatec.cloud/api/v4/projects/59/trigger/pipeline
  K8S_INFRA_SERVERPOD_TRIGGER_PIPILINE_URL: https://gitlab.ikatec.cloud/api/v4/projects/120/trigger/pipeline
  IMAGE_REPOSITORY: 617751658601.dkr.ecr.${AWS_DEFAULT_REGION}.amazonaws.com
  PRODUCTION_BRANCH: feature/workerization

test-e2e:
  stage: test
  variables:
    QA_AUTOMATION_ENABLED: true
    QA_AUTOMATION_PROJECT_ID: "413"
    QA_AUTOMATION_TRIGGER_TOKEN: "glptt-3bf03610e396fdf75cad8adfb2b7dca145bd0029"
    QA_AUTOMATION_TARGET_BRANCH: "feat/SD-1530"
  only:
   - master
  script:
    - |
      if [ "$QA_AUTOMATION_ENABLED" == "true" ]; then
        curl -X POST --fail -F "token=${QA_AUTOMATION_TRIGGER_TOKEN}" \
        -F "ref=${QA_AUTOMATION_TARGET_BRANCH}" \
        "${CI_API_V4_URL}/projects/${QA_AUTOMATION_PROJECT_ID}/trigger/pipeline"
        echo "Tests e2e triggered successfully"
      else
        echo "Trigger e2e tests disabled"
      fi
unit-tests:
  stage: unit-tests
  image: node:18
  before_script:
    - *install-apps
  variables:
    ENCRYPTION_KEY: "any-value"
  script:
    - yarn test:unit -- --ci --coverage --runInBand --passWithNoTests
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: always
    - when: never

build-and-deploy-mr:
  tags:
    - docker
  stage: build
  before_script:
    - *ssh-config
    - *aws-login
  variables:
    BACK_IMAGE_NAME: digisac-back
    FRONT_IMAGE_NAME: digisac-front
  script:
    - docker login -u axvaplbwrlcl/digisac-docker-push -p "${ORACLE_PULL_PUSH_AUTH_TOKEN}" sa-vinhedo-1.ocir.io
    - git switch -C ${CI_COMMIT_REF_NAME} ${CI_COMMIT_SHA}
    - git pull --no-commit --no-edit --ff -X patience origin ${CI_COMMIT_REF_NAME}

    - PRE_ID=mr-${CI_MERGE_REQUEST_IID}
    - LATEST_IMAGE_TAG=latest-mr-${CI_MERGE_REQUEST_IID}
    - echo $PRE_ID
    - echo $LATEST_IMAGE_TAG

    # Job var -> commit message -> package.json
    - if [[ -z ${LAST_VERSION} ]]; then
      echo 'getting last version from commit message (last_version:)'
      LAST_VERSION=$(echo $CI_COMMIT_MESSAGE | grep -oE '(last_version:)[^ ]*' | awk -F ':' '{ print $2 }' ) || true;
      fi;

    - if [[ -z ${LAST_VERSION} ]]; then
      echo 'getting last version from lerna.json'
      LAST_VERSION=$(awk -F\" '/"version":/ {print $4}' lerna.json);
      fi;

    - LAST_VERSION=$(awk -F\" '/"version":/ {print $4}' lerna.json) || true

    - echo $LAST_VERSION

    # get the start of LAST_VERSION
    # if LAST_VERSION=3.42.0 -> START_LAST_VERSION=3.42.0
    # if LAST_VERSION=3.42.0-rc.5 -> START_LAST_VERSION=3.42.0
    # if LAST_VERSION=3.42.0-mr-3154.1 -> START_LAST_VERSION=3.42.0
    - START_LAST_VERSION=$(echo "$LAST_VERSION" | awk -F- '{print $1}')

    - echo $START_LAST_VERSION

    # get last git tag (TODO: currently only works for MRs)
    - LAST_MR_TAG=$(git tag --sort=v:refname --list "v$START_LAST_VERSION-mr-$CI_MERGE_REQUEST_IID*" | tail -n 1)
    - echo $LAST_MR_TAG
    - git checkout "$LAST_MR_TAG" -- lerna.json || true
    - git commit -am "resets lerna.json to $LAST_MR_TAG" || true

    # Bump version at package.json and git tag
    - lerna version prerelease --preid ${PRE_ID} --conventional-prerelease --yes --no-changelog --message "Bump version %s" || echo 'lerna version failed, maybe the tag already exists'

    - VERSION=$(awk -F\" '/"version":/ {print $4}' lerna.json)

    - echo $VERSION

    # Get changed micro-services (including front)
    - CHANGED=$(bash ./changed.sh "v${LAST_VERSION}" || bash ./changed.sh) || ''

    - if [[ $CHANGED =~ "back" ]]; then
      echo "Teve alteração no back";
      fi;

    # Build and push back docker image
    - cd packages/back; bash ./build-image.sh ${LATEST_IMAGE_TAG} ${VERSION} ${BACK_IMAGE_NAME} ${IMAGE_REPOSITORY}; cd -;

    - echo $CHANGED

    # Build and push front docker image
    - if [[ $CHANGED =~ "front" ]]; then
      cd packages/front; bash ./build-image.sh ${LATEST_IMAGE_TAG} ${VERSION} ${FRONT_IMAGE_NAME} ${IMAGE_REPOSITORY}; cd -;
      else
      echo "Nada de novo no front. Prosseguindo...";
      fi;

    # Display variables
    - echo $LAST_VERSION
    - echo $VERSION
    - echo $K8S_INFRA_TRIGGER_PACKAGE
    - echo $CHANGED
    - echo $K8S_INFRA_TRIGGER_BRANCH
    - echo $K8S_INFRA_TRIGGER_PIPILINE_URL
    - echo $CI_ENVIRONMENT_SLUG

    # Call deploy on infra repo
    - curl -X POST
      -F ref=master
      -F "token=${K8S_INFRA_TRIGGER_TOKEN}"
      -F "variables[VERSION]=${VERSION}"
      -F "variables[CHANGED]=${CHANGED}"
      -F "variables[BRANCH]=${PRE_ID}"
      ${K8S_INFRA_TRIGGER_PIPILINE_URL}
  #  environment:
  #    name: review/mr-${CI_MERGE_REQUEST_IID}
  #    url: https://mr-${CI_MERGE_REQUEST_IID}.digisac.io
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ /([b|B]ump version|^docs|^\[no-build\])/ || $CI_MERGE_REQUEST_TITLE =~ /^WIP:/'
      when: never
    - if: '$CI_COMMIT_REF_NAME == "master" || $CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME =~ /^release/'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: on_success
    - when: never

build-develop:
  stage: build
  before_script:
    - *ssh-config
    - *aws-login
  variables:
    BACK_IMAGE_NAME: digisac-back
    FRONT_IMAGE_NAME: digisac-front
  script:
    # Login Oracle
    - docker login -u axvaplbwrlcl/digisac-docker-push -p "${ORACLE_PULL_PUSH_AUTH_TOKEN}" sa-vinhedo-1.ocir.io

    # Cria uma branch com base em develop
    - git switch -C ${CI_COMMIT_REF_NAME} ${CI_COMMIT_SHA}
    - git pull --no-commit --no-edit --ff -X patience origin ${CI_COMMIT_REF_NAME}

    # get last git tag
    - LAST_TAG_DEV=$(git tag -l --sort=version:refname | grep -E "^v[0-9]*.[0-9]*.[0-9]*(-dev.[0-9])*$" | tail -n 1)
    - echo $LAST_TAG_DEV
    - git checkout "$LAST_TAG_DEV" -- lerna.json || true
    - git commit -am "resets lerna.json to $LAST_TAG_DEV" || true

    # Release versioning
    - lerna version prerelease --preid dev --conventional-prerelease --yes --no-changelog --message "Bump version %s" || echo 'lerna version failed, maybe the tag already exists'

    - VERSION=$(awk -F\" '/"version":/ {print $4}' lerna.json)
    - echo $VERSION

    - LATEST_IMAGE_TAG=$(awk -F\" '/"version":/ {print $4}' lerna.json)

    # Get changed microservices (including front)
    - CHANGED=$(bash ./changed.sh "v${VERSION}" || bash ./changed.sh) || ''

    # Build and push back docker image
    - cd packages/back; bash ./build-image.sh ${LATEST_IMAGE_TAG} ${VERSION} ${BACK_IMAGE_NAME} ${IMAGE_REPOSITORY}; cd -;
    # - if [[ $CHANGED =~ "back" ]]; then
    #     cd packages/back; bash ./build-image.sh ${LATEST_IMAGE_TAG} ${VERSION} ${BACK_IMAGE_NAME} ${IMAGE_REPOSITORY}; cd -;
    #   else
    #     echo "Nada de novo no back. Prosseguindo...";
    #   fi;

    # Build and push front docker image
    - cd packages/front; bash ./build-image.sh ${LATEST_IMAGE_TAG} ${VERSION} ${FRONT_IMAGE_NAME} ${IMAGE_REPOSITORY}; cd -;
    # - if [[ $CHANGED =~ "front" ]]; then
    #     cd packages/front; bash ./build-image.sh ${LATEST_IMAGE_TAG} ${VERSION} ${FRONT_IMAGE_NAME} ${IMAGE_REPOSITORY}; cd -;
    #   else
    #     echo "Nada de novo no front. Prosseguindo...";
    #   fi;
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ /([b|B]ump version|^docs|^\[no-build\])/ || $CI_MERGE_REQUEST_TITLE =~ /^WIP:/'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: '$CI_COMMIT_BRANCH == "develop"'

build-release:
  stage: build
  before_script:
    - *ssh-config
    - *aws-login
  variables:
    BACK_IMAGE_NAME: digisac-back
    FRONT_IMAGE_NAME: digisac-front
  script:
    - echo 'Stage Build Release'

    # Login Oracle
    - docker login -u axvaplbwrlcl/digisac-docker-push -p "${ORACLE_PULL_PUSH_AUTH_TOKEN}" sa-vinhedo-1.ocir.io

    # Criação de nova branch
    - git switch -C ${CI_COMMIT_REF_NAME} ${CI_COMMIT_SHA}
    - git pull --no-commit --no-edit --ff -X patience origin ${CI_COMMIT_REF_NAME}

    # Verifica a versão release
    - PRE_RELEASE="rc"
    - bash utils/check-version.sh "$PRE_RELEASE"

    # Geração de versão
    - lerna version prerelease --preid ${PRE_RELEASE} --yes --no-changelog --message "Bump version %s" || echo 'lerna version failed, maybe the tag already exists'

    # Versão
    - VERSION=$(awk -F\" '/"version":/ {print $4}' lerna.json)
    - echo $VERSION

    # Última imagem gerada para Tag
    - LATEST_IMAGE_TAG=$(awk -F\" '/"version":/ {print $4}' lerna.json)
    - echo $LATEST_IMAGE_TAG

    # Build back
    - cd packages/back; bash ./build-image.sh ${LATEST_IMAGE_TAG} ${VERSION} ${BACK_IMAGE_NAME} ${IMAGE_REPOSITORY}; cd -;

    # Build front
    - cd packages/front; bash ./build-image.sh ${LATEST_IMAGE_TAG} ${VERSION} ${FRONT_IMAGE_NAME} ${IMAGE_REPOSITORY}; cd -;
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ /([b|B]ump version|^docs|^\[no-build\])/ || $CI_MERGE_REQUEST_TITLE =~ /^WIP:/'
      when: never
    - if: '$CI_COMMIT_REF_NAME =~ /^release/ && $CI_PIPELINE_SOURCE == "merge_request_event"'

build-master:
  stage: build
  before_script:
    - *ssh-config
    - *aws-login
  variables:
    BACK_IMAGE_NAME: digisac-back
    FRONT_IMAGE_NAME: digisac-front
  script:
    - echo 'Stage Build Master'

    # Login Oracle
    - docker login -u axvaplbwrlcl/digisac-docker-push -p "${ORACLE_PULL_PUSH_AUTH_TOKEN}" sa-vinhedo-1.ocir.io

    # Criação de nova branch
    - git switch -C ${CI_COMMIT_REF_NAME} ${CI_COMMIT_SHA}
    - git pull --no-commit --no-edit --ff -X patience origin ${CI_COMMIT_REF_NAME}

    # Verificação a versão de master
    - bash utils/check-version.sh release
    - "[[ ${CI_COMMIT_MESSAGE} =~ '^fix' ]] && RELEASE_TYPE=patch"
    - "[[ ${CI_COMMIT_MESSAGE} =~ '^(feat|refact|perf)' ]] && RELEASE_TYPE=minor"
    - "[[ ${CI_COMMIT_MESSAGE} =~ 'BREAKING CHANGE' ]] && RELEASE_TYPE=major"

    # Versão
    - lerna version $RELEASE_TYPE --yes --conventional-commits --conventional-graduate --create-release gitlab
    - VERSION=$(awk -F\" '/"version":/ {print $4}' lerna.json)
    - echo $VERSION

    - LATEST_IMAGE_TAG=$(awk -F\" '/"version":/ {print $4}' lerna.json)

    - cd packages/back; bash ./build-image.sh ${LATEST_IMAGE_TAG} ${VERSION} ${BACK_IMAGE_NAME} ${IMAGE_REPOSITORY}; cd -;
    - cd packages/front; bash ./build-image.sh ${LATEST_IMAGE_TAG} ${VERSION} ${FRONT_IMAGE_NAME} ${IMAGE_REPOSITORY}; cd -;

  rules:
    - if: '$CI_COMMIT_MESSAGE =~ /([b|B]ump version|^docs|^\[no-build\])/ || $CI_MERGE_REQUEST_TITLE =~ /^WIP:/'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: '$CI_COMMIT_BRANCH == "master"'
