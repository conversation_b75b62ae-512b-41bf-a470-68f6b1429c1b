#!/bin/bash

set -e

LAST_TAG=${1:-'HEAD^'}

git fetch origin "refs/tags/${LAST_TAG}:refs/tags/${LAST_TAG}" || true

DIFF=$(git diff --name-only "$LAST_TAG" | sort -u | uniq)

MICRO_SERVICES_THAT_CHANGED=''

# back
BACK_SERVICES_PATH=packages/back/src/microServices
BLACK_LIST='_+_cemetery'

MICRO_SERVICES="$(ls $BACK_SERVICES_PATH | grep -v $BLACK_LIST)"

for SERVICE in $MICRO_SERVICES; do
    if echo "$DIFF" | grep "${BACK_SERVICES_PATH}/${SERVICE}" > /dev/null; then
        MICRO_SERVICES_THAT_CHANGED+=" $SERVICE"
    fi
done

# front
FRONT_SERVICES_PATH=packages/front

if echo "$DIFF" | grep $FRONT_SERVICES_PATH > /dev/null; then
    MICRO_SERVICES_THAT_CHANGED+=" front"
fi

echo "$MICRO_SERVICES_THAT_CHANGED"
