{"name": "@digisac/root", "license": "NO LICENSE", "private": true, "scripts": {"lerna": "lerna", "bootstrap": "yarn lerna bootstrap", "setup": "yarn install && yarn run bootstrap", "start": "yarn lerna run start --parallel", "inner-install": "yarn lerna exec --parallel yarn", "format-all": "(cd packages/back && yarn run format) & (cd packages/front && yarn run format)", "test-lint-staged": "lint-staged", "dc:logs": "docker-compose logs --tail 500 -f $*", "dc:exec": "docker-compose exec app-api bash -c $*", "dc:migrate": "'cd packages/back && yarn sequelize db:migrate --debug", "dc:res": "docker-compose restart $*", "format:back": "cd packages/back && yarn run format", "format:front": "cd packages/front && yarn run format", "prepare": "husky install"}, "devDependencies": {"husky": "8.0.3", "lerna": "8.0.0", "lint-staged": "15.2.0", "prettier": "^3.1.0"}, "lint-staged": {"packages/back/**/*.{js,ts,jsx,tsx}": ["yarn format:back"], "packages/front/**/*.{js,ts,jsx,tsx}": ["yarn format:front"]}, "dependencies": {"@hookform/resolvers": "^3.3.1", "react-hook-form": "^7.47.0", "yup": "^1.3.2"}}