/* eslint-disable no-undef */
// eslint-disable-next-line import/no-extraneous-dependencies
require('@cypress/snapshot').register()

Cypress.Commands.add('logout', () => {
  cy.clearCookies()
})

Cypress.Commands.add('login', (username, password, scope = '*') => {
  const { env } = Cypress

  const grantData = {
    grant_type: 'password',
    client_id: env('CLIENT_ID'),
    client_secret: env('CLIENT_SECRET'),
    username,
    password,
    scope,
  }

  cy.request({
    method: 'POST',
    url: `${env('API_URL')}/oauth/token`,
    body: grantData,
    form: true,
  }).then((res) => {
    cy.setCookie('auth', JSON.stringify({ accessToken: res.body.access_token }))
    cy.setCookie('reduxSagaPersistor', JSON.stringify(['auth']))
  })
})

Cypress.Commands.add('assertRoute', (route) => {
  cy.url().should('equal', `${window.location.origin}${route}`)
})

Cypress.Commands.add('seedDB', (seeds = []) => {
  // if (!Array.isArray(seeds)) seeds = [seeds]
  seeds.map((seed) => cy.task('seedDb', seed, { timeout: 20000 }))
})

Cypress.Commands.add('uploadFile', (file) => {
  cy.get('input[type=file]').then((el) => {
    const dataTransfer = new DataTransfer()
    dataTransfer.items.add(file)
    // eslint-disable-next-line no-param-reassign
    el[0].files = dataTransfer.files
    el[0].dispatchEvent(
      new Event('change', {
        bubbles: true,
        target: {
          files: [file],
        },
      }),
    )
    return el
  })
})
