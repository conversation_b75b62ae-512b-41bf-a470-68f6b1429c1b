{"presets": ["@babel/preset-env", "@babel/preset-react", "@babel/preset-typescript"], "plugins": ["@babel/plugin-syntax-dynamic-import", "@babel/plugin-transform-react-jsx", "@babel/plugin-transform-runtime", ["@babel/plugin-proposal-class-properties", {"loose": true}], ["@babel/plugin-transform-private-methods", {"loose": true}], ["@babel/plugin-transform-private-property-in-object", {"loose": true}]]}