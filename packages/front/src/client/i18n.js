import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import detector from 'i18next-browser-languagedetector'
import ptBr from '../app/locales/ptbr'
import es from '../app/locales/es'
import en from '../app/locales/en'

const resources = {
  en,
  es,
  'pt-BR': ptBr,
}

i18n.use(detector).use(initReactI18next).init({
  resources,
  keySeparator: '#', // we do not use keys in form messages.welcome,
  // lng: 'pt-BR',
  // fallbackLng: 'pt-BR',
  // debug: true,
})

export default i18n
