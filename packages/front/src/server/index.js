import 'source-map-support/register'
import express from 'express'
import cookieParser from 'cookie-parser'
import compression from 'compression'
import { resolve as pathResolve } from 'path'
import appRootDir from 'app-root-dir'
import cors from 'cors'
import security from './middlewares/security'
import clientBundle from './middlewares/clientBundle'
import serviceWorker from './middlewares/serviceWorker'
import offlinePage from './middlewares/offlinePage'
import errorHandlers from './middlewares/errorHandlers'
import config from '../../config'

const app = express()

app.disable('x-powered-by')
app.use(...security)
app.use(cookieParser())
app.use(compression())
app.use(cors())

if (process.env.BUILD_FLAG_IS_DEV === 'false' && config('serviceWorker.enabled')) {
  app.get(`/${config('serviceWorker.fileName')}`, serviceWorker)
  app.get(`${config('bundles.client.webPath')}${config('serviceWorker.offlinePageFileName')}`, offlinePage)
}

app.use(config('bundles.client.webPath'), clientBundle)

app.use(express.static(pathResolve(appRootDir.get(), config('publicAssetsPath'))))

app.get('/pdf/tickets/:id/:lng', async (request, response) => {
  request.headers['x-real-ip-alternative'] = request.headers['x-real-ip'] || ''

  const render = await import('./middlewares/reactApplication/pdf')

  return render.default(request, response)
})

app.get('/pdf/export-term-pdf/:id/:language', async (request, response) => {
  const render = await import('./middlewares/reactApplication/pdf')
  return render.exportTerm(request, response)
})

app.get('*', (request, response) => {
  // if (process.env.BUILD_FLAG_DISABLE_SSR === 'true') {
  const renderStatic = require('./middlewares/renderStatic').default
  return renderStatic(request, response)
  // }
  // const reactApplication = require('./middlewares/reactApplication').default
  // return reactApplication(request, response)
})

app.use(...errorHandlers)

const listener = app.listen(config('port'), () =>
  console.log(`✓ ${config('htmlPage.defaultTitle')} is ready!
Service Workers: ${config('serviceWorker.enabled')}
Server is now listening on Port ${config('port')}
You can access it in the browser at http://${config('host')}:${config('port')}
Press Ctrl-C to stop.
`),
)

export default listener
