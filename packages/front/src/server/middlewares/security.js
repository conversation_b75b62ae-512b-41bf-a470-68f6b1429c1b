import { v4 as uuid } from 'uuid'
import hpp from 'hpp'
import helmet from 'helmet'
import config from '../../../config'

const cspConfig = {
  directives: {
    childSrc: ["'self'"],
    connectSrc: ['*'], // ["'self'", 'ws:'],
    defaultSrc: ["'self'"],
    imgSrc: ["'self'", 'data: https:'],
    fontSrc: ["'self'", '*.hotjar.com', 'https://fonts.googleapis.com', 'data:'],
    objectSrc: ["'self'"],
    mediaSrc: ["'self'", 'data:'],
    manifestSrc: ["'self'"],
    scriptSrc: ["'self'", "'unsafe-inline'", 'https://www.googletagmanager.com/', 'https://www.clarity.ms'],
    styleSrc: ["'self'", "'unsafe-inline'", 'blob:'],
    formAction: ["'self'", 'https://www.googletagmanager.com/', 'https://announcekit.co/'],
  },
}

const cspExtensions = config('cspExtensions')
Object.keys(cspExtensions).forEach((key) => {
  if (cspConfig.directives[key]) {
    cspConfig.directives[key] = cspConfig.directives[key].concat(cspExtensions[key])
  } else {
    cspConfig.directives[key] = cspExtensions[key]
  }
})

if (process.env.BUILD_FLAG_IS_DEV === 'true') {
  Object.keys(cspConfig.directives).forEach((directive) => {
    cspConfig.directives[directive].push(`${config('host')}:${config('clientDevServerPort')}`)
  })
}

function nonceMiddleware(req, res, next) {
  res.locals.nonce = uuid()
  next()
}

const securityMiddleware = [
  nonceMiddleware,
  hpp(),
  helmet.xssFilter(),
  helmet.ieNoOpen(),
  helmet.noSniff(),
  ...(process.env.BUILD_FLAG_IS_DEV !== 'true' ? [helmet.contentSecurityPolicy(cspConfig)] : []),
]

export default securityMiddleware
