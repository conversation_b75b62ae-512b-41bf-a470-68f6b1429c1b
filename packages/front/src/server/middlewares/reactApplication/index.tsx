import React from 'react'
import { Helmet } from 'react-helmet'
import { renderToPipeableStream, renderToStaticMarkup } from 'react-dom/server'
import { AsyncComponentProvider, createAsyncContext } from 'react-async-component'
import asyncBootstrapper from 'react-async-bootstrapper'
import { Provider as ReduxProvider } from 'react-redux'
import { unstable_HistoryRouter as HistoryRouter } from 'react-router-dom'
import { createMemoryHistory, parsePath } from 'history'
import configureStore from '../../../app/store/configureStore'
import config from '../../../../config'
import ServerHTML from './ServerHTML'
import App from '../../../app/components/App'
import StaticContextProvider from './StaticContextProvider'

const respondStatic = (response, nonce) => {
  const html = `<!DOCTYPE html>${renderToStaticMarkup(<ServerHTML nonce={nonce} />)}`
  response.status(200).type('html').send(html)
}

export default async (request, response) => {
  if (process.env.BUILD_FLAG_IS_DEV === 'true') {
    console.log(`REQUEST: Received for "${request.url}"`)
  }

  if (typeof response.locals.nonce !== 'string') {
    throw new Error('A "nonce" value has not been attached to the response')
  }
  const { nonce } = response.locals

  if (config('disableSSR')) {
    if (process.env.BUILD_FLAG_IS_DEV === 'true') {
      console.log('==> Handling react route without SSR')
    }

    respondStatic(response, nonce)
    return
  }

  try {
    const initialLocation = parsePath(request.url)
    const history = createMemoryHistory({
      initialEntries: [initialLocation],
      initialIndex: 0,
    })
    const asyncComponentsContext = createAsyncContext()
    const reactRouterContext = {}

    const store = await configureStore({}, { history, cookies: request.cookies })

    const AppContainer = () => (
      <AsyncComponentProvider asyncContext={asyncComponentsContext}>
        <ReduxProvider store={store}>
          <StaticContextProvider context={reactRouterContext}>
            <HistoryRouter history={history}>
              <App />
            </HistoryRouter>
          </StaticContextProvider>
        </ReduxProvider>
      </AsyncComponentProvider>
    )

    await asyncBootstrapper(<AppContainer />)

    let didError = false
    const htmlStream = renderToPipeableStream(
      <ServerHTML
        nonce={nonce}
        helmet={Helmet.renderStatic()}
        appState={store.getState()}
        asyncComponentsState={asyncComponentsContext.getState()}
        wasSSR
      >
        <AppContainer />
      </ServerHTML>,
      {
        onShellReady() {
          response
            .status(reactRouterContext.status || 200)
            .type('html')
            .write('<!doctype html>')
          htmlStream.pipe(response)
        },
        onError(err) {
          console.error(err)
          didError = true
          if (!response.headersSent) {
            respondStatic(response, nonce)
          }
        },
      },
    )

    htmlStream.on('end', () => {
      if (didError) {
        respondStatic(response, nonce)
      }
    })

    switch (reactRouterContext.status) {
      case 301:
      case 302:
        response.status(reactRouterContext.status).location(reactRouterContext.url).end()
        break
      default:
        if (!didError) {
          response
            .status(reactRouterContext.status || 200)
            .type('html')
            .write('<!doctype html>')
          htmlStream.pipe(response)
        }
    }
  } catch (err) {
    console.error(err)
    respondStatic(response, nonce)
  }
}
