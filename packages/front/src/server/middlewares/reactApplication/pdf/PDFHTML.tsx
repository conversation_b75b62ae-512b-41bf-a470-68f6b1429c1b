import React, { Children } from 'react'
import PropTypes from 'prop-types'
import { identity } from 'lodash'
import ifElse from '../../../../../internal/utils/logic/ifElse'
import getClientBundleEntryAssets from '../getClientBundleEntryAssets'
import HTML from '../../../../../internal/components/HTML'

function KeyedComponent({ children }) {
  return Children.only(children)
}

const clientEntryAssetsRaw = getClientBundleEntryAssets(true)
const clientEntryAssets = {
  css: [
    clientEntryAssetsRaw.index.css,
    clientEntryAssetsRaw['digisac-style'].css,
    clientEntryAssetsRaw['innerchatbox-digisac-style'].css,
  ],
}

function stylesheetTag(stylesheetFilePath) {
  if (Array.isArray(stylesheetFilePath)) {
    return <>{stylesheetFilePath.map(stylesheetTag)}</>
  }

  const urlStyle =
    process.env.NODE_ENV == 'development' ? stylesheetFilePath.replace('127.0.0.1', 'app-front') : stylesheetFilePath

  return <link href={urlStyle} media="screen, projection, print" rel="stylesheet" type="text/css" />
}

function PDFHTML(props) {
  const { helmet, reactAppString } = props

  const headerElements = [
    ...ifElse(helmet)(() => helmet.title.toComponent(), []),
    ifElse(!!clientEntryAssets?.css)(() => stylesheetTag(clientEntryAssets.css)),
    ...ifElse(helmet)(() => helmet.style.toComponent(), []),
  ].filter(identity)

  return (
    <HTML
      htmlAttributes={ifElse(helmet)(() => helmet.htmlAttributes.toComponent(), null)}
      headerElements={headerElements.map((x, idx) => (
        <KeyedComponent key={idx}>{x}</KeyedComponent>
      ))}
      appBodyString={reactAppString}
    />
  )
}

PDFHTML.propTypes = {
  helmet: PropTypes.object,
  reactAppString: PropTypes.string,
}

export default PDFHTML
