import React from 'react'
import { withTranslation } from 'react-i18next'
import Helmet from 'react-helmet'
import { Badge, Col, Row } from 'reactstrap'
import Message from '../App/Dashboard/Chat/ChatBox/InnerChatBox/Message/Message'
import messageApi from '../../resources/message/api'
import ticketApi from '../../resources/ticket/api'
import Avatar from '../common/connected/Avatar/Avatar'
import ContactName from '../common/unconnected/ContactName'
import ServiceBadge from '../common/unconnected/ServiceBadge'
import DepartmentBadge from '../common/unconnected/DepartmentBadge'
import UserBadge from '../common/unconnected/UserBadge'
import Icon from '../common/unconnected/Icon'
import formatDate from '../../utils/date/format'
import Join from '../common/connected/Join'
import config from '../../../../config'

export const fetch = async ({ id, authorization, impersonate = false, headers = null, withoutComments = false }) => {
  const options = {
    headers: { ...headers, authorization, impersonate },
    baseURL: config('privateApiUrl'),
  }

  const messagesQuery = {
    includeTicketTransfer: true,
    where: {
      ticketId: id,
      ...(withoutComments && {
        isComment: false,
      }),
      type: {
        $ne: 'reaction',
      },
    },
    include: [
      'file',
      'files',
      'preview',
      'thumbnail',
      'ticket.department',
      'hsmFile',
      'hsm',
      'reactions',
      {
        model: 'user',
        attributes: ['id', 'name'],
      },
      {
        model: 'ticketTransfer',
        include: ['fromUser', 'toUser', 'fromDepartment', 'toDepartment', 'byUser'],
      },
      {
        model: 'from',
        attributes: ['id', 'name', 'alternativeName', 'internalName'],
      },
      {
        model: 'contact',
        attributes: ['id'],
      },
      {
        model: 'quotedMessage',
        include: ['file', 'files', 'preview', 'thumbnail', 'user', 'contact', 'from', 'hsmFile', 'hsm'],
      },
    ],
    order: [
      ['timestamp', 'ASC'],
      ['id', 'ASC'],
    ],
    paginate: false,
    noExpirationToken: true,
  }
  const messages = await messageApi.fetchMany(messagesQuery, options)

  const ticketQuery = {
    include: [
      'account',
      {
        model: 'contact',
        include: ['avatar', 'thumbAvatar', 'service'],
      },
      {
        include: ['department', 'user'],
      },
      {
        model: 'ticketTopics',
      },
    ],
  }
  const ticket = await ticketApi.fetchById(id, ticketQuery, options)

  return { ticket, messages }
}

function Ticket({ ticket, messagesByDate = [], exportPdf = false, webchatExport = false, t }) {
  const { contact, protocol, department, user, ticketTopics, comments } = ticket
  const { avatar, service, thumbAvatar } = contact

  return (
    <div>
      <Helmet title={t(`TICKET ${protocol}`)} />

      <div className="d-flex p-3 mt-3 shadow-sm bg-white" style={{ border: '1px solid #8C97A4', borderRadius: '1px' }}>
        <div className="d-flex align-items-center flex-wrap">
          {(thumbAvatar || avatar) && (
            <Avatar
              url={(thumbAvatar || {}).url || (avatar || {}).url}
              style={{
                height: 50,
                width: 50,
                marginRight: 16,
              }}
            />
          )}
        </div>

        <div className="container-fluid">
          <Row>
            <Col className="col-6">
              <div className="d-block">
                <ContactName contact={contact} wrapNames={true} pdf={true} />
                <strong className="d-block">Canal do chamado:</strong>
                <ServiceBadge flag service={service} />
                <DepartmentBadge flag department={department} />
                <UserBadge flag user={user} />
              </div>
            </Col>

            <Col className="col-6">
              {contact.data && contact.data.number && (
                <p
                  className="my-0"
                  title={t(`CREATE_STATS_TICKET_HISTORY_LABEL_CONTACT_NUMBER ${contact.data.number}`)}
                >
                  <Icon name="mobile-alt" className="mr-2" fixedWidth />
                  {contact.data.number}
                </p>
              )}

              {protocol && (
                <div>
                  <strong>Chamado número: </strong>
                  {protocol}
                </div>
              )}

              <p
                className="my-0"
                title={t(`CREATE_STATS_TICKET_HISTORY_LABEL_START_CALL ${formatDate(ticket.startedAt)}`)}
              >
                <strong>Abertura do chamado: </strong>
                {formatDate(ticket.startedAt)}
              </p>

              {ticket.endedAt && (
                <p
                  className="my-0"
                  title={t(`CREATE_STATS_TICKET_HISTORY_LABEL_END_CALL ${formatDate(ticket.endedAt)}`)}
                >
                  <strong>Fechamento de chamado: </strong>
                  {formatDate(ticket.endedAt)}
                </p>
              )}
            </Col>
          </Row>
        </div>
      </div>

      {!webchatExport && (
        <div className="bg-white shadow-sm rounded-bottom p-3">
          <div>
            <div className="text-muted font-weight-bold">{t('CREATE_STATS_TICKET_HISTORY_LABEL_TOPIC_MANY')}</div>
            {ticketTopics?.length ? <Join items={ticketTopics} render={(topic) => topic.name} /> : '-'}

            <br />

            <div className="text-muted mt-3 font-weight-bold">{t('CREATE_STATS_TICKET_HISTORY_LABEL_RESUME_MANY')}</div>
            {comments || '-'}
          </div>
        </div>
      )}

      <div className="container">
        <div className="row">
          <div className="col-12 text-center">
            <p className="font-weight-bold" style={{ fontSize: '24px', padding: '20px' }}>
              Histórico de mensagens do chamado
            </p>
          </div>
        </div>
      </div>

      <div>
        {messagesByDate.map(({ date, messages }) => (
          <div key={date}>
            <div className="d-flex justify-content-center align-items-center my-3">
              <h5>
                <Badge
                  className="p-3 text-white fw-semibold"
                  style={{
                    minHeight: 0,
                    backgroundColor: '#95A7CC',
                    borderRadius: '5px',
                    fontFamily: 'Montserrat',
                  }}
                >
                  {date}
                </Badge>
              </h5>
            </div>
            {messages.map((message) => (
              <Message
                key={message.id}
                message={message}
                viewMode
                style={{ background: '#FF0000' }}
                print
                exportPdf
                webchatExport={webchatExport}
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  )
}

export default withTranslation(['ticketHistory', 'common'])(Ticket)
