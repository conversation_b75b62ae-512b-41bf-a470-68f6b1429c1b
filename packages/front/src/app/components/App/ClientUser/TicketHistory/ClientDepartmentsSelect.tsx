import React from 'react'
import GenericResourceSelect from '../../../common/unconnected/GenericResourceSelect'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyDepartments } from '../../../../resources/client/department/requests'

function DepartmentsSelect(props) {
  const [{ data: models = [], pagination, isLoading }, fetch] = useFetchManyDepartments()

  const handleFetch = useEventCallback(({ search }) =>
    fetch({
      where: {
        ...(search && {
          name: { $iLike: `%${search}%` },
        }),
      },
    }),
  )

  return <GenericResourceSelect {...props} options={models} fetch={handleFetch} isLoading={isLoading} pagination />
}

export default DepartmentsSelect
