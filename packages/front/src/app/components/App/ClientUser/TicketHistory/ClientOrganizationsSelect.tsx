import React from 'react'
import GenericResourceSelect from '../../../common/unconnected/GenericResourceSelect'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyOrganizations } from '../../../../resources/client/organization/requests'

function OrganizationsSelect(props) {
  const [{ data: models = [], pagination, isLoading }, fetch] = useFetchManyOrganizations()

  const handleFetch = useEventCallback(({ search }) =>
    fetch({
      where: {
        ...(search && {
          name: { $iLike: `%${search}%` },
        }),
      },
    }),
  )

  return <GenericResourceSelect {...props} options={models} fetch={handleFetch} isLoading={isLoading} pagination />
}

export default OrganizationsSelect
