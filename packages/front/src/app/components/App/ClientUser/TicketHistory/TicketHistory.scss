.contact-item {
  cursor: pointer;
  padding: 15px;
}

.contact-item.active {
  background: #e9ebeb;
}

.filter-button {
  color: #009947;
  border-color: #009947;
  margin-left: 12px;
}

.filter-button:hover {
  background: #009947;
  color: white;
}

.avatarDefault {
  z-index: 1 !important;
  height: 50px !important;
  width: 50px !important;
  position: absolute;
  padding-left: 13px;
  padding-top: 9px;
  border-radius: 50%;
  background-color: whitesmoke;
  color: lightgrey;
}

.avatarImage {
  position: relative !important;
  overflow: hidden !important;
  z-index: 100 !important;
}

$opened-color: #3fb24f;
$closed-color: #bab2b2;

.opened-ticket-border {
  border-left: 7px $opened-color solid;
}

.closed-ticket-border {
  border-left: 7px $closed-color solid;
}

.bg-opened {
  background-color: $opened-color;
}

.bg-closed {
  background-color: $closed-color;
}

.color-opened {
  color: $opened-color;
}

.color-closed {
  color: $closed-color;
}
