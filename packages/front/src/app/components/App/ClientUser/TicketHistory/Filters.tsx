import React from 'react'
import { Col, FormGroup, Input, Label, Row } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { InputGroupWrapper } from '../../../common/unconnected/InputGroup'
import Select from '../../../common/unconnected/Select'
import DataSelect from '../../../common/unconnected/Datetime'
import ClientOrganizationsSelect from './ClientOrganizationsSelect'
import ClientDepartmentsSelect from './ClientDepartmentsSelect'

function Filters({ filters, handleFilterChange }) {
  const { t } = useTranslation(['ticketHistory'])

  return (
    <Row>
      <Col md="3">
        <InputGroupWrapper
          id="organization"
          label={t('CREATE_STATS_TICKET_HISTORY_LABEL_ORGANIZATION')}
          render={(input) => (
            <ClientOrganizationsSelect
              stateId="clientUserTicketsOrganization"
              id={input.id}
              value={filters.organization}
              onChange={(value) => handleFilterChange('organization', value)}
            />
          )}
        />
      </Col>

      <Col md="3">
        <InputGroupWrapper
          id="department"
          label={t('CREATE_STATS_TICKET_HISTORY_LABEL_DEPARTMENT')}
          render={(input) => (
            <ClientDepartmentsSelect
              stateId="clientUserTicketsDepartment"
              id={input.id}
              value={filters.department}
              onChange={(value) => handleFilterChange('department', value)}
            />
          )}
        />
      </Col>

      <Col md="3">
        <FormGroup>
          <Label htmlFor="contato">{t('CREATE_STATS_TICKET_HISTORY_LABEL_CONTACT_NAME')}</Label>
          <Input
            type="text"
            value={filters.contactName}
            onChange={(e) => handleFilterChange('contactName', e.target.value)}
            placeholder={t('CREATE_STATS_TICKET_HISTORY_LABEL_CONTACT_NAME_PLACEHOLDER')}
          />
        </FormGroup>
      </Col>

      <Col md="3">
        <FormGroup>
          <Label htmlFor="number">{t('CREATE_STATS_TICKET_HISTORY_LABEL_CONTACT_NUMBER')}</Label>
          <Input
            type="text"
            value={filters.contactNumber}
            onChange={(e) => handleFilterChange('contactNumber', e.target.value)}
            placeholder={t('CREATE_STATS_TICKET_HISTORY_LABEL_CONTACT_NUMBER_PLACEHOLDER')}
          />
        </FormGroup>
      </Col>

      <Col md="3">
        <FormGroup>
          <Label htmlFor="protocolNumber">{t('CREATE_STATS_TICKET_HISTORY_LABEL_PROTOCOL')}</Label>
          <Input
            type="text"
            value={filters.protocolNumber}
            onChange={(e) => handleFilterChange('protocolNumber', e.target.value)}
            placeholder={t('CREATE_STATS_TICKET_HISTORY_LABEL_PROTOCOL_PLACEHOLDER')}
          />
        </FormGroup>
      </Col>

      <Col md="3">
        <FormGroup>
          <InputGroupWrapper
            id="from"
            label={t('CREATE_STATS_TICKET_HISTORY_LABEL_FILTER_FROM')}
            type="textarea"
            {...{
              filters,
            }}
            render={(input) => <DataSelect value={filters.from} onChange={(e) => handleFilterChange('from', e)} />}
          />
        </FormGroup>
      </Col>

      <Col md="3">
        <FormGroup>
          <InputGroupWrapper
            id="to"
            label={t('CREATE_STATS_TICKET_HISTORY_LABEL_FILTER_TO')}
            type="textarea"
            {...{
              filters,
            }}
            render={(input) => <DataSelect value={filters.to} onChange={(e) => handleFilterChange('to', e)} />}
          />
        </FormGroup>
      </Col>

      <Col md="3">
        <FormGroup>
          <InputGroupWrapper
            id="periodType"
            label={t('CREATE_STATS_TICKET_HISTORY_LABEL_PERIOD_TYPE')}
            render={({ id }) => (
              <Select
                id={id}
                value={!filters.periodType ? { value: 'all', label: 'Todos' } : filters.periodType}
                onChange={(value) => {
                  handleFilterChange('periodType', value)
                }}
                options={[
                  {
                    value: 'all',
                    label: t('CREATE_STATS_TICKET_HISTORY_LABEL_ALL'),
                  },
                  {
                    value: 'openDate',
                    label: t('CREATE_STATS_TICKET_HISTORY_LABEL_PERIOD_TYPE_OPEN_DATE'),
                  },
                  {
                    value: 'closeDate',
                    label: t('CREATE_STATS_TICKET_HISTORY_LABEL_PERIOD_TYPE_CLOSE_DATE'),
                  },
                ]}
                getOptionLabel={(o) => o.label}
              />
            )}
          />
        </FormGroup>
      </Col>
    </Row>
  )
}

export default Filters
