import { connect } from 'react-redux'
import {
  actions as listActions,
  selectors as listSelector,
} from '../../../../modules/overview/modules/ticketHistory/modules/list'
import TicketHistory from './TicketHistory'

const mapStateToProps = (state) => ({
  tickets: listSelector.getCurrentPageTickets(state),
  pagination: listSelector.getPagination(state),
  isLoading: listSelector.getIsLoading(state),
  total: listSelector.getTotal(state),
})

const actionCreators = {
  fetchManyTickets: listActions.fetchMany,
}

export default connect(mapStateToProps, actionCreators)(TicketHistory)
