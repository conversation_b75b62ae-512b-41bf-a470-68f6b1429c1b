import React, { useEffect, useState } from 'react'
import { with<PERSON><PERSON><PERSON> } from 'react-router'
import { useTranslation } from 'react-i18next'
import Helmet from 'react-helmet'
import { Modal, ModalBody, ModalHeader } from 'reactstrap'
import { useRequest } from '../../../../../hooks/useRequest'
import messageApi from '../../../../../resources/client/message/api'
import ticketsApi from '../../../../../resources/client/ticket/api'
import Message from '../../../Dashboard/Chat/ChatBox/InnerChatBox/Message/Message'
import LoadingButton from '../../../../common/unconnected/LoadingButton'
import Lightbox from '../../../../common/unconnected/LightBox'
import HistoryItem from '../HistoryItem'

function TicketsShow({ match, history }) {
  const { t } = useTranslation(['ticketHistory', 'common'])
  const { id } = match.params
  const [isOpen, setIsOpen] = useState(true)
  const [lightBoxConfig, setLightboxConfig] = useState({})
  const [ticketsData, setTicketsData] = useState({})

  const [pagination, setPagination] = useState({
    limit: 30,
    offset: 0,
    paginate: false,
    withTotal: true,
  })
  const [messages, setMessages] = useState([])
  const [{ response: messagesResponse, isLoading: isMessagesLoading }, fetchManyMessages] = useRequest(
    messageApi.fetchMany,
  )
  const [{ response: ticketsResponse, isLoading: isTicketsLoading }, fetchTicketById] = useRequest(ticketsApi.fetchById)

  const loadMore = () => {
    const { offset } = pagination
    if (messagesResponse && messagesResponse.total && pagination.offset >= messagesResponse.total) return

    setPagination({
      ...pagination,
      offset: offset + 30,
    })

    fetchManyMessages({
      where: {
        ticketId: id,
      },
      include: [
        'file',
        'files',
        'preview',
        'thumbnail',
        'ticket',
        {
          model: 'user',
          attributes: ['id', 'name'],
        },
        {
          model: 'ticketTransfer',
          include: ['fromUser', 'toUser', 'fromDepartment', 'toDepartment', 'byUser'],
        },
        {
          model: 'from',
          attributes: ['id', 'name', 'alternativeName', 'internalName'],
        },
        {
          model: 'contact',
          attributes: ['id', 'name'],
        },
        {
          model: 'quotedMessage',
          include: ['file', 'files', 'preview', 'thumbnail'],
        },
      ],
      order: [
        ['timestamp', 'ASC'],
        ['id', 'ASC'],
      ],
      ...pagination,
    })
  }

  const closeModal = () => {
    setIsOpen(false)
    setTimeout(() => history.push('/client/ticket-history'), 300)
  }

  useEffect(() => {
    loadMore()
    fetchTicketById(id, {
      include: [
        {
          model: 'contact',
          include: [
            {
              model: 'thumbAvatar',
            },
            {
              model: 'person',
              include: ['organizations'],
            },
          ],
        },
        'department',
        'user',
      ],
    })
  }, [id])

  useEffect(() => {
    if (!messagesResponse) return

    setMessages((messages) => [...messages, ...messagesResponse.data])
  }, [messagesResponse])

  useEffect(() => {
    if (!ticketsResponse) return

    const { contact, department, protocol } = ticketsResponse
    const { name: contactName, alternativeName, internalName } = contact
    const { name: departmentName } = department

    setTicketsData({
      protocolNumber: protocol,
      contactName: internalName || contactName || alternativeName,
      departmentName,
    })
  }, [ticketsResponse])

  const isLoadMorePossible =
    messagesResponse && messagesResponse.total ? messagesResponse.total > messages.length : true

  return (
    <>
      <Helmet title={t('CREATE_STATS_TICKET_HISTORY_LABEL_TICKET')} />

      <Modal isOpen={isOpen} toggle={closeModal} autoFocus={false} size="lg" scrollable zIndex={10} backdrop="static">
        <ModalHeader toggle={closeModal} className="w-100">
          {t('CREATE_STATS_TICKET_HISTORY_LABEL_TICKET')}
        </ModalHeader>

        <ModalBody className="bg-light rounded">
          {!isTicketsLoading && <HistoryItem ticket={ticketsResponse} />}

          <div className="p-3 mt-3 shadow-sm rounded" style={{ background: 'rgb(230, 230, 230)' }}>
            {isLoadMorePossible && (
              <div className="d-flex justify-content-center mb-3">
                <LoadingButton color="default" onClick={loadMore} isLoading={isMessagesLoading}>
                  {t('common:LABEL_LOAD_MORE')}
                </LoadingButton>
              </div>
            )}

            {messages.map((message) => (
              <Message
                key={message.id}
                message={message}
                viewMode
                onImageClick={(imageUrl) =>
                  setLightboxConfig({
                    imageUrl,
                    isOpen: true,
                  })
                }
              />
            ))}
          </div>
        </ModalBody>
      </Modal>

      <Lightbox
        {...lightBoxConfig}
        onClose={() =>
          setLightboxConfig({
            ...lightBoxConfig,
            isOpen: false,
          })
        }
      />
    </>
  )
}

export default withRouter(TicketsShow)
