import React, { useEffect, useRef, useState } from 'react'
import Helmet from 'react-helmet'
import { Row, Col, Button, UncontrolledTooltip } from 'reactstrap'
import { Route, Switch } from 'react-router'
import { pick } from 'lodash'
import Icon from '../../../common/unconnected/Icon'
import TicketsShow from './TicketsShow'
import LoadingSpinner from '../../../common/unconnected/LoadingSpinner'
import TablePagination from '../../../common/unconnected/TablePagination'
import useIndexController from '../../../../hooks/crud/useIndexController'
import { useFetchManyTickets } from '../../../../resources/client/ticket/requests'
import Filters from './Filters'
import HistoryItem from './HistoryItem'
import './TicketHistory.scss'
import Header from '../Header'

const buildQuery = ({ filters, localPagination }) => ({
  order: [['updatedAt', 'DESC']],
  filters: {
    ...pick(filters, ['protocolNumber', 'from', 'to', 'contactName', 'contactNumber']),
    status: filters?.status.value,
    periodType: filters?.periodType.value,
    organizationId: (filters?.organization || {}).id,
    departmentId: (filters?.department || {}).id,
  },
  ...(localPagination && {
    page: localPagination.page,
    perPage: localPagination.perPage,
  }),
})

const initialFilters = {
  userName: '',
  contactNumber: '',
  contactName: '',
  protocolNumber: '',
  ticketTopics: [],
  status: { value: 'all', label: 'Todos' },
  periodType: { value: 'all', label: 'Todos' },
  from: '',
  to: '',
  organization: null,
  department: null,
}

function TicketHistory({ match }) {
  const {
    models: tickets,
    pagination,
    isLoading,
    fetch,
    isFiltersShowing,
    toggleFilters,
    filters,
    handleFilterChange,
    handleClearFilters,
    localPagination,
    handleLocalPaginationChange,
  } = useIndexController({
    buildQuery,
    initialFilters,
    useFetchMany: useFetchManyTickets,
    autoFetchOnFiltersChange: false,
  })

  // pooling
  const poolingInterval = useRef(null)
  const [poolingActive, setPoolingActive] = useState(true)

  useEffect(() => {
    if (!isLoading) {
      if (poolingActive && !poolingInterval.current) {
        poolingInterval.current = setInterval(fetch, 1000 * 10)
      }

      if (!poolingActive && poolingInterval.current) {
        clearInterval(poolingInterval.current)
        poolingInterval.current = null
      }
    }
  }, [poolingActive])

  return (
    <>
      <Helmet title="Histórico de Chamados" />

      <Header />

      <div className="container mt-4">
        <Row className="mb-2">
          <Col>
            <h2>Histórico de Chamados</h2>
          </Col>

          <Col className="text-right">
            <Button color="default" onClick={toggleFilters} disabled={isLoading} className="mr-2">
              <Icon name="search" fixedWidth className="mr-1" />
              {isFiltersShowing ? 'Esconder ' : 'Exibir '}
              filtros
            </Button>

            <Button
              id="clear-filters"
              color="default"
              onClick={handleClearFilters}
              className="mr-2"
              disabled={isLoading}
            >
              <Icon name="eraser" fixedWidth className="mr-1" />
              <UncontrolledTooltip placement="bottom" target="clear-filters">
                Limpar filtros
              </UncontrolledTooltip>
            </Button>
            <Button id="refresh" className="mr-2" color="default" type="button" onClick={fetch} disabled={isLoading}>
              <Icon name="sync-alt" fixedWidth />
              <UncontrolledTooltip placement="bottom" target="refresh">
                Atualizar dados
              </UncontrolledTooltip>
            </Button>
          </Col>
        </Row>

        <div>
          <div
            className="mt-3 mb-3 p-3 rounded shadow-sm bg-light"
            style={!isFiltersShowing ? { display: 'none' } : {}}
            data-testid="filters-modal"
          >
            <Filters filters={filters} tickets={tickets} handleFilterChange={handleFilterChange} />
          </div>

          {tickets.length === 0 && isLoading ? (
            <LoadingSpinner isLoading />
          ) : (
            <>
              <div>
                {tickets.length <= 0 ? (
                  <p className="text-center text-secondary" style={{ marginTop: '10%', marginBottom: '5%' }}>
                    Nenhum resultado encontrado
                  </p>
                ) : (
                  tickets.map((ticket) => (
                    <div key={ticket.id} className="mt-3">
                      <HistoryItem ticket={ticket} />
                    </div>
                  ))
                )}
              </div>
              <TablePagination
                pagination={pagination}
                localPagination={localPagination}
                handlePaginationChange={handleLocalPaginationChange}
              />
            </>
          )}
        </div>
      </div>

      <Switch>
        <Route path={`${match.url}/:id`} component={TicketsShow} />
      </Switch>
    </>
  )
}

export default TicketHistory
