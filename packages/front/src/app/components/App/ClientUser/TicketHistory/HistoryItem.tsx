import React, { memo } from 'react'
import { Badge, Col, Row } from 'reactstrap'
import cns from 'classnames'
import { Link } from 'react-router-dom'
import EllipsisSpinner from '../../../common/unconnected/EllipsisSpinner'
import Avatar from '../../../common/connected/Avatar/Avatar'
import ContactName from '../../../common/unconnected/ContactName'
import UserBadge from '../../../common/unconnected/UserBadge'
import DepartmentBadge from '../../../common/unconnected/DepartmentBadge'
import Icon from '../../../common/unconnected/Icon'
import formatDate from '../../../../utils/date/format'
import Join from '../../../common/connected/Join'

export default memo(({ ticket, position, style, id, isLoading, match, className } = {}) => {
  if (!ticket && isLoading) {
    return (
      <div className="text-center pt-2" style={style}>
        <EllipsisSpinner color="#b3b3b3" />
      </div>
    )
  }

  if (!ticket || !ticket.contact) return null

  const { contact, user, department, protocol, ticketTopics = [], comments } = ticket
  const { avatar, person, thumbAvatar } = contact
  const { organizations } = person

  return (
    <>
      <div
        className={cns(
          ['d-flex p-3 shadow-sm bg-white'],
          ticket.isOpen ? ['opened-ticket-border'] : ['closed-ticket-border'],
          ['rounded'],
          className,
        )}
      >
        <div className="d-flex flex-grow-1">
          <Link to={`/client/ticket-history/${ticket.id}`}>
            <div className="d-flex align-items-center flex-wrap">
              <Avatar
                url={(thumbAvatar || avatar || {}).url}
                style={{
                  height: 50,
                  width: 50,
                  marginRight: 16,
                }}
              />
            </div>
          </Link>

          <div className="container">
            <Row>
              <Col className="col-auto mr-auto">
                <div className="h-100 d-flex align-items-center flex-wrap">
                  <div>
                    <Row
                      style={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                      className="mb-1"
                    >
                      <Link to={`/client/ticket-history/${ticket.id}`} className="text-dark">
                        <ContactName contact={contact} />
                      </Link>
                    </Row>

                    <Row>
                      <Badge color="secondary" className="mr-2" title="Organizações">
                        <Icon name="building" className="mr-2" fixedWidth />
                        <Join items={organizations} render={(organization) => organization.name} />
                      </Badge>
                      <DepartmentBadge department={department} />
                      <UserBadge user={user} />
                    </Row>
                  </div>
                </div>
              </Col>

              <Col className="d-inline-block h-100 col-auto">
                {contact.data && contact.data.number && (
                  <p className="my-0" title={`Número do contato: ${contact.data.number}`}>
                    <Icon name="mobile-alt" className="mr-2" fixedWidth />
                    {contact.data.number}
                  </p>
                )}

                {protocol && (
                  <Link to={`/client/ticket-history/${ticket.id}`} replace>
                    <div className="d-flex align-items-center flex-wrap">
                      <p className="my-0" title={`Número do protocolo: ${protocol}`}>
                        <Icon name={['fas', 'marker']} className="mr-2" fixedWidth />
                        {protocol}
                      </p>
                    </div>
                  </Link>
                )}
              </Col>

              <Col className="d-inline-block h-100 col-auto">
                <p className="my-0" title={`Início do chamado: ${formatDate(ticket.startedAt)}`}>
                  <Icon name={['far', 'calendar']} className="mr-2" fixedWidth />
                  {formatDate(ticket.startedAt)}
                </p>

                {ticket.endedAt && (
                  <p className="my-0" title={`Término do chamado: ${formatDate(ticket.endedAt)}`}>
                    <Icon name={['far', 'calendar-check']} className="mr-2" fixedWidth />
                    {formatDate(ticket.endedAt)}
                  </p>
                )}
              </Col>
            </Row>
          </div>
        </div>
      </div>
    </>
  )
})
