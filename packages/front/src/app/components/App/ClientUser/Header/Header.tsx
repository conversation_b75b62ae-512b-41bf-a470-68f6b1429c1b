import React, { memo } from 'react'
import { <PERSON>lapse, Navbar, Nav<PERSON><PERSON><PERSON><PERSON>, Nav, Dropdown<PERSON>enu, DropdownToggle, UncontrolledDropdown } from 'reactstrap'
import Icon from '../../../common/unconnected/Icon'
// @ts-ignore
import logoDigisac from '../../../../assets/logos/digisac.svg'
import Toggler from '../../../common/unconnected/Toggler'
import Link from '../../../common/unconnected/Link'
import isEqual from '../../../../utils/logic/isEqual'
import { MAIN_MENU_HEIGHT } from './contants'
import config from '../../../../../../config'

class Header extends React.Component {
  constructor(props) {
    super(props)

    this.handleLogout = this.handleLogout.bind(this)
    this.handleStopImpersonation = this.handleStopImpersonation.bind(this)
  }

  handleLogout(e) {
    e.preventDefault()
    this.props.logout()
  }

  handleStopImpersonation(e) {
    e.preventDefault()
    this.props.stopImpersonation()
  }

  render() {
    const { user, isImpersonating } = this.props

    return (
      <div id="main-menu">
        <Toggler
          render={({ active, toggle }) => (
            <Navbar color="primary" dark expand style={{ height: MAIN_MENU_HEIGHT }}>
              <NavbarToggler onClick={toggle} />
              <Link to="/" className="navbar-brand" data-testid="menu-button-home">
                <img
                  src={logoDigisac}
                  alt={config('whitelabel.appName')}
                  style={{
                    height: '45px',
                    margin: '-5px 0 -5px -5px',
                  }}
                />
              </Link>
              <Collapse isOpen={active} navbar>
                <Nav className="ml-auto" navbar>
                  {!!user && (
                    <UncontrolledDropdown tag="li" className="nav-item dropdown">
                      <DropdownToggle tag="a" className="nav-link dropdown-toggle hover-pointer" caret>
                        <Icon name={isImpersonating ? 'user-secret' : 'user-circle'} size="lg" fixedWidth />

                        {user.name}
                      </DropdownToggle>

                      <DropdownMenu className="dropdown-menu" right>
                        {isImpersonating && (
                          <a className="dropdown-item" href="#" onClick={this.handleStopImpersonation}>
                            <Icon name="user-secret" fixedWidth className="mr-2" />
                            Parar de impersonar
                          </a>
                        )}

                        <a id="logoutLink" className="dropdown-item" href="#" onClick={this.handleLogout}>
                          <Icon name="sign-out-alt" fixedWidth className="mr-2" />
                          Sair
                        </a>
                      </DropdownMenu>
                    </UncontrolledDropdown>
                  )}
                </Nav>
              </Collapse>
            </Navbar>
          )}
        />
      </div>
    )
  }
}

const propsAreEqual = (props, next) =>
  isEqual(props.user, next.user, ['id', 'updatedAt']) &&
  isEqual(props.isImpersonating, next.isImpersonating) &&
  isEqual(props.disconnectedServicesCount, next.disconnectedServicesCount)

export default memo(Header, propsAreEqual)
