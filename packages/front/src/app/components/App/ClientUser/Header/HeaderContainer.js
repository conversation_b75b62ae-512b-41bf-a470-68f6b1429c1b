import { connect } from 'react-redux'
import { with<PERSON><PERSON><PERSON> } from 'react-router-dom'
import Menu from './Header'
import { logout } from '../../../../modules/auth/actions'
import { getUser } from '../../../../modules/auth/selectors'
import { selectors } from '../../../../modules/services'
import * as impersonateModule from '../../../../modules/admin/modules/users/modules/impersonate'

const mapStateToProps = (state) => ({
  user: getUser(state),
  disconnectedServicesCount: selectors.getDisconnectedCount(state),
  isImpersonating: impersonateModule.selectors.getIsImpersonating(state),
})

const actionCreators = {
  logout,
  stopImpersonation: impersonateModule.actions.stopImpersonation,
}

export default withRouter(connect(mapStateToProps, actionCreators)(Menu))
