import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import SweetAlert from 'react-bootstrap-sweetalert'
import { getUser } from '../../../../modules/auth/selectors'
import config from '../../../../../../config'

interface PostMessageData {
  action?: 'navigate'
  path?: string
  type?: 'handshake:init' | 'handshake:ack' | 'auth:user'
  nonce?: string
  payload?: any
}

const Marketplace: React.FC = () => {
  const [hasError, setHasError] = useState(false)
  const user = useSelector(getUser)
  const urlFront = config('frontUrl')
  const urlMarketplace = config('marketPlaceUrl')
  const url = !urlMarketplace || !urlFront ? '' : `${urlMarketplace}?url=${urlFront}`
  const { push } = useHistory()
  const { t } = useTranslation('marketplace')

  useEffect(() => {
    if (!url) {
      setHasError(true)
      return
    }

    const marketplaceIframe = document.getElementById('marketplace-iframe') as HTMLIFrameElement | null
    if (!marketplaceIframe) {
      setHasError(true)
      return
    }

    const userData = JSON.stringify({
      id: user.id,
      accountId: user.accountId,
      name: user.name,
      email: user.email,
      roles: user.roles,
    })

    let targetOrigin = '*'
    try {
      if (urlMarketplace) {
        targetOrigin = new URL(urlMarketplace).origin
      }
    } catch (e) {
      targetOrigin = '*'
    }

    let handshakeNonce: string | null = null

    const onLoad = () => {
      handshakeNonce = (crypto as any)?.randomUUID?.() || Math.random().toString(36).slice(2)

      marketplaceIframe.contentWindow?.postMessage({ type: 'handshake:init', nonce: handshakeNonce }, targetOrigin)
    }

    const onMessage = (event: MessageEvent<PostMessageData>) => {
      const data = event.data as PostMessageData & any

      if (data?.type === 'handshake:ack' && handshakeNonce && data?.nonce === handshakeNonce) {
        marketplaceIframe.contentWindow?.postMessage(
          { type: 'auth:user', nonce: handshakeNonce, payload: userData },
          targetOrigin,
        )
        handshakeNonce = null
        return
      }

      if (data?.action === 'navigate') {
        push(data?.path || '')
      }
    }

    marketplaceIframe.addEventListener('load', onLoad)
    window.addEventListener('message', onMessage)

    return () => {
      marketplaceIframe.removeEventListener('load', onLoad)
      window.removeEventListener('message', onMessage)
    }
  }, [url, urlMarketplace, user, push])

  const SweetAlertAny: any = SweetAlert

  return (
    <>
      {!hasError ? (
        <iframe
          id="marketplace-iframe"
          title="marketplace"
          src={url}
          width="100%"
          height="100%"
          frameBorder="0"
          allowFullScreen
          data-testid="marketplace-iframe-open_marketplace"
        />
      ) : (
        <SweetAlertAny
          type="error"
          error
          title={t('LABEL_MARKETPLACE_TITLE')}
          onConfirm={() => {
            push('')
          }}
        >
          {t('LABEL_MARKETPLACE_ERROR_DESCRIPTION')}
        </SweetAlertAny>
      )}
    </>
  )
}

export default Marketplace
