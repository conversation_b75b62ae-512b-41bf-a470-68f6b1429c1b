import React, { memo } from 'react'
import Helmet from 'react-helmet'
import { pickBy } from 'lodash'
import { Button, Form, FormGroup, ModalBody, ModalHeader, Label } from 'reactstrap'
import SweetAlert from 'react-bootstrap-sweetalert'
import pick from 'lodash/pick'
import { useTranslation } from 'react-i18next'
import { required } from '../../../../../utils/validator/validators'
import { isAllBlankSpace } from '../../../../../utils/isAllBlankSpace'
import InputGroup, { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import { ModalFooter, ModalDigisac } from '../../../styles/common'
import DataSelect from '../../../../common/unconnected/Datetime'
import FileUploader from '../../../../common/unconnected/FileUploader'
import ButtonClose from '../../../../common/unconnected/ButtonClose'
import useToggle from '../../../../../hooks/useToggle'
import useFormController from '../../../../../hooks/crud/useFormController'
import {
  useCreateAcceptanceterms,
  useFetchOneAcceptanceterms,
  useUpdateAcceptanceterms,
} from '../../../../../resources/acceptanceTerms/requests'

import EditAlertModal from './EditAlertModal'

export const formatToApi = (acceptanceTerms) => ({
  ...pick(acceptanceTerms, ['name']),
  file: {
    base64: acceptanceTerms.file?.base64Url?.split('base64,')[1],
    mimetype: acceptanceTerms.file?.mimetype,
    name: acceptanceTerms.file?.fileName,
  },
  textField: acceptanceTerms.text,
})

export const formatFromApi = (acceptanceTerms) => ({
  ...pick(acceptanceTerms, ['name', 'file']),
  text: acceptanceTerms.textField,
})

const buildQuery = () =>
  pickBy({
    include: ['file'],
  })

const initialModel = {
  name: '',
  text: '',
  file: null,
}

function AcceptanceTermForm() {
  const { t } = useTranslation(['acceptancetermsPage', 'common'])
  const requiredValidation = [required, t('common:REQUIRED_FIELD')]
  const blankSpaceValidation = [isAllBlankSpace, t('common:INVALID_FIELD')]

  const validationRules = {
    name: [requiredValidation, blankSpaceValidation],
    text: [requiredValidation, blankSpaceValidation],
  }
  const {
    submit,
    exit,
    isModalOpen,
    isEditing,
    isAlertShowing,
    closeAlert,
    isLoading,
    error,
    bindInput,
    validation,
    setProperty,
    model,
  } = useFormController({
    exitToPath: '/acceptanceTerms',
    initialModel,
    validationRules,
    buildQuery,
    formatToApi,
    formatFromApi,
    useCreateOne: useCreateAcceptanceterms,
    useUpdateOne: useUpdateAcceptanceterms,
    useFetchOne: useFetchOneAcceptanceterms,
  })

  const title = `${isEditing ? t('common:LABEL_EDITING') : t('common:LABEL_CREATING')} ${t(
    'CREATE_ACCEPTANCE_TERMS_LABEL',
  ).toLocaleLowerCase()}`

  const { isOpen: isEditTermsModalOpen, open: openEditTermsModal, close: closeEditTermsModal } = useToggle()

  const handleConfirmEdit = () => {
    submit()
  }

  return (
    <>
      <Helmet title={title} />

      <Form onSubmit={submit} data-testid="create-accounts">
        <ModalDigisac isOpen={isModalOpen} toggle={exit} autoFocus={false}>
          <ModalHeader toggle={exit} data-testid="terms_of_acceptance-header-modal_create_edit">
            {title}
            <ButtonClose onClick={exit} />
          </ModalHeader>
          <ModalBody>
            <InputGroup
              id="name"
              label={t('CREATE_ACCEPTANCE_TERMS_LABEL_NAME')}
              required
              {...{ bindInput, validation }}
              data-testid="terms_of_acceptance-input-name"
            />

            <FormGroup>
              <Label htmlFor="value">{t('CREATE_ACCEPTANCE_TERMS_LABEL_FILE')}</Label>
              <FileUploader
                hasRemoveButton
                value={model.file}
                onChange={(currentValue) => setProperty('file', currentValue)}
              />

              <InputGroup
                id="text"
                type="textarea"
                required
                label={t('CREATE_ACCEPTANCE_TERMS_LABEL_MESSAGE')}
                {...{ bindInput, validation }}
                data-testid="terms_of_acceptance-input-text_field"
              />
            </FormGroup>
          </ModalBody>
          <ModalFooter>
            <Button className="cancel" type="button" onClick={exit} data-testid="terms_of_acceptance-button-cancel">
              {t('common:FORM_ACTION_CANCEL')}
            </Button>
            <Button
              data-testid="terms_of_acceptance-button-save"
              className="confirm"
              type="submit"
              onClick={isEditing ? openEditTermsModal : submit}
              disabled={isLoading}
            >
              {t('common:FORM_ACTION_SAVE')}
            </Button>
          </ModalFooter>
        </ModalDigisac>
      </Form>

      <EditAlertModal isOpen={isEditTermsModalOpen} onClose={closeEditTermsModal} confirm={handleConfirmEdit} />

      {isAlertShowing && (
        <SweetAlert error title={t('common:MESSAGE_ATTENTION')} onConfirm={closeAlert}>
          {t('CREATE_ACCEPTANCE_TERMS_MESSAGE_POSSIBLE_CREATE')}
        </SweetAlert>
      )}
    </>
  )
}

export default memo(AcceptanceTermForm)
