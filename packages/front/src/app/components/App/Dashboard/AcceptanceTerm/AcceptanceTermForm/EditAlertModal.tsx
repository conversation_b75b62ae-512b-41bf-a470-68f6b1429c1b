import React, { memo } from 'react'
import SweetAlert from 'react-bootstrap-sweetalert'
import { useTranslation } from 'react-i18next'

function EditAlertModal(props) {
  const { isOpen, onClose, confirm } = props

  const handleConfirm = () => {
    confirm()
    onClose()
  }

  const { t } = useTranslation(['acceptancetermsPage', 'common'])

  return (
    <>
      {isOpen ? (
        <SweetAlert
          warning
          showCancel
          size="lg"
          title={t('common:MESSAGE_ATTENTION')}
          onConfirm={handleConfirm}
          onCancel={onClose}
          confirmBtnText={t('common:FORM_ACTION_CONTINUE')}
          confirmBtnBsStyle="danger"
          cancelBtnText={t('common:FORM_ACTION_CANCEL')}
          cancelBtnBsStyle="success"
          style={{ textAlign: 'center' }}
        >
          <p>
            {t('ACCEPTANCE_TERMS_MESSAGE_RESET_WARNING')}
            <br />
            {t('ACCEPTANCE_TERMS_MESSAGE_SURE_CONTINUE')}
          </p>
        </SweetAlert>
      ) : (
        ''
      )}
    </>
  )
}

export default memo(EditAlertModal)
