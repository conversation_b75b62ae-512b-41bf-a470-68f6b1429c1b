import React, { memo } from 'react'
import Helmet from 'react-helmet'
import { Link, Route, Switch } from 'react-router-dom'
import { DropdownItem } from 'reactstrap'
import { pickBy, identity } from 'lodash'
import { useTranslation } from 'react-i18next'
import AcceptancetermsDeleteRoute from '../AcceptanceTermDelete'
import AcceptancetermsFormRoute from '../AcceptanceTermForm'
import AcceptancetermsShowRoute from '../AcceptanceTermShow'
import Icon from '../../../../common/unconnected/Icon'
import IfUserCan from '../../../../common/connected/IfUserCan'
import Toggler from '../../../../common/unconnected/Toggler'
import IfUserCanRoute from '../../../../common/connected/IfUserCanRoute'
import TablePagination from '../../../../common/unconnected/TablePagination'
import CardLoading from '../../../../common/unconnected/CardLoading'
import Button from '../../../../common/unconnected/Button'
import Container from '../../../styles/container'
import Filters from './Filters'
import useIndexController from '../../../../../hooks/crud/useIndexController'
import { useFetchManyAcceptanceterms } from '../../../../../resources/acceptanceTerms/requests'
import * as I from '../../../../common/unconnected/IconDigisac'

import {
  Table,
  TableCell,
  TableColumn,
  TableBody,
  TableHead,
  TableOptions,
  TableRow,
  DropdownMenuActions,
  ButtonDropdownActions,
  ButtonRefresh,
  CardFilters,
} from '../../../styles/table'

import {
  IconSearch,
  IconOptions,
  IconTrash,
  IconEdit,
  IconShowPassword as IconEyes,
} from '../../../../common/unconnected/IconDigisac'

import { PrimaryColor, TextColor } from '../../../styles/colors'

const buildQuery = ({ filters, localPagination }) =>
  pickBy(
    {
      attributes: ['id', 'name', 'textField'],
      where: pickBy(
        {
          name: filters.name && { $iLike: `%${filters.name}%` },
        },
        identity,
      ),
      order: [['name', 'ASC']],
      page: localPagination.page,
      perPage: localPagination.perPage,
    },
    identity,
  )

const initialFilters = {
  name: '',
  startPeriod: '',
  endPeriod: '',
  filterExpiration: '',
}

function AcceptanceTermIndex({ match }) {
  const {
    models: acceptanceTerms,
    pagination,
    isLoading,
    fetch,
    isFiltersShowing,
    toggleFilters,
    filters,
    handleFilterChange,
    localPagination,
    handleLocalPaginationChange,
  } = useIndexController({
    buildQuery,
    initialFilters,
    useFetchMany: useFetchManyAcceptanceterms,
  })

  const { t } = useTranslation(['acceptancetermsPage', 'common'])
  return (
    <div>
      <Helmet title={t('TITLE_ACCEPTANCE_TERMS')} />

      <Container>
        <div className="d-flex align-items-center justify-content-between">
          <div>
            <h2 data-testid="terms_of_acceptance-label-title">{t('TITLE_ACCEPTANCE_TERMS')}</h2>
          </div>
          <div className="d-flex">
            <IfUserCan permission="acceptanceTerms.create">
              <Button background={PrimaryColor} size="xl" className="mr-2">
                <Link data-testid="terms_of_acceptance-button-create_terms_of_acceptance" to="/acceptanceTerms/create">
                  <I.IconTerms className="mr-2" fill="#fff" width="28" height="28" />
                  {t('CREATE_ACCEPTANCE_TERMS_LABEL_NEW')}
                </Link>
              </Button>
            </IfUserCan>
            <Button
              background={PrimaryColor}
              size="xl"
              onClick={toggleFilters}
              className="mr-2"
              data-testid="terms_of_acceptance-button-show_hiden_filters"
            >
              <IconSearch fill="#fff" width="25" height="25" />
              {isFiltersShowing ? `${t('common:BUTTON_TEXT_HIDDEN')} ` : `${t('common:BUTTON_TEXT_SHOW')} `}
              {t('common:BUTTON_TEXT_FILTERS')}
            </Button>
            <ButtonRefresh
              loadIcon="sync-alt"
              color="default"
              isLoading={isLoading}
              onClick={fetch}
              data-testid="terms_of_acceptance-button-refresh"
            >
              <Icon name="sync-alt" fixedWidth />
            </ButtonRefresh>
          </div>
        </div>

        <CardLoading isLoading={isLoading} />

        {isFiltersShowing && (
          <CardFilters>
            <Filters filters={filters} handleFilterChange={handleFilterChange} />
          </CardFilters>
        )}

        <Table className="mb-0" hover>
          <TableHead columns={3}>
            <TableColumn data-testid="terms_of_acceptance-label-name">
              {t('CREATE_ACCEPTANCE_TERMS_LABEL_NAME')}
            </TableColumn>
            <TableColumn data-testid="terms_of_acceptance-label-text_field">
              {t('CREATE_ACCEPTANCE_TERMS_LABEL_MESSAGE')}
            </TableColumn>

            <TableColumn
              data-testid="terms_of_acceptance-label-actions"
              className="text-right justify-content-end pr-4"
            >
              {t('common:ACTIONS_SUBMENU_TITLE')}
            </TableColumn>
          </TableHead>
          <TableBody data-testid="terms_of_acceptance-body-table">
            {acceptanceTerms &&
              acceptanceTerms.map((acceptanceTerms) => (
                <TableRow cells={3} key={acceptanceTerms.id}>
                  <TableCell className="break-long-texts">
                    <Link to={`/acceptanceTerms/${acceptanceTerms.id}`}>{acceptanceTerms.name}</Link>
                  </TableCell>
                  <TableCell>{acceptanceTerms.textField}</TableCell>
                  <TableCell actions>
                    <Toggler
                      render={({ active, toggle }) => (
                        <ButtonDropdownActions
                          group={false}
                          isOpen={active}
                          toggle={toggle}
                          data-testid="terms_of_acceptance-button-actions"
                        >
                          <TableOptions size="sm" color="primary">
                            <IconOptions className="icon-options" fill={PrimaryColor} />
                          </TableOptions>
                          <DropdownMenuActions>
                            <DropdownItem header>{t('common:ACTIONS_SUBMENU_TITLE')}</DropdownItem>
                            <Link
                              to={`/acceptanceTerms/${acceptanceTerms.id}`}
                              className="dropdown-item"
                              data-testid="terms_of_acceptance-button_sub_menu-view"
                            >
                              <IconEyes fill={TextColor} width="29" height="29" />
                              {t('common:ACTIONS_SUBMENU_VIEW')}
                            </Link>

                            <IfUserCan permission="acceptanceTerms.update">
                              <Link
                                to={`/acceptanceTerms/${acceptanceTerms.id}/edit`}
                                className="dropdown-item"
                                data-testid="terms_of_acceptance-button_sub_menu-edit"
                              >
                                <IconEdit fill={TextColor} width="28" height="28" />
                                {t('common:ACTIONS_SUBMENU_EDIT')}
                              </Link>
                            </IfUserCan>

                            <IfUserCan permission="acceptanceTerms.destroy">
                              <Link
                                to={`/acceptanceTerms/${acceptanceTerms.id}/delete`}
                                className="dropdown-item"
                                data-testid="terms_of_acceptance-button_sub_menu-delete"
                              >
                                <IconTrash fill={TextColor} width="28" height="28" />
                                {t('common:ACTIONS_SUBMENU_DELETE')}
                              </Link>
                            </IfUserCan>
                          </DropdownMenuActions>
                        </ButtonDropdownActions>
                      )}
                    />
                  </TableCell>
                </TableRow>
              ))}
            {acceptanceTerms.length < 1 && (
              <tr>
                <td colSpan={6} className="text-center">
                  {t('common:NO_RESULTS_FOUND')}
                </td>
              </tr>
            )}
          </TableBody>
        </Table>

        <TablePagination
          pagination={pagination}
          localPagination={localPagination}
          handlePaginationChange={handleLocalPaginationChange}
        />
      </Container>

      <Switch>
        <IfUserCanRoute
          permission="acceptanceTerms.create"
          exact
          path={`${match.url}/create`}
          component={AcceptancetermsFormRoute}
        />
        <IfUserCanRoute
          permission="acceptanceTerms.update"
          exact
          path={`${match.url}/:id/edit`}
          component={AcceptancetermsFormRoute}
        />
        <IfUserCanRoute
          permission="acceptanceTerms.destroy"
          exact
          path={`${match.url}/:id/delete`}
          component={AcceptancetermsDeleteRoute}
        />
        Acceptanceterms <Route path={`${match.url}/:id`} component={AcceptancetermsShowRoute} />
        Acceptanceterms{' '}
      </Switch>
    </div>
  )
}

export default memo(AcceptanceTermIndex)
