import React, { memo, useEffect } from 'react'
import Helmet from 'react-helmet'
import { useTranslation } from 'react-i18next'
import SweetModal from '../../../../common/unconnected/SweetModal'
import { useDeleteAcceptanceterms, useFetchOneAcceptanceterms } from '../../../../../resources/acceptanceTerms/requests'
import useDeleteController from '../../../../../hooks/crud/useDeleteController'
import toast from '../../../../../utils/toast'

function AcceptanceTermDelete() {
  const { t } = useTranslation(['acceptancetermsPage', 'common'])

  const {
    isModalOpen,
    exit,
    model: acceptanceTerms,
    submit,
    error = null,
  } = useDeleteController({
    exitToPath: '/acceptanceTerms',
    useFetchOne: useFetchOneAcceptanceterms,
    useDeleteOne: useDeleteAcceptanceterms,
  })

  useEffect(() => {
    if (!error?.response?.data) return

    const { message } = error.response.data

    if (message === 'Term in use') {
      exit()
      toast.warn(t('ACCEPTANCE_TERMS_MESSAGE_TERM_IN_USE'))
    }
  }, [error])

  if (!acceptanceTerms) return null

  return (
    <div data-testid="modal-delete-account">
      <Helmet
        title={`${t('TITLE_ACCEPTANCE_TERMS')} - ${t('common:ACTIONS_SUBMENU_DELETE')} ${acceptanceTerms.name}?`}
      />

      <SweetModal
        type="warning"
        title={`${t('common:ACTIONS_SUBMENU_DELETE')} ${t('CREATE_ACCEPTANCE_TERMS_LABEL')}`}
        confirmBtnText={t('common:ACTIONS_SUBMENU_DELETE')}
        cancelBtnText={t('common:FORM_ACTION_CANCEL')}
        onConfirm={submit}
        show={isModalOpen}
        onCancel={exit}
        data-testid="terms_of_acceptance-modal-delete"
      >
        {t('common:MESSAGE_MODAL_DELETE')}
      </SweetModal>
    </div>
  )
}

export default memo(AcceptanceTermDelete)
