import React, { memo } from 'react'
import Helmet from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { ModalBody, ModalHeader } from 'reactstrap'
import { ModalDigisac } from '../../../styles/common'
import useShowController from '../../../../../hooks/crud/useShowController'
import { useFetchOneAcceptanceterms } from '../../../../../resources/acceptanceTerms/requests'
import ButtonClose from '../../../../common/unconnected/ButtonClose'

function AcceptanceTermShow() {
  const { t } = useTranslation(['acceptancetermsPage', 'common'])
  const {
    model: acceptanceTerms,
    isOpen,
    exit,
  } = useShowController({
    exitToPath: '/acceptanceTerms',
    useFetchOne: useFetchOneAcceptanceterms,
    query: {
      include: ['file'],
    },
  })

  if (!acceptanceTerms) return null

  return (
    <>
      <Helmet title={`${t('CREATE_ACCEPTANCE_TERMS_LABEL')} - ${acceptanceTerms.name}`} />

      <ModalDigisac isOpen={isOpen} toggle={exit} autoFocus={false}>
        <ModalHeader toggle={exit} className="break-long-texts" data-testid="terms_of_acceptance-header-modal_view">
          {acceptanceTerms.name}
          <ButtonClose onClick={exit} />
        </ModalHeader>
        <ModalBody className="break-long-texts" data-testid="terms_of_acceptance-body-modal_view">
          <b>{t('CREATE_ACCEPTANCE_TERMS_LABEL_NAME')}:</b> {acceptanceTerms.name} <br />
          <b>{t('CREATE_ACCEPTANCE_TERMS_LABEL_MESSAGE')}:</b> {acceptanceTerms.textField}
          <br />
          <b> {t('CREATE_ACCEPTANCE_TERMS_LABEL_FILE')}: </b> {acceptanceTerms?.file?.name}
        </ModalBody>
      </ModalDigisac>
    </>
  )
}

export default memo(AcceptanceTermShow)
