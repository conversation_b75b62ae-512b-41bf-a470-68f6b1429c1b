import React, { useEffect, useState } from 'react'
import { Col, FormGroup, Input, Label, Row } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import isAfter from 'date-fns/isAfter'
import isBefore from 'date-fns/isBefore'
import { subDays } from 'date-fns'
import Datetime from '../../../../common/unconnected/Datetime'
import { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import Select from '../../../../common/unconnected/Select'

function Filters({ filters, handleFilterChange, isLoading, applyFilters }) {
  const { t } = useTranslation(['acceptancetermsPage', 'common'])

  return (
    <Row>
      <Col md="3">
        <FormGroup>
          <Label htmlFor="tag">{t('CREATE_ACCEPTANCE_TERMS')}</Label>
          <Input
            type="text"
            value={filters.name}
            onChange={(e) => handleFilterChange('name', e.target.value)}
            placeholder={`${t('CREATE_ACCEPTANCE_TERMS')}...`}
            data-testid="termsO-of_acceptance-input_filter-name"
          />
        </FormGroup>
      </Col>
    </Row>
  )
}

export default Filters
