import React, { useState, useEffect, useCallback, useRef } from 'react'
import Container from '../../../styles/container'
import * as S from '../styles'
import { Button } from '../../../../common/unconnected/ui/button'
import { useHist<PERSON>, Link } from 'react-router-dom'
import { Container as Box } from '../../../../common/unconnected/Box/styles'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../../../common/unconnected/ui/table'
import { Trans, useTranslation } from 'react-i18next'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../../../../common/unconnected/ui/dialog'
import { ChevronLeft, Plus, Search, MoreVertical, Eye, Edit2, Trash2, Database } from 'lucide-react'
import { Label } from '../../../../common/unconnected/ui/label'
import { Input } from '../../../../common/unconnected/ui/input'
import { Textarea } from '../../../../common/unconnected/ui/textarea'
import { useRequest } from '../../../../../hooks/useRequest'
import knowledgeDataBaseApi from '../../../../../resources/knowledgeBase/api'
import useIndexController from '../../../../../hooks/crud/useIndexController'
import { useFetchManyknowledgeBases } from '../../../../../resources/knowledgeBase/requests'
import TablePagination from '../../../../common/unconnected/TablePagination/TablePagination'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../../../common/unconnected/ui/dropdown-menu'
import { pickBy, identity } from 'lodash'
import { DeleteDialog } from '../DeleteDialog'
import IfUserCan from '../../../../common/connected/IfUserCan'
import { useToast } from '../../../../../hooks/useToast'

export function KnowledgeBaseIndex() {
  const { t } = useTranslation(['knowledgeBase', 'common'])
  const history = useHistory()

  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [currentKnowledgeBase, setCurrentKnowledgeBase] = useState(null)
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [showPrompt, setShowPrompt] = useState(false)
  const textareaRef = useRef(null)

  const [, execCreate] = useRequest(knowledgeDataBaseApi.create, {})
  const [{ isLoading }, execExclude] = useRequest(knowledgeDataBaseApi.deleteById, {})
  const [, execUpdate] = useRequest(knowledgeDataBaseApi.updateById, {})

  const { toast } = useToast()

  const initialPagination = {
    page: 1,
    perPage: 8,
  }

  const buildQuery = ({ filters, localPagination }) => {
    return {
      query: JSON.stringify({
        where: pickBy(
          {
            name: { $iLike: `%${filters.search}%` },
          },
          identity,
        ),
        include: ['items'],
        order: [['updatedAt', 'DESC']],
        page: localPagination.page,
        perPage: localPagination.perPage,
      }),
    }
  }

  const initialFilters = {
    search: '',
  }

  const {
    models: bases,
    pagination,
    fetch,
    filters,
    setFilters,
    localPagination,
    handleLocalPaginationChange,
  } = useIndexController({
    buildQuery,
    initialFilters,
    initialPagination,
    useFetchMany: useFetchManyknowledgeBases,
  })

  const handleCreateKnowledgeBase = async (name, description) => {
    try {
      if (currentKnowledgeBase) {
        await execUpdate(currentKnowledgeBase.id, { name, description })
        await fetch()
        toast({
          title: t('TOAST_KNOWLEDGEBASE_UPDATED'),
          variant: 'success',
        })
      } else {
        const response = await execCreate({ name, description })
        history.push(`/knowledge-base/${response.id}`)
        toast({
          title: t('TOAST_KNOWLEDGEBASE_CREATED'),
          variant: 'success',
        })
      }
    } catch (error) {
      toast({
        title: t('common:MESSAGE_ERROR_NETWORK'),
        variant: 'warn',
      })
    }
  }

  const setKnowledgeBaseToUpdate = async (base) => {
    setIsDialogOpen(true)
    setCurrentKnowledgeBase(base)
    setName(base.name)
    setDescription(base.description)
  }

  const setKnowledgeBaseExclude = async (base) => {
    setShowPrompt(true)
    setCurrentKnowledgeBase(base)
  }

  const excludeKnowledgeBase = async () => {
    await execExclude(currentKnowledgeBase.id)
    await fetch()
    setShowPrompt(false)
    toast({
      title: t('TOAST_KNOWLEDGE_BASE_DELETED'),
      variant: 'success',
    })
  }

  const handleChange = useCallback(
    (e) => {
      setFilters({ ...filters, search: e.target.value, debounced: true })
    },
    [setFilters],
  )

  const clearDialog = () => {
    setName('')
    setDescription('')
    setCurrentKnowledgeBase(null)
  }

  useEffect(() => {
    if (!isDialogOpen) {
      clearDialog()
    }
  }, [isDialogOpen])

  useEffect(() => {
    if (!showPrompt) {
      clearDialog()
    }
  }, [showPrompt])

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [description])

  return (
    <Container>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}>
        <S.Breadcrumb>
          <Link to="/">{t('common:HOME')}</Link>
          <span> / </span>
          <span>{t('KNOWLEDGE_BASES')}</span>
        </S.Breadcrumb>
        <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', flexDirection: 'row' }}>
            <Button data-testid="backToHome" onClick={() => history.push('/')} variant="link">
              <ChevronLeft />
            </Button>
            <S.Title>{t('KNOWLEDGE_BASES')}</S.Title>
          </div>
          {
            // funcionalidade temporariamente desativada GROW-551
            // <OutsideTheKnowledgeBase />
          }
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <IfUserCan permission="knowledgeBase.create">
              <DialogTrigger asChild>
                <Button data-testid="createKnowledgeBase" onClick={() => setCurrentKnowledgeBase(null)}>
                  <Plus size={16} />
                  <span style={{ paddingRight: '8px', paddingLeft: '8px' }}>{t('BUTTON_CREATE_BASE')}</span>
                </Button>
              </DialogTrigger>
            </IfUserCan>
            <DialogContent>
              <DialogHeader style={{ marginBottom: '40px' }}>
                <DialogTitle>{t('MODAL_TITLE_CREATE_KNOWLEDGE_BASE')}</DialogTitle>
              </DialogHeader>
              <main style={{ marginBottom: '40px' }}>
                <Label style={{ color: '#6E7A89', paddingBottom: '4px' }}>{t('LABEL_DATABASE_NAME')}</Label>
                <Input
                  value={name}
                  data-testid="knowledgeBaseTitle"
                  placeholder={t('PLACEHOLDER_TYPE_HERE')}
                  onChange={(e) => e.target.value.length <= 60 && setName(e.target.value)}
                />
                <p style={{ color: '#6E7A89', fontSize: '12px', marginTop: '4px' }}>
                  {t('common:CHAR_LIMIT', { amount: name?.length, max: 60 })}
                </p>

                <Label style={{ color: '#6E7A89', paddingBottom: '4px' }}>{t('LABEL_DESCRIPTION')}</Label>
                <Textarea
                  ref={textareaRef}
                  data-testid="knowledgeBaseDescription"
                  value={description}
                  placeholder={t('PLACEHOLDER_DESCRIPTION')}
                  style={{ overflow: 'hidden', minHeight: '160px' }}
                  onChange={(e) => e.target.value.length <= 600 && setDescription(e.target.value)}
                />
                <p style={{ color: '#6E7A89', fontSize: '12px', marginTop: '4px' }}>
                  {t('common:CHAR_LIMIT', { amount: description?.length, max: 600 })}
                </p>
              </main>
              <DialogFooter>
                <DialogClose asChild>
                  <Button style={{ width: '100%' }} variant="outline" data-testid="createKnowledgeBaseCancel">
                    {t('common:FORM_ACTION_BACK')}
                  </Button>
                </DialogClose>
                <DialogClose asChild>
                  <Button
                    data-testid="createKnowledgeBaseConfirm"
                    onClick={() => handleCreateKnowledgeBase(name, description)}
                    disabled={!name || name.trim() === '' || !description || description.trim() === ''}
                    style={{ width: '100%' }}
                  >
                    {t('common:BUTTON_TEXT_CONFIRM')}
                  </Button>
                </DialogClose>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
        <Box style={{ background: 'white', border: '0px' }}>
          <Input
            icon={<Search color="#6E7A89" />}
            data-testid="KnowledgeBasesFilter"
            style={{ width: '320px', marginBottom: '24px' }}
            value={filters.search}
            onChange={handleChange}
            placeholder={t('BASE_NAME_SEARCH')}
          />
          <div
            style={{
              border: '1px solid #D7DBE0',
              borderTopRightRadius: '8px',
              borderTopLeftRadius: '8px',
            }}
          >
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead style={{ fontWeight: '600', width: '300px' }}>{t('TABLE_HEAD_NAME')}</TableHead>
                  <TableHead style={{ fontWeight: '600', width: '500px' }}>{t('TABLE_HEAD_DESCRIPTION')}</TableHead>
                  <TableHead style={{ fontWeight: '600', width: '216px' }}>
                    {t('TABLE_HEAD_ATTACHED_ITEMS_COUNT')}
                  </TableHead>
                  <TableHead style={{ fontWeight: '600', width: '75px' }}>{t('TABLE_HEAD_ACTIONS')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {bases.length === 0 ? (
                  <TableRow style={{ height: '400px' }}>
                    <TableCell colSpan={4} style={{ justifyItems: 'center' }}>
                      <div
                        style={{
                          width: '48px',
                          height: '48px',
                          background: '#E1EDF8',
                          borderRadius: '80px',
                          alignContent: 'center',
                          textAlign: 'center',
                        }}
                      >
                        <Database size={32} color="#3C66B9" />
                      </div>
                      <p style={{ marginTop: '16px', marginBottom: '0px' }}>{t('NO_KNOWLEDGE_BASE_CREATED_1')}</p>
                      <Trans components={{ bold: <strong /> }}>
                        <p>{t('NO_KNOWLEDGE_BASE_CREATED_2')}</p>
                      </Trans>
                    </TableCell>
                  </TableRow>
                ) : (
                  bases.map((base) => (
                    <TableRow key={base.id}>
                      <TableCell title={base.name}>
                        {base.name?.length > 30 ? base.name.slice(0, 30) + '...' : base.name}
                      </TableCell>
                      <TableCell title={base.description}>
                        {base.description?.length > 50 ? base.description.slice(0, 50) + '...' : base.description}
                      </TableCell>
                      <TableCell>{t('ITEMS', { amount: base.items.length })}</TableCell>
                      <div style={{ justifyItems: 'center' }}>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <MoreVertical data-testid="moreOptions" color="#324B7D" />
                            </DropdownMenuTrigger>
                            <DropdownMenuContent style={{ width: '200px' }}>
                              <DropdownMenuItem onClick={() => history.push(`/knowledge-base/${base.id}`)}>
                                <Eye style={{ marginRight: '8px' }} />
                                {t('common:ACTIONS_SUBMENU_VIEW')}
                              </DropdownMenuItem>
                              <IfUserCan permission="knowledgeBase.update">
                                <DropdownMenuItem onClick={() => setKnowledgeBaseToUpdate(base)}>
                                  <Edit2 style={{ marginRight: '8px' }} />
                                  {t('common:ACTIONS_SUBMENU_EDIT')}
                                </DropdownMenuItem>
                              </IfUserCan>
                              <IfUserCan permission="knowledgeBase.destroy">
                                <DropdownMenuItem onClick={() => setKnowledgeBaseExclude(base)}>
                                  <Trash2 style={{ marginRight: '8px' }} />
                                  {t('common:ACTIONS_SUBMENU_DELETE')}
                                </DropdownMenuItem>
                              </IfUserCan>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </div>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          <TablePagination
            simplePagination
            pagination={pagination}
            localPagination={localPagination}
            handlePaginationChange={handleLocalPaginationChange}
          />
        </Box>
        <DeleteDialog
          excluding={isLoading}
          isOpen={showPrompt}
          setIsOpen={() => {
            if (!isLoading) {
              setShowPrompt(false)
            }
          }}
          onConfirm={excludeKnowledgeBase}
          warningMessage={
            <Trans
              values={{
                knowledgeBaseName:
                  currentKnowledgeBase?.name?.length > 20
                    ? currentKnowledgeBase?.name?.slice(0, 20) + '...'
                    : currentKnowledgeBase?.name,
              }}
              components={{ strong: <strong title={currentKnowledgeBase?.name} /> }}
            >
              {t('DELETE_KNOWLEDGE_BASE_WARNING')}
            </Trans>
          }
        />
      </div>
    </Container>
  )
}
