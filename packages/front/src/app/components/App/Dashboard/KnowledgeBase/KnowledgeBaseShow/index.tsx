import React, { use<PERSON><PERSON>back, useEffect, useState } from 'react'
import Container from '../../../styles/container'
import { Container as Box } from '../../../../common/unconnected/Box/styles'
import { useHist<PERSON>, Link, useParams, useRouteMatch } from 'react-router-dom'
import * as S from '../styles'
import { Button } from '../../../../common/unconnected/ui/button'
import { Trans, useTranslation } from 'react-i18next'
import { ChevronLeft, Search, FilePlus } from 'lucide-react'
import { Input } from '../../../../common/unconnected/ui/input'
import Select from '../../../../common/unconnected/Select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../../../common/unconnected/ui/table'
import TablePagination from '../../../../common/unconnected/TablePagination/TablePagination'
import { Plus, Edit2, Trash2, <PERSON><PERSON><PERSON><PERSON>, LinkIcon, FileText, Type } from 'lucide-react'
import useIndexController from '../../../../../hooks/crud/useIndexController'
import { useFetchManyknowledgeBaseItems } from '../../../../../resources/knowledgeBaseItem/requests'
import { useRequest } from '../../../../../hooks/useRequest'
import { pickBy, identity } from 'lodash'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../../../common/unconnected/ui/dropdown-menu'
import knowledgeBaseApi from '../../../../../resources/knowledgeBase/api'
import knowledgeBaseItemApi from '../../../../../resources/knowledgeBaseItem/api'
import { DeleteDialog } from '../DeleteDialog'
import IfUserCan from '../../../../common/connected/IfUserCan'
import toast from '../../../../../utils/toast'

export function KnowledgeBaseShow() {
  const { t } = useTranslation(['knowledgeBase', 'common'])
  const history = useHistory()
  const { id } = useParams()
  const match = useRouteMatch()

  const [knowledgeBaseName, setKnowledgeBaseName] = useState('')
  const [items, setItems] = useState([])
  const [showPrompt, setShowPrompt] = useState(false)
  const [itemId, setItemId] = useState()
  const [fileSizes, setFileSizes] = useState([])

  const [{ response }, fetchById] = useRequest(knowledgeBaseApi.fetchById)
  const [, execExclude] = useRequest(knowledgeBaseItemApi.deleteById, {})

  const reasons = [
    {
      id: 1,
      name: t('FILTER_LABEL_URL'),
      type: 'urls',
    },
    {
      id: 2,
      name: t('FILTER_LABEL_FILE'),
      type: 'files',
    },
    {
      id: 3,
      name: t('FILTER_LABEL_MANUAL_TEXT'),
      type: 'messages',
    },
  ]

  const typeMapper = {
    urls: 'URL',
    files: t('TYPE_FILES'),
    messages: t('TYPE_TEXT'),
  }

  const initialPagination = {
    page: 1,
    perPage: 8,
  }

  const buildQuery = ({ filters, localPagination }) => {
    return {
      query: JSON.stringify({
        where: pickBy(
          {
            knowledgeBaseId: id,
            name: { $iLike: `%${filters.search}%` },
            type: filters.type,
          },
          identity,
        ),
        include: ['file'],
        order: [['updatedAt', 'DESC']],
        page: localPagination.page,
        perPage: localPagination.perPage,
      }),
    }
  }

  const initialFilters = {
    search: '',
  }

  const {
    models: baseItems,
    pagination,
    fetch,
    filters,
    setFilters,
    localPagination,
    handleLocalPaginationChange,
  } = useIndexController({
    buildQuery,
    initialFilters,
    initialPagination,
    useFetchMany: useFetchManyknowledgeBaseItems,
  })

  const customStyles = {
    control: (styles) => ({
      ...styles,
      borderRadius: '500px',
      width: '163px',
      height: '40px',
    }),
  }

  const setKnowledgeBaseItemExclude = async (base) => {
    setShowPrompt(true)
    setItemId(base.id)
  }

  const exlcludeKnowledgeBaseItem = async () => {
    await execExclude(itemId)
    await fetch()
    setShowPrompt(false)
    toast.success(t('TOAST_KNOWLEDGE_DELETED'))
  }

  const getFileSizeInMB = async (url) => {
    const response = await window.fetch(url, { method: 'GET' })
    const size = response.headers.get('Content-Length')
    const sizeInMB = (Number(size) / (1024 * 1024)).toFixed(2)
    return `${sizeInMB} MB`
  }

  const handleChange = useCallback(
    (e) => {
      setFilters({ ...filters, search: e.target.value, debounced: true })
    },
    [setFilters],
  )

  const getSizeById = (id) => {
    const item = fileSizes.find((file) => file.id === id)
    return item ? item.size : null
  }

  useEffect(() => {
    fetchById(id, { include: ['items'] })
  }, [])

  useEffect(() => {
    if (response) {
      setKnowledgeBaseName(response.name)
      setItems(response.items)
    }
  }, [response])

  useEffect(() => {
    if (!showPrompt) {
      setItemId(null)
    }
  }, [showPrompt])

  useEffect(() => {
    if (baseItems && baseItems?.length > 0 && items.some((item) => item.id === baseItems[0].id)) {
      const fetchAllFileSizes = async () => {
        const sizes = await Promise.all(
          baseItems.map(async (item) => {
            if (item?.file?.url) {
              const size = await getFileSizeInMB(item?.file?.url)
              return { id: item?.id, size }
            }
            return { id: item?.id, size: null }
          }),
        )

        setFileSizes(sizes)
      }

      fetchAllFileSizes()
    }
  }, [baseItems])

  return (
    <Container>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}>
        <S.Breadcrumb>
          <Link to="/" data-testid="backToHome">
            {t('common:HOME')}
          </Link>
          <span> / </span>
          <Link to="/knowledge-base" data-testid="backToKnowledgeBases">
            {t('KNOWLEDGE_BASES')}
          </Link>
          <span> / </span>
          <span>{knowledgeBaseName}</span>
        </S.Breadcrumb>
        <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', flexDirection: 'row' }}>
            <Button onClick={() => history.push(`/knowledge-base`)} variant="link" data-testid="backButton">
              <ChevronLeft />
            </Button>
            <S.Title>{knowledgeBaseName}</S.Title>
          </div>
        </div>
        <Box style={{ background: 'white', border: '0px' }}>
          <h2 style={{ fontSize: '20px' }}>{t('CONTENT_OF_KNOWLEDGEBASE')}</h2>
          <p style={{ marginBottom: '40px' }}>{t('MESSAGE_ADD_CONTENT')}</p>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div style={{ display: 'inline-flex', gap: '16px', height: '40px', marginBottom: '16px' }}>
              <Input
                icon={<Search color="#6E7A89" />}
                data-testid="knowledgeBaseItemsFilter"
                style={{ width: '320px', marginBottom: '24px' }}
                value={filters.search}
                onChange={handleChange}
                placeholder={t('SEARCH_BY_NAME_OR_TYPE')}
              />
              <Select
                data-testid="knowledgeBaseItemsTypeFilter"
                placeholder={t('FILTER_BY_TYPE')}
                styles={customStyles}
                options={reasons}
                onChange={(value) =>
                  setFilters((prev) => ({
                    ...prev,
                    type: value?.type,
                  }))
                }
              />
            </div>
            <DropdownMenu>
              <IfUserCan permission="knowledgeBase.update">
                <DropdownMenuTrigger asChild>
                  <Button data-testid="knowledgeBaseItemsAdd">
                    <Plus size={16} />
                    <span style={{ paddingRight: '8px', paddingLeft: '8px' }}>{t('LABEL_ADD')}</span>
                  </Button>
                </DropdownMenuTrigger>
              </IfUserCan>
              <DropdownMenuContent align={'end'} style={{ width: '226px' }}>
                <DropdownMenuItem onClick={() => history.push(`${match.url}/urls`)}>
                  <LinkIcon style={{ marginRight: '8px' }} />
                  {t('ACTIONS_EXTERNAL_URL')}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => history.push(`${match.url}/files`)}>
                  <FileText style={{ marginRight: '8px' }} />
                  {t('ACTIONS_FILE')}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => history.push(`${match.url}/messages`)}>
                  <Type style={{ marginRight: '8px' }} />
                  {t('ACTIONS_MANUAL_TEXT')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div
            style={{
              border: '1px solid #D7DBE0',
              borderTopRightRadius: '8px',
              borderTopLeftRadius: '8px',
            }}
          >
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('TABLE_HEAD_NAME')}</TableHead>
                  <TableHead>{t('TABLE_HEAD_URL_OR_FILE')}</TableHead>
                  <TableHead style={{ fontWeight: '600', width: '87px' }}>{t('TABLE_HEAD_TYPE')}</TableHead>
                  <TableHead style={{ fontWeight: '600', width: '119px' }}>{t('TABLE_HEAD_SIZE')}</TableHead>
                  <IfUserCan permission="knowledgeBase.update">
                    <TableHead style={{ fontWeight: '600', width: '75px' }}>{t('TABLE_HEAD_ACTIONS')}</TableHead>
                  </IfUserCan>
                </TableRow>
              </TableHeader>
              <TableBody>
                {baseItems.length === 0 ? (
                  <TableRow style={{ height: '400px' }}>
                    <TableCell colSpan={5} style={{ justifyItems: 'center' }}>
                      <div
                        style={{
                          width: '48px',
                          height: '48px',
                          background: '#E1EDF8',
                          borderRadius: '80px',
                          alignContent: 'center',
                          textAlign: 'center',
                        }}
                      >
                        <FilePlus size={32} color="#3C66B9" />
                      </div>
                      <p style={{ marginTop: '16px', marginBottom: '0px' }}>{t('MESSAGE_MATERIAL_NOT_ADDED_1')}</p>
                      <p style={{ marginBottom: '0px' }}>
                        <Trans components={{ bold: <strong /> }}>{t('MESSAGE_MATERIAL_NOT_ADDED_2')}</Trans>
                      </p>
                      <p>{t('MESSAGE_MATERIAL_NOT_ADDED_3')}</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  baseItems.map((baseItem) => (
                    <TableRow key={baseItem.id}>
                      <TableCell>{baseItem.name}</TableCell>
                      <TableCell>
                        {baseItem.type === 'urls' ? (
                          <a
                            href={baseItem.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{ textDecoration: 'underline', color: '#3C66B9' }}
                          >
                            {baseItem.url}
                          </a>
                        ) : baseItem.type === 'messages' ? (
                          <span>-</span>
                        ) : (
                          <a
                            href={baseItem?.file?.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{ textDecoration: 'underline', color: '#3C66B9' }}
                          >
                            {baseItem?.file?.name}
                          </a>
                        )}
                      </TableCell>
                      <TableCell>{typeMapper[baseItem.type]}</TableCell>
                      <TableCell>
                        {['urls', 'messages'].includes(baseItem.type) ? '-' : getSizeById(baseItem.id)}
                      </TableCell>
                      <IfUserCan permission="knowledgeBase.update">
                        <div style={{ justifyItems: 'center' }}>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <MoreVertical color="#324B7D" />
                              </DropdownMenuTrigger>
                              <DropdownMenuContent style={{ width: '200px' }}>
                                <DropdownMenuItem
                                  onClick={() => history.push(`${match.url}/${baseItem.type}/${baseItem.id}`)}
                                >
                                  <Edit2 style={{ marginRight: '8px' }} />
                                  {t('common:ACTIONS_SUBMENU_EDIT')}
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => setKnowledgeBaseItemExclude(baseItem)}>
                                  <Trash2 style={{ marginRight: '8px' }} />
                                  {t('common:ACTIONS_SUBMENU_DELETE')}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </div>
                      </IfUserCan>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          <TablePagination
            simplePagination
            pagination={pagination}
            localPagination={localPagination}
            handlePaginationChange={handleLocalPaginationChange}
          />
        </Box>
        <DeleteDialog isOpen={showPrompt} setIsOpen={setShowPrompt} onConfirm={exlcludeKnowledgeBaseItem} />
      </div>
    </Container>
  )
}
