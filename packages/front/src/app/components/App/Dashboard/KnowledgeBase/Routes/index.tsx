import React from 'react'
import IfUserCanRoute from '../../../../common/connected/IfUserCanRoute'

import { KnowledgeBaseShow } from '../KnowledgeBaseShow'
import { KnowledgeBaseIndex } from '../KnowledgeBaseIndex'
import { KnowledgeBaseItemRoutes } from '../KnowledgeBaseItem/Routes'

export function KnowledgeBaseRoutes() {
  return (
    <>
      <IfUserCanRoute
        permission="knowledgeBase.view"
        exact
        path={`/knowledge-base/:id/:item`}
        component={KnowledgeBaseItemRoutes}
      />
      <IfUserCanRoute
        permission="knowledgeBase.view"
        exact
        path={`/knowledge-base/:id/:item/:itemId`}
        component={KnowledgeBaseItemRoutes}
      />
      <IfUserCanRoute
        permission="knowledgeBase.view"
        exact
        path={`/knowledge-base/:id`}
        component={KnowledgeBaseShow}
      />
      <IfUserCanRoute permission="knowledgeBase.view" exact path={`/knowledge-base`} component={KnowledgeBaseIndex} />
    </>
  )
}
