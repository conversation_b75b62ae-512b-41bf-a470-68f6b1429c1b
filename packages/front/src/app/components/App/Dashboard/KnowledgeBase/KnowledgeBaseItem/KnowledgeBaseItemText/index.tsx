import React, { useState, useEffect, useRef } from 'react'
import { use<PERSON><PERSON><PERSON>, Link, useParams } from 'react-router-dom'
import Container from '../../../../styles/container'
import { Container as Box } from '../../../../../common/unconnected/Box/styles'
import { useTranslation } from 'react-i18next'
import * as S from '../../styles'
import { ChevronLeft } from 'lucide-react'
import { Button } from '../../../../../common/unconnected/ui/button'
import knowledgeBaseApi from '../../../../../../resources/knowledgeBase/api'
import knowledgeBaseItemApi from '../../../../../../resources/knowledgeBaseItem/api'
import { useRequest } from '../../../../../../hooks/useRequest'
import { Label } from '../../../../../common/unconnected/ui/label'
import { Input } from '../../../../../common/unconnected/ui/input'
import { Textarea } from '../../../../../common/unconnected/ui/textarea'
import { DiscardChangesDialog } from '../../DiscardChangesDialog'
import toast from '../../../../../../utils/toast'
import { Skeleton } from '../../../../../common/unconnected/ui/skeleton'
import { LoadingSpinner } from '../../Spinner'

export function KnowledgeBaseItemText() {
  const { t } = useTranslation(['knowledgeBase', 'common'])
  const { id, itemId } = useParams<{ id: string; itemId?: string }>()
  const history = useHistory()

  const [knowledgeBaseName, setKnowledgeBaseName] = useState('')
  const [contentTitle, setContentTitle] = useState('')
  const [content, setContent] = useState('')
  const [showPrompt, setShowPrompt] = useState(false)
  const textareaRef = useRef(null)

  const [{ response, isLoading: isResponseLoading }, fetchById] = useRequest(knowledgeBaseApi.fetchById)
  const [{ response: itemResponse, isLoading: isItemLoading }, fetchItemById] = useRequest(
    knowledgeBaseItemApi.fetchById,
  )
  const [{ isLoading }, execCreate] = useRequest(knowledgeBaseItemApi.create)
  const [, execExclude] = useRequest(knowledgeBaseItemApi.deleteById, {})

  const save = async () => {
    try {
      if (itemId) {
        await execExclude(itemId)
        await execCreate({
          name: contentTitle,
          type: 'messages',
          knowledgeBaseId: id,
          text: content,
        })
        toast.success(t('TOAST_KNOWLEDGE_UPDATE'))
      } else {
        await execCreate({
          name: contentTitle,
          type: 'messages',
          knowledgeBaseId: id,
          text: content,
        })
        toast.success(t('TOAST_KNOWLEDGE_ADDED'))
      }

      history.push(`/knowledge-base/${id}`)
    } catch (error) {
      toast.error(t('TOAST_KNOWLEDGE_FAILED'))
    }
  }

  useEffect(() => {
    fetchById(id, {})
  }, [])

  useEffect(() => {
    if (response) {
      setKnowledgeBaseName(response.name)
    }
  }, [response])

  useEffect(() => {
    if (itemId) {
      fetchItemById(itemId, { include: ['docs'] })
    }
  }, [itemId])

  useEffect(() => {
    if (itemResponse) {
      setContentTitle((prevTitle) => prevTitle || itemResponse.name)
      setContent((prevContent) => prevContent || itemResponse.docs[0]?.content)
    }
  }, [itemResponse])

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [content])

  return (
    <Container>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}>
        <S.Breadcrumb>
          <Link to="/" data-testid="backToHome">
            {t('common:HOME')}
          </Link>
          <span> / </span>
          <Link to="/knowledge-base" data-testid="backToKnowledgeBases">
            {t('KNOWLEDGE_BASES')}
          </Link>
          <span> / </span>
          {isItemLoading || isResponseLoading ? (
            <Skeleton width="56px" height="14px" style={{ display: 'inline-block' }} />
          ) : (
            <Link to={`/knowledge-base/${id}`} data-testid="backToKnowledgeBase">
              {knowledgeBaseName}
            </Link>
          )}
          <span> / </span>
          <span>{`${t('LABEL_ADD')} ${t('ACTIONS_MANUAL_TEXT').toLowerCase()}`}</span>
        </S.Breadcrumb>
        <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', flexDirection: 'row' }}>
            <Button onClick={() => history.push(`/knowledge-base/${id}`)} variant="link" data-testid="backButton">
              <ChevronLeft />
            </Button>
            <S.Title>{`${t('LABEL_ADD')} ${t('ACTIONS_MANUAL_TEXT').toLowerCase()}`}</S.Title>
          </div>
        </div>
        <Box style={{ background: 'white', border: '0px' }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
            <div>
              <Label htmlFor="contentTitle" style={{ color: '#6E7A89', marginBottom: '4px' }}>
                {t('LABEL_CONTENT_TITLE')}
              </Label>
              {isItemLoading ? (
                <>
                  <Skeleton height="40px" width="100%" borderRadius="40px" />
                  <Skeleton height="18px" width="96px" borderRadius="18px" style={{ marginTop: '4px' }} />
                </>
              ) : (
                <>
                  <Input
                    id="contentTitle"
                    placeholder={t('PLACEHOLDER_TYPE_HERE')}
                    value={contentTitle}
                    data-testid="contentTitle"
                    onChange={(e) => e.target.value.length <= 60 && setContentTitle(e.target.value)}
                  />
                  <p style={{ color: '#6E7A89', fontSize: '12px', marginTop: '4px' }}>
                    {t('common:CHAR_LIMIT', { amount: contentTitle?.length, max: 60 })}
                  </p>
                </>
              )}
            </div>

            <div>
              <Label htmlFor="content" style={{ color: '#6E7A89', marginBottom: '4px' }}>
                {t('LABEL_BASE_TEXT')}
              </Label>
              {isItemLoading ? (
                <Skeleton height="140px" width="100%" borderRadius="20px" />
              ) : (
                <Textarea
                  ref={textareaRef}
                  style={{ minHeight: '140px' }}
                  id="content"
                  data-testid="content"
                  placeholder={t('PLACEHOLDER_TYPE_HERE')}
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                />
              )}
            </div>
          </div>
        </Box>
        <div style={{ display: 'flex', gap: '16px' }}>
          <Button
            variant="outline"
            onClick={() => setShowPrompt(true)}
            style={{ width: '134px' }}
            data-testid="cancelSave"
          >
            {t('common:FORM_ACTION_DISCARD')}
          </Button>
          <Button
            style={{ width: '134px' }}
            disabled={!contentTitle || !content || isLoading}
            onClick={save}
            data-testid="confirmSave"
          >
            {isLoading ? <LoadingSpinner color="#6e7a89" /> : t('common:FORM_ACTION_SAVE')}
          </Button>
        </div>
      </div>
      <DiscardChangesDialog
        isOpen={showPrompt}
        setIsOpen={setShowPrompt}
        onConfirm={() => history.push(`/knowledge-base/${id}`)}
      />
    </Container>
  )
}
