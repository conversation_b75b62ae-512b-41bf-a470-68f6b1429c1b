import React, { useState, useEffect } from 'react'
import { Container as Box } from '../../../../../common/unconnected/Box/styles'
import Container from '../../../../styles/container'
import { useHist<PERSON>, Link, useParams } from 'react-router-dom'
import * as S from '../../styles'
import { useTranslation } from 'react-i18next'
import { useRequest } from '../../../../../../hooks/useRequest'
import knowledgeBaseApi from '../../../../../../resources/knowledgeBase/api'
import knowledgeBaseItemApi from '../../../../../../resources/knowledgeBaseItem/api'
import { ChevronLeft, Trash2 } from 'lucide-react'
import { Button } from '../../../../../common/unconnected/ui/button'
import { Input } from '../../../../../common/unconnected/ui/input'
import { Label } from '../../../../../common/unconnected/ui/label'
import toast from '../../../../../../utils/toast'
import { DiscardChangesDialog } from '../../DiscardChangesDialog'
import { Skeleton } from '../../../../../common/unconnected/ui/skeleton'
import { LoadingSpinner } from '../../Spinner'

type Item = {
  title: string
  url: string
  urlExists: boolean
}

const isValidUrl = (url) => {
  const urlPattern = new RegExp(
    '^(https?:\\/\\/)?' +
      '((([a-zA-Z\\d]([a-zA-Z\\d-]*[a-zA-Z\\d])*)\\.)+[a-zA-Z]{2,}|' +
      '((\\d{1,3}\\.){3}\\d{1,3}))' +
      '(\\:\\d+)?' +
      '(\\/[-a-zA-Z\\d%_.~+]*)*' +
      '(\\?[&a-zA-Z\\d%_.~+=-]*)?' +
      '(\\#[-a-zA-Z\\d_]*)?$',
    'i',
  )
  return !!urlPattern.test(url)
}

const checkUrl = async (url) => {
  if (!isValidUrl(url)) {
    return false
  }

  try {
    const response = await fetch(url, {
      method: 'HEAD',
      mode: 'no-cors',
    })
    return response.ok || response.type === 'opaque'
  } catch (error) {
    if (error instanceof TypeError) {
      return false
    }
    throw error
  }
}

export function KnowledgeBaseItemUrl() {
  const { t } = useTranslation(['knowledgeBase', 'common'])
  const { id, itemId } = useParams<{ id: string; itemId?: string }>()
  const history = useHistory()

  const [knowledgeBaseName, setKnowledgeBaseName] = useState('')
  const [items, setItems] = useState<Item[]>([{ title: '', url: '', urlExists: true }])
  const [showPrompt, setShowPrompt] = useState(false)
  const [errorMessages, setErrorMessages] = useState<string[]>([])
  const [isSaving, setIsSaving] = useState(false)

  const [{ response, isLoading: isResponseLoading }, fetchById] = useRequest(knowledgeBaseApi.fetchById)
  const [{ response: itemResponse, isLoading: isItemLoading }, fetchItemById] = useRequest(
    knowledgeBaseItemApi.fetchById,
  )
  const [{ isLoading }, save] = useRequest(knowledgeBaseItemApi.create)
  const [, update] = useRequest(knowledgeBaseItemApi.updateById)

  const addItem = (title, url) => {
    const newItem = { title, url, urlExists: true }
    setItems([...items, newItem])
  }

  const handleTitleChange = (index: number, title: string) => {
    const updatedItems = [...items]
    updatedItems[index].title = title
    setItems(updatedItems)
  }

  const handleUrlChange = (index: number, url: string) => {
    const updatedItems = [...items]
    updatedItems[index].url = url
    updatedItems[index].urlExists = true
    setItems(updatedItems)
  }

  const removeItem = (indexToRemove: number) => {
    setErrorMessages([])

    setItems((prevItems) => prevItems.filter((_, index) => index !== indexToRemove))
  }

  const checkValidUrls = async () => {
    const updatedItems = await Promise.all(
      items.map(async (item) => {
        const exists = await checkUrl(item.url)
        return { ...item, urlExists: exists }
      }),
    )

    setItems(updatedItems)
    return updatedItems
  }

  const execSave = async () => {
    setIsSaving(true)

    setErrorMessages([])

    if (await checkValidUrls().then((checkedItems) => checkedItems.some((item) => !item.urlExists))) {
      setIsSaving(false)
      return
    }

    if (itemId) {
      try {
        await update(itemId, {
          name: items[0].title,
          type: 'urls',
          knowledgeBaseId: id,
          url: items[0].url,
        })
        toast.success(t('TOAST_KNOWLEDGE_UPDATE'))
        history.push(`/knowledge-base/${id}`)
      } catch (error) {
        setIsSaving(false)
        toast.error(t('TOAST_URL_ADD_FAILED'))
      }
    } else {
      try {
        await Promise.all(
          items.map((item, i) =>
            save({
              index: i,
              name: item.title,
              type: 'urls',
              knowledgeBaseId: id,
              url: item.url,
            }),
          ),
        )

        if (items.length > 1) {
          toast.success(t('TOAST_URLS_ADDED'))
        } else {
          toast.success(t('TOAST_URL_ADDED'))
        }

        history.push(`/knowledge-base/${id}`)
      } catch (error) {
        setIsSaving(false)

        if (error?.response?.data?.extra?.index >= 0 && error?.response?.data?.message) {
          const index = error?.response?.data?.extra?.index
          const errorMessage = t(error?.response?.data?.message)

          setErrorMessages((prev) => {
            const updated = [...prev]
            updated[index] = errorMessage
            return updated
          })
        } else {
          toast.error(t('TOAST_FILE_ADD_FAILED'))
        }
      }
    }
  }

  const hasEmptyFields = items?.length > 0 && items?.some((item) => !item?.title?.trim() || !item?.url?.trim())

  useEffect(() => {
    fetchById(id, {})
  }, [])

  useEffect(() => {
    if (response) {
      setKnowledgeBaseName(response.name)
    }
  }, [response])

  useEffect(() => {
    if (itemId) {
      fetchItemById(itemId, {})
    }
  }, [itemId])

  useEffect(() => {
    if (itemResponse) {
      const updatedItems = [...items]
      updatedItems[0].title = itemResponse.name
      updatedItems[0].url = itemResponse.url
      setItems(updatedItems)
    }
  }, [itemResponse])

  return (
    <Container>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}>
        <S.Breadcrumb>
          <Link to="/" data-testid="backToHome">
            {t('common:HOME')}
          </Link>
          <span> / </span>
          <Link to="/knowledge-base" data-testid="backToKnowledgeBases">
            {t('KNOWLEDGE_BASES')}
          </Link>
          <span> / </span>
          {isItemLoading || isResponseLoading ? (
            <Skeleton width="56px" height="14px" style={{ display: 'inline-block' }} />
          ) : (
            <Link to={`/knowledge-base/${id}`} data-testid="backToKnowledgeBase">
              {knowledgeBaseName}
            </Link>
          )}
          <span> / </span>
          <span>{`${t('LABEL_ADD')} ${t('ACTIONS_EXTERNAL_URL')}`}</span>
        </S.Breadcrumb>
        <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', flexDirection: 'row' }}>
            <Button onClick={() => history.push(`/knowledge-base/${id}`)} variant="link" data-testid="backButton">
              <ChevronLeft />
            </Button>
            <S.Title>{`${t('LABEL_ADD')} ${t('ACTIONS_EXTERNAL_URL')}`}</S.Title>
          </div>
        </div>
        <Box style={{ display: 'flex', flexDirection: 'column', gap: '24px', background: 'white', border: '0px' }}>
          {isItemLoading ? (
            <div style={{ display: 'flex', gap: '24px', alignItems: 'end' }}>
              <div style={{ width: '25%', display: 'flex', flexDirection: 'column', gap: 4 }}>
                <Label htmlFor="contentTitle" style={{ color: '#6E7A89' }}>
                  {t('LABEL_CONTENT_TITLE')}
                </Label>
                <Skeleton height="40px" width="100%" borderRadius="40px" />
              </div>

              <div style={{ width: '75%', display: 'flex', flexDirection: 'column', gap: 4 }}>
                <Label htmlFor="contentUrl" style={{ color: '#6E7A89' }}>
                  {t('LABEL_CONTENT_URL')}
                </Label>
                <Skeleton height="40px" width="100%" borderRadius="40px" />
              </div>
            </div>
          ) : (
            [...Array(items.length)].map((_, i) => (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }} key={i}>
                <div style={{ display: 'flex', gap: '24px', alignItems: 'end' }}>
                  <div style={{ width: '25%', display: 'flex', flexDirection: 'column', gap: '4px' }}>
                    <Label htmlFor="contentTitle" style={{ color: '#6E7A89' }}>
                      {t('LABEL_CONTENT_TITLE')}
                    </Label>
                    <Input
                      id="contentTitle"
                      data-testid="contentTitle"
                      placeholder={t('PLACEHOLDER_TYPE_HERE')}
                      value={items[i].title}
                      onChange={(e) => handleTitleChange(i, e.target.value)}
                    />
                  </div>

                  <div style={{ width: '75%', display: 'flex', flexDirection: 'column', gap: '4px' }}>
                    <Label htmlFor="contentUrl" style={{ color: '#6E7A89' }}>
                      {t('LABEL_CONTENT_URL')}
                    </Label>
                    <Input
                      style={
                        !items[i].urlExists || errorMessages[i] ? { color: '#DB2727', borderColor: '#DB2727' } : {}
                      }
                      id="contentUrl"
                      data-testid="contentUrl"
                      placeholder="https://"
                      value={items[i].url}
                      onChange={(e) => handleUrlChange(i, e.target.value)}
                    />
                  </div>

                  {items.length > 1 && (
                    <div style={{ width: '40px', height: '40px', textAlign: 'center', alignContent: 'center' }}>
                      <Button onClick={() => removeItem(i)} asChild data-testid="removeItem">
                        <Trash2 size={16} />
                      </Button>
                    </div>
                  )}
                </div>
                <div style={{ display: 'flex', flexDirection: 'row', gap: '24px', alignItems: 'end' }}>
                  <div style={{ width: '25%', display: 'flex', flexDirection: 'column', gap: 4 }}></div>
                  <div style={{ width: '75%', display: 'flex', flexDirection: 'column', gap: 4 }}>
                    {!items[i].urlExists && (
                      <Label style={{ position: 'absolute', fontSize: '12px', color: '#DB2727' }}>
                        {t('INVALID_WEBSITE_ADDRESS')}
                      </Label>
                    )}
                    {errorMessages[i] && (
                      <Label style={{ position: 'absolute', fontSize: '12px', color: '#DB2727' }}>
                        {errorMessages[i]}
                      </Label>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
          {!itemId && (
            <div>
              <Button
                variant="link"
                onClick={() => addItem('', '')}
                data-testid="plusUrl"
              >{`+ ${t('LABEL_INCLUDE_URL')}`}</Button>
            </div>
          )}
        </Box>
        <div style={{ display: 'flex', gap: '16px' }}>
          <Button
            variant="outline"
            onClick={() => setShowPrompt(true)}
            style={{ width: '134px' }}
            data-testid="cancelSave"
          >
            {t('common:FORM_ACTION_DISCARD')}
          </Button>
          <Button
            disabled={hasEmptyFields || isSaving}
            style={{ width: '134px' }}
            onClick={execSave}
            data-testid="confirmSave"
          >
            {isLoading ? <LoadingSpinner color="#6e7a89" /> : t('common:FORM_ACTION_SAVE')}
          </Button>
        </div>
      </div>
      <DiscardChangesDialog
        isOpen={showPrompt}
        setIsOpen={setShowPrompt}
        onConfirm={() => history.push(`/knowledge-base/${id}`)}
      />
    </Container>
  )
}
