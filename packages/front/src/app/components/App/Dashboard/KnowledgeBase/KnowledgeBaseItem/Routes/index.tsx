import React from 'react'
import IfUserCanRoute from '../../../../../common/connected/IfUserCanRoute'
import { KnowledgeBaseItemUrl } from '../KnowledgeBaseItemUrl'
import { KnowledgeBaseItemText } from '../KnowledgeBaseItemText'
import { KnowledgeBaseItemFile } from '../KnowledgeBaseItemFile'

export function KnowledgeBaseItemRoutes() {
  return (
    <>
      <IfUserCanRoute
        permission="knowledgeBase.view"
        exact
        path={`/knowledge-base/:id/urls`}
        component={KnowledgeBaseItemUrl}
      />
      <IfUserCanRoute
        permission="knowledgeBase.view"
        exact
        path={`/knowledge-base/:id/urls/:itemId`}
        component={KnowledgeBaseItemUrl}
      />
      <IfUserCanRoute
        permission="knowledgeBase.view"
        exact
        path={`/knowledge-base/:id/messages`}
        component={KnowledgeBaseItemText}
      />
      <IfUserCanRoute
        permission="knowledgeBase.view"
        exact
        path={`/knowledge-base/:id/messages/:itemId`}
        component={KnowledgeBaseItemText}
      />
      <IfUserCanRoute
        permission="knowledgeBase.view"
        exact
        path={`/knowledge-base/:id/files`}
        component={KnowledgeBaseItemFile}
      />
      <IfUserCanRoute
        permission="knowledgeBase.view"
        exact
        path={`/knowledge-base/:id/files/:itemId`}
        component={KnowledgeBaseItemFile}
      />
    </>
  )
}
