import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, Link, useParams } from 'react-router-dom'
import Container from '../../../../styles/container'
import { Container as Box } from '../../../../../common/unconnected/Box/styles'
import { useTranslation } from 'react-i18next'
import * as S from '../../styles'
import { ChevronLeft, FilePlus, Trash2, Upload } from 'lucide-react'
import { Button } from '../../../../../common/unconnected/ui/button'
import filesApi from '../../../../../../resources/file/api'
import knowledgeBaseApi from '../../../../../../resources/knowledgeBase/api'
import knowledgeBaseItemApi from '../../../../../../resources/knowledgeBaseItem/api'
import { useRequest } from '../../../../../../hooks/useRequest'
import { Label } from '../../../../../common/unconnected/ui/label'
import { Input } from '../../../../../common/unconnected/ui/input'
import { DiscardChangesDialog } from '../../DiscardChangesDialog'
import Dropzone from 'react-dropzone'
import useEventCallback from '../../../../../../hooks/useEventCallback'
import toast from '../../../../../../utils/toast'
import { Skeleton } from '../../../../../common/unconnected/ui/skeleton'
import { LoadingSpinner } from '../../Spinner'

type Item = {
  title: string
  fileName: string
  fileId?: string
  blob: any
}

const ALLOWED_MIME_TYPES = {
  pdf: 'application/pdf',
  txt: 'text/plain',
  md: 'text/markdown',
  html: 'text/html',
}

export function KnowledgeBaseItemFile() {
  const { t } = useTranslation(['knowledgeBase', 'common'])
  const { id, itemId } = useParams<{ id: string; itemId?: string }>()
  const history = useHistory()

  const [knowledgeBaseName, setKnowledgeBaseName] = useState('')
  const [showPrompt, setShowPrompt] = useState(false)
  const [items, setItems] = useState<Item[]>([])
  const [unsupportedFile, setUnsupportedFile] = useState<string[]>([])
  const [unsupportedSizeFile, setUnSupportedSizeFile] = useState(false)
  const [errorMessages, setErrorMessages] = useState<string[]>([])

  const [{ response: knowledgeBaseResponse, isLoading: isKnowledgeBaseResponseLoading }, fetchById] = useRequest(
    knowledgeBaseApi.fetchById,
  )
  const [, createToUpload] = useRequest(filesApi.createToUpload)
  const [{ response: itemResponse, isLoading: isItemLoading }, fetchItemById] = useRequest(
    knowledgeBaseItemApi.fetchById,
  )
  const [{ isLoading }, execCreate] = useRequest(knowledgeBaseItemApi.create)
  const [, execUpdate] = useRequest(knowledgeBaseItemApi.updateById)
  const [, deleteRecord] = useRequest(knowledgeBaseItemApi.deleteById, {})
  const [, checkPdfText] = useRequest(knowledgeBaseItemApi.checkPdfText)

  const validateFileType = (file: File) => {
    if (!file) {
      return toast.error(t('NO_FILE_SELECTED'))
    }
    if (file.size > 10 * 1024 * 1024) {
      setUnSupportedSizeFile(true)
      return false
    } else {
      setUnSupportedSizeFile(false)
    }

    const extension = file?.name?.split('.')?.pop()?.toLowerCase()
    if (Object.keys(ALLOWED_MIME_TYPES).includes(extension)) {
      return true
    }
    setUnsupportedFile((prev) => [...prev, file?.name?.split('.')?.pop()])
    return false
  }

  const onFilesDrop = useEventCallback(async (files) => {
    setUnSupportedSizeFile(false)
    setUnsupportedFile([])
    const validFiles = files.filter(validateFileType)

    const newItems = [
      ...items,
      ...validFiles.map((file) => ({
        title: '',
        fileName: file.name,
        blob: file,
      })),
    ]

    setErrorMessages([])
    setItems(newItems)
  })

  const save = async () => {
    let createdRecords = []

    if (items.length <= 0) {
      return toast.error(t('NO_FILE_SELECTED'))
    }

    setErrorMessages([])

    try {
      if (!itemId) {
        const createdFiles = await Promise.all(
          items.map(async (item) => {
            const extension = item?.blob?.name?.split('.')?.pop()?.toLowerCase()
            const mimetype = extension === 'md' ? 'text/markdown' : item?.blob?.type

            const createdFile = await createToUpload({
              name: item?.blob?.name,
              mimetype,
              attachedType: 'knowledgebase.item',
            })

            item.fileId = createdFile?.id

            return {
              ...createdFile,
              blob: item?.blob,
              url: URL.createObjectURL(item?.blob),
            }
          }),
        )

        await Promise.all(
          createdFiles.map(async (createdFile) => {
            if (createdFile?.urlToUpload) {
              await fetch(createdFile.urlToUpload, {
                method: 'PUT',
                body: createdFile.blob,
                headers: {
                  'Content-Type': createdFile.mimetype,
                },
              })
            }
          }),
        )

        const pdfCheckResults = await Promise.all(
          items.map(async (item) => {
            if (item.blob?.type === 'application/pdf') {
              try {
                const data = { fileId: item.fileId }
                const { hasText } = await checkPdfText(data)
                if (!hasText) {
                  return t('EMPTY_FILE_ERROR')
                }
              } catch (err) {
                return err?.response?.data?.message || t('TOAST_FILE_ADD_FAILED')
              }
            }
            return null
          }),
        )

        if (pdfCheckResults.some((msg) => msg)) {
          setErrorMessages(pdfCheckResults)
          return
        }

        for (let i = 0; i < items.length; i++) {
          const item = items[i]

          const response = await execCreate({
            index: i,
            name: item.title,
            type: 'files',
            knowledgeBaseId: id,
            fileId: item.fileId,
          })

          createdRecords.push(response)
        }

        toast.success(t('TOAST_FILE_ADDED'))
      } else {
        await execUpdate(itemId, {
          name: items[0].title,
          type: 'files',
          knowledgeBaseId: id,
          fileId: items[0].fileId,
        })
        toast.success(t('TOAST_KNOWLEDGE_UPDATE'))
      }

      history.push(`/knowledge-base/${id}`)
    } catch (error) {
      if (error?.response?.data?.extra?.index && error?.response?.data?.message) {
        const index = error?.response?.data?.extra?.index
        const errorMessage = t(error?.response?.data?.message)

        setErrorMessages((prev) => {
          const updated = [...prev]
          updated[index] = errorMessage
          return updated
        })

        await Promise.all(createdRecords.map(async (record) => deleteRecord(record.id)))
      } else {
        toast.error(t('TOAST_FILE_ADD_FAILED'))
      }
    }
  }

  const hasEmptyFields = items?.length > 0 && items.some((item) => !item?.title?.trim() || !item?.fileName?.trim())

  const handleTitleChange = (index: number, title: string) => {
    const updatedItems = [...items]
    updatedItems[index].title = title
    setItems(updatedItems)
  }

  const removeItem = (indexToRemove: number) => {
    setItems((prevItems) => prevItems.filter((_, index) => index !== indexToRemove))
  }

  useEffect(() => {
    fetchById(id, {})
  }, [])

  useEffect(() => {
    if (knowledgeBaseResponse) {
      setKnowledgeBaseName(knowledgeBaseResponse.name)
    }
  }, [knowledgeBaseResponse])

  useEffect(() => {
    if (itemId) {
      fetchItemById(itemId, { include: ['docs'] })
    }
  }, [itemId])

  useEffect(() => {
    if (itemResponse) {
      const itemFromResponse = [
        {
          title: itemResponse.name,
          fileName: itemResponse.docs[0]?.meta?.name,
          blob: '',
        },
      ]
      setItems(itemFromResponse)
    }
  }, [itemResponse])

  return (
    <Container>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}>
        <S.Breadcrumb>
          <Link to="/" data-testid="backToHome">
            {t('common:HOME')}
          </Link>
          <span> / </span>
          <Link to="/knowledge-base" data-testid="backToKnowledgeBases">
            {t('KNOWLEDGE_BASES')}
          </Link>
          <span> / </span>
          {isItemLoading || isKnowledgeBaseResponseLoading ? (
            <Skeleton width="56px" height="14px" style={{ display: 'inline-block' }} />
          ) : (
            <Link to={`/knowledge-base/${id}`} data-testid="backToKnowledgeBase">
              {knowledgeBaseName}
            </Link>
          )}
          <span> / </span>
          <span>{`${t('LABEL_ADD')} ${t('ACTIONS_FILE').toLowerCase()}`}</span>
        </S.Breadcrumb>
        <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', flexDirection: 'row' }}>
            <Button onClick={() => history.push(`/knowledge-base/${id}`)} variant="link" data-testid="backButton">
              <ChevronLeft />
            </Button>
            <S.Title>{`${t('LABEL_ADD')} ${t('ACTIONS_FILE').toLowerCase()}`}</S.Title>
          </div>
        </div>
        <Box
          style={{
            background: 'white',
            border: '0px',
            display: 'flex',
            flexDirection: 'column',
            gap: '24px',
          }}
        >
          {!itemId && (
            <>
              <Dropzone onDrop={onFilesDrop} data-testid="dropFile">
                {({ getRootProps, getInputProps, isDragActive }) => (
                  <section>
                    <div
                      {...getRootProps()}
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '16px',
                        height: '150px',
                        width: '303px',
                        border: 'dashed 2px',
                        borderRadius: '8px',
                        borderColor: '#B4BBC5',
                        alignItems: 'center',
                        placeContent: 'center',
                        background: isDragActive ? 'rgba(0,0,0,0.5)' : 'transparent',
                        marginBottom: '8px',
                      }}
                    >
                      <div
                        style={{
                          width: '40px',
                          height: '40px',
                          background: isDragActive ? 'rgba(0,0,0,0.5)' : '#E1EDF8',
                          alignContent: 'center',
                          textAlign: 'center',
                          borderRadius: '500px',
                        }}
                      >
                        <FilePlus size={24} color="#3C66B9" />
                      </div>
                      <span style={{ fontSize: '14px', height: '14px' }}>{t('DROP_FILES_OR_CLICK_BUTTON')}</span>
                      <Button variant="ghost" style={{ height: '32px' }} data-testid="dropFileButton">
                        <Upload size={16} />
                        <span style={{ fontSize: '14px', height: '14px', marginLeft: '8px' }}>{t('SELECT_FILES')}</span>
                      </Button>
                      <input {...getInputProps()} />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                      <span style={{ color: '#6E7A89' }}>{t('SUPPORTED_FILES')}</span>
                      {unsupportedFile.length > 0 &&
                        unsupportedFile.map((type) => (
                          <span style={{ color: '#DB2727' }}>{t('UNSUPPORTED_FILE_TYPE', { type })}</span>
                        ))}
                      {unsupportedSizeFile && (
                        <span style={{ color: '#DB2727' }}>
                          {t('UNSUPPORTED_FILE_SIZE', { size: unsupportedSizeFile })}
                        </span>
                      )}
                    </div>
                  </section>
                )}
              </Dropzone>
            </>
          )}
          {isItemLoading ? (
            <div style={{ display: 'flex', gap: '24px', alignItems: 'end' }}>
              <div style={{ width: '25%', display: 'flex', flexDirection: 'column', gap: 4 }}>
                <Label htmlFor="title" style={{ color: '#6E7A89' }}>
                  {t('LABEL_CONTENT_TITLE')}
                </Label>
                <Skeleton height="40px" width="100%" borderRadius="40px" />
              </div>

              <div style={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 4 }}>
                <Label htmlFor="fileName" style={{ color: '#6E7A89' }}>
                  {t('LABEL_FILE_NAME')}
                </Label>
                <Skeleton height="40px" width="100%" borderRadius="40px" />
              </div>

              <div style={{ width: '40px', height: '40px', textAlign: 'center', alignContent: 'center' }} />
            </div>
          ) : (
            [...Array(items.length)].map((_, i) => (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }} key={i}>
                <div style={{ display: 'flex', flexDirection: 'row', gap: '24px', alignItems: 'end' }}>
                  <div style={{ width: '25%', display: 'flex', flexDirection: 'column', gap: 4 }}>
                    <Label htmlFor="title" style={{ color: '#6E7A89' }}>
                      {t('LABEL_CONTENT_TITLE')}
                    </Label>
                    <Input
                      id="title"
                      onChange={(e) => handleTitleChange(i, e.target.value)}
                      value={items[i].title}
                      data-testid="title"
                    />
                  </div>

                  <div style={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 4 }}>
                    <Label htmlFor="fileName" style={{ color: '#6E7A89' }}>
                      {t('LABEL_FILE_NAME')}
                    </Label>
                    <Input id="fileName" value={items[i].fileName} disabled data-testid="fileName" />
                  </div>

                  <div style={{ width: '40px', height: '40px', textAlign: 'center', alignContent: 'center' }}>
                    {!itemId && (
                      <Button onClick={() => removeItem(i)} asChild data-testid="removeItem">
                        <Trash2 size={16} />
                      </Button>
                    )}
                  </div>
                </div>
                <div style={{ display: 'flex', flexDirection: 'row', gap: '24px', alignItems: 'end' }}>
                  <div style={{ width: '25%', display: 'flex', flexDirection: 'column', gap: 4 }}></div>
                  <div style={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 4 }}>
                    {errorMessages[i] && (
                      <p style={{ color: '#DB2727', fontSize: 12, margin: 0 }}>{errorMessages[i]}</p>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </Box>
        <div style={{ display: 'flex', gap: '16px' }}>
          <Button
            variant="outline"
            style={{ width: '134px' }}
            onClick={() => setShowPrompt(true)}
            data-testid="cancelSave"
          >
            {t('common:FORM_ACTION_DISCARD')}
          </Button>
          <Button
            style={{ width: '134px' }}
            disabled={hasEmptyFields || isLoading}
            onClick={() => save()}
            data-testid="confirmSave"
          >
            {isLoading ? <LoadingSpinner color="#6e7a89" /> : t('common:FORM_ACTION_SAVE')}
          </Button>
        </div>
      </div>
      <DiscardChangesDialog
        isOpen={showPrompt}
        setIsOpen={setShowPrompt}
        onConfirm={() => history.push(`/knowledge-base/${id}`)}
      />
    </Container>
  )
}
