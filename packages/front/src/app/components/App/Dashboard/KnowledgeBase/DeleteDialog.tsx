import React from 'react'
import {
  <PERSON><PERSON>,
  DialogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from '../../../common/unconnected/ui/dialog'
import { Button } from '../../../common/unconnected/ui/button'
import { AlertTriangle } from 'lucide-react'
import * as S from './styles'
import { useTranslation } from 'react-i18next'
import { LoadingSpinner } from './Spinner'

export function DeleteDialog({ isOpen, setIsOpen, onConfirm, warningMessage = null, excluding = false }) {
  const { t } = useTranslation(['knowledgeBase', 'common'])

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent closeButton={false}>
        <DialogHeader style={{ marginBottom: '24px' }}>
          <AlertTriangle size={40} color="#B81D1D" />
          <DialogTitle>{t('CONFIRM_DELETE_KNOWLEDGE_BASE')}</DialogTitle>
        </DialogHeader>
        <main>
          <S.DialogBody>
            <p>{warningMessage || t('DELETE_KNOWLEDGE_WARNING')}</p>
            <p>{t('common:CONFIRMATION_PROMPT')}</p>
          </S.DialogBody>
        </main>
        <DialogFooter style={{ marginTop: '24px' }}>
          <DialogClose asChild>
            <Button style={{ width: '100%' }} variant="outline" data-testid="dialogCancelDelete">
              {t('common:FORM_ACTION_BACK')}
            </Button>
          </DialogClose>
          <Button
            style={{ width: '100%', background: '#B81D1D' }}
            disabled={excluding}
            onClick={onConfirm}
            data-testid="dialogConfirmDelete"
          >
            {excluding ? <LoadingSpinner color="#6e7a89" /> : t('common:ACTIONS_SUBMENU_DELETE')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
