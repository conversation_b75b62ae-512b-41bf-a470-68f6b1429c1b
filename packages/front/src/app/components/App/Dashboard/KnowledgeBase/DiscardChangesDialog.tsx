import React from 'react'
import {
  <PERSON><PERSON>,
  DialogClose,
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from '../../../common/unconnected/ui/dialog'
import { Button } from '../../../common/unconnected/ui/button'
import { AlertTriangle } from 'lucide-react'
import * as S from './styles'
import { useTranslation } from 'react-i18next'

export function DiscardChangesDialog({ isOpen, setIsOpen, onConfirm }) {
  const { t } = useTranslation(['knowledgeBase', 'common'])

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent closeButton={false}>
        <DialogHeader style={{ marginBottom: '24px' }}>
          <AlertTriangle size={40} color="#B81D1D" />
          <DialogTitle>{t('DISCARD_CHANGES')}</DialogTitle>
        </DialogHeader>
        <main>
          <S.DialogBody>
            <p>{t('DISCARD_CHANGES_WARNING')}</p>
            <p>{t('common:CONFIRMATION_PROMPT')}</p>
          </S.DialogBody>
        </main>
        <DialogFooter style={{ marginTop: '24px' }}>
          <DialogClose asChild>
            <Button style={{ width: '100%' }} variant="outline">
              {t('common:FORM_ACTION_BACK')}
            </Button>
          </DialogClose>
          <Button style={{ width: '100%', background: '#B81D1D' }} onClick={onConfirm}>
            {t('common:FORM_ACTION_DISCARD')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
