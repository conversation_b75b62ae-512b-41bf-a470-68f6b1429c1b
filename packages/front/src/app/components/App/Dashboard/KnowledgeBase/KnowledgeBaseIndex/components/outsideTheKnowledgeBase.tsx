import { useSelector } from 'react-redux'
import IfUserCan from '../../../../../common/connected/IfUserCan'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '../../../../../common/unconnected/ui/dialog'
import React, { useState, useEffect } from 'react'
import { getUserAccount } from '../../../../../../modules/auth/selectors'
import { useRequest } from '../../../../../../hooks/useRequest'
import api from '../../../../../../resources/account/api'
import { useToast } from '../../../../../../hooks/useToast'
import { useTranslation } from 'react-i18next'
import { Button } from '../../../../../common/unconnected/ui/button'
import { Settings } from 'lucide-react'
import Switch from '../../../../../common/unconnected/Switch'

export function OutsideTheKnowledgeBase() {
  const { toast } = useToast()
  const { t } = useTranslation(['knowledgeBase', 'common'])

  const account = useSelector(getUserAccount)
  const [isDialogOpenCopilot, setIsDialogOpenCopilot] = useState(false)
  const [outsideTheKnowledgeBaseConfirm, setOutsideTheKnowledgeBaseConfirm] = useState(
    account?.settings?.outsideTheKnowledgeBase,
  )
  const [, execAccountUpdate] = useRequest(api.update, {})

  const handleOutsideTheKnowledgeBase = async () => {
    try {
      account.settings.outsideTheKnowledgeBase = outsideTheKnowledgeBaseConfirm
      await execAccountUpdate(account)
      toast({
        title: t('TOAST_OUTSIDE_THE_KNOWLEDGE_BASE'),
        variant: 'success',
      })
    } catch (error) {
      toast({
        title: t('common:MESSAGE_ERROR_NETWORK'),
        variant: 'warn',
      })
    }
  }

  useEffect(() => {
    setOutsideTheKnowledgeBaseConfirm(account?.settings?.outsideTheKnowledgeBase === true)
  }, [isDialogOpenCopilot, account])

  return (
    <Dialog open={isDialogOpenCopilot} onOpenChange={setIsDialogOpenCopilot}>
      <IfUserCan permission="knowledgeBase.create">
        <DialogTrigger asChild>
          <Button data-testid="button_settings" variant="ghost" style={{ marginLeft: 'auto', marginRight: '10px' }}>
            <Settings />
          </Button>
        </DialogTrigger>
      </IfUserCan>
      <DialogContent>
        <DialogHeader style={{ marginBottom: '40px' }}>
          <DialogTitle>{t('MODAL_TITLE_COPILOT')}</DialogTitle>
        </DialogHeader>
        <main style={{ marginBottom: '40px' }}>
          <Switch
            id="outside_the_knowledge_base_confirm"
            data-testid="outside_the_knowledge_base_confirm"
            label={t('OUTSIDE_THE_KNOWLEDGE_BASE')}
            checked={outsideTheKnowledgeBaseConfirm}
            onChange={(e) => setOutsideTheKnowledgeBaseConfirm(e.target.checked)}
            className=""
            reverseSwitch={false}
          />
        </main>
        <DialogFooter>
          <DialogClose asChild>
            <Button style={{ width: '100%' }} variant="outline" data-testid="handleOutsideTheKnowledgeBaseCancel">
              {t('common:FORM_ACTION_BACK')}
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              data-testid="handle_outside_the_knowledge_base_confirm"
              onClick={() => handleOutsideTheKnowledgeBase()}
              style={{ width: '100%' }}
            >
              {t('common:FORM_ACTION_SAVE')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
