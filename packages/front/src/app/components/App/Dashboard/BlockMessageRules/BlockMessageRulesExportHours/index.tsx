import React from 'react'
import { useTranslation } from 'react-i18next'
import { CalendarClock, Download } from 'lucide-react'
import {
  <PERSON>alog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from '../../../../common/unconnected/ui/dialog'
import { Button } from '../../../../common/unconnected/ui/button'
import { useRequest } from '../../../../../hooks/useRequest'
import { useToast } from '../../../../../hooks/useToast'
import useToggle from '../../../../../hooks/useToggle'
import blockMessageRuleApi from '../../../../../resources/blockMessageRule/api'
import * as S from './styles'

export function BlockMessageRulesExportHours() {
  const { t } = useTranslation(['blockMessageRulesPage', 'common'])

  const { toast } = useToast()
  const { isOpen, toggle, close } = useToggle()
  const [{ isLoading }, getExportHours] = useRequest(blockMessageRuleApi.exportHours)

  const handleExport = () =>
    getExportHours({})
      .then((response) => {
        const link = document.createElement('a')
        link.download = `${t('EXPORT_HOURS_FILENAME')}.xlsx`

        const blob = new Blob([response], {
          type: 'vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
        })

        link.href = window.URL.createObjectURL(blob)
        link.click()

        toast({
          title: t('EXPORT_HOURS_SUCCESS'),
          variant: 'success',
        })
      })
      .catch(() => {
        toast({
          title: t('EXPORT_HOURS_ERROR'),
          variant: 'destructive',
        })
      })
      .finally(() => {
        close()
      })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogTrigger asChild>
        <S.DropDownButton data-testid="menu-button-export_hours">
          <CalendarClock
            style={{
              color: '#333',
              marginRight: 11,
              width: 20,
              height: 20,
            }}
          />

          {t('MENU_BUTTON_EXPORT_HOURS')}
        </S.DropDownButton>
      </DialogTrigger>

      <DialogContent closeButton={false}>
        <DialogHeader style={{ marginBottom: '16px' }}>
          <Download size={40} color="#324B7D" />

          <DialogTitle>{t('MODAL_EXPORT_HOURS_TITLE')}</DialogTitle>
        </DialogHeader>

        <main className="text-center">
          <p style={{ margin: '0px 0px 8px', color: '#586171', fontSize: '16px' }}>
            {t('MODAL_EXPORT_HOURS_DESCRIPTION')}
          </p>
          <p style={{ margin: '0px', color: '#586171', fontSize: '16px' }}>
            {t('MODAL_EXPORT_HOURS_CONFIRM_QUESTION')}
          </p>
        </main>

        <DialogFooter style={{ marginTop: '40px' }}>
          <DialogClose asChild>
            <Button
              style={{ width: '100%' }}
              type="button"
              variant="outline"
              data-testid="block_message_rules-button-cancel"
            >
              {t('common:FORM_ACTION_CANCEL')}
            </Button>
          </DialogClose>

          <Button
            style={{ width: '100%' }}
            onClick={handleExport}
            disabled={isLoading}
            data-testid="block_message_rules-button-export"
          >
            {t('common:BUTTON_TEXT_EXPORT')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
