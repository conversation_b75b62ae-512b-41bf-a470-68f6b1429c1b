import styled from 'styled-components'
import React from 'react'
import { PrimaryColor } from '../../../styles/colors'

export const Item = styled.div`
  cursor: ${(props) => (props.disabled ? 'initial' : 'pointer')};
  transition: color 0.2s ease-in-out;
  &:hover {
    color: ${(props) => !props.disabled && `${PrimaryColor}`};
  }
`

const CheckboxContainer = styled.div`
  display: flex;
  vertical-align: middle;
`

const Icon = styled.svg`
  fill: none;
  stroke: white;
  stroke-width: 3px;
  position: absolute;
  top: 2px;
  margin-right: 0px !important;
`

const StyledCheckbox = styled.div`
  display: inline-block;
  width: 15px;
  height: 15px;
  background: ${(props) => (props.checked ? PrimaryColor : 'white')};
  border: 1.2px solid ${(props) => (props.checked ? 'transparent' : PrimaryColor)};
  border-radius: 3px;
  transition: all 150ms;
  cursor: ${(props) => (props.disabled ? 'initial' : 'pointer')};
  margin-right: 10px;
  position: relative;

  opacity: ${(props) => (props.disabled ? '0.5' : '1')};

  ${Icon} {
    visibility: ${(props) => (props.checked ? 'visible' : 'hidden')};
  }
`

export const Checkbox = ({ className, checked, onChange, onClick, disabled, ...props }) => (
  <CheckboxContainer className={className}>
    <StyledCheckbox
      checked={checked}
      onClick={onClick}
      disabled={disabled}
      data-testid="call_distribution-checkbox-redistribute"
    >
      <Icon viewBox="2 5 19 22">
        <polyline points="20 6 9 17 4 12" />
      </Icon>
    </StyledCheckbox>
  </CheckboxContainer>
)
