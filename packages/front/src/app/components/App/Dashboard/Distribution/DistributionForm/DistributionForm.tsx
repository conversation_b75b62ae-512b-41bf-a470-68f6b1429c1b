import React, { memo, useState } from 'react'
import Helmet from 'react-helmet'
import {
  Button,
  Form,
  Label,
  Input,
  ModalBody,
  ModalHeader,
  FormGroup,
  Popover,
  PopoverBody,
  Col,
  Row,
} from 'reactstrap'
import pick from 'lodash/pick'
import { useTranslation } from 'react-i18next'
import SweetAlert from '../../../../common/unconnected/SweetModal'
import Icon from '../../../../common/unconnected/Icon'
import { <PERSON>dalFooter, ModalDigisac } from '../../../styles/common'
import { required, hasLengthGreaterThanOrEqual } from '../../../../../utils/validator/validators'
import { isAllBlankSpace } from '../../../../../utils/isAllBlankSpace'
import Switch from '../../../../common/unconnected/Switch/Switch'
import DepartmentsSelect from '../../../../common/connected/DepartmentsSelect'
import RolesSelect from '../../../../common/connected/RolesSelect'
import InputGroup, { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import useFormController from '../../../../../hooks/crud/useFormController'
import {
  useCreateDistribution,
  useFetchOneDistribution,
  useUpdateDistribution,
} from '../../../../../resources/distribution/requests'
import ButtonClose from '../../../../common/unconnected/ButtonClose'
import * as S from './styles'

export const formatToApi = (distribution) => ({
  ...pick(distribution, [
    'id',
    'name',
    'maxNum',
    'timeToRedistribute',
    'departments',
    'roles',
    'archivedAt',
    'redistributeAll',
    'redistributeAssignedTickets',
    'distributeQueue',
  ]),
})

export const formatFromApi = (distribution) => ({
  ...pick(distribution, [
    'id',
    'name',
    'maxNum',
    'timeToRedistribute',
    'redistributeAll',
    'departments',
    'roles',
    'archivedAt',
    'redistributeAll',
    'redistributeAssignedTickets',
    'distributeQueue',
  ]),
})

const departmentSelectExtraQuery = (distributionId) => ({
  where: {
    distributionId: {
      $or: {
        $is: null,
        ...(distributionId && { $eq: distributionId }),
      },
    },
  },
})

const initialModel = {
  name: '',
  maxNum: 1,
  timeToRedistribute: 5,
  departments: [],
  roles: [],
  archivedAt: null,
  redistributeAll: false,
  redistributeAssignedTickets: false,
}

const DistributionForm = () => {
  const { t } = useTranslation(['common', 'distribution'])

  const requiredValidation = [required, t('common:REQUIRED_FIELD')]
  const blankSpaceValidation = [isAllBlankSpace, t('common:INVALID_FIELD')]

  const validationRules = {
    name: [requiredValidation, blankSpaceValidation],
    timeToRedistribute: [[({ value }) => Number(value) >= 5, t('common:INVALID_VALUE')]],
    departments: [requiredValidation, [hasLengthGreaterThanOrEqual(1), t('distribution:SELECT_DEPARTMENT')]],
  }

  const [openPopOver, setOpenPopOver] = useState(false)
  const togglePopover = () => setOpenPopOver(!openPopOver)

  const [openPopOver2, setOpenPopOver2] = useState(false)
  const togglePopover2 = () => setOpenPopOver2(!openPopOver2)

  const [openPopOver3, setOpenPopOver3] = useState(false)
  const togglePopover3 = () => setOpenPopOver3(!openPopOver3)

  const [openPopOver4, setOpenPopOver4] = useState(false)
  const togglePopover4 = () => setOpenPopOver4(!openPopOver4)

  const [openPopOver5, setOpenPopOver5] = useState(false)
  const togglePopover5 = () => setOpenPopOver5(!openPopOver5)

  const {
    submit,
    exit,
    isModalOpen,
    isEditing,
    isAlertShowing,
    closeAlert,
    isLoading,
    error,
    bindInput,
    validation,
    model,
    setModel,
    setProperty,
  } = useFormController({
    exitToPath: '/distribution',
    initialModel,
    validationRules,
    formatToApi,
    formatFromApi,
    useCreateOne: useCreateDistribution,
    useUpdateOne: useUpdateDistribution,
    useFetchOne: useFetchOneDistribution,
  })

  const title = `${isEditing ? t('common:LABEL_EDITING') : t('common:LABEL_CREATING')} ${t(
    'distribution:CALL_DISTRIBUTION',
  )}`

  const enableSwitch = (e, input) => {
    if (e.target.checked) {
      input.setProperty(input.id, null)
      return false
    }

    input.setProperty(input.id, new Date())
  }

  const handleDistAll = () => {
    if (model.redistributeAll) {
      setModel({ ...model, redistributeAll: false })
    } else {
      setModel({
        ...model,
        redistributeAll: true,
        redistributeAssignedTickets: false,
      })
    }
  }

  const handleDistAssigned = () => {
    if (model.redistributeAssignedTickets) {
      setModel({ ...model, redistributeAssignedTickets: false })
    } else {
      setModel({
        ...model,
        redistributeAll: false,
        redistributeAssignedTickets: true,
      })
    }
  }

  const handleQueueDistribute = () => {
    if (model.distributeQueue) {
      setModel({ ...model, distributeQueue: false })
    } else {
      setModel({
        ...model,
        distributeQueue: true,
      })
    }
  }

  return (
    <>
      <Helmet title={title} />

      <Form onSubmit={submit}>
        <ModalDigisac isOpen={isModalOpen} toggle={exit} autoFocus={false}>
          <ModalHeader data-testid="call_distribution-header-modal_create_edit">
            {title}
            <ButtonClose onClick={exit} />
          </ModalHeader>
          <ModalBody>
            <FormGroup>
              <InputGroupWrapper
                id="archivedAt"
                {...{
                  bindInput,
                  validation,
                  model,
                  setProperty,
                }}
                render={(input) => (
                  <Switch
                    id={input.id}
                    label={t('common:LABEL_UNARCHIVE')}
                    checked={input.model[input.id] === null}
                    onChange={(e) => enableSwitch(e, input)}
                    data-testid="call_distribution-switch-activete"
                  />
                )}
              />
            </FormGroup>

            <FormGroup>
              <Row>
                <Col md="6">
                  <S.Item className="mb-0 d-flex align-items-center">
                    <S.Checkbox
                      type="checkbox"
                      id="distributeDefault"
                      checked={!model.distributeQueue}
                      data-testid="distribution-default-checkbox"
                      onClick={handleQueueDistribute}
                    />{' '}
                    {t('distribution:DISTRIBUTION_DEFAULT')}{' '}
                    <div>
                      <Icon
                        id="distributionDefaultInfo"
                        size="lg"
                        name="info-circle"
                        color="#52648d"
                        style={{ cursor: 'pointer', marginLeft: '10px' }}
                        onClick={() => togglePopover4()}
                      />
                      <Popover
                        placement="top"
                        isOpen={openPopOver4}
                        target="distributionDefaultInfo"
                        toggle={togglePopover4}
                      >
                        <PopoverBody>{t('distribution:DISTRIBUTION_DEFAULT_INFO')}</PopoverBody>
                      </Popover>
                    </div>
                  </S.Item>
                </Col>
                <Col md="6">
                  <S.Item className="mb-0 d-flex align-items-center">
                    <S.Checkbox
                      type="checkbox"
                      id="distributeQueue"
                      checked={model.distributeQueue}
                      data-testid="distribution-queue-checkbox"
                      onClick={handleQueueDistribute}
                    />{' '}
                    {t('distribution:DISTRIBUTION_QUEUE')}{' '}
                    <div>
                      <Icon
                        id="distributionQueueInfo"
                        size="lg"
                        name="info-circle"
                        color="#52648d"
                        style={{ cursor: 'pointer', marginLeft: '10px' }}
                        onClick={() => togglePopover5()}
                      />
                      <Popover
                        placement="top"
                        isOpen={openPopOver5}
                        target="distributionQueueInfo"
                        toggle={togglePopover5}
                      >
                        <PopoverBody>{t('distribution:DISTRIBUTION_QUEUE_INFO')}</PopoverBody>
                      </Popover>
                    </div>
                  </S.Item>
                </Col>
              </Row>
            </FormGroup>

            <InputGroup
              id="name"
              label={t('common:LABEL_NAME')}
              required
              {...{ bindInput, validation }}
              data-testid="call_distribution-input-name"
            />

            <InputGroupWrapper
              id="departments"
              {...{
                bindInput,
                validation,
                model,
                setProperty,
              }}
              render={(input) => (
                <div data-testid="select-department">
                  <label htmlFor="defaultDepartmentSelect">
                    {t('distribution:DEPARTMENTS_INCLUDED')}
                    <Icon
                      id="departmentInfo"
                      size="lg"
                      name="info-circle"
                      color="#52648d"
                      style={{ cursor: 'pointer', marginLeft: '10px' }}
                      onClick={() => togglePopover3()}
                    />
                    <Popover placement="top" isOpen={openPopOver3} target="departmentInfo" toggle={togglePopover3}>
                      <PopoverBody>{t('distribution:DEPARTMENT_INFO')}</PopoverBody>
                    </Popover>
                  </label>
                  <DepartmentsSelect
                    isMulti
                    stateId="defaultDepartmentSelect"
                    id={input.id}
                    value={input.model[input.id]}
                    onChange={(value) => input.setProperty(input.id, value)}
                    extraQuery={departmentSelectExtraQuery(model.id)}
                    onBlur={() => input.validation.setTouched(input.id)}
                    hideArchived
                  />
                </div>
              )}
            />

            <InputGroupWrapper
              id="roles"
              label={t('distribution:ROLES_INCLUDED')}
              {...{
                bindInput,
                validation,
                model,
                setProperty,
              }}
              render={(input) => (
                <RolesSelect
                  isMulti
                  stateId="distributionRoles"
                  id={input.id}
                  value={input.model[input.id]}
                  onChange={(value) => input.setProperty(input.id, value)}
                  onBlur={() => input.validation.setTouched(input.id)}
                />
              )}
            />

            <FormGroup>
              <Label htmlFor="maxNum">{t('distribution:MAX_NUMBER')}</Label>
              <Input
                id="maxNum"
                type="number"
                min="1"
                {...bindInput('maxNum')}
                data-testid="call_distribution-input-max_calls"
              />
            </FormGroup>

            <FormGroup>
              <InputGroup
                id="timeToRedistribute"
                type="number"
                label={t('distribution:TIME_TO_KEEP_DISTRIBUTION_AFTER_INACTIVE_USER')}
                value={model?.timeToRedistribute}
                {...{
                  bindInput,
                  validation,
                }}
                data-testid="call_distribution-input-time_to_keep_distribution_after_inactive_user"
              />
            </FormGroup>

            <S.Item className="mb-0 d-flex align-items-center">
              <Label>{t('distribution:DO_NOT_REDISTRIBUTE_TO_USERS_INFO')}</Label>
            </S.Item>

            <FormGroup>
              <S.Item className="mb-0 d-flex align-items-center">
                <S.Checkbox
                  type="checkbox"
                  id="redistributeAll"
                  checked={model.redistributeAll}
                  data-testid="call_distribution-checkbox-all_tickets"
                  onClick={handleDistAll}
                />{' '}
                {t('distribution:REDISTRIBUTE_ALL')}{' '}
                <div>
                  <Icon
                    id="redistributeAllInfo"
                    size="lg"
                    name="info-circle"
                    color="#52648d"
                    style={{ cursor: 'pointer', marginLeft: '10px' }}
                    onClick={() => togglePopover()}
                  />
                  <Popover placement="top" isOpen={openPopOver} target="redistributeAllInfo" toggle={togglePopover}>
                    <PopoverBody>{t('distribution:REDISTRIBUTE_ALL_INFO')}</PopoverBody>
                  </Popover>
                </div>
              </S.Item>
            </FormGroup>

            <FormGroup>
              <S.Item className="mb-0 d-flex align-items-center">
                <S.Checkbox
                  type="checkbox"
                  id="notRedistributeAll"
                  checked={model.redistributeAssignedTickets}
                  data-testid="call_distribution-checkbox-only_unanswered"
                  onClick={handleDistAssigned}
                />{' '}
                {t('distribution:REDISTRIBUTE_ONLY_NOT_ANSWER')}{' '}
                <div>
                  <Icon
                    id="notRedistributeAllInfo"
                    size="lg"
                    name="info-circle"
                    color="#52648d"
                    style={{ cursor: 'pointer', marginLeft: '10px' }}
                    onClick={() => togglePopover2()}
                  />
                  <Popover
                    placement="top"
                    isOpen={openPopOver2}
                    target="notRedistributeAllInfo"
                    toggle={togglePopover2}
                  >
                    <PopoverBody>{t('distribution:NOT_REDISTRIBUTE_ALL_INFO')}</PopoverBody>
                  </Popover>
                </div>
              </S.Item>
            </FormGroup>
          </ModalBody>
          <ModalFooter>
            <Button className="cancel" type="button" onClick={exit} data-testid="call_distribution-button-cancel">
              {t('common:FORM_ACTION_CANCEL')}
            </Button>
            <Button
              data-testid="save-account-button"
              color="primary"
              type="submit"
              onClick={submit}
              disabled={isLoading}
              data-testid="call_distribution-button-save"
            >
              {t('common:FORM_ACTION_SAVE')}
            </Button>
          </ModalFooter>
        </ModalDigisac>
      </Form>

      {isAlertShowing && (
        <SweetAlert type="error" title={t('common:MESSAGE_ATTENTION')} onConfirm={closeAlert} showCancel={false}>
          {`${t('distribution:NOT_POSSIBLE')} ${
            isEditing ? t('common:LABEL_EDITING').toLowerCase() : t('common:FORM_ACTION_SAVE').toLowerCase()
          } ${t('distribution:CALL_DISTRIBUTION')}`}
        </SweetAlert>
      )}
    </>
  )
}

export default memo(DistributionForm)
