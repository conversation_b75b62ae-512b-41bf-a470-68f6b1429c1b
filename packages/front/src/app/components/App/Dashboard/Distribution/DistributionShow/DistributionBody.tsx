import React from 'react'
import { useTranslation } from 'react-i18next'
import { Label, Input, FormGroup, Col, Row } from 'reactstrap'
import InputGroup from '../../../../common/unconnected/InputGroup'
import DepartmentsSelect from '../../../../common/connected/DepartmentsSelect'
import RolesSelect from '../../../../common/connected/RolesSelect'
import Switch from '../../../../common/unconnected/Switch/Switch'
import * as S from '../DistributionForm/styles'

const DistributionBody = ({ distribution }) => {
  const { t } = useTranslation('ticketTopicsPage', 'distribution')
  return (
    <div className="container mt-4 mb-4">
      <FormGroup>
        <Switch
          id="archivedAt"
          label={t('common:LABEL_UNARCHIVED')}
          checked={distribution.archivedAt === null}
          disabled
        />
      </FormGroup>

      <FormGroup>
        <Row>
          <Col md="6">
            <S.Item className="mb-0 d-flex align-items-center" disabled>
              <S.Checkbox
                type="checkbox"
                id="distributeDefault"
                checked={!distribution.distributeQueue}
                data-testid="distribution-default-checkbox"
                disabled
              />{' '}
              {t('distribution:DISTRIBUTION_DEFAULT')}{' '}
            </S.Item>
          </Col>
          <Col md="6">
            <S.Item className="mb-0 d-flex align-items-center" disabled>
              <S.Checkbox
                type="checkbox"
                id="distributeQueue"
                checked={distribution.distributeQueue}
                data-testid="distribution-queue-checkbox"
                disabled
              />{' '}
              {t('distribution:DISTRIBUTION_QUEUE')}{' '}
            </S.Item>
          </Col>
        </Row>
      </FormGroup>

      <InputGroup id="name" label={t('common:LABEL_NAME')} value={distribution.name} disabled />

      <FormGroup>
        <Label htmlFor="distributionDep">{t('distribution:DEPARTMENTS_INCLUDED')}</Label>

        <DepartmentsSelect isMulti id="distributionDep" value={distribution.departments} disabled />
      </FormGroup>

      <FormGroup>
        <Label htmlFor="distributionRoles">{t('distribution:ROLES_INCLUDED')}</Label>

        <RolesSelect isMulti stateId="distributionRoles" id="distributionRoles" value={distribution.roles} disabled />
      </FormGroup>

      <FormGroup>
        <Label htmlFor="maxNum">{t('distribution:MAX_NUMBER')}</Label>
        <Input id="maxNum" type="number" value={distribution.maxNum} disabled />
      </FormGroup>

      <FormGroup>
        <Label htmlFor="timeToKeepDistributionAfterInactiveUser">
          {t('distribution:LABEL_TIME_TO_KEEP_DISTRIBUTION_AFTER_INACTIVE_USER')}
        </Label>
        <Input
          id="timeToKeepDistributionAfterInactiveUser"
          type="number"
          min={5}
          value={distribution.timeToKeepDistributionAfterInactiveUser}
          disabled
        />
      </FormGroup>

      <FormGroup>
        <S.Item className="mb-0 d-flex align-items-center" disabled>
          <S.Checkbox
            type="checkbox"
            id="redistributeAll"
            checked={distribution.redistributeAll && distribution.redistributeAssignedTickets}
            disabled
          />{' '}
          {t('distribution:REDISTRIBUTE_ALL')}{' '}
        </S.Item>
      </FormGroup>

      <FormGroup>
        <S.Item className="mb-0 d-flex align-items-center" disabled>
          <S.Checkbox
            type="checkbox"
            id="notRedistributeAll"
            checked={!distribution.redistributeAll && distribution.redistributeAssignedTickets}
            disabled
          />{' '}
          {t('distribution:REDISTRIBUTE_ONLY_NOT_ANSWER')}{' '}
        </S.Item>
      </FormGroup>
    </div>
  )
}

export default DistributionBody
