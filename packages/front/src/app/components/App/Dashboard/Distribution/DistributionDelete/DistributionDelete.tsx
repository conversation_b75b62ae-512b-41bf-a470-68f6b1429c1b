import React, { memo } from 'react'
import Helmet from 'react-helmet'
import { useTranslation } from 'react-i18next'
import SweetModal from '../../../../common/unconnected/SweetModal'
import { useFetchOneDistribution, useDeleteDistribution } from '../../../../../resources/distribution/requests'
import useDeleteController from '../../../../../hooks/crud/useDeleteController'

const DistributionDelete = () => {
  const {
    isModalOpen,
    exit,
    model: distribution,
    submit,
  } = useDeleteController({
    exitToPath: '/distribution',
    useFetchOne: useFetchOneDistribution,
    useDeleteOne: useDeleteDistribution,
  })

  const { t } = useTranslation(['common', 'distribution'])

  if (!distribution) return null

  return (
    <>
      <Helmet
        title={`${t('distribution:CALL_DISTRIBUTION')} - ${t('common:MODAL_DELETE_BUTTON_CONFIRM')} ${
          distribution.name
        }?`}
      />

      <SweetModal
        type="warning"
        title={`${t('common:MODAL_DELETE_BUTTON_CONFIRM')} ${t('distribution:CALL_DISTRIBUTION')}`}
        confirmBtnText={t('common:MODAL_DELETE_BUTTON_CONFIRM')}
        cancelBtnText={t('common:FORM_ACTION_CANCEL')}
        show={isModalOpen}
        onCancel={exit}
        onConfirm={submit}
      >
        {t('common:MESSAGE_MODAL_DELETE')}
      </SweetModal>
    </>
  )
}

export default memo(DistributionDelete)
