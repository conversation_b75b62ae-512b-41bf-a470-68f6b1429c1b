import React, { memo, useEffect, useCallback } from 'react'
import Helmet from 'react-helmet'
import { ModalBody, ModalHeader } from 'reactstrap'
import { withRouter } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import DistributionBody from './DistributionBody'
import { useFetchOneDistribution } from '../../../../../resources/distribution/requests'
import useToggle from '../../../../../hooks/useToggle'
import ButtonClose from '../../../../common/unconnected/ButtonClose'
import { ModalDigisac } from '../../../styles/common'

const useController = ({ history, match }) => {
  const { id } = match.params
  const { isOpen, close } = useToggle(true)
  const [{ data: distribution }, fetchOne] = useFetchOneDistribution()

  useEffect(() => {
    fetchOne(id, {})
  }, [])

  const exit = useCallback(() => {
    close()
    setTimeout(() => history.push('/distribution'), 300)
  }, [close, history])

  return { distribution, isOpen, exit }
}

const DistributionShow = ({ history, match }) => {
  const { distribution, isOpen, exit } = useController({ history, match })
  const { t } = useTranslation(['common', 'distribution'])
  if (!distribution) return null

  return (
    <>
      <Helmet title={`${t('distribution:CALL_DISTRIBUTION')} - ${distribution.name}`} />

      <ModalDigisac isOpen={isOpen} toggle={exit} autoFocus={false}>
        <ModalHeader data-testid="call_distribution-header-modal_view">
          {distribution.name}
          <ButtonClose onClick={exit} />
        </ModalHeader>
        <ModalBody>
          <DistributionBody distribution={distribution} />
        </ModalBody>
      </ModalDigisac>
    </>
  )
}

export default withRouter(memo(DistributionShow))
