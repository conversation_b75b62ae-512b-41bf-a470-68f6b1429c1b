import React, { memo } from 'react'
import { useTranslation } from 'react-i18next'
import { Col, FormGroup, Input, Label, Row } from 'reactstrap'
import { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import Select from '../../../../common/unconnected/Select'
import { GroupInput } from '../../../styles/common'

function Filters({ filters, handleFilterChange }) {
  const { t } = useTranslation(['common'])

  return (
    <Row>
      <Col md="4">
        <FormGroup>
          <Label>{t('common:LABEL_NAME')}</Label>
          <Input
            id="name"
            type="text"
            data-testid="call_distribution-input-filter_name"
            value={filters.name}
            onChange={(e) => handleFilterChange('name', e.target.value)}
            placeholder={t('common:LABEL_NAME')}
          />
        </FormGroup>
      </Col>

      <Col md="4">
        <FormGroup>
          <InputGroupWrapper
            id="archivedAt"
            label="Status"
            render={({ id }) => (
              <GroupInput>
                <Select
                  id={id}
                  value={filters.archivedAt}
                  onChange={(e) => handleFilterChange('archivedAt', e)}
                  options={[
                    {
                      value: 'unarchived',
                      label: t('common:LABEL_UNARCHIVED'),
                    },
                    {
                      value: 'archived',
                      label: t('common:LABEL_ARCHIVED'),
                    },
                  ]}
                  instanceId={id}
                  getOptionValue={(o) => o.value}
                  getOptionLabel={(o) => o.label}
                  searchPromptText={t('common:SELECT_SEARCH_PROMPT_TEXT')}
                  loadingPlaceholder={t('common:LOADING')}
                  placeholder={t('common:SELECT_LOADING_PLACEHOLDER')}
                  noResultsText={t('common:MESSAGE_NOT_FOUND_ELEMENTS')}
                  clearValueText={t('common:MESSAGE_CLEAR')}
                  clearAllText={t('common:MESSAGE_CLEAR_ALL')}
                />
              </GroupInput>
            )}
          />
        </FormGroup>
      </Col>
    </Row>
  )
}
export default memo(Filters)
