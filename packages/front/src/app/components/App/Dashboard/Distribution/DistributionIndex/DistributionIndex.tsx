import React, { memo } from 'react'
import Helmet from 'react-helmet'
import { Link, Switch } from 'react-router-dom'
import { DropdownItem } from 'reactstrap'
import { pickBy, identity } from 'lodash'
import { useTranslation } from 'react-i18next'
import Filters from './Filters'
import Icon from '../../../../common/unconnected/Icon'
import IfUserCan from '../../../../common/connected/IfUserCan'
import Toggler from '../../../../common/unconnected/Toggler'
import IfUserCanRoute from '../../../../common/connected/IfUserCanRoute'
import TablePagination from '../../../../common/unconnected/TablePagination'
import useIndexController from '../../../../../hooks/crud/useIndexController'

import { useFetchManyDistributions } from '../../../../../resources/distribution/requests'

import DistributionShow from '../DistributionShow/DistributionShow'
import DistributionForm from '../DistributionForm/DistributionForm'
import DistributionDelete from '../DistributionDelete/DistributionDelete'

import {
  Table,
  TableCell,
  TableColumn,
  TableBody,
  TableHead,
  TableOptions,
  TableRow,
  DropdownMenuActions,
  ButtonDropdownActions,
  ButtonRefresh,
  CardFilters,
} from '../../../styles/table'

import {
  IconSearch,
  IconOptions,
  IconTrash,
  IconEdit,
  IconShowPassword as IconEyes,
} from '../../../../common/unconnected/IconDigisac'
import Button from '../../../../common/unconnected/Button'

import { PrimaryColor, TextColor } from '../../../styles/colors'
import Container from '../../../styles/container'

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../../../common/unconnected/ui/tooltip'
import { Info } from 'lucide-react'

const TooptipDistribution = (info: string) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Info
            style={{
              color: '#324B7D',
              marginTop: '4px',
              marginLeft: '8px',
            }}
            size={14}
          />
        </TooltipTrigger>
        <TooltipContent>{info}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

const buildQuery = ({ filters, localPagination }) => ({
  query: JSON.stringify({
    where: {
      ...pickBy(
        {
          name: filters.name && { $iLike: `%${filters.name}%` },
        },
        identity,
      ),
      ...(filters.archivedAt && {
        archivedAt: { archived: { $ne: null }, unarchived: { $eq: null } }[filters.archivedAt.value],
      }),
    },
    include: [
      {
        model: 'departments',
        attributes: ['name'],
      },
      {
        model: 'roles',
        attributes: ['displayName'],
      },
    ],
    order: [['name', 'ASC']],
    page: localPagination.page,
    perPage: localPagination.perPage,
  }),
})

function Distributionindex({ match }) {
  const { t } = useTranslation(['common', 'distribution'])

  const initialFilters = {
    name: '',
    maxNum: 1,
    timeToRedistribute: 0,
    distributionDep: [],
    archivedAt: { value: 'unarchived', label: t('LABEL_UNARCHIVED') },
  }

  const {
    models: distributions,
    pagination,
    isLoading,
    fetch,
    localPagination,
    handleLocalPaginationChange,
    handleFilterChange,
    filters,
    toggleFilters,
    isFiltersShowing,
  } = useIndexController({
    buildQuery,
    initialFilters,
    useFetchMany: useFetchManyDistributions,
  })

  return (
    <div>
      <Helmet title={t('distribution:CALL_DISTRIBUTION')} />

      <Container>
        <div className="d-flex align-itens-center justify-content-between">
          <div className="title-page">
            <h2 data-testid="call_distribution-label-title">{t('distribution:CALL_DISTRIBUTION')}</h2>
          </div>

          <div className="d-flex">
            <IfUserCan permission="distribution.create">
              <Button
                background={PrimaryColor}
                size="xl"
                className="mr-2"
                data-testid="call_distribution-button-create"
              >
                <Link to="/distribution/create">
                  <Icon name="arrows-alt" fixedWidth className="mr-1" />
                  {t('distribution:NEW')} {t('distribution:DISTRIBUTION')}
                </Link>
              </Button>
            </IfUserCan>

            <Button
              background={PrimaryColor}
              size="xl"
              onClick={toggleFilters}
              className="mr-2"
              data-testid="call_distribution-button-show_hidden_filters"
            >
              <IconSearch fill="#fff" width="25" height="25" />
              {isFiltersShowing ? `${t('common:BUTTON_TEXT_HIDDEN')} ` : `${t('common:BUTTON_TEXT_SHOW')} `}
              {t('common:BUTTON_TEXT_FILTERS')}
            </Button>

            <ButtonRefresh
              loadIcon="sync-alt"
              color="default"
              isLoading={isLoading}
              onClick={fetch}
              data-testid="call_distribution-button-refresh"
            >
              <Icon name="sync-alt" fixedWidth />
            </ButtonRefresh>
          </div>
        </div>

        {isFiltersShowing && (
          <CardFilters>
            <Filters filters={filters} handleFilterChange={handleFilterChange} />
          </CardFilters>
        )}

        <Table className="mb-0">
          <TableHead columns={7} data-testid="call_distribution-header-table">
            <TableColumn>{t('common:LABEL_NAME')}</TableColumn>
            <TableColumn>{t('distribution:LABEL_STATUS')}</TableColumn>
            <TableColumn>
              {t('distribution:TABLE_HEAD_DEPARTMENT')}
              {TooptipDistribution(t('distribution:DEPARTMENTS_INCLUDED'))}
            </TableColumn>
            <TableColumn>
              {t('distribution:TABLE_HEAD_ROLE')}
              {TooptipDistribution(t('distribution:ROLES_INCLUDED'))}
            </TableColumn>
            <TableColumn>
              {t('distribution:TABLE_HEAD_MAX_TICKET')}
              {TooptipDistribution(t('distribution:MAX_NUMBER'))}
            </TableColumn>
            <TableColumn>
              {t('distribution:TABLE_HEAD_MAX_NUMBER')}
              {TooptipDistribution(t('distribution:LABEL_TIME_TO_KEEP_DISTRIBUTION_AFTER_INACTIVE_USER'))}
            </TableColumn>
            <TableColumn className="text-right justify-content-end pr-4">
              {t('common:ACTIONS_SUBMENU_TITLE')}
            </TableColumn>
          </TableHead>

          <TableBody data-testid="call_distribution-body-table">
            {distributions &&
              (distributions || []).map((distribution) => (
                <TableRow cells={7} key={distribution.id}>
                  <TableCell>
                    <Link to={`/distribution/${distribution.id}`}>{distribution.name}</Link>
                  </TableCell>

                  <TableCell>
                    {distribution.archivedAt ? t('common:LABEL_ARCHIVED') : t('common:LABEL_UNARCHIVED')}
                  </TableCell>

                  <TableCell>{distribution.departments?.map((item) => item.name).join(', ')}</TableCell>

                  <TableCell>{distribution.roles?.map((item) => item.displayName).join(', ')}</TableCell>

                  <TableCell>{distribution.maxNum}</TableCell>

                  <TableCell>{distribution?.timeToRedistribute >= 5 ? distribution?.timeToRedistribute : 5}</TableCell>

                  <TableCell actions>
                    <Toggler
                      render={({ active, toggle }) => (
                        <ButtonDropdownActions
                          group={false}
                          isOpen={active}
                          toggle={toggle}
                          data-testid="call_distribution-button-actions"
                        >
                          <TableOptions size="sm" color="primary">
                            <IconOptions className="icon-options" fill={PrimaryColor} />
                          </TableOptions>

                          <DropdownMenuActions>
                            <DropdownItem header>{t('common:ACTIONS_SUBMENU_TITLE')}</DropdownItem>
                            <Link
                              to={`/distribution/${distribution.id}`}
                              className="dropdown-item"
                              data-testid="call_distribution-button-view"
                            >
                              <IconEyes fill={TextColor} width="29" height="29" />
                              {t('common:ACTIONS_SUBMENU_VIEW')}
                            </Link>

                            <IfUserCan permission="distribution.update">
                              <Link
                                to={`/distribution/${distribution.id}/edit`}
                                className="dropdown-item"
                                data-testid="call_distribution-button-edit"
                              >
                                <IconEdit fill={TextColor} width="28" height="28" />
                                {t('common:ACTIONS_SUBMENU_EDIT')}
                              </Link>
                            </IfUserCan>

                            <IfUserCan permission="distribution.destroy">
                              <Link
                                to={`/distribution/${distribution.id}/delete`}
                                className="dropdown-item"
                                data-testid="call_distribution-button-delete"
                              >
                                <IconTrash fill={TextColor} width="28" height="28" />
                                {t('common:ACTIONS_SUBMENU_DELETE')}
                              </Link>
                            </IfUserCan>
                          </DropdownMenuActions>
                        </ButtonDropdownActions>
                      )}
                    />
                  </TableCell>
                </TableRow>
              ))}
            {distributions.length < 1 && (
              <tr>
                <td colSpan={7} className="text-center">
                  {t('common:NO_RESULTS_FOUND')}
                </td>
              </tr>
            )}
          </TableBody>
        </Table>

        <TablePagination
          pagination={pagination}
          localPagination={localPagination}
          handlePaginationChange={handleLocalPaginationChange}
        />
      </Container>

      <Switch>
        <IfUserCanRoute
          permission="distribution.create"
          exact
          path={`${match.url}/create`}
          component={DistributionForm}
        />
        <IfUserCanRoute
          permission="distribution.update"
          exact
          path={`${match.url}/:id/edit`}
          component={DistributionForm}
        />
        <IfUserCanRoute
          permission="distribution.destroy"
          exact
          path={`${match.url}/:id/delete`}
          component={DistributionDelete}
        />
        <IfUserCanRoute permission="distribution.view" exact path={`${match.url}/:id`} component={DistributionShow} />
      </Switch>
    </div>
  )
}

export default memo(Distributionindex)
