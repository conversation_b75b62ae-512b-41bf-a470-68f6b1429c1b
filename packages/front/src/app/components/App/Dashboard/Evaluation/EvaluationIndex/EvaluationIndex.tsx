import React from 'react'
import Helmet from 'react-helmet'
import { <PERSON>, Switch, withRouter } from 'react-router-dom'
import PropTypes from 'prop-types'
import { pickBy, identity } from 'lodash'
import { useTranslation } from 'react-i18next'
import LoadingSpinner from '../../../../common/unconnected/LoadingSpinner'
import Icon from '../../../../common/unconnected/Icon'
import Toggler from '../../../../common/unconnected/Toggler'
import TablePagination from '../../../../common/unconnected/TablePagination'
import IfUserCan from '../../../../common/connected/IfUserCan'
import IfUserCanRoute from '../../../../common/connected/IfUserCanRoute'
import { useFetchManyQuestions } from '../../../../../resources/questions/requests'
import useIndexController from '../../../../../hooks/crud/useIndexController'
import EvaluationDeleteRoute from '../EvaluationDelete'
import EvaluationFormRoute from '../EvaluationForm'
import EvaluationShowRoute from '../EvaluationShow'
import Filters from './Filters'
import Button from '../../../../common/unconnected/Button'

import {
  Table,
  TableHead,
  TableBody,
  TableColumn,
  TableRow,
  TableCell,
  TableOptions,
  ButtonRefresh,
  ButtonDropdownActions,
  DropdownItemActions,
  DropdownMenuActions,
  CardFilters,
} from '../../../styles/table'

import {
  IconSearch,
  IconRate,
  IconOptions,
  IconTrash,
  IconEdit,
  IconShowPassword as IconEyes,
} from '../../../../common/unconnected/IconDigisac'

import { PrimaryColor, TextColor } from '../../../styles/colors'
import Container from '../../../styles/container'

const buildQuery = ({ filters, localPagination }) =>
  pickBy(
    {
      where: pickBy(
        {
          name: filters.name && { $iLike: `%${filters.name}%` },
        },
        identity,
      ),
      page: localPagination.page,
      perPage: localPagination.perPage,
      order: [['name', 'ASC']],
    },
    identity,
  )

const initialFilters = {
  name: '',
}

const EvaluationIndex = ({ match }) => {
  const {
    models: questions,
    pagination,
    isLoading,
    fetch,
    localPagination,
    handleLocalPaginationChange,
    handleFilterChange,
    filters,
    toggleFilters,
    isFiltersShowing,
  } = useIndexController({
    buildQuery,
    initialFilters,
    useFetchMany: useFetchManyQuestions,
  })

  const { t } = useTranslation(['evaluationPage', 'common'])

  return (
    <div>
      <Helmet title={t('TITLE_EVALUATION')} />

      <Container>
        <div className="d-flex align-itens-center justify-content-between">
          <div>
            <h2 data-testid="evaluation-label-title">{t('TITLE_EVALUATION')}</h2>
          </div>

          <div className="d-flex">
            <IfUserCan permission="evaluation.create">
              <Button background={PrimaryColor} size="xl" className="mr-2">
                <Link to="/evaluation/create" data-testid="evaluation-button-create_evaluation">
                  <IconRate fill="white" width="25" height="25" />
                  {t('common:BUTTON_NEW_ITEM_A', {
                    item: `${t('TITLE_RATING')}`,
                  })}
                </Link>
              </Button>
            </IfUserCan>
            <Button
              background={PrimaryColor}
              size="xl"
              onClick={toggleFilters}
              className="mr-2"
              data-testid="evaluation-button-show_filters"
            >
              <IconSearch fill="white" width="25" height="25" />
              {isFiltersShowing ? `${t('common:BUTTON_TEXT_HIDDEN')} ` : `${t('common:BUTTON_TEXT_SHOW')} `}
              {t('common:BUTTON_TEXT_FILTERS')}
            </Button>
            <ButtonRefresh color="default" onClick={fetch} data-testid="evaluation-button-refresh">
              <Icon name="sync-alt" fixedWidth />
            </ButtonRefresh>
          </div>
        </div>

        {isFiltersShowing && (
          <CardFilters>
            <Filters filters={filters} handleFilterChange={handleFilterChange} />
          </CardFilters>
        )}

        <Table className="mb-0" hover>
          <TableHead columns={5}>
            <TableColumn data-testid="evaluation-label-name">{t('TABLE_COLUMN_NAME')}</TableColumn>
            <TableColumn data-testid="evaluation-label-type">{t('TABLE_COLUMN_TYPE')}</TableColumn>
            <TableColumn data-testid="evaluation-label-question">{t('TABLE_COLUMN_QUESTION')}</TableColumn>
            <TableColumn data-testid="evaluation-label-duration">{t('TABLE_COLUMN_DURATION')}</TableColumn>
            <TableColumn data-testid="evaluation-label-actions" className="text-right justify-content-end pr-4">
              {t('common:ACTIONS_SUBMENU_TITLE')}
            </TableColumn>
          </TableHead>
          <TableBody data-testid="evaluation-body-table">
            {questions &&
              questions.map((question) => (
                <TableRow cells={5} key={question.id}>
                  <TableCell>
                    <IconRate fill={TextColor} width="24" height="24" className="mr-2" />
                    <Link to={`/evaluation/${question.id}`}>{question.name}</Link>
                  </TableCell>
                  <TableCell>{question.type.toUpperCase()}</TableCell>
                  <TableCell>{question.questionMessage}</TableCell>
                  <TableCell>{question.duration}</TableCell>
                  <TableCell actions>
                    <Toggler
                      render={({ active, toggle }) => (
                        <ButtonDropdownActions
                          group={false}
                          isOpen={active}
                          toggle={toggle}
                          data-testid="evaluation-button-actions"
                        >
                          <TableOptions size="sm" color="primary">
                            <IconOptions className="icon-options" fill={PrimaryColor} />
                          </TableOptions>
                          <DropdownMenuActions>
                            <DropdownItemActions header data-testid="evaluations-label_submenu-actions">
                              {t('common:ACTIONS_SUBMENU_TITLE')}
                            </DropdownItemActions>
                            <Link
                              to={`/evaluation/${question.id}`}
                              className="dropdown-item"
                              data-testid="evaluation-button-view"
                            >
                              <IconEyes fill={TextColor} width="29" height="29" />
                              {t('common:ACTIONS_SUBMENU_VIEW')}
                            </Link>

                            <IfUserCan permission="evaluation.update">
                              <Link
                                to={`/evaluation/${question.id}/edit`}
                                className="dropdown-item"
                                data-testid="evaluation-button-edit"
                              >
                                <IconEdit fill={TextColor} width="28" height="28" />
                                {t('common:ACTIONS_SUBMENU_EDIT')}
                              </Link>
                            </IfUserCan>

                            <IfUserCan permission="evaluation.destroy">
                              <Link
                                to={`/evaluation/${question.id}/delete`}
                                className="dropdown-item"
                                data-testid="evaluation-button-delete"
                              >
                                <IconTrash fill={TextColor} width="28" height="28" />
                                {t('common:ACTIONS_SUBMENU_DELETE')}
                              </Link>
                            </IfUserCan>
                          </DropdownMenuActions>
                        </ButtonDropdownActions>
                      )}
                    />
                  </TableCell>
                </TableRow>
              ))}
            {!isLoading && questions.length < 1 && (
              <tr>
                <td colSpan={6} className="text-center">
                  {t('common:NO_RESULTS_FOUND')}
                </td>
              </tr>
            )}
            {questions.length === 0 && isLoading && <LoadingSpinner isLoading />}
          </TableBody>
        </Table>

        <TablePagination
          pagination={pagination}
          localPagination={localPagination}
          handlePaginationChange={handleLocalPaginationChange}
        />
      </Container>

      <Switch>
        <IfUserCanRoute permission="evaluation.create" path={`${match.url}/create`} component={EvaluationFormRoute} />
        <IfUserCanRoute permission="evaluation.update" path={`${match.url}/:id/edit`} component={EvaluationFormRoute} />
        <IfUserCanRoute
          permission="evaluation.destroy"
          path={`${match.url}/:id/delete`}
          component={EvaluationDeleteRoute}
        />
        <IfUserCanRoute permission="evaluation.view" path={`${match.url}/:id`} component={EvaluationShowRoute} />
      </Switch>
    </div>
  )
}

EvaluationIndex.propTypes = {
  match: PropTypes.object,
}

export default withRouter(EvaluationIndex)
