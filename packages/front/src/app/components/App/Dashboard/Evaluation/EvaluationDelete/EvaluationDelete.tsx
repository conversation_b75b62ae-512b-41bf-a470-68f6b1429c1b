import React, { useCallback, useEffect, useState } from 'react'
import Helmet from 'react-helmet'
import { with<PERSON><PERSON><PERSON> } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import SweetModal from '../../../../common/unconnected/SweetModal'
import useToggle from '../../../../../hooks/useToggle'
import { useDeleteQuestions, useFetchOneQuestions } from '../../../../../resources/questions/requests'
import usePrevious from '../../../../../hooks/usePrevious'

const useExit = () => {
  const { isOpen: isModalOpen, close: closeModal } = useToggle(true)

  const exit = useCallback(() => {
    closeModal()
  }, [])

  const [isModalErrorOpen, setIsModalErrorOpen] = useState(false)

  const exitError = useCallback(() => {
    setIsModalErrorOpen(true)
  }, [])

  return { isModalOpen, exit, isModalErrorOpen, exitError }
}

const useFetch = ({ id }) => {
  const [{ data: question = null }, fetch] = useFetchOneQuestions(id)

  useEffect(() => {
    fetch()
  }, [])

  return { question }
}

const useDelete = ({ id, exit, exitError, history }) => {
  const [{ response, error, isLoading }, execDelete] = useDeleteQuestions(id)
  const wasLoading = usePrevious(isLoading)
  const justEndedLoading = wasLoading && !isLoading

  const submit = useCallback(() => {
    execDelete(id)
    exit()
  }, [id])

  useEffect(() => {
    if (justEndedLoading && !error) {
      exit()
    }
  }, [justEndedLoading, error])

  useEffect(() => {
    if (error?.response) {
      if (
        error?.response?.status === 403 &&
        error?.response?.data?.message === 'Cannot archive because it is used in a bot.'
      ) {
        exitError()
      }
    }
  }, [error])

  useEffect(() => {
    if (response && response === 'OK') {
      setTimeout(() => history.push('/evaluation', { refresh: true }), 300)
    }
  }, [response])

  return { isLoading, submit, error }
}

const useController = ({ match, history }) => {
  const { id } = match.params

  const { isModalOpen, exit, isModalErrorOpen, exitError } = useExit({
    history,
  })
  const { question } = useFetch({ id })
  const { isLoading, submit, error } = useDelete({
    id,
    exit,
    exitError,
    history,
  })

  return {
    isModalOpen,
    exit,
    isModalErrorOpen,
    exitError,
    question,
    isLoading,
    submit,
    error,
  }
}

const EvaluationDelete = ({ match, history }) => {
  const { t } = useTranslation(['evaluationPage', 'common'])

  const { isModalOpen, question, submit, isModalErrorOpen } = useController({
    match,
    history,
  })

  if (!question) return null

  return (
    <div data-testid="evaluation-modal-delete_evaluation">
      <Helmet title={`${t('TITLE_RATING')} - ${t('common:ACTIONS_SUBMENU_DELETE')} ${question.name}?`} />

      <SweetModal
        type="warning"
        title={`${t('common:ACTIONS_SUBMENU_DELETE')} ${t('TITLE_RATING')}`}
        confirmBtnText={t('common:ACTIONS_SUBMENU_DELETE')}
        cancelBtnText={t('common:FORM_ACTION_CANCEL')}
        onConfirm={submit}
        show={isModalOpen}
        onCancel={() => {
          setTimeout(() => history.push('/evaluation', { refresh: true }), 300)
        }}
        data-testid="evaluation-modal-delete_evaluation"
      >
        {t('common:MESSAGE_MODAL_DELETE')}
      </SweetModal>

      <SweetModal
        type="warning"
        title={t('common:THIS_ACTION_COULD_NOT_BE_PERFORMED')}
        confirmBtnText="Ok"
        show={isModalErrorOpen}
        showCancel={false}
        onConfirm={() => {
          setTimeout(() => history.push('/evaluation', { refresh: true }), 300)
        }}
        data-testid="evaluation-modal-delete_evaluation-error"
      >
        {t('CANNOT_ARCHIVE_BECAUSE_USED_BOT')}
      </SweetModal>
    </div>
  )
}

export default withRouter(EvaluationDelete)
