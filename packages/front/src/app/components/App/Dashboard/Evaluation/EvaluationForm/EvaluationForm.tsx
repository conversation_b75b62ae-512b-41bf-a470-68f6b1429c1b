import React from 'react'
import Helmet from 'react-helmet'
import find from 'lodash/find'
import pick from 'lodash/pick'
import <PERSON><PERSON>lert from 'react-bootstrap-sweetalert'
import { Button, Form, ModalBody, ModalHeader, Row, Col } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { required, isGreaterThanOrEqual } from '../../../../../utils/validator/validators'
import { isAllBlankSpace } from '../../../../../utils/isAllBlankSpace'
import InputGroup, { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import Select from '../../../../common/unconnected/Select'
import useFormController from '../../../../../hooks/crud/useFormController'
import options from '../EvaluationConstants'
import {
  useCreateQuestions,
  useFetchOneQuestions,
  useUpdateQuestions,
} from '../../../../../resources/questions/requests'
import { <PERSON><PERSON><PERSON><PERSON>sa<PERSON>, <PERSON>dalFooter, GroupInput } from '../../../styles/common'
import ButtonClose from '../../../../common/unconnected/ButtonClose'

const formatToApi = (question) => ({
  ...pick(question, ['name', 'questionMessage', 'duration', 'successMessage', 'invalidMessage', 'reasonMessage']),
  tries: +question.tries,
  type: question.type && question.type.id,
})

const formatFromApi = (question) => ({
  ...question,
  type: find(options, (o) => question && o.id === question.type) || options[0],
})

const initialModel = {
  name: '',
  type: '',
  questionMessage: '',
  duration: '',
  tries: 3,
  successMessage: '',
  invalidMessage: '',
  reasonMessage: '',
}

const EvaluationForm = () => {
  const { t } = useTranslation(['evaluationPage', 'common'])
  const requiredValidation = [required, t('common:REQUIRED_FIELD')]
  const blankSpaceValidation = [isAllBlankSpace, t('common:INVALID_FIELD')]

  const isGreaterThanOrEqualValidation = [isGreaterThanOrEqual(1), t('common:INVALID_VALUE')]

  const validationRules = {
    name: [requiredValidation, blankSpaceValidation],
    type: [requiredValidation],
    questionMessage: [requiredValidation],
    duration: [requiredValidation, isGreaterThanOrEqualValidation],
    tries: [requiredValidation, isGreaterThanOrEqualValidation],
  }

  const { submit, exit, isModalOpen, isEditing, isAlertShowing, closeAlert, isLoading, error, bindInput, validation } =
    useFormController({
      exitToPath: '/evaluation',
      initialModel,
      validationRules,
      formatToApi,
      formatFromApi,
      useCreateOne: useCreateQuestions,
      useUpdateOne: useUpdateQuestions,
      useFetchOne: useFetchOneQuestions,
    })

  const title = `${isEditing ? t('common:LABEL_EDITING') : t('common:LABEL_CREATING')} ${t('TITLE_RATING')}`

  const errorCode = error?.response?.status

  const getErrorMessage = () => {
    if (errorCode === 402) {
      return t('common:ERROR_TO_SAVE_DATA')
    }

    if (errorCode === 409) {
      return t('MODAL_DUPLICATE_NAME')
    }

    return t('ERROR_SAVE_EVALUATION')
  }

  return (
    <>
      <Helmet title={t('TITLE_RATING')} />

      <Form onSubmit={submit} data-testid="create-evaluation">
        <ModalDigisac isOpen={isModalOpen} toggle={exit} autoFocus={false} size="lg">
          <ModalHeader data-testid="evaluation-header-modal_create_evaluation">
            {title}
            <ButtonClose onClick={exit} />
          </ModalHeader>
          <ModalBody data-testid="evaluation-body-modal_create_evaluation">
            <Row>
              <Col>
                <InputGroup
                  data-testid="evaluation-input-name_modal_create_evaluation"
                  id="name"
                  label={t('TABLE_COLUMN_NAME')}
                  required
                  maxLength={255}
                  {...{ bindInput, validation }}
                />
              </Col>
              <Col>
                <InputGroup
                  data-testid="evaluation-input-duration_modal_create_evaluation"
                  type="number"
                  id="duration"
                  label={t('CREATE_EVALUATION_DURATION_MINUTES')}
                  min="0"
                  required
                  {...{ bindInput, validation }}
                />
              </Col>
            </Row>

            <InputGroupWrapper
              id="type"
              label={t('TABLE_COLUMN_TYPE')}
              required
              {...{
                bindInput,
                validation,
              }}
              render={(input) => (
                <GroupInput>
                  <Select
                    className="filter-type"
                    id={input.id}
                    onlyValue
                    {...bindInput(input.id)}
                    options={options}
                    disabled={isEditing}
                  />
                </GroupInput>
              )}
            />

            <InputGroup
              data-testid="evaluation-input-question_modal_create_evaluation"
              id="questionMessage"
              label={t('TABLE_COLUMN_QUESTION')}
              placeholder={t('CREATE_EVALUATION_QUESTION_PLACEHOLDER')}
              type="textarea"
              required
              {...{ bindInput, validation }}
            />

            <InputGroup
              data-testid="evaluation-input-tries_modal_create_evaluation"
              type="number"
              id="tries"
              label={t('CREATE_EVALUATION_NUMBER_OF_INVALID')}
              required
              min="0"
              {...{ bindInput, validation }}
            />

            <Row>
              <Col>
                <InputGroup
                  data-testid="evaluation-input-seccess_message_modal_create_evaluation"
                  id="successMessage"
                  label={t('CREATE_EVALUATION_MESSAGE_SEND')}
                  placeholder={t('CREATE_EVALUATION_MESSAGE_SEND_PLACEHOLDER')}
                  type="textarea"
                  {...{ bindInput, validation }}
                />
              </Col>
              <Col>
                <InputGroup
                  data-testid="evaluation-input-invalid_message_modal_create_evaluation"
                  id="invalidMessage"
                  label={t('CREATE_EVALUATION_MESSAGE_INVALID')}
                  placeholder={t('CREATE_EVALUATION_MESSAGE_INVALID_PLACEHOLDER')}
                  type="textarea"
                  {...{ bindInput, validation }}
                />
              </Col>
            </Row>

            <Row>
              <Col>
                <InputGroup
                  data-testid="evaluation-input-reason_message_modal_create_evaluation"
                  id="reasonMessage"
                  label={t('EVALUATION_REASON')}
                  placeholder={t('EVALUATION_REASON')}
                  type="textarea"
                  {...{ bindInput, validation }}
                />
              </Col>
            </Row>
          </ModalBody>

          <ModalFooter>
            <Button
              key="2"
              data-testid="cancel-evaluation-button"
              type="button"
              className="cancel"
              onClick={exit}
              disabled={false}
            >
              {t('common:FORM_ACTION_CANCEL')}
            </Button>
            <Button
              key="1"
              data-testid="evaluation-button-save_evaluation"
              className="confirm"
              type="submit"
              onClick={submit}
              disabled={isLoading}
            >
              {t('common:FORM_ACTION_SAVE')}
            </Button>
          </ModalFooter>
        </ModalDigisac>
      </Form>
      {isAlertShowing && (
        <SweetAlert
          type={errorCode === 500 ? 'error' : 'warning'}
          error
          title={t('common:MESSAGE_ATTENTION')}
          onConfirm={closeAlert}
        >
          {getErrorMessage()}
        </SweetAlert>
      )}
    </>
  )
}

export default EvaluationForm
