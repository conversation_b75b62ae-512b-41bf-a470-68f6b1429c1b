import React from 'react'
import { Col, Input, Row } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { GroupInputFilter } from '../../../styles/table'

function Filters({ filters, handleFilterChange }) {
  const { t } = useTranslation('customFields')
  return (
    <Row>
      <Col md="3">
        <GroupInputFilter hiddenIcon>
          <Input
            type="text"
            value={filters.name}
            onChange={(e) => handleFilterChange('name', e.target.value)}
            placeholder={`${t('COLUMN_CUSTOM_FIELD_NAME')}...`}
            data-testid="custom_fields-imput-name_filter"
          />
        </GroupInputFilter>
      </Col>
    </Row>
  )
}

export default Filters
