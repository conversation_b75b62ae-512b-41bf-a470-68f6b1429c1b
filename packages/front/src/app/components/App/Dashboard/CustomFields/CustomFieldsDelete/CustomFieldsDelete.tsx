import React, { memo } from 'react'
import Helmet from 'react-helmet'
import { useTranslation } from 'react-i18next'
import SweetModal from '../../../../common/unconnected/SweetModal'
import { useDeleteCustomFields, useFetchOneCustomFields } from '../../../../../resources/customFields/requests'
import useDeleteController from '../../../../../hooks/crud/useDeleteController'

function CustomFieldsDelete() {
  const { t } = useTranslation(['customFields', 'common'])

  const {
    isModalOpen,
    exit,
    model: customFields,
    isLoading,
    submit,
    error,
  } = useDeleteController({
    exitToPath: '/custom-fields',
    useFetchOne: useFetchOneCustomFields,
    useDeleteOne: useDeleteCustomFields,
  })

  if (!customFields) return null

  return (
    <div data-testid="custom_fields-modal-delete_fields">
      <Helmet
        title={`${t('TITLE_CUSTOM_FIELDS')} - ${t('common:MODAL_DELETE_BUTTON_CONFIRM')} ${customFields.name}?`}
      />

      <SweetModal
        type="warning"
        title={`${t('common:MODAL_DELETE_BUTTON_CONFIRM')} ${t('TITLE_FIELD')}`}
        confirmBtnText={t('common:MODAL_DELETE_BUTTON_CONFIRM')}
        cancelBtnText={t('common:FORM_ACTION_CANCEL')}
        onConfirm={submit}
        show={isModalOpen}
        onCancel={exit}
        data-testid="custom_fields-modal-delete_fields"
      >
        {t('common:MESSAGE_MODAL_DELETE')}
      </SweetModal>
    </div>
  )
}

export default memo(CustomFieldsDelete)
