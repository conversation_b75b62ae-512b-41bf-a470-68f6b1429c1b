import React, { memo } from 'react'
import Helmet from 'react-helmet'
import { Link, useRouteMatch, Route, Switch } from 'react-router-dom'
import { pickBy, identity } from 'lodash'
import { useTranslation } from 'react-i18next'
import CustomFieldsDeleteRoute from '../CustomFieldsDelete'
import Icon from '../../../../common/unconnected/Icon'
import IfUserCan from '../../../../common/connected/IfUserCan'
import Toggler from '../../../../common/unconnected/Toggler'
import IfUserCanRoute from '../../../../common/connected/IfUserCanRoute'
import TablePagination from '../../../../common/unconnected/TablePagination'
import CardLoading from '../../../../common/unconnected/CardLoading'
import Filters from './Filters'
import useIndexController from '../../../../../hooks/crud/useIndexController'
import { useFetchManyCustomFields } from '../../../../../resources/customFields/requests'

import Button from '../../../../common/unconnected/Button'

import {
  Table,
  TableHead,
  TableBody,
  TableColumn,
  TableRow,
  TableCell,
  TableOptions,
  ButtonRefresh,
  ButtonDropdownActions,
  DropdownItemActions,
  DropdownMenuActions,
  CardFilters,
} from '../../../styles/table'

import {
  IconSearch,
  IconOptions,
  IconTrash,
  IconEdit,
  IconShowPassword as IconEyes,
} from '../../../../common/unconnected/IconDigisac'

import { PrimaryColor, TextColor } from '../../../styles/colors'
import Container from '../../../styles/container'
import { CustomFieldTypeToTranslateKey } from '../../../../../constants/customFields'

const buildQuery = ({ filters, localPagination }) =>
  pickBy(
    {
      where: pickBy(
        {
          name: filters.name && { $iLike: `%${filters.name}%` },
        },
        identity,
      ),
      page: localPagination.page,
      perPage: localPagination.perPage,
      order: [['name', 'asc']],
    },
    identity,
  )

const initialFilters = {
  name: '',
}

function CustomFieldsIndex() {
  const {
    models: customFields,
    pagination,
    isLoading,
    fetch,
    isFiltersShowing,
    toggleFilters,
    filters,
    handleFilterChange,
    localPagination,
    handleLocalPaginationChange,
  } = useIndexController({
    buildQuery,
    initialFilters,
    useFetchMany: useFetchManyCustomFields,
  })

  const { t } = useTranslation(['customFields', 'common'])

  const match = useRouteMatch()

  const translationTypesEntitys = {
    type: t('LABEL_TYPE'),
    ...Object.entries(CustomFieldTypeToTranslateKey).reduce((prev, acc) => {
      const [key, value] = acc
      prev[key] = t(value.label)
      return prev
    }, {}),
    contacts: t('OPTION_SELECT_CONTACTS'),
  }

  return (
    <div>
      <Helmet title={t('TITLE_CUSTOM_FIELDS')} />

      <Container>
        <div className="d-flex align-itens-center justify-content-between">
          <div className="title-page">
            <h2 data-testid="custom_fields-label-title">{t('TITLE_CUSTOM_FIELDS')}</h2>
          </div>

          <div className="d-flex align-items-center">
            <IfUserCan permission="customFields.create">
              <Button background={PrimaryColor} size="xl" className="mr-2">
                <Link to="/custom-fields/create" data-testid="custom_fields-button-new_field">
                  <Icon name="plus" fixedWidth className="mr-1" />
                  {t('common:BUTTON_NEW_ITEM', { item: t('TITLE_FIELD') })}
                </Link>
              </Button>
            </IfUserCan>
            <Button
              background={PrimaryColor}
              size="xl"
              onClick={toggleFilters}
              className="mr-2"
              data-testid="custom_fields-button-filter"
            >
              <IconSearch fill="white" width="25" height="25" />
              {isFiltersShowing ? `${t('common:BUTTON_TEXT_HIDDEN')} ` : `${t('common:BUTTON_TEXT_SHOW')} `}
              {t('common:BUTTON_TEXT_FILTERS')}
            </Button>
            <ButtonRefresh
              loadIcon="sync-alt"
              isLoading={isLoading}
              onClick={fetch}
              data-testid="custom_fields-button-refresh"
            >
              <Icon name="sync-alt" fixedWidth />
            </ButtonRefresh>
          </div>
        </div>

        <CardLoading isLoading={isLoading} />

        {isFiltersShowing && (
          <CardFilters>
            <Filters filters={filters} handleFilterChange={handleFilterChange} />
          </CardFilters>
        )}

        <Table data-testid="custom_fields-table-custom_fields">
          <TableHead columns={6}>
            <TableColumn data-testid="custom_fields-column-name">{t('COLUMN_CUSTOM_FIELD_NAME')}</TableColumn>
            <TableColumn data-testid="custom_fields-column-type">{t('COLUMN_CUSTOM_FIELD_TYPE')}</TableColumn>
            <TableColumn data-testid="custom_fields-column-entity">{t('COLUMN_CUSTOM_FIELD_ENTITY')}</TableColumn>
            <TableColumn data-testid="custom_fields-column-show-on-register">
              {t('COLUMN_CUSTOM_FIELD_SHOW_ON_REGISTER')}
            </TableColumn>
            <TableColumn data-testid="custom_fields-column-required">{t('COLUMN_CUSTOM_FIELD_REQUIRED')}</TableColumn>
            <TableColumn data-testid="custom_fields-column-actions" className="text-right justify-content-end pr-4">
              {t('common:ACTIONS_SUBMENU_TITLE')}
            </TableColumn>
          </TableHead>
          <TableBody data-testid="custom_fields-body-table">
            {customFields &&
              customFields.map((customFields) => (
                <TableRow cells={6} key={customFields.id}>
                  <TableCell>
                    <Link to={`/customFields/${customFields.id}`}>{customFields.name}</Link>
                  </TableCell>
                  <TableCell>{translationTypesEntitys[customFields.type]}</TableCell>
                  <TableCell>{translationTypesEntitys[customFields.allowed]}</TableCell>
                  <TableCell>{customFields.showOnRegister ? t('common:YES_LABEL') : t('common:NO_LABEL')}</TableCell>
                  <TableCell>{customFields.required ? t('common:YES_LABEL') : t('common:NO_LABEL')}</TableCell>

                  <TableCell actions>
                    <Toggler
                      render={({ active, toggle }) => (
                        <ButtonDropdownActions
                          group={false}
                          isOpen={active}
                          toggle={toggle}
                          data-testid="custom_fields-buttom-action"
                        >
                          <TableOptions size="sm" color="primary">
                            <IconOptions className="icon-options" fill={PrimaryColor} />
                          </TableOptions>
                          <DropdownMenuActions>
                            <DropdownItemActions header>{t('common:ACTIONS_SUBMENU_TITLE')}</DropdownItemActions>
                            <Link to={`/custom-fields/${customFields.id}`} className="dropdown-item">
                              <IconEyes fill={TextColor} width="29" height="29" />
                              {t('common:ACTIONS_SUBMENU_VIEW')}
                            </Link>

                            <IfUserCan permission="customFields.update">
                              <Link
                                to={`/custom-fields/${customFields.id}/edit`}
                                className="dropdown-item"
                                data-testid="custom_fields-button-edit"
                              >
                                <IconEdit fill={TextColor} width="28" height="28" />
                                {t('common:ACTIONS_SUBMENU_EDIT')}
                              </Link>
                            </IfUserCan>

                            <IfUserCan permission="customFields.destroy">
                              <Link
                                to={`/custom-fields/${customFields.id}/delete`}
                                className="dropdown-item"
                                data-testid="custom_fields-button-delete"
                              >
                                <IconTrash fill={TextColor} width="28" height="28" />
                                {t('common:ACTIONS_SUBMENU_DELETE')}
                              </Link>
                            </IfUserCan>
                          </DropdownMenuActions>
                        </ButtonDropdownActions>
                      )}
                    />
                  </TableCell>
                </TableRow>
              ))}
            {customFields.length < 1 && (
              <tr>
                <td colSpan={6} className="text-center">
                  {t('common:NO_RESULTS_FOUND')}
                </td>
              </tr>
            )}
          </TableBody>
        </Table>

        <TablePagination
          pagination={pagination}
          localPagination={localPagination}
          handlePaginationChange={handleLocalPaginationChange}
        />
      </Container>

      <Switch>
        <IfUserCanRoute
          permission="customFields.destroy"
          exact
          path={`${match.url}/:id/delete`}
          component={CustomFieldsDeleteRoute}
        />
      </Switch>
    </div>
  )
}

export default memo(CustomFieldsIndex)
