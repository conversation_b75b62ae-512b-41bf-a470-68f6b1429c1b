import React, { useCallback, useState, useEffect } from 'react'
import Helmet from 'react-helmet'
import { <PERSON><PERSON>, Col, Container, Row } from 'reactstrap'
import { connect } from 'react-redux'
import ServiceSelect from '../../../common/connected/ServicesSelect/ServicesSelectContainer'
import InputGroup, { InputGroupWrapper } from '../../../common/unconnected/InputGroup'
import { getUser } from '../../../../modules/auth/selectors'
import config from '../../../../../../config'

const getNumber = (service) => service.data.myId.replace('@c.us', '')
const getApiUrl = () => config('apiUrl').replace('/v1', '')

const generateCode = (service, user, tag) => {
  let script = `<script src="https://plugin.mandeumzap.com.br/v1/mandeumzap.js"></script>
<script>
  mandeUmZapChat.whatsapp.number = '${getNumber(service)}';
  mandeUmZapChat.api.serviceId = '${service.id}';
  mandeUmZapChat.api.accountId = '${user.accountId}';
  `
  script += tag ? `mandeUmZapChat.api.tag = '${tag.id}';` : ''
  script += `mandeUmZapChat.templates.url.urlApiMandeUmZap = '${getApiUrl()}/';
</script>`
  return script
}

const GenerateIntegration = ({ user }) => {
  const [state, setState] = useState({
    service: null,
    tag: null,
    code: '',
  })

  const serviceChangeHandler = useCallback((service) => {
    setState((prevState) => ({
      ...prevState,
      service,
    }))
  }, [])

  const generate = useCallback(() => {
    const code = state.service.data.myId
      ? generateCode(state.service, user, state.tag)
      : 'É necessário conectar a conexão antes de gerar o código.'

    setState((prevState) => ({
      ...prevState,
      code,
    }))
  }, [state.service, state.tag])

  return (
    <>
      <Helmet title="Gerar script da integração" />

      <Container className="mt-5 mb-4">
        <Row className="justify-content-center align-middle">
          <Col md={5} className="mt-md-7">
            <InputGroupWrapper
              id="integration"
              label="Tipo"
              render={(input) => (
                <ServiceSelect
                  stateId="generateIntegrationFormService"
                  id={input.id}
                  value={state.service}
                  onChange={serviceChangeHandler}
                />
              )}
            />
            <InputGroupWrapper render={(input) => <InputGroup id="url" label="Url" />} />
            <InputGroupWrapper render={(input) => <InputGroup id="icon" label="Ícone" />} />
            <InputGroupWrapper render={(input) => <InputGroup id="text" label="Texto" />} />
            <Button color="primary" type="button" onClick={generate} disabled={!state.service} block>
              Gerar
            </Button>

            <hr />

            <InputGroup
              type="textarea"
              label="Código"
              noLabel
              value={state.code}
              disabled
              className="mt-3"
              style={{
                height: 280,
              }}
            />
          </Col>
        </Row>
      </Container>
    </>
  )
}

const mapStateToProps = (state) => ({
  user: getUser(state),
})
export default connect(mapStateToProps)(GenerateIntegration)
