import React from 'react'

import { Col, Input, Row, FormGroup, Label } from 'reactstrap'

import { useTranslation } from 'react-i18next'
import { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import { IconTimeTable } from '../../../../common/unconnected/IconDigisac'
import { TextColor } from '../../../styles/colors'
import { GroupInputFilter } from '../../../styles/table'
import { GroupInput } from '../../../styles/common'
import Select from '../../../../common/unconnected/Select'
import ServicesSelect from '../../../../common/connected/ServicesSelect'

function Filters({ filters, handleFilterChange, ...props }) {
  const { t } = useTranslation(['whatsappBusinessTemplatesPage', 'common'])
  const { categoryOptions } = props

  return (
    <div>
      <Row>
        <Col md="4">
          <Label htmlFor="tag">{t('INPUT_SEARCH_PLACEHOLDER')}</Label>
          <GroupInputFilter>
            <Input
              label={t('INPUT_SEARCH_PLACEHOLDER')}
              type="text"
              value={filters.name}
              onChange={(e) => handleFilterChange('name', e.target.value)}
              placeholder={t('INPUT_SEARCH_PLACEHOLDER')}
              data-testid="templates-filters-input-search"
              style={{ paddingLeft: '1.5rem' }}
            />
          </GroupInputFilter>
        </Col>
        <Col md="4">
          <FormGroup>
            <InputGroupWrapper
              id="archivedAt"
              label="Status"
              render={({ id }) => (
                <GroupInput>
                  <Select
                    id={id}
                    value={filters.archivedAt}
                    onChange={(e) => handleFilterChange('archivedAt', e)}
                    options={[
                      {
                        value: 'all',
                        label: t('common:BUTTON_TEXT_LABEL_FILTERS_ALL'),
                      },
                      {
                        value: 'unarchived',
                        label: t('common:BUTTON_TEXT_LABEL_FILTERS_UNARCHIVE'),
                      },
                      {
                        value: 'archived',
                        label: t('common:BUTTON_TEXT_LABEL_FILTERS_TO_FILE'),
                      },
                    ]}
                    instanceId={id}
                    getOptionValue={(o) => o.value}
                    getOptionLabel={(o) => o.label}
                    searchPromptText={t('common:SELECT_SEARCH_PROMPT_TEXT')}
                    loadingPlaceholder={t('common:TABLE_LOADING')}
                    placeholder={t('common:SELECT_LOADING_PLACEHOLDER')}
                    noResultsText={t('common:NO_RESULTS_FOUND')}
                    clearValueText={t('common:MESSAGE_CLEAR')}
                    clearAllText={t('common:MESSAGE_CLEAR_ALL')}
                  />
                </GroupInput>
              )}
            />
          </FormGroup>
        </Col>
        <Col md="4">
          <FormGroup>
            <InputGroupWrapper
              id="category"
              label={t('TABLE_COLUMN_CATEGORY')}
              render={({ id }) => (
                <GroupInput>
                  <Select
                    id={id}
                    value={filters.category}
                    onChange={(e) => handleFilterChange('category', e)}
                    options={categoryOptions.filter((c) =>
                      ['UTILITY', 'MARKETING', 'AUTHENTICATION'].includes(c.value),
                    )}
                    instanceId={id}
                    getOptionValue={(o) => o.value}
                    getOptionLabel={(o) => o.label}
                    searchPromptText={t('common:SELECT_SEARCH_PROMPT_TEXT')}
                    loadingPlaceholder={t('common:TABLE_LOADING')}
                    placeholder={t('common:SELECT_LOADING_PLACEHOLDER')}
                    noResultsText={t('common:NO_RESULTS_FOUND')}
                    clearValueText={t('common:MESSAGE_CLEAR')}
                    clearAllText={t('common:MESSAGE_CLEAR_ALL')}
                  />
                </GroupInput>
              )}
            />
          </FormGroup>
        </Col>
      </Row>
      <Row>
        <Col md="4">
          <FormGroup>
            <InputGroupWrapper
              id="service"
              label={t('TABLE_COLUMN_SERVICE')}
              render={(input) => (
                <GroupInput>
                  <ServicesSelect
                    className="filter-connection-contact"
                    stateId="filterServices"
                    id={input.id}
                    value={filters.service}
                    onChange={(value) => handleFilterChange('service', value)}
                    hideArchived
                    extraQuery={{
                      where: {
                        type: 'whatsapp-business',
                      },
                    }}
                  />
                </GroupInput>
              )}
            />
          </FormGroup>
        </Col>

        <Col md="4">
          <FormGroup>
            <InputGroupWrapper
              id="quality"
              label={t('TABLE_COLUMN_TEMPLATE_HEALTH')}
              render={({ id }) => (
                <GroupInput>
                  <Select
                    id={id}
                    value={filters.quality}
                    onChange={(e) => handleFilterChange('quality', e)}
                    options={[
                      {
                        value: 'HIGH',
                        label: t('TEMPLATE_QUALITY_HIGH'),
                      },
                      {
                        value: 'MEDIUM',
                        label: t('TEMPLATE_QUALITY_MEDIUM'),
                      },
                      {
                        value: 'LOW',
                        label: t('TEMPLATE_QUALITY_LOW'),
                      },
                    ]}
                    instanceId={id}
                    getOptionValue={(o) => o.value}
                    getOptionLabel={(o) => o.label}
                    searchPromptText={t('common:SELECT_SEARCH_PROMPT_TEXT')}
                    loadingPlaceholder={t('common:TABLE_LOADING')}
                    placeholder={t('common:SELECT_LOADING_PLACEHOLDER')}
                    noResultsText={t('common:NO_RESULTS_FOUND')}
                    clearValueText={t('common:MESSAGE_CLEAR')}
                    clearAllText={t('common:MESSAGE_CLEAR_ALL')}
                  />
                </GroupInput>
              )}
            />
          </FormGroup>
        </Col>
      </Row>
    </div>
  )
}

export default Filters
