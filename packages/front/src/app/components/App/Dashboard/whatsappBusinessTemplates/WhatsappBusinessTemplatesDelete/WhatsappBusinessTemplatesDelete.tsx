import React from 'react'
import Helmet from 'react-helmet'
import <PERSON><PERSON><PERSON><PERSON> from 'react-bootstrap-sweetalert'
import { withRouter } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import SweetModal from '../../../../common/unconnected/SweetModal'
import {
  useDeleteWhatsappBusinessTemplate,
  useFetchOneWhatsappBusinessTemplate,
} from '../../../../../resources/whatsappBusinessTemplate/requests'
import useDeleteController from '../../../../../hooks/crud/useDeleteController'

const WhatsappBusinessTemplateDelete = () => {
  const { t } = useTranslation(['whatsappBusinessTemplatesPage', 'common'])

  const {
    isModalOpen,
    exit,
    error,
    isAlertShowing,
    closeAlert,
    isLoading,
    model: template,
    submit,
  } = useDeleteController({
    exitToPath: '/whatsapp-business-templates',
    useFetchOne: useFetchOneWhatsappBusinessTemplate,
    useDeleteOne: useDeleteWhatsappBusinessTemplate,
  })

  if (!template || isLoading) return null

  return (
    <div data-testid="modal-delete-template">
      <Helmet title={`${t('TITLE_TEMPLATES')} - ${t('common:MODAL_DELETE_BUTTON_CONFIRM')} ${template.name}?`} />

      <SweetModal
        type="warning"
        title={`${t('common:MODAL_DELETE_BUTTON_CONFIRM')} ${t('TITLE_TEMPLATES')}`}
        confirmBtnText={t('common:MODAL_DELETE_BUTTON_CONFIRM')}
        cancelBtnText={t('common:FORM_ACTION_CANCEL')}
        onConfirm={submit}
        show={isModalOpen}
        onCancel={exit}
      >
        {t('whatsappBusinessTemplatesPage:MESSAGE_MODAL_DELETE')}
        <a
          href="https://www.facebook.com/business/help/2047376461998278?id=2129163877102343"
          target="_blank"
          rel="noreferrer"
        >
          {t('whatsappBusinessTemplatesPage:LINK_KNOW_MORE')}
        </a>
      </SweetModal>
      {isAlertShowing && error && (
        <SweetAlert error title={t('ERROR_MESSAGE_MODAL')} onConfirm={closeAlert}>
          {error?.response?.data?.message}
        </SweetAlert>
      )}
    </div>
  )
}

export default withRouter(WhatsappBusinessTemplateDelete)
