import React, { useCallback, useEffect, useRef, useState } from 'react'
import Helmet from 'react-helmet'
import get from 'lodash/get'
import pickBy from 'lodash/pickBy'
import uniq from 'lodash/uniq'
import identity from 'lodash/identity'
import SweetAlert from 'react-bootstrap-sweetalert'
import { withRouter } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { Button, Form, FormGroup, ModalBody, ModalHeader, Row, Col, Card, CardHeader, CardBody } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { subDays } from 'date-fns'
import { required } from '../../../../../utils/validator/validators'
import InputGroup, { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import Switch from '../../../../common/unconnected/Switch'
import Select from '../../../../common/unconnected/Select'
import * as S from '../../../../common/unconnected/HsmModal/styles'
import ServiceSelect from '../../../../common/connected/ServicesSelect'
import useFormController from '../../../../../hooks/crud/useFormController'
import {
  useCreateWhatsappBusinessTemplate,
  useFetchOneWhatsappBusinessTemplate,
  useUpdateWhatsappBusinessTemplate,
} from '../../../../../resources/whatsappBusinessTemplate/requests'
import whatsappBusinessTemplatesApi from '../../../../../resources/whatsappBusinessTemplate/api'
import { fetchUser } from '../../../../../modules/auth/actions'
import { ModalDigisac, ModalFooter, GroupInput } from '../../../styles/common'
import ButtonClose from '../../../../common/unconnected/ButtonClose'
import { IconConnection } from '../../../../common/unconnected/IconDigisac'
import { PrimaryColor } from '../../../styles/colors'
import { Label } from '../../../../common/unconnected/InputGroup/styles'
import UncontrolledTooltip from '../../../../common/unconnected/UncontrolledTooltip'
import templateImage from '../../../../../assets/logos/template-image.svg'
import Icon from '../../../../common/unconnected/Icon'
import MultipleInput from '../../../../common/unconnected/MultipleInput'
import wait from '../../../../../utils/wait'
import { Media } from '../../../../common/unconnected/HsmModal/styles'
import { useRequest } from '../../../../../hooks/useRequest'

// utils
import {
  getAutheticationText,
  getButtonsText,
  getExpirationWarningText,
} from '../../../../../utils/whatsappTemplateLanguage'
import readFileAsDataURL from '../../../../../utils/readFileAsDataURL'
import toast from '../../../../../utils/toast'

// components
import CharacterLimit from './CharacterLimit'
import TemplateActionButtons from './TemplateActionButtons'
import TemplateActionButtonsOTP from './TemplateActionButtonOTP'
import TemplateBody from './TemplateBody'
import TemplateOTPFooter from './TemplateOTPFooter'

export const getOptions = (t) => {
  const categoryOptions = [
    {
      value: 'UTILITY',
      label: t('MESSAGE_CATEGORY_UTILITY'),
    },
    {
      value: 'MARKETING',
      label: t('MESSAGE_CATEGORY_MARKETING'),
    },
    {
      value: 'AUTHENTICATION',
      label: t('MESSAGE_CATEGORY_AUTHENTICATION'),
    },
    // deprecated - 01/04/2023
    {
      value: 'TRANSACTIONAL',
      label: t('MESSAGE_CATEGORY_TRANSACTIONAL'),
    },
    {
      value: 'OTP',
      label: t('MESSAGE_CATEGORY_ONE_TIME_PASSWORD'),
    },
    // deprecated
    {
      value: 'AUTO_REPLY',
      label: t('MESSAGE_CATEGORY_OPTIONS_AUTO_REPLY'),
    },
    {
      value: 'ACCOUNT_UPDATE',
      label: t('MESSAGE_CATEGORY_OPTIONS_ACCOUNT_UPDATE'),
    },
    {
      value: 'PAYMENT_UPDATE',
      label: t('MESSAGE_CATEGORY_OPTIONS_PAYMENT_UPDATE'),
    },
    {
      value: 'PERSONAL_FINANCE_UPDATE',
      label: t('MESSAGE_CATEGORY_OPTIONS_PERSONAL_FINANCE_UPDATE'),
    },
    {
      value: 'SHIPPING_UPDATE',
      label: t('MESSAGE_CATEGORY_OPTIONS_SHIPPING_UPDATE'),
    },
    {
      value: 'RESERVATION_UPDATE',
      label: t('MESSAGE_CATEGORY_OPTIONS_RESERVATION_UPDATE'),
    },
    {
      value: 'ISSUE_RESOLUTION',
      label: t('MESSAGE_CATEGORY_OPTIONS_ISSUE_RESOLUTION'),
    },
    {
      value: 'APPOINTMENT_UPDATE',
      label: t('MESSAGE_CATEGORY_OPTIONS_APPOINTMENT_UPDATE'),
    },
    {
      value: 'TRANSPORTATION_UPDATE',
      label: t('MESSAGE_CATEGORY_OPTIONS_TRANSPORTATION_UPDATE'),
    },
    {
      value: 'TICKET_UPDATE',
      label: t('MESSAGE_CATEGORY_OPTIONS_TICKET_UPDATE'),
    },
    {
      value: 'ALERT_UPDATE',
      label: t('MESSAGE_CATEGORY_OPTIONS_ALERT_UPDATE'),
    },
  ]

  const languageOptions = [
    {
      value: 'pt_BR',
      label: t('hsmPage:LABEL_LANGUAGE_pt_BR'),
    },
    {
      value: 'en',
      label: t('hsmPage:LABEL_LANGUAGE_en'),
    },
    {
      value: 'es',
      label: t('hsmPage:LABEL_LANGUAGE_es'),
    },
  ]

  const messageTypeOptions = [
    {
      value: 'text_only',
      label: t('CREATE_WHATSAPP_BUSINESS_MESSAGE_TYPE_TEXT_ONLY'),
    },
    {
      value: 'interactive',
      label: t('CREATE_WHATSAPP_BUSINESS_MESSAGE_TYPE_MEDIA_INTERACTIVE'),
    },
  ]

  return [categoryOptions, languageOptions, messageTypeOptions]
}

const getErrorMessage = (error, t) => {
  if (error?.response?.status === 402) {
    return t('ERROR_TO_SAVE_DATA')
  }
}

const normalizePlaceholders = (text: string): string => {
  let counter = 1
  return text.replace(/\{\{\d+\}\}/g, () => `{{${counter++}}}`)
}

const useController = ({ match, history }) => {
  const { t } = useTranslation(['whatsappBusinessTemplatesPage', 'common', 'hsmPage'])

  const initialModel = {
    name: '',
    service: '',
    category: '',
    namespace: '',
    language: '',
    components: [{ text: '', type: 'BODY', params: [] }],
    rejectedReason: '',
    status: '',
    messageType: {
      value: 'text_only',
      label: t('CREATE_WHATSAPP_BUSINESS_MESSAGE_TYPE_TEXT_ONLY'),
    },
    isFooterActive: null,
    isButtonsActive: null,
    fileExample: null,
  }

  const { id } = match.params
  const dispatch = useDispatch()
  const onExit = useCallback(() => {
    dispatch(fetchUser())
    setTimeout(() => history.push('/whatsapp-business-templates', { refresh: false }), 300)
  }, [history])

  const requiredValidation = [required, t('MESSAGE_VALIDATION_REQUIRED_FIELD')]

  const validationRules = {
    internalName: [requiredValidation],
    name: [
      requiredValidation,
      [
        (value) => /^[A-Za-z0-9_]+$/.test(value.inputData.name),
        t('MESSAGE_VALIDATION_REQUIRED_ALPHANUMERIC_UNDERSCORE'),
      ],
    ],
    service: [requiredValidation],
    category: [requiredValidation],
    language: [requiredValidation],
    messageType: [requiredValidation],
    bodyTextOnlyInput: [
      [
        (value) =>
          value.inputData.messageType.value === 'text_only' ? value.inputData.components[0].text.length <= 1024 : true,
        t('MESSAGE_VALIDATION_REQUIRED_LIMIT_CARACTERES'),
      ],
      [
        (value) =>
          value.inputData.messageType.value === 'text_only' ? value.inputData.components[0].text.length > 0 : true,
        t('MESSAGE_VALIDATION_REQUIRED_FIELD'),
      ],
      [
        (value) =>
          value.inputData.status === '' && value.inputData.messageType.value === 'text_only'
            ? !value.inputData.components[0].text.startsWith('{{') && !value.inputData.components[0].text.endsWith('}}')
            : true,
        t('MESSAGE_VALIDATION_REQUIRED_PARAMETER_RULE'),
      ],
      [
        (value) =>
          value.inputData.messageType.value === 'text_only'
            ? !value.inputData.components[0].text.includes('    ')
            : true,
        t('MESSAGE_VALIDATION_REQUIRED_CAN_NOT_HAVE_FOUR_CONSECUTIVE_SPACES'),
      ],
      [
        (value) =>
          value.inputData.messageType.value === 'text_only'
            ? !value.inputData.components[0].text.replaceAll(' ', '').includes('}}{{')
            : true,
        t('MESSAGE_VALIDATION_REQUIRED_SEQUENTIAL_PARAMETERS'),
      ],
      [
        (value) => {
          if (value.inputData.messageType.value !== 'text_only') return true
          const component = value.inputData.components[0]

          const textParamsLength = component.text?.match(/(\{{\d{0,}\}\})/g)?.length || 0

          const textWordsAmount = component.text.split(' ').filter(Boolean).length || 0

          return textWordsAmount - textParamsLength >= textParamsLength * 2 + 1
        },
        t('MESSAGE_VALIDATION_REQUIRED_DOUBLE_PARAMETERS_IN_WORDS'),
      ],
    ],
    bodyTextOnlyParams: [
      [
        (value) => {
          if (value.inputData.messageType.value !== 'text_only') return true
          const component = value.inputData.components[0]

          return (
            ((component && uniq(component.text?.match(/(\{{\d{0,}\}\})/g))?.length) || 0) ===
            (component.params?.length || 0)
          )
        },
        t('MESSAGE_VALIDATION_REQUIRED_NUMBER_OF_PARAMETERS_TEXT'),
      ],
    ],
    headerInteractiveInput: [
      [
        (value) => {
          const components = value.inputData.components
          const header = components.find((c) => c.type === 'HEADER')

          return value.inputData.messageType.value === 'interactive' && header?.format === 'TEXT'
            ? (header.params?.length || 0) <= 1
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_MORE_THAN_A_PARAMETER'),
      ],
      [
        (value) => {
          const components = value.inputData.components
          const header = components.find((c) => c.type === 'HEADER')

          return value.inputData.messageType.value === 'interactive' && header?.format === 'TEXT'
            ? header.text.length <= 60
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_CAN_NOT_BE_MORE_THAN_SIXTY_CHARACTERS'),
      ],
    ],
    headerParams: [
      [
        (value) => {
          const components = value.inputData.components
          const header = components.find((c) => c.type === 'HEADER')

          return value.inputData.messageType.value === 'interactive' && header?.format === 'TEXT'
            ? ((header && header.text.match(/(\{{\d{0,}\}\})/g)?.length) || 0) === (header.params?.length || 0)
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_NUMBER_OF_PARAMETERS_TEXT'),
      ],
    ],
    bodyInteractiveInput: [
      [
        (value) => {
          const components = value.inputData.components
          const body = components.find((c) => c.type === 'BODY')

          return value.inputData.messageType.value === 'interactive' ? body.text.length <= 1024 : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_LIMIT_CARACTERES'),
      ],
      [
        (value) => {
          const components = value.inputData.components
          const body = components.find((c) => c.type === 'BODY')

          return value.inputData.messageType.value === 'interactive' ? body.text.length > 0 : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_FIELD'),
      ],
      [
        (value) => {
          const components = value.inputData.components
          const body = components.find((c) => c.type === 'BODY')

          return value.inputData.status === '' &&
            value.inputData.messageType.value === 'interactive' &&
            value.inputData.category.value !== 'AUTHENTICATION'
            ? !body.text.startsWith('{{') && !body.text.endsWith('}}')
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_PARAMETER_RULE'),
      ],
      [
        (value) => {
          const components = value.inputData.components
          const body = components.find((c) => c.type === 'BODY')

          return value.inputData.messageType.value === 'interactive' ? !body.text.includes('    ') : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_CAN_NOT_HAVE_FOUR_CONSECUTIVE_SPACES'),
      ],
      [
        (value) => {
          const components = value.inputData.components
          const body = components.find((c) => c.type === 'BODY')

          return value.inputData.messageType.value === 'interactive'
            ? !body.text.replaceAll(' ', '').includes('}}{{')
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_SEQUENTIAL_PARAMETERS'),
      ],
      [
        (value) => {
          if (value.inputData.messageType.value !== 'interactive') return true
          const components = value.inputData.components

          const body = components.find((c) => c.type === 'BODY')

          const textParamsLength = body.text?.match(/(\{{\d{0,}\}\})/g)?.length || 0

          const textWordsAmount = body.text.split(' ').filter(Boolean).length || 0

          return textWordsAmount - textParamsLength >= textParamsLength * 2 + 1
        },
        t('MESSAGE_VALIDATION_REQUIRED_DOUBLE_PARAMETERS_IN_WORDS'),
      ],
    ],
    bodyParams: [
      [
        (value) => {
          const components = value.inputData.components
          const body = components.find((c) => c.type === 'BODY')

          return value.inputData.messageType.value === 'interactive'
            ? (uniq(body.text.match(/(\{{\d{0,}\}\})/g))?.length || 0) === (body.params?.length || 0)
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_NUMBER_OF_PARAMETERS_TEXT'),
      ],
    ],
    footerInteractiveInput: [
      [
        (value) => {
          const components = value.inputData.components
          const footer = components.find((c) => c.type === 'FOOTER')

          return value.inputData.messageType.value === 'interactive' ? (footer?.text?.length || 0) < 60 : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_CAN_NOT_BE_MORE_THAN_SIXTY_CHARACTERS'),
      ],
    ],
    code_expiration_minutes: [
      [
        (value) => {
          const components = value.inputData.components
          const footer = components.find((c) => c.type === 'FOOTER')

          if (
            value.inputData.category.value === 'AUTHENTICATION' &&
            footer?.active_code_expiration_minutes &&
            !footer.code_expiration_minutes
          ) {
            return false
          }

          return true
        },
        t('MESSAGE_VALIDATION_REQUIRED_FIELD'),
      ],
    ],
    buttonsInteractiveInput0: [
      [
        (value) => {
          if (id) return true
          const components = value.inputData.components
          const button = components.find((c) => c.type === 'BUTTONS')

          return value.inputData.messageType.value === 'interactive' &&
            button &&
            button.buttons.length >= 1 &&
            button.buttons.every((b) => b.type === 'QUICK_REPLY')
            ? (button.buttons[0]?.text?.length || 0) <= 20
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_NOT_BE_MORE_THAN_20_CHARACTERS'),
      ],
    ],
    buttonsInteractiveInput1: [
      [
        (value) => {
          if (id) return true
          const components = value.inputData.components
          const button = components.find((c) => c.type === 'BUTTONS')

          return value.inputData.messageType.value === 'interactive' &&
            button &&
            button.buttons.length >= 2 &&
            button.buttons.every((b) => b.type === 'QUICK_REPLY')
            ? (button.buttons[1]?.text?.length || 0) <= 20
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_NOT_BE_MORE_THAN_20_CHARACTERS'),
      ],
    ],
    buttonsInteractiveInput2: [
      [
        (value) => {
          if (id) return true
          const components = value.inputData.components
          const button = components.find((c) => c.type === 'BUTTONS')

          return value.inputData.messageType.value === 'interactive' &&
            button &&
            button.buttons.length >= 3 &&
            button.buttons.every((b) => b.type === 'QUICK_REPLY')
            ? (button.buttons[2]?.text?.length || 0) <= 20
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_NOT_BE_MORE_THAN_20_CHARACTERS'),
      ],
    ],
    buttonsTextInteractiveInput0: [
      [
        (value) => {
          const components = value.inputData.components
          const button = components.find((c) => c.type === 'BUTTONS')

          return value.inputData.messageType.value === 'interactive' &&
            button &&
            button.buttons.length >= 1 &&
            button.buttons.every((b) => b.type !== 'QUICK_REPLY')
            ? (button.buttons[0]?.text?.length || 0) <= 20
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_NOT_BE_MORE_THAN_20_CHARACTERS'),
      ],
    ],
    buttonsTextInteractiveInput1: [
      [
        (value) => {
          const components = value.inputData.components
          const button = components.find((c) => c.type === 'BUTTONS')

          return value.inputData.messageType.value === 'interactive' &&
            button &&
            button.buttons.length >= 2 &&
            button.buttons.every((b) => b.type !== 'QUICK_REPLY')
            ? (button.buttons[1]?.text?.length || 0) <= 20
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_NOT_BE_MORE_THAN_20_CHARACTERS'),
      ],
    ],
    buttonsUrlInteractiveInput: [
      [
        (value) => {
          const components = value.inputData.components
          const button = components.find((c) => c.type === 'BUTTONS')

          const urlIndex = button?.buttons?.findIndex((b) => b.type === 'URL')

          return value.inputData.messageType.value === 'interactive' &&
            button &&
            button.buttons.length >= 1 &&
            urlIndex !== -1 &&
            button.buttons[urlIndex]?.url?.length > 0 &&
            (button.buttons[urlIndex]?.url?.match(/(\{{\d{0,}\}\})/g)?.length || 0) > 0
            ? button.buttons[urlIndex]?.url.endsWith('}}')
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_FINAL_PARAMETER_URL'),
      ],
      [
        (value) => {
          const components = value.inputData.components
          const button = components.find((c) => c.type === 'BUTTONS')

          const urlIndex = button?.buttons?.findIndex((b) => b.type === 'URL')

          return value.inputData.messageType.value === 'interactive' &&
            button &&
            button.buttons.length >= 1 &&
            urlIndex !== -1 &&
            button.buttons[urlIndex]?.url?.length > 0
            ? (button.buttons[urlIndex]?.url?.match(/(\{{\d{0,}\}\})/g)?.length || 0) <= 1
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_URL_DOES_NOT_HAVE_MORE_THAN_ONE_PARAMETER'),
      ],
    ],
    buttonUrlParams: [
      [
        (value) => {
          const components = value.inputData.components
          const button = components.find((c) => c.type === 'BUTTONS')

          const urlIndex = button?.buttons?.findIndex((b) => b.type === 'URL')

          return value.inputData.messageType.value === 'interactive' &&
            button &&
            button.buttons.length >= 1 &&
            urlIndex !== -1 &&
            button.buttons[urlIndex]?.url?.length > 0
            ? (button.buttons[urlIndex]?.url?.match(/(\{{\d{0,}\}\})/g)?.length || 0) ===
                (button.buttons[urlIndex]?.params?.length || 0)
            : true
        },
        t('MESSAGE_VALIDATION_REQUIRED_NUMBER_OF_PARAMETERS_TEXT'),
      ],
    ],
  }

  const [categoryOptions, languageOptions, messageTypeOptions] = getOptions(t)

  const formatToApi = (template) => ({
    ...template,
    components: template.components.filter(
      (c) => c.type !== 'HEADER' || (c.format && c.format === 'TEXT' && !!c.text) || (c.format && c.format !== 'TEXT'),
    ),
    messageType: get(template, 'messageType.value'),
    language: get(template, 'language.value') || get(template, 'language'),
    category: get(template, 'category.value'),
    serviceId: get(template, 'service.id'),
  })

  const formatFromApi = (template) => {
    const messageType = get(template, 'messageType')
    const language = get(template, 'language')
    const category = get(template, 'category')

    return {
      ...template,
      ...pickBy(
        {
          messageType: messageTypeOptions.find((i) => i.value === messageType),
          language: languageOptions.find((i) => i.value === language),
          category: categoryOptions.find((i) => i.value === category),
        },
        identity,
      ),
      isFooterActive:
        template.isFooterActive === undefined
          ? template.components.some((c) => c.type === 'FOOTER')
          : !!template?.isFooterActive,
      isButtonsActive:
        template.isButtonsActive === undefined
          ? template.components.some((c) => c.type === 'BUTTONS')
          : !!template?.isButtonsActive,
    }
  }

  const buildQuery = () => ({
    include: [
      {
        model: 'fileExample',
        order: [['createdAt', 'DESC']],
        limit: 1,
      },
      { model: 'service', required: true },
    ],
  })

  return useFormController({
    id,
    exitToPath: '/whatsapp-business-templates',
    initialModel,
    validationRules,
    formatToApi,
    formatFromApi,
    buildQuery,
    onExit,
    useCreateOne: useCreateWhatsappBusinessTemplate,
    useUpdateOne: useUpdateWhatsappBusinessTemplate,
    useFetchOne: useFetchOneWhatsappBusinessTemplate,
  })
}

const WhatsappBusinessTemplatesForm = ({ match, history }) => {
  const { t } = useTranslation(['whatsappBusinessTemplatesPage', 'common', 'hsmPage'])

  const [categoryOptions, languageOptions, messageTypeOptions] = getOptions(t)

  const [onFocus, setOnFocus] = useState(false)

  const addParameterBodyTextOnlyRef = useRef()
  const addParameterHeaderInteractiveRef = useRef()
  const addParameterBodyInteractiveRef = useRef()
  const addParameterButtonsInteractiveRef = useRef()

  const {
    submit,
    exit,
    isModalOpen,
    isEditing,
    isAlertShowing,
    closeAlert,
    isLoading,
    error,
    model,
    bindInput,
    setModel,
    setProperty,
    validation,
  } = useController({ match, history })

  const [, countTemplates] = useRequest(whatsappBusinessTemplatesApi.count)

  const footer = model.components.find((c) => c.type === 'FOOTER')
  const header = model.components.find((c) => c.type === 'HEADER')
  const button = model.components.find((c) => c.type === 'BUTTONS')
  const buttons = button?.buttons
  const buttonsIndex = model.components.findIndex((c) => c.type === 'BUTTONS')
  const buttonsPosition = buttonsIndex == -1 ? model.components.length : buttonsIndex

  const buttonType = buttons && buttons.reduce((_, currButton) => currButton.type, '')
  const buttonTypeOtp = buttons && buttons.reduce((_, currButton) => currButton.otp_type, '')

  const buttonConfigs = {
    QUICK_REPLY: [
      {
        text: '',
        type: 'QUICK_REPLY',
      },
    ],
    CALL_TO_ACTION: [
      {
        type: 'CALL_TO_ACTION',
        text: '',
        url: '',
      },
    ],
    OTP_AUTOFILL: [
      {
        text: t('CREATE_WHATSAPP_BUSINESS_LABEL_COPY_CODE'),
        type: 'OTP',
        otp_type: 'ONE_TAP',
        autofill_text: 'Autofill',
        package_name: 'com.example.myapplication',
        signature_hash: 'K8a%2FAINcGX7',
      },
    ],
    OTP_COPY_CODE: [
      {
        text: t('CREATE_WHATSAPP_BUSINESS_LABEL_COPY_CODE'),
        type: 'OTP',
        otp_type: 'COPY_CODE',
      },
    ],
  }

  const handleNameExists = async () => {
    if (!model.service?.id || !model.name) return
    return !!(
      await countTemplates({
        query: JSON.stringify({
          where: {
            ...(model.id && {
              id: {
                $ne: model.id,
              },
            }),
            accountId: model.accountId,
            name: model.name,
            archivedAt: {
              $or: [null, { $gte: subDays(new Date(), 30) }],
            },
          },
        }),
      })
    )?.count
  }

  const findParamsInComponent = (type, format) => {
    const component = model.components.find((item) => item.type === type && item.format === format)
    if (!component) return []
    return component.text.match(/{{(100|[0-9][0-9]?)}}/g) || []
  }

  const validateDuplicateParams = () => {
    const bodyParams = findParamsInComponent('BODY', undefined)
    const headerParams = findParamsInComponent('HEADER', 'TEXT')

    const isDuplicateBodyParams = bodyParams.length > uniq(bodyParams).length
    const isDuplicateHeaderParams = headerParams.length > uniq(headerParams).length

    return isDuplicateBodyParams || isDuplicateHeaderParams
  }

  const handleSubmit = async () => {
    await validation.validateAll().then(async (valid) => {
      const wrongParams = validateDuplicateParams()
      if (wrongParams) return toast.warn(t('TOAST_WARN_DUPLICATE_PARAMS'))

      if (valid && (!isEditing || (isEditing && model.status == ''))) {
        const exists = await handleNameExists(model.name)

        if (exists) {
          return toast.warn(t('TOAST_WARN_NAME_ALREADY_EXISTS'))
        }
      }
      await submit()
    })
  }

  const title = `${isEditing ? t('common:LABEL_EDITING') : t('common:LABEL_CREATING')} ${t('TITLE_TEMPLATE')}`

  const handleAddParameter = async (type, id, buttonIndex = null) => {
    document.getElementById(id).focus()

    await wait(200)

    const input = document.getElementById(id)

    const text =
      id === 'buttonsUrlInteractiveInput'
        ? buttons[buttonIndex].url
        : model.components.find((c) => c.type === type)?.text || ''

    const matches = uniq(text.match(/{{(100|[0-9][0-9]?)}}/g) || [])

    const newText = `${text} {{${matches?.length + 1 || 1}}}`

    const componentIndex = model.components.findIndex((c) => c.type === type)

    const newComponents = model.components

    const position = componentIndex === -1 ? model.components.length : componentIndex

    if (id === 'buttonsUrlInteractiveInput') {
      const newButtons = newComponents[position].buttons

      newButtons[buttonIndex] = {
        params: newButtons[buttonIndex].params,
        type: 'URL',
        text: newButtons[buttonIndex].text,
        url: newText,
      }

      newComponents[position] = {
        buttons: newButtons,
        type,
      }
    } else {
      newComponents[position] = {
        params: newComponents[position]?.params,
        text: newText,
        type,
        ...(type === 'HEADER' && { format: 'TEXT' }),
      }
    }

    setProperty('components', newComponents)

    input.focus()
  }

  const handleChangeHeaderText = (e) => {
    const index = model.components.findIndex((c) => c.type === 'HEADER')

    const newComponents = model.components

    const position = index === -1 ? model.components.length : index

    newComponents[position] = {
      params: newComponents[position].params,
      format: 'TEXT',
      text: normalizePlaceholders(e.target.value),
      type: 'HEADER',
    }

    return setProperty('components', newComponents)
  }

  const handleChangeBody = (e) => {
    const index = model.components.findIndex((c) => c.type === 'BODY')

    const newComponents = model.components

    const position = index == -1 ? model.components.length : index

    newComponents[position] = {
      params: newComponents[position].params,
      text: normalizePlaceholders(e.target.value),
      type: 'BODY',
    }

    return setProperty('components', newComponents)
  }

  const handleChangeFooter = (e) => {
    const index = model.components.findIndex((c) => c.type === 'FOOTER')
    const newComponents = model.components

    const position = index == -1 ? model.components.length : index

    newComponents[position] = {
      text: e.target.value,
      type: 'FOOTER',
    }

    return setProperty('components', newComponents)
  }

  const handleChangeButtonType = (type) => {
    const newComponents = model.components

    newComponents[buttonsPosition] = {
      buttons: buttonConfigs[type],
      type: 'BUTTONS',
    }

    return setProperty('components', newComponents)
  }

  const handleAddButton = () => {
    const newComponents = model.components

    if (buttonType === 'QUICK_REPLY') {
      newComponents[buttonsPosition] = {
        buttons: [
          ...(newComponents[buttonsPosition]?.buttons || []),
          {
            text: '',
            type: 'QUICK_REPLY',
          },
        ],
        type: 'BUTTONS',
      }
    }
    if (buttonType === 'CALL_TO_ACTION') {
      if (!buttons?.length || buttons[0].type === 'URL') {
        newComponents[buttonsPosition] = {
          buttons: [
            ...(newComponents[buttonsPosition]?.buttons || []),
            {
              type: 'PHONE_NUMBER',
              text: '',
              phone_number: '',
            },
          ],
          type: 'BUTTONS',
        }
      }
      if (buttons[0].type === 'PHONE_NUMBER') {
        newComponents[buttonsPosition] = {
          buttons: [
            ...(newComponents[buttonsPosition]?.buttons || []),
            {
              type: 'URL',
              text: '',
              url: '',
              // example: ['https://www.website.com/dynamic-url-example'],
            },
          ],
          type: 'BUTTONS',
        }
      }
    }
    return setProperty('components', newComponents)
  }

  const handleRemoveButton = (buttonIndex) => {
    if (buttons.length <= 1) {
      return
    }
    const newComponents = model.components

    newComponents[buttonsPosition].buttons = buttons.filter((b, index) => index !== buttonIndex)

    return setProperty('components', newComponents)
  }

  const handleChangeButton = (e, buttonIndex, button, isButtonName = false) => {
    const value = e.target.value
    const fieldName = e.target?.name
    const newComponents = model.components

    if (buttonType === 'QUICK_REPLY') {
      newComponents[buttonsPosition].buttons[buttonIndex].text = value
    }

    if (isButtonName) {
      newComponents[buttonsPosition].buttons[buttonIndex].text = value
    } else {
      if (button?.type === 'URL') {
        newComponents[buttonsPosition].buttons[buttonIndex].url = value
      }
      if (button?.type === 'PHONE_NUMBER') {
        newComponents[buttonsPosition].buttons[buttonIndex].phone_number = value
      }
    }

    if (buttonType === 'OTP' && fieldName && value) {
      newComponents[buttonsPosition].buttons[buttonIndex][fieldName] = value
    }

    return setProperty('components', newComponents)
  }

  const handleChangeHeaderType = (type) => {
    const index = model.components.findIndex((c) => c.type === 'HEADER')

    const newComponents = model.components

    const position = index == -1 ? model.components.length : index

    if (type === 'TEXT') {
      newComponents[position] = {
        text: newComponents[position]?.text || '',
        format: type,
        type: 'HEADER',
      }
    } else {
      newComponents[position] = {
        format: type,
        // example: {
        //   header_handle: ['https://url-to-media-file/media.file'],
        // },
        type: 'HEADER',
      }
    }
    setProperty('components', newComponents)
    setProperty('fileExample', null)
  }

  const handleChangeMessageType = (e) => {
    if (e.value === 'interactive') {
      if (model.category.value === 'AUTHENTICATION') {
        setProperty('components', [
          {
            buttons: [
              {
                type: 'OTP',
                otp_type: 'ONE_TAP',
                text: 'Copy Code',
                autofill_text: 'Autofill',
                package_name: 'com.example.myapplication',
                signature_hash: 'K8a%2FAINcGX7',
              },
            ],
          },
        ])
      } else {
        setProperty('components', [
          {
            format: 'TEXT',
            text: '',
            type: 'HEADER',
            params: [],
          },
          {
            text: '',
            type: 'BODY',
            params: [],
          },
        ])
      }
    }

    if (e.value === 'text_only') {
      setProperty('components', [{ text: '', type: 'BODY', params: [] }])
    }

    setProperty('messageType', e)
  }

  const handleChangeCallToActionType = (type, buttonIndex) => {
    const newComponents = model.components
    const newButtons = buttons

    if (type === 'URL') {
      newButtons[buttonIndex] = {
        type: 'URL',
        text: '',
        url: '',
        // example: ['https://www.website.com/dynamic-url-example'],
      }
    }

    if (type === 'PHONE_NUMBER') {
      newButtons[buttonIndex] = {
        type: 'PHONE_NUMBER',
        text: '',
        phone_number: '',
      }
    }

    newComponents[buttonsPosition] = {
      buttons: newButtons,
      type: 'BUTTONS',
    }

    setProperty('components', newComponents)
  }

  /**
   * Quando ativar os botões é assicionado essa função
   *
   * @param object Evento
   */
  const handleChangeIsButtonActive = (e) => {
    if (e.target.checked) {
      const newComponents = model.components

      if (model.category.value !== 'AUTHENTICATION') {
        newComponents[buttonsPosition] = {
          buttons: buttonConfigs.QUICK_REPLY,
          type: 'BUTTONS',
        }
      }

      if (model.category.value === 'AUTHENTICATION') {
        newComponents[buttonsPosition] = {
          buttons: buttonConfigs.OTP_COPY_CODE,
          type: 'BUTTONS',
        }
      }

      setProperty('components', newComponents)
    } else {
      setProperty(
        'components',
        model.components.filter((c) => c.type !== 'BUTTONS'),
      )
    }

    setProperty('isButtonsActive', e.target.checked)
  }

  const handleChangeIsFooterActive = (e) => {
    if (!e.target.checked) {
      setProperty(
        'components',
        model.components.filter((c) => c.type !== 'FOOTER'),
      )
    }

    setProperty('isFooterActive', !!e.target.checked)
  }

  const interpolate = (text, params = []) => {
    params.forEach((param, index) => {
      text = text.replaceAll(`{{${index + 1}}}`, param)
    })
    return text
  }

  const handleChangeTextOnlyParams = (e) => {
    const matches = model.components[0]?.text.match(/(\{{\d{0,}\}\})/g)

    if (e?.length <= matches?.length) {
      const newComponents = model.components

      newComponents[0] = {
        text: newComponents[0].text,
        type: 'BODY',
        params: e,
      }
      setProperty('components', newComponents)
    }
  }

  const handleChangeHeaderParams = (e) => {
    const matches = header?.text.match(/(\{{\d{0,}\}\})/g)

    const index = model.components.findIndex((c) => c.type === 'HEADER')

    const position = index == -1 ? model.components.length : index

    if (e?.length <= matches?.length) {
      const newComponents = model.components

      newComponents[position] = {
        text: newComponents[position].text,
        format: 'TEXT',
        type: 'HEADER',
        params: e,
      }
      setProperty('components', newComponents)
    }
  }

  const handleChangeBodyParams = (e) => {
    const fieldName = e.target?.name
    const matches = model.components.find((c) => c.type === 'BODY')?.text.match(/(\{{\d{0,}\}\})/g)
    const index = model.components.findIndex((c) => c.type === 'BODY')
    const position = index == -1 ? model.components.length : index
    const newComponents = model.components

    if (e?.length <= matches?.length) {
      newComponents[position] = {
        text: newComponents[position].text,
        type: 'BODY',
        params: e,
      }
    }

    setProperty('components', newComponents)
  }

  /**
   * Ativa mensagem de segurança
   */
  const handleSecurityRecomendation = (e) => {
    const index = model.components.findIndex((c) => c.type === 'BODY')

    model.components[index] = {
      ...model.components[index],
      add_security_recommendation: e.target?.checked ? true : false,
    }

    setProperty('components', model.components)
  }

  const handleActiveTimeExpiration = (e) => {
    const languageTemplate = model.language.value
    const index = model.components.findIndex((c) => c.type === 'FOOTER')
    const position = index == -1 ? model.components.length : index
    const newComponents = model.components

    if (e.target.checked) {
      newComponents[position] = {
        type: 'FOOTER',
        active_code_expiration_minutes: true,
        text: getExpirationWarningText(languageTemplate),
      }
    } else {
      newComponents[position] = {
        type: 'FOOTER',
        active_code_expiration_minutes: false,
      }
    }

    setProperty('components', newComponents)
  }

  const handleChangeTimeExpiration = (e) => {
    const index = model.components.findIndex((c) => c.type === 'FOOTER')

    if (e.target.value.length >= 3 || e.target.value > 90) {
      return false
    }

    if (index) {
      model.components[index] = {
        ...footer,
        code_expiration_minutes: +e.target?.value,
      }

      setProperty('components', model.components)
    }
  }

  const handleChangeButtonUrlParams = (e) => {
    const matches = buttons.find((b) => b.type === 'URL')?.url.match(/(\{{\d{0,}\}\})/g)

    if (e?.length <= matches?.length) {
      const newComponents = model.components

      const buttonUrlIndex = buttons.findIndex((b) => b.type === 'URL')

      newComponents[buttonsPosition].buttons[buttonUrlIndex] = {
        text: buttons[buttonUrlIndex].text,
        url: buttons[buttonUrlIndex].url,
        params: e,
        type: 'URL',
      }
      setProperty('components', newComponents)
    }
  }

  const handleChangeHeaderFileExample = async (e) => {
    const file = e.target.files[0]

    if (!file) return

    const url = await readFileAsDataURL(file)

    setProperty('fileExample', {
      base64Url: url,
      mimetype: file.type,
      fileName: file.name,
    })
  }

  /**
   * Atualiza as propriedades para o padrão ao alterar a categoria
   */
  const handleClearFieldsOnChangeCategory = () => {
    setProperty('language', '')
    setProperty('isButtonsActive', false)
    setProperty('isFooterActive', false)

    setProperty('messageType', {
      value: 'text_only',
      label: t('CREATE_WHATSAPP_BUSINESS_MESSAGE_TYPE_TEXT_ONLY'),
    })

    setProperty('components', [
      {
        text: '',
        type: 'BODY',
        params: [],
      },
      {
        text: '',
        type: 'FOOTER',
        params: [],
      },
    ])
  }

  /**
   * Atualiza a propriedade do tipo de mensagem conforme selecionado a categoria
   *
   * @param {object} category - Objeto da categoria selecionada
   */
  const handleChangeCategory = (category) => {
    if (category && category.value === 'AUTHENTICATION') {
      setProperty('messageType', {
        value: 'interactive',
        label: t('CREATE_WHATSAPP_BUSINESS_MESSAGE_TYPE_MEDIA_INTERACTIVE'),
      })

      setProperty('isButtonsActive', true)
      handleChangeButtonType('OTP_COPY_CODE')
    }
  }

  /**
   * Atualiza o texto da propriedade body conforme o idioma selecionado
   *
   * @param {object} language - O objeto de idioma selecionado
   */
  const handleChangeLanguage = (language) => {
    const index = model.components.findIndex((c) => c.type === 'BODY')

    if (language && model.category.value === 'AUTHENTICATION') {
      model.components[index] = {
        text: getAutheticationText(language.value),
        type: 'BODY',
        params: ['VERIFICATION_CODE'],
        add_security_recommendation: false,
      }

      setProperty('components', model.components)

      if (model.isFooterActive && footer?.active_code_expiration_minutes) {
        footer.text = getExpirationWarningText(language.value)
      }

      if (buttons) {
        model.components[buttonsPosition].buttons[0].text = getButtonsText(language.value)
      }
    } else if (model.category.value === 'AUTHENTICATION' && !language) {
      model.components[index] = {
        text: '',
        type: 'BODY',
        params: [],
        add_security_recommendation: false,
      }

      setProperty('components', model.components)
    }
  }

  /**
   * Função para quando o template de autenticação vier em branco da sincronização
   *
   * @param {object} model - Objeto do componente
   *
   * @returns {string}
   */
  const bodyValue = (model) => {
    const category = model.category.value
    const language = model.language.value
    const bodyText = model.components.find((c) => c.type === 'BODY')?.text
    const bodyParams = model.components.find((c) => c.type === 'BODY')?.params

    if (category === 'AUTHENTICATION' && !bodyText && language) {
      return interpolate(getAutheticationText(language), ['VERIFICATION_CODE'])
    }

    return interpolate(bodyText, bodyParams)
  }

  useEffect(() => {
    if (error) {
      validation.validateAll()
    }
  }, [error])

  const isEditable = model.status === ''

  return (
    <>
      <Helmet title={t('TITLE_TEMPLATE')} />

      <Form onSubmit={handleSubmit} data-testid="create-template">
        <ModalDigisac isOpen={isModalOpen} toggle={exit} autoFocus={false} size="lg">
          <ModalHeader>
            {title}
            <ButtonClose onClick={exit} data-testid="templates-form-button-close" />
          </ModalHeader>
          <ModalBody>
            <Row>
              <p className="pl-3 pr-3">
                {t('CREATE_WHATSAPP_BUSINESS_INFORMATION_MESSAGES_BULK')}
                <a
                  href="https://developers.facebook.com/docs/whatsapp/message-templates/guidelines"
                  target="_blank"
                  rel="noreferrer"
                >
                  &nbsp;
                  {t('CLICK_HERE')}
                </a>
              </p>
            </Row>
            <Row>
              <Col>
                <InputGroup
                  id="internalName"
                  data-testid="templates-form-input-name_digisac"
                  label={t('CREATE_WHATSAPP_BUSINESS_LABEL_INPUT_NAME_DIGISAC')}
                  required
                  {...{ bindInput, validation }}
                />
              </Col>
            </Row>
            <Row>
              <Col>
                <InputGroup
                  id="name"
                  data-testid="templates-form-input-name"
                  label={t('CREATE_WHATSAPP_BUSINESS_LABEL_MODEL_NAME')}
                  required
                  disabled={!isEditable}
                  {...{ bindInput, validation }}
                  onChange={(e) => setProperty('name', e.target.value.toLowerCase())}
                />
              </Col>
            </Row>
            <Row>
              <Col data-testid="templates-form-input-service">
                <InputGroupWrapper
                  id="service"
                  label={t('CREATE_WHATSAPP_BUSINESS_LABEL_SERVICE')}
                  required
                  {...{
                    bindInput,
                    validation,
                    model,
                    setProperty,
                  }}
                  render={(input) => (
                    <GroupInput withIcon>
                      <ServiceSelect
                        icon={<IconConnection fill={PrimaryColor} width="25" height="25" />}
                        data-testid="templates-form-input-services"
                        disabled={!isEditable}
                        stateId="whatsappBusinessTemplatesFormServices"
                        id={input.id}
                        value={input.model[input.id]}
                        onChange={(value) => input.setProperty(input.id, value)}
                        onBlur={() => input.validation.setTouched(input.id)}
                        extraQuery={{
                          where: {
                            type: 'whatsapp-business',
                            'data.providerType': {
                              $or: ['360Dialog', 'meta', 'gupshup'],
                            },
                          },
                        }}
                        hideArchived
                      />
                    </GroupInput>
                  )}
                />
              </Col>
            </Row>
            <Row>
              <Col md="8">
                <FormGroup data-testid="templates-form-input-category">
                  <InputGroupWrapper
                    id="category"
                    label={t('CREATE_WHATSAPP_BUSINESS_INPUT_MODEL_CATEGORY')}
                    required
                    {...{
                      bindInput,
                      validation,
                      model,
                      setProperty,
                    }}
                    render={(input) => (
                      <GroupInput data-testid="templates-form-select-category">
                        <Select
                          stateId="whatsappBusinessTemplatesFormCategory"
                          id={input.id}
                          value={input.model[input.id]}
                          onChange={(value) => {
                            handleClearFieldsOnChangeCategory()

                            if (value) {
                              input.setProperty(input.id, value)
                              handleChangeCategory(value)
                            } else {
                              input.setProperty(input.id, '')
                            }
                          }}
                          disabled={!isEditable}
                          options={categoryOptions.filter((c) =>
                            ['UTILITY', 'MARKETING', 'AUTHENTICATION'].includes(c.value),
                          )}
                          instanceId={input.id}
                          getOptionValue={(o) => o.value}
                          getOptionLabel={(o) => o.label}
                          searchPromptText={t('common:SELECT_SEARCH_PROMPT_TEXT')}
                          loadingPlaceholder={t('common:TABLE_LOADING')}
                          placeholder={t('common:SELECT_LOADING_PLACEHOLDER')}
                          noResultsText={t('common:NO_RESULTS_FOUND')}
                          clearValueText={t('common:MESSAGE_CLEAR')}
                          clearAllText={t('common:MESSAGE_CLEAR_ALL')}
                        />
                      </GroupInput>
                    )}
                  />
                </FormGroup>
              </Col>
              <Col md="4" data-testid="templates-form-input-language">
                <FormGroup>
                  <InputGroupWrapper
                    id="language"
                    label={t('CREATE_WHATSAPP_BUSINESS_LABEL_MODEL_LANGUAGE')}
                    required
                    {...{
                      bindInput,
                      validation,
                      model,
                      setProperty,
                    }}
                    render={(input) => (
                      <GroupInput>
                        <Select
                          stateId="whatsappBusinessTemplatesFormLanguage"
                          id={input.id}
                          data-testid="templates-form-select-language"
                          value={input.model[input.id]}
                          onChange={(value) => {
                            input.setProperty(input.id, value)
                            handleChangeLanguage(value)
                          }}
                          disabled={!isEditable}
                          options={languageOptions}
                          instanceId={input.id}
                          getOptionValue={(o) => o.value}
                          getOptionLabel={(o) => o.label}
                          searchPromptText={t('common:SELECT_SEARCH_PROMPT_TEXT')}
                          loadingPlaceholder={t('common:TABLE_LOADING')}
                          placeholder={t('common:SELECT_LOADING_PLACEHOLDER')}
                          noResultsText={t('common:NO_RESULTS_FOUND')}
                          clearValueText={t('common:MESSAGE_CLEAR')}
                          clearAllText={t('common:MESSAGE_CLEAR_ALL')}
                        />
                      </GroupInput>
                    )}
                  />
                </FormGroup>
              </Col>
            </Row>
            <Label>{t('CREATE_WHATSAPP_BUSINESS_LABEL_CONTENTS')}</Label>
            <Row>
              <Col>
                <FormGroup data-testid="templates-form-input-message_type">
                  <InputGroupWrapper
                    id="messageType"
                    label={t('CREATE_WHATSAPP_BUSINESS_LABEL_MESSAGE_TYPE')}
                    {...{
                      bindInput,
                      validation,
                      model,
                      setProperty,
                    }}
                    render={(input) => (
                      <GroupInput>
                        <Select
                          stateId="whatsappBusinessTemplatesFormMessageType"
                          id={input.id}
                          data-testid="templates-form-select-message_type"
                          value={input.model[input.id]}
                          onChange={handleChangeMessageType}
                          options={messageTypeOptions}
                          disabled={!isEditable || model.category.value === 'AUTHENTICATION'}
                          instanceId={input.id}
                          getOptionValue={(o) => o.value}
                          getOptionLabel={(o) => o.label}
                          searchPromptText={t('common:SELECT_SEARCH_PROMPT_TEXT')}
                          loadingPlaceholder={t('common:TABLE_LOADING')}
                          placeholder={t('common:SELECT_LOADING_PLACEHOLDER')}
                          noResultsText={t('common:NO_RESULTS_FOUND')}
                          clearValueText={t('common:MESSAGE_CLEAR')}
                          clearAllText={t('common:MESSAGE_CLEAR_ALL')}
                          isClearable={false}
                        />
                      </GroupInput>
                    )}
                  />
                </FormGroup>
              </Col>
            </Row>
            {model.messageType.value === 'text_only' ? (
              <Card>
                <CardHeader>
                  <Label>{t('CREATE_WHATSAPP_BUSINESS_LABEL_MESSAGE_BODY')}</Label>
                  <div ref={addParameterBodyTextOnlyRef} className="float-right">
                    <Button
                      className="float-right"
                      data-testid="templates-form-button-more_parameter"
                      size="sm"
                      color="primary"
                      disabled={!isEditable}
                      onClick={() => handleAddParameter('BODY', 'bodyTextOnlyInput')}
                    >
                      {t('CREATE_WHATSAPP_BUSINESS_BUTTON_MORE_PARAMETER')}
                    </Button>
                  </div>
                  <UncontrolledTooltip placement="top" target={addParameterBodyTextOnlyRef}>
                    {t('CREATE_WHATSAPP_BUSINESS_TOOLTIP_TEXT_PARAMETER')}
                  </UncontrolledTooltip>
                </CardHeader>
                <CardBody>
                  <Row>
                    <Col>
                      <Label htmlFor="bodyTextOnlyInput">{t('CREATE_WHATSAPP_BUSINESS_LABEL_MESSAGE_TEXT')}</Label>
                      <InputGroup
                        style={{
                          resize: 'vertical',
                        }}
                        id="bodyTextOnlyInput"
                        type="textarea"
                        required
                        disabled={!isEditable}
                        rows="6"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        onFocus={() => setOnFocus('BODY_TEXT_ONLY')}
                        onBlur={() => setOnFocus(false)}
                        placeholder={t('CREATE_WHATSAPP_BUSINESS_PLACEHOLDER_TYPE_YOUR_MESSAGE_HERE')}
                        value={
                          onFocus === 'BODY_TEXT_ONLY'
                            ? model.components[0]?.text
                            : interpolate(model.components[0]?.text, model.components[0]?.params)
                        }
                        onChange={(e) =>
                          setProperty('components', [
                            {
                              params: model.components[0]?.params || [],
                              text: normalizePlaceholders(e.target.value),
                              type: 'BODY',
                            },
                          ])
                        }
                        data-testid="templates-form-input-body_text_only"
                      />
                      <CharacterLimit
                        message={model.components[0]?.text}
                        limit={1024}
                        label={t('CREATE_WHATSAPP_BUSINESS_CHARACTERLIMIT_CHARACTERES')}
                      />
                    </Col>
                  </Row>
                  <Label htmlFor="bodyTextOnlyParams" className="mt-2">
                    {t('CREATE_WHATSAPP_BUSINESS_LABEL_PARAMETER_NAMES')}
                  </Label>
                  <InputGroupWrapper
                    id="bodyTextOnlyParams"
                    {...{
                      bindInput,
                      validation,
                      model,
                      setProperty,
                    }}
                    required
                    render={(input) => (
                      <GroupInput data-testid="templates-form-multiple-input-parameters_names">
                        <MultipleInput
                          placeholder={t('CREATE_WHATSAPP_BUSINESS_PLACEHOLDER_PARAMETERS_NAMES')}
                          isClearable
                          onChange={handleChangeTextOnlyParams}
                          value={uniq(model.components[0]?.params)}
                          id={input.id}
                        />
                      </GroupInput>
                    )}
                  />
                </CardBody>
              </Card>
            ) : (
              <>
                {model.category.value !== 'AUTHENTICATION' && (
                  <>
                    <Label>{t('CREATE_WHATSAPP_BUSINESS_LABEL_HEADER')}</Label>
                    <Card>
                      <CardHeader>
                        <Button
                          size="sm"
                          color="primary"
                          disabled={!isEditable}
                          outline
                          active={header?.format === 'TEXT'}
                          style={{
                            ...(header?.format === 'TEXT' && { opacity: 1 }),
                          }}
                          onClick={() => handleChangeHeaderType('TEXT')}
                          data-testid="templates-form-button-header_text"
                        >
                          {t('CREATE_WHATSAPP_BUSINESS_BUTTON_TEXT')}
                        </Button>
                        <Button
                          size="sm"
                          color="primary"
                          disabled={!isEditable}
                          outline={header?.format !== 'IMAGE'}
                          active={header?.format === 'IMAGE'}
                          // style={{
                          //   ...(header?.format === 'IMAGE' && { opacity: 1 })
                          // }}
                          onClick={() => handleChangeHeaderType('IMAGE')}
                          data-testid="templates-form-button-header_image"
                        >
                          {t('CREATE_WHATSAPP_BUSINESS_BUTTON_IMAGEM')}
                        </Button>
                        <Button
                          size="sm"
                          disabled={!isEditable}
                          color="primary"
                          outline
                          active={header?.format === 'DOCUMENT'}
                          style={{
                            ...(header?.format === 'DOCUMENT' && {
                              opacity: 1,
                            }),
                          }}
                          onClick={() => handleChangeHeaderType('DOCUMENT')}
                          data-testid="templates-form-button-header_document"
                        >
                          {t('CREATE_WHATSAPP_BUSINESS_BUTTON_DOCUMENT')}
                        </Button>
                        <Button
                          size="sm"
                          disabled={!isEditable}
                          color="primary"
                          outline
                          active={header?.format === 'VIDEO'}
                          style={{
                            ...(header?.format === 'VIDEO' && { opacity: 1 }),
                          }}
                          onClick={() => handleChangeHeaderType('VIDEO')}
                          data-testid="templates-form-button-header_video"
                        >
                          {t('CREATE_WHATSAPP_BUSINESS_BUTTON_VIDEO')}
                        </Button>

                        {header?.format === 'TEXT' && model?.service?.data?.providerType !== 'gupshup' && (
                          <>
                            <div ref={addParameterHeaderInteractiveRef} className="float-right">
                              <Button
                                className="float-right"
                                data-testid="templates-form-button-more_parameter"
                                size="sm"
                                color="primary"
                                disabled={!isEditable || (header.text.match(/(\{{\d{0,}\}\})/g)?.length || 0) >= 1}
                                onClick={() => handleAddParameter('HEADER', 'headerInteractiveInput')}
                              >
                                {t('CREATE_WHATSAPP_BUSINESS_BUTTON_MORE_PARAMETER')}
                              </Button>
                            </div>
                            <UncontrolledTooltip placement="top" target={addParameterHeaderInteractiveRef}>
                              {t('CREATE_WHATSAPP_BUSINESS_TOOLTIP_TEXT_PARAMETER')} - lalala
                            </UncontrolledTooltip>
                          </>
                        )}
                      </CardHeader>
                      <CardBody>
                        {header &&
                          (header?.format === 'TEXT' ? (
                            <>
                              <Row>
                                <Col>
                                  <Label>{t('CREATE_WHATSAPP_BUSINESS_LABEL_HEADER_TEXT')}</Label>
                                  <InputGroup
                                    {...{
                                      bindInput,
                                      validation,
                                      model,
                                      setProperty,
                                    }}
                                    style={{
                                      resize: 'vertical',
                                    }}
                                    id="headerInteractiveInput"
                                    type="textarea"
                                    required
                                    disabled={!isEditable}
                                    rows="4"
                                    onFocus={() => setOnFocus('HEADER')}
                                    onBlur={() => setOnFocus(false)}
                                    placeholder={t('CREATE_WHATSAPP_BUSINESS_PLACEHOLDER_MESSAGE_HEADER')}
                                    value={
                                      onFocus === 'HEADER' ? header?.text : interpolate(header?.text, header?.params)
                                    }
                                    onChange={handleChangeHeaderText}
                                    data-testid="templates-form-input-message_header"
                                  />
                                  <CharacterLimit
                                    message={header?.text}
                                    limit={60}
                                    label={t('CREATE_WHATSAPP_BUSINESS_CHARACTERLIMIT_CHARACTERES')}
                                  />
                                </Col>
                              </Row>

                              {model?.service?.data?.providerType !== 'gupshup' && (
                                <>
                                  <Label htmlFor="headerParams" className="mt-2">
                                    {t('CREATE_WHATSAPP_BUSINESS_LABEL_PARAMETER_NAME')}
                                  </Label>
                                  <InputGroupWrapper
                                    id="headerParams"
                                    {...{
                                      bindInput,
                                      validation,
                                      model,
                                      setProperty,
                                    }}
                                    render={(input) => (
                                      <GroupInput>
                                        <MultipleInput
                                          placeholder={t('CREATE_WHATSAPP_BUSINESS_PLACEHOLDER_PARAMETER_NAME')}
                                          isClearable
                                          onChange={handleChangeHeaderParams}
                                          value={header?.params}
                                          id={input.id}
                                          data-testid="templates-form-multiple-input-parameter_name"
                                        />
                                      </GroupInput>
                                    )}
                                  />
                                  <CharacterLimit
                                    message={(header?.params || []).map(() => '1').join('')}
                                    limit={1}
                                    label={t('CREATE_WHATSAPP_BUSINESS_LABEL_PARAMETERS')}
                                  />
                                </>
                              )}
                            </>
                          ) : (
                            <div>
                              {header.format === 'IMAGE' ? (
                                <img
                                  src={model.fileExample?.base64Url || model.fileExample?.url || templateImage}
                                  style={{
                                    margin: 'auto',
                                    width: '100%',
                                    maxHeight: '300px',
                                    borderRadius: '10px',
                                  }}
                                />
                              ) : model.fileExample?.base64Url || model.fileExample?.url ? (
                                header.format === 'VIDEO' ? (
                                  <video
                                    width="100%"
                                    height="300"
                                    controls
                                    src={model.fileExample?.base64Url || model.fileExample?.url}
                                    type="video/mp4"
                                  />
                                ) : (
                                  <div style={{ padding: '10px 5px 20px 0' }}>
                                    <a
                                      href={model.fileExample?.base64Url || model.fileExample?.url}
                                      download={model.fileExample?.base64Url && model.fileExample?.fileName}
                                      target="_blank"
                                      rel="noreferrer"
                                    >
                                      {model.fileExample?.fileName ||
                                        model.fileExample?.name ||
                                        t('CREATE_WHATSAPP_BUSINESS_BUTTON_DOCUMENT')}
                                    </a>
                                  </div>
                                )
                              ) : (
                                <>
                                  <Media format={header.format}>
                                    <Icon
                                      fixedWidth
                                      name={header.format === 'VIDEO' ? 'play' : 'file'}
                                      size="2x"
                                      className="text-primary"
                                    />
                                  </Media>
                                </>
                              )}
                              <label htmlFor="inputfile">
                                <label>{t('CREATE_WHATSAPP_BUSINESS_LABEL_CHOOSE_SAMPLE_MEDIA')}</label>
                                <InputGroup
                                  id="inputfile"
                                  name="inputfile"
                                  type="file"
                                  label
                                  accept={
                                    (header.format === 'IMAGE' && 'image/*') ||
                                    (header.format === 'VIDEO' && 'video/*') ||
                                    '*'
                                  }
                                  onChange={handleChangeHeaderFileExample}
                                  style={{ display: 'none' }}
                                  data-testid="templates-form-input-choose_file"
                                />
                                <S.ButtonFile>
                                  <Icon
                                    name="file"
                                    fixedWidth
                                    className="mr-1"
                                    data-testid="templates-form-button-choose_file"
                                  />
                                  {t('CREATE_WHATSAPP_BUSINESS_BUTTON_CHOOSE_FILE')}
                                </S.ButtonFile>
                              </label>
                              <div>
                                <label style={{ padding: '10px 5px 6px 3px' }}>{model.fileExample?.fileName}</label>
                              </div>
                            </div>
                          ))}
                      </CardBody>
                    </Card>
                  </>
                )}

                {/* Start Boby Message Interactive */}

                <Label className="mt-3">{t('CREATE_WHATSAPP_BUSINESS_LABEL_BODY')}</Label>

                <Card>
                  <CardHeader>
                    <Label>{t('CREATE_WHATSAPP_BUSINESS_LABEL_MESSAGE_BODY')}</Label>
                    {model.category.value !== 'AUTHENTICATION' && (
                      <>
                        <div ref={addParameterBodyInteractiveRef} className="float-right">
                          <Button
                            className="float-right"
                            data-testid="templates-form-button-more_parameter"
                            size="sm"
                            color="primary"
                            disabled={!isEditable}
                            onClick={() => handleAddParameter('BODY', 'bodyInteractiveInput')}
                          >
                            {t('CREATE_WHATSAPP_BUSINESS_BUTTON_MORE_PARAMETER')}
                          </Button>
                        </div>
                        <UncontrolledTooltip placement="top" target={addParameterBodyInteractiveRef}>
                          {t('CREATE_WHATSAPP_BUSINESS_TOOLTIP_TEXT_PARAMETER')}
                        </UncontrolledTooltip>
                      </>
                    )}
                  </CardHeader>

                  <CardBody>
                    {model.category.value === 'AUTHENTICATION' && (
                      <TemplateBody
                        handleSecurityRecomendation={handleSecurityRecomendation}
                        add_security_recommendation={
                          model.components.find((c) => c.type === 'BODY')?.add_security_recommendation
                        }
                      />
                    )}

                    <Row>
                      <Col>
                        <Label htmlFor="bodyInteractiveInput">{t('CREATE_WHATSAPP_BUSINESS_LABEL_MESSAGE_TEXT')}</Label>
                        <InputGroup
                          {...{
                            bindInput,
                            validation,
                            model,
                            setProperty,
                          }}
                          style={{
                            resize: 'vertical',
                          }}
                          id="bodyInteractiveInput"
                          type="textarea"
                          required
                          disabled={!isEditable}
                          readOnly={model.category.value === 'AUTHENTICATION'}
                          rows="4"
                          onFocus={() => setOnFocus('BODY')}
                          onBlur={() => setOnFocus(false)}
                          placeholder={t('CREATE_WHATSAPP_BUSINESS_PLACEHOLDER_TYPE_YOUR_MESSAGE_HERE')}
                          value={
                            onFocus === 'BODY'
                              ? model.components.find((c) => c.type === 'BODY')?.text
                              : bodyValue(model)
                          }
                          onChange={handleChangeBody}
                          data-testid="templates-form-input-body_interactive"
                        />
                        <CharacterLimit
                          message={model.components.find((c) => c.type === 'BODY')?.text}
                          limit={1024}
                          label={t('CREATE_WHATSAPP_BUSINESS_CHARACTERLIMIT_CHARACTERES')}
                        />
                      </Col>
                    </Row>

                    <div className={model.category.value === 'AUTHENTICATION' ? 'd-none' : 'd-block'}>
                      <Label htmlFor="bodyParams" className="mt-2">
                        {t('CREATE_WHATSAPP_BUSINESS_LABEL_PARAMETER_NAME')}
                      </Label>

                      <InputGroupWrapper
                        id="bodyParams"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <GroupInput>
                            <MultipleInput
                              placeholder={t('CREATE_WHATSAPP_BUSINESS_PLACEHOLDER_PARAMETER_NAME')}
                              isClearable
                              onChange={handleChangeBodyParams}
                              value={model.components.find((c) => c.type === 'BODY')?.params}
                              id={input.id}
                              data-testid="templates-form-multiple-input-parameter_name"
                            />
                          </GroupInput>
                        )}
                      />
                    </div>
                  </CardBody>
                </Card>

                {/* End Body Message Interactive */}

                <Row className="mt-3">
                  <Col>
                    <InputGroupWrapper
                      id="isFooterActive"
                      noLabel
                      {...{
                        bindInput,
                        validation,
                        model,
                        setProperty,
                        setModel,
                      }}
                      render={(input) => (
                        <Switch
                          className="d-flex justify-content-between"
                          reverseSwitch
                          label={t('CREATE_WHATSAPP_BUSINESS_LABEL_FOOTER')}
                          disabled={!isEditable}
                          id={input.id}
                          data-testid="templates-form-footer_active"
                          checked={model.isFooterActive}
                          onChange={handleChangeIsFooterActive}
                        />
                      )}
                    />
                  </Col>
                </Row>

                {model.isFooterActive && (
                  <Card>
                    <CardHeader>{t('CREATE_WHATSAPP_BUSINESS_MESSAGE_FOOTER')}</CardHeader>
                    <CardBody>
                      <Label htmlFor="footerInteractiveInput">{t('CREATE_WHATSAPP_BUSINESS_LABEL_FOOTER_TEXT')}</Label>

                      {model.category.value === 'AUTHENTICATION' && (
                        <TemplateOTPFooter
                          handleActiveTimeExpiration={handleActiveTimeExpiration}
                          handleChangeTimeExpiration={handleChangeTimeExpiration}
                          footer={footer}
                          validation={validation}
                        />
                      )}

                      <InputGroup
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        style={{
                          resize: 'vertical',
                        }}
                        id="footerInteractiveInput"
                        type="textarea"
                        required
                        disabled={!isEditable}
                        rows="4"
                        placeholder={t('CREATE_WHATSAPP_BUSINESS_PLACEHOLDER_FOOTER_TEXT')}
                        value={footer?.text}
                        readOnly={model.category.value === 'AUTHENTICATION'}
                        onChange={handleChangeFooter}
                        data-testid="templates-form-input-footer_text"
                      />

                      <CharacterLimit
                        message={footer?.text}
                        limit={60}
                        label={t('CREATE_WHATSAPP_BUSINESS_CHARACTERLIMIT_CHARACTERES')}
                      />
                    </CardBody>
                  </Card>
                )}

                <Row className={model.isFooterActive ? 'mt-3' : ''}>
                  <Col>
                    <InputGroupWrapper
                      id="isButtonsActive"
                      noLabel
                      {...{
                        bindInput,
                        validation,
                        model,
                        setProperty,
                        setModel,
                      }}
                      render={(input) => (
                        <Switch
                          className="d-flex justify-content-between"
                          reverseSwitch
                          id={input.id}
                          disabled={!isEditable || (model.category.value === 'AUTHENTICATION' && true)}
                          label={t('CREATE_WHATSAPP_BUSINESS_LABEL_BUTTONS')}
                          data-testid="templates-form-buttons_active"
                          checked={model.isButtonsActive}
                          onChange={handleChangeIsButtonActive}
                        />
                      )}
                    />
                  </Col>
                </Row>

                {model.isButtonsActive && (
                  <Card>
                    <CardHeader>
                      <TemplateActionButtons
                        handleChangeButtonType={handleChangeButtonType}
                        buttonType={buttonType}
                        buttonTypeOtp={buttonTypeOtp}
                        isEditable={isEditable}
                        category={model.category.value}
                      />

                      {model.category.value !== 'AUTHENTICATION' && (
                        <Button
                          className="float-right"
                          size="sm"
                          data-testid="templates-form-button-more_button"
                          color="primary"
                          onClick={handleAddButton}
                          disabled={
                            !isEditable ||
                            (buttons.every((b) => b.type === 'QUICK_REPLY')
                              ? buttons?.length >= 3
                              : buttons.length >= 2)
                          }
                        >
                          {t('CREATE_WHATSAPP_BUSINESS_MORE_BUTTON')}
                        </Button>
                      )}
                    </CardHeader>
                    {buttonType === 'QUICK_REPLY' &&
                      buttons?.map((button, index) => (
                        <CardBody key={index}>
                          <Card>
                            <CardHeader>
                              <Button
                                className="float-left"
                                size="sm"
                                data-testid="templates-form-button-simple_button"
                                disabled
                                color="primary"
                              >
                                {t('CREATE_WHATSAPP_BUSINESS_BUTTON')}
                              </Button>
                              <Button
                                className="float-right"
                                size="sm"
                                data-testid="templates-form-button-remove_button"
                                color="danger"
                                disabled={!isEditable}
                                onClick={() => handleRemoveButton(index)}
                              >
                                <Icon name="trash" />
                              </Button>
                            </CardHeader>
                            <CardBody>
                              <InputGroup
                                {...{
                                  bindInput,
                                  validation,
                                  model,
                                  setProperty,
                                }}
                                style={{
                                  resize: 'vertical',
                                }}
                                id={`buttonsInteractiveInput${index}`}
                                type="textarea"
                                required
                                rows="4"
                                disabled={!isEditable}
                                placeholder={t('CREATE_WHATSAPP_BUSINESS_PLACEHOLDER_BUTTON_TEXT')}
                                value={buttons[index].text}
                                onChange={(e) => handleChangeButton(e, index)}
                                data-testid="templates-form-input-button_text"
                              />
                              <CharacterLimit
                                message={buttons[index].text}
                                limit={20}
                                label={t('CREATE_WHATSAPP_BUSINESS_CHARACTERLIMIT_CHARACTERES')}
                              />
                            </CardBody>
                          </Card>
                        </CardBody>
                      ))}

                    {(buttonType === 'CALL_TO_ACTION' || buttonType === 'URL' || buttonType === 'PHONE_NUMBER') &&
                      buttons?.map((button, index) => (
                        <CardBody key={index}>
                          <Card>
                            <CardHeader>
                              <Button
                                size="sm"
                                color="primary"
                                data-testid="templates-form-button-url"
                                outline
                                disabled={
                                  !isEditable || (index === 0 ? buttons[1]?.type === 'URL' : buttons[0]?.type === 'URL')
                                }
                                active={button.type === 'URL'}
                                onClick={() => handleChangeCallToActionType('URL', index)}
                              >
                                {t('CREATE_WHATSAPP_BUSINESS_URL')}
                              </Button>
                              <Button
                                size="sm"
                                color="primary"
                                data-testid="templates-form-button-phone_number"
                                outline
                                disabled={
                                  !isEditable ||
                                  (index === 0
                                    ? buttons[1]?.type === 'PHONE_NUMBER'
                                    : buttons[0]?.type === 'PHONE_NUMBER')
                                }
                                active={button.type === 'PHONE_NUMBER'}
                                onClick={() => handleChangeCallToActionType('PHONE_NUMBER', index)}
                              >
                                {t('CREATE_WHATSAPP_BUSINESS_PHONE_NUMBER')}
                              </Button>
                              <Button
                                className="float-right"
                                size="sm"
                                data-testid="templates-form-button-remove_button"
                                color="danger"
                                disabled={!isEditable}
                                onClick={() => handleRemoveButton(index)}
                              >
                                <Icon name="trash" />
                              </Button>
                            </CardHeader>
                            <CardBody>
                              <Card>
                                <CardHeader>{t('CREATE_WHATSAPP_BUSINESS_CARDHEADER_BUTTON_TEXT')}</CardHeader>
                                <CardBody>
                                  <InputGroup
                                    {...{
                                      bindInput,
                                      validation,
                                      model,
                                      setProperty,
                                    }}
                                    style={{
                                      resize: 'vertical',
                                    }}
                                    id={`buttonsTextInteractiveInput${index}`}
                                    type="textarea"
                                    required
                                    rows="4"
                                    disabled={!isEditable}
                                    placeholder={t('CREATE_WHATSAPP_BUSINESS_PLACEHOLDER_BUTTON_TEXT')}
                                    value={buttons[index].text}
                                    onChange={(e) => handleChangeButton(e, index, button, true)}
                                    data-testid="templates-form-input-text_button"
                                  />
                                  <CharacterLimit
                                    message={buttons[index].text}
                                    limit={20}
                                    label={t('CREATE_WHATSAPP_BUSINESS_CHARACTERLIMIT_CHARACTERES')}
                                  />
                                </CardBody>
                              </Card>
                              {button.type === 'PHONE_NUMBER' && (
                                <Card className="mt-2">
                                  <CardHeader>{t('CREATE_WHATSAPP_BUSINESS_PHONE_NUMBER')}</CardHeader>
                                  <CardBody>
                                    <InputGroup
                                      {...{
                                        bindInput,
                                        validation,
                                        model,
                                        setProperty,
                                      }}
                                      style={{
                                        resize: 'vertical',
                                      }}
                                      id="buttonsPhoneNumberInteractiveInput"
                                      type="textarea"
                                      required
                                      disabled={!isEditable}
                                      rows="4"
                                      placeholder={t('CREATE_WHATSAPP_BUSINESS_PHONE_NUMBER')}
                                      value={buttons[index].phone_number}
                                      onChange={(e) => handleChangeButton(e, index, button)}
                                      data-testid="templates-form-input-phone_number"
                                    />
                                  </CardBody>
                                </Card>
                              )}
                              {button.type === 'URL' && (
                                <Card className="mt-2">
                                  <CardHeader>
                                    <Label>{t('CREATE_WHATSAPP_BUSINESS_URL')}</Label>
                                    <div ref={addParameterButtonsInteractiveRef} className="float-right">
                                      <Button
                                        className="float-right"
                                        size="sm"
                                        color="primary"
                                        disabled={
                                          !isEditable ||
                                          (buttons[index].url.match(/(\{{\d{0,}\}\})/g)?.length || 0) >= 1
                                        }
                                        onClick={() =>
                                          handleAddParameter('BUTTONS', 'buttonsUrlInteractiveInput', index)
                                        }
                                        data-testid="templates-form-button-url_more_parameter"
                                      >
                                        {t('CREATE_WHATSAPP_BUSINESS_BUTTON_MORE_PARAMETER')}
                                      </Button>
                                    </div>
                                    <UncontrolledTooltip placement="top" target={addParameterButtonsInteractiveRef}>
                                      {t('CREATE_WHATSAPP_BUSINESS_TOOLTIP_TEXT_PARAMETER')}
                                    </UncontrolledTooltip>
                                  </CardHeader>
                                  <CardBody>
                                    <InputGroup
                                      {...{
                                        bindInput,
                                        validation,
                                        model,
                                        setProperty,
                                      }}
                                      style={{
                                        resize: 'vertical',
                                      }}
                                      id="buttonsUrlInteractiveInput"
                                      type="textarea"
                                      required
                                      rows="4"
                                      disabled={!isEditable}
                                      onFocus={() => setOnFocus('URL')}
                                      onBlur={() => setOnFocus(false)}
                                      placeholder={t('CREATE_WHATSAPP_BUSINESS_URL')}
                                      value={
                                        onFocus === 'URL'
                                          ? buttons[index].url
                                          : interpolate(buttons[index].url, buttons[index].params)
                                      }
                                      onChange={(e) => handleChangeButton(e, index, button)}
                                      data-testid="templates-form-input-url_button"
                                    />
                                    <Label htmlFor="buttonUrlParams" className="mt-2">
                                      {t('CREATE_WHATSAPP_BUSINESS_LABEL_PARAMETER_NAME')}
                                    </Label>
                                    <InputGroupWrapper
                                      id="buttonUrlParams"
                                      {...{
                                        bindInput,
                                        validation,
                                        model,
                                        setProperty,
                                      }}
                                      render={(input) => (
                                        <GroupInput>
                                          <MultipleInput
                                            placeholder={t('CREATE_WHATSAPP_BUSINESS_PLACEHOLDER_PARAMETER_NAME')}
                                            isClearable
                                            onChange={handleChangeButtonUrlParams}
                                            value={buttons?.find((b) => b.type === 'URL')?.params}
                                            id={input.id}
                                            data-testid="templates-form-multiple-input-parameter_name"
                                          />
                                        </GroupInput>
                                      )}
                                    />
                                    <CharacterLimit
                                      message={(buttons[index].params || []).map((i) => '1').join('')}
                                      limit={1}
                                      label={t('CREATE_WHATSAPP_BUSINESS_PARAMETERS_AT_END_OF_URL')}
                                    />
                                  </CardBody>
                                </Card>
                              )}
                            </CardBody>
                          </Card>
                        </CardBody>
                      ))}

                    {buttonType === 'QUICK_REPLY' && (
                      <CharacterLimit
                        className="d-flex justify-content-end"
                        message={buttons.map((b) => '1').join('')}
                        label={t('CREATE_WHATSAPP_BUSINESS_LABEL_BUTTONS')}
                        limit={3}
                      />
                    )}

                    {buttonType == 'OTP' && (
                      <Row className="m-2">
                        <Col>
                          <TemplateActionButtonsOTP
                            buttons={model.components[buttonsPosition].buttons[0]}
                            handleChangeButton={handleChangeButton}
                            isEditable={isEditable}
                          />
                        </Col>
                      </Row>
                    )}
                  </Card>
                )}
              </>
            )}
          </ModalBody>
          <ModalFooter>
            <Button
              key="2"
              data-testid="cancel-template-button"
              className="cancel"
              type="button"
              onClick={exit}
              disabled={false}
            >
              {t('common:FORM_ACTION_CANCEL')}
            </Button>

            <Button
              key="1"
              data-testid="save-template-button"
              className="confirm"
              type="submit"
              onClick={handleSubmit}
              disabled={isLoading}
            >
              {t('common:FORM_ACTION_SAVE')}
            </Button>
          </ModalFooter>
        </ModalDigisac>
      </Form>
      {isAlertShowing && (
        <SweetAlert error title={t('ERROR_MESSAGE_MODAL')} onConfirm={closeAlert}>
          {getErrorMessage(error, t)}
        </SweetAlert>
      )}
    </>
  )
}

export default withRouter(WhatsappBusinessTemplatesForm)
