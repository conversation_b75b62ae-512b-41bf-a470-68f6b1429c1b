import React, { useState } from 'react'
import { Info, HelpCircleIcon } from 'lucide-react'
import Helmet from 'react-helmet'
import { Link, Switch, withRouter } from 'react-router-dom'
import { pickBy, identity } from 'lodash'
import { Button as RButton } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import LoadingSpinner from '../../../../common/unconnected/LoadingSpinner'
import Icon from '../../../../common/unconnected/Icon'
import Toggler from '../../../../common/unconnected/Toggler'
import TablePagination from '../../../../common/unconnected/TablePagination'
import IfUserCan from '../../../../common/connected/IfUserCan'
import IfUserCanRoute from '../../../../common/connected/IfUserCanRoute'
import { useFetchManyWhatsappBusinessTemplate } from '../../../../../resources/whatsappBusinessTemplate/requests'
import useIndexController from '../../../../../hooks/crud/useIndexController'
import WhatsappBusinessTemplatesDeleteRoute from '../WhatsappBusinessTemplatesDelete'
import WhatsappBusinessTemplatesFormRoute from '../WhatsappBusinessTemplatesForm'
import WhatsappBusinessTemplatesShowRoute from '../WhatsappBusinessTemplatesShow'
import Filters from './Filters'
import SendToReview from './SendToReview'
import Button from '../../../../common/unconnected/Button'
import { getOptions } from '../WhatsappBusinessTemplatesForm/WhatsappBusinessTemplatesForm'
import whatsappBusinessTemplateApi from '../../../../../resources/whatsappBusinessTemplate/api'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../../../common/unconnected/ui/tooltip'
import { Badge } from '../../../../common/unconnected/ui/badge'

// styles
import {
  Table,
  TableHead,
  TableBody,
  TableColumn,
  TableRow,
  TableCell,
  TableOptions,
  ButtonRefresh,
  ButtonDropdownActions,
  DropdownItemActions,
  DropdownMenuActions,
  CardFilters,
} from '../../../styles/table'

import {
  IconSearch,
  IconOptions,
  IconTrash,
  IconEdit,
  IconShowPassword as IconEyes,
  IconSend,
} from '../../../../common/unconnected/IconDigisac'

import { PrimaryColor, TextColor } from '../../../styles/colors'
import Container from '../../../styles/container'
import * as S from './styles'
import { useRequest } from '../../../../../hooks/useRequest'
import CardLoading from '../../../../common/unconnected/CardLoading'

export const getStatus = (status, t) =>
  ({
    SENDING: {
      text: t('WHATSAPP_BUSINESS_GET_STATUS_SENDING'),
      variant: 'neutral',
    },
    SUBMITTED: {
      text: t('WHATSAPP_BUSINESS_GET_STATUS_SUBMITTED'),
      variant: 'neutral',
    },
    APPROVED: {
      text: t('WHATSAPP_BUSINESS_GET_STATUS_APPROVED'),
      variant: 'success',
    },
    PENDING: {
      text: t('WHATSAPP_BUSINESS_GET_STATUS_PENDING'),
      variant: 'neutral',
    },
    REJECTED: {
      text: t('WHATSAPP_BUSINESS_GET_STATUS_REJECTED'),
      variant: 'error',
    },
    DISABLED: {
      text: t('WHATSAPP_BUSINESS_GET_STATUS_DISABLED'),
      variant: 'error',
    },
    PAUSED: {
      text: t('WHATSAPP_BUSINESS_GET_STATUS_PAUSED'),
      variant: 'warning',
    },
    '': {
      text: t('WHATSAPP_BUSINESS_GET_STATUS_CREATED'),
      variant: 'neutral',
    },
    ERROR: {
      text: t('WHATSAPP_BUSINESS_GET_STATUS_ERROR'),
      variant: 'error',
    },
  })[status] || { text: status || '', variant: status ? 'neutral' : '' }

export const getQuality = (quality, t) =>
  ({
    HIGH: {
      text: t('TEMPLATE_QUALITY_HIGH'),
      variant: 'success',
    },
    MEDIUM: {
      text: t('TEMPLATE_QUALITY_MEDIUM'),
      variant: 'warning',
    },
    LOW: {
      text: t('TEMPLATE_QUALITY_LOW'),
      variant: 'error',
    },
  })[quality] || { text: '', variant: '' }

const buildQuery = ({ filters, localPagination }) => ({
  query: JSON.stringify({
    ...pickBy({
      include: [
        {
          attributes: ['id', 'name'],
          model: 'service',
          required: true,
          where: {
            archivedAt: { $eq: null },
          },
        },
      ],
      where: {
        ...(filters.name && {
          $or: {
            internalName: {
              $iLike: `%${filters.name}%`,
            },
            name: {
              $iLike: `%${filters.name}%`,
            },
          },
        }),
        ...(filters.archivedAt && {
          archivedAt: { archived: { $ne: null }, unarchived: { $eq: null } }[filters.archivedAt.value],
        }),
        ...(filters.category && {
          category: filters.category.value,
        }),
        ...(filters.service && {
          serviceId: filters.service?.id || null,
        }),
        ...(filters.quality && {
          quality: filters.quality.value,
        }),
      },
      page: localPagination.page,
      perPage: localPagination.perPage,
      order: [['createdAt', 'DESC']],
    }),
    identity,
  }),
})

const WhatsappBusinessTemplatesIndex = ({ match }) => {
  const { t } = useTranslation(['whatsappBusinessTemplatesPage', 'common'])

  const initialFilters = {
    name: '',
    archivedAt: { value: 'unarchived', label: t('common:LABEL_UNARCHIVED') },
    category: '',
    service: '',
    quality: '',
  }

  const {
    models: whatsappBusinessTemplates,
    pagination,
    isLoading,
    fetch,
    localPagination,
    handleLocalPaginationChange,
    handleFilterChange,
    filters,
    toggleFilters,
    isFiltersShowing,
  } = useIndexController({
    buildQuery,
    initialFilters,
    useFetchMany: useFetchManyWhatsappBusinessTemplate,
  })

  const [{ isLoading: isLoadingRefreshTemplates }, refreshTemplates] = useRequest(
    whatsappBusinessTemplateApi.refreshTemplates,
  )

  const [reviewId, setReviewId] = useState(null)

  const [categoryOptions] = getOptions(t)

  const handleRefreshTemplates = async () => {
    await refreshTemplates()
    await fetch()
  }

  return (
    <div>
      {reviewId && (
        <SendToReview
          template={whatsappBusinessTemplates.find((i) => i.id === reviewId)}
          onSend={fetch}
          setReviewId={setReviewId}
        />
      )}
      <Helmet title={t('TITLE_TEMPLATES')} />

      <Container>
        <div className="d-flex align-itens-center justify-content-between">
          <div className="title-page" data-testid="whatsappBusinessTemplate-text">
            <h2>{t('TITLE_TEMPLATES')}</h2>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircleIcon
                    style={{
                      color: '#324B7D',
                      marginLeft: '4px',
                      marginBottom: '5px',
                    }}
                    size={14}
                  />
                </TooltipTrigger>
                <TooltipContent>{t('SUBTITLE_TEMPLATE')}</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="d-flex align-items-center">
            <IfUserCan permission="hsm.create">
              <Button
                background={PrimaryColor}
                size="xxl"
                onClick={handleRefreshTemplates}
                data-testid="force-sync-whatsappBusinessTemplate-button"
                className="mr-2"
              >
                <Icon name="sync-alt" fixedWidth />
                {t('BUTTON_SYNC_WITH_PROVIDER')}
              </Button>
              <Button background={PrimaryColor} size="xll" className="mr-2">
                <Link to="/whatsapp-business-templates/create" data-testid="create-whatsappBusinessTemplate-button">
                  <Icon name="plus" fixedWidth className="mr-1" />
                  {t('NEW_TEMPLATE')}
                </Link>
              </Button>
            </IfUserCan>

            <Button
              background={PrimaryColor}
              size="xl"
              onClick={toggleFilters}
              data-testid="filter-whatsappBusinessTemplate-button"
              className="mr-2"
            >
              <IconSearch fill="white" width="25" height="25" />
              {isFiltersShowing ? `${t('common:BUTTON_TEXT_HIDDEN')} ` : `${t('common:BUTTON_TEXT_SHOW')} `}
              {t('common:BUTTON_TEXT_FILTERS')}
            </Button>

            <ButtonRefresh onClick={fetch} data-testid="refresh-whatsappBusinessTemplate-button">
              <Icon name="sync-alt" fixedWidth />
            </ButtonRefresh>
          </div>
        </div>

        {isFiltersShowing && (
          <CardFilters>
            <Filters filters={filters} handleFilterChange={handleFilterChange} categoryOptions={categoryOptions} />
          </CardFilters>
        )}

        <Table>
          <TableHead gridColumns="23% 10% 10% 30% 10% 12% 5%" disableGap>
            <TableColumn data-testid="templates-table-column-Name">{t('TABLE_COLUMN_NAME')}</TableColumn>
            <TableColumn data-testid="templates-table-column-category">{t('TABLE_COLUMN_CATEGORY')}</TableColumn>
            <TableColumn data-testid="templates-table-column-service">{t('TABLE_COLUMN_SERVICE')}</TableColumn>
            <TableColumn data-testid="templates-table-column-preview">{t('TABLE_COLUMN_PREVIEW')}</TableColumn>
            <TableColumn data-testid="templates-table-column-status">{t('TABLE_COLUMN_STATUS')}</TableColumn>
            <TableColumn data-testid="templates-table-column-analysis">{t('TABLE_COLUMN_TEMPLATE_HEALTH')}</TableColumn>
            <TableColumn data-testid="templates-table-column-actions" className="text-right justify-content-end pr-4">
              {t('common:ACTIONS_SUBMENU_TITLE')}
            </TableColumn>
          </TableHead>

          <TableBody data-testid="hsm-modal-body">
            {whatsappBusinessTemplates &&
              whatsappBusinessTemplates.map((whatsappBusinessTemplate) => {
                const status = getStatus(whatsappBusinessTemplate.status, t)
                const quality = getQuality(whatsappBusinessTemplate.quality, t)
                return (
                  <TableRow gridColumns="23% 10% 10% 30% 10% 12% 5%" disableGap key={whatsappBusinessTemplate.id}>
                    <TableCell>
                      <Link
                        to={`/whatsapp-business-templates/${whatsappBusinessTemplate.id}`}
                        data-testid="Text-name-whatsappBusinessTemplate"
                      >
                        {whatsappBusinessTemplate.internalName || whatsappBusinessTemplate.name}
                      </Link>
                    </TableCell>
                    <TableCell>
                      {categoryOptions.find((o) => o.value === whatsappBusinessTemplate.category)?.label}
                    </TableCell>
                    <TableCell style={{ paddingRight: '1rem' }}>{whatsappBusinessTemplate.service?.name}</TableCell>
                    <TableCell className="pr-3">
                      {(whatsappBusinessTemplate?.components ?? []).find((c) => c.type === 'BODY')?.text}
                    </TableCell>
                    <TableCell>
                      <Badge variant={status.variant}>{status.text}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={quality.variant}>{quality.text}</Badge>
                    </TableCell>

                    <TableCell actions>
                      {!whatsappBusinessTemplate.archivedAt && (
                        <Toggler
                          render={({ active, toggle }) => (
                            <ButtonDropdownActions
                              data-testid="action-whatsappBusinessTemplate-button"
                              group={false}
                              isOpen={active}
                              toggle={toggle}
                            >
                              <TableOptions size="sm" color="primary">
                                <IconOptions className="icon-options" fill={PrimaryColor} />
                              </TableOptions>
                              <DropdownMenuActions style={{ zIndex: '49' }}>
                                <DropdownItemActions header>{t('common:ACTIONS_SUBMENU_TITLE')}</DropdownItemActions>
                                <Link
                                  to={`/whatsapp-business-templates/${whatsappBusinessTemplate.id}`}
                                  data-testid="view-whatsappBusinessTemplate-button"
                                  className="dropdown-item"
                                >
                                  <IconEyes fill={TextColor} width="29" height="29" />
                                  {t('common:ACTIONS_SUBMENU_VIEW')}
                                </Link>
                                {(whatsappBusinessTemplate.status === 'ERROR' ||
                                  whatsappBusinessTemplate.status === '') && (
                                  <Link
                                    to={'/whatsapp-business-templates/'}
                                    data-testid="action-whatsappBusinessTemplate-Rbutton-status"
                                    className="dropdown-item"
                                    onClick={() => setReviewId(whatsappBusinessTemplate.id)}
                                  >
                                    <IconSend fill={TextColor} width="28" height="20" />
                                    {t('WHATSAPP_BUSINESS_RBUTTON_SEND_TO_ANALYSYS')}
                                  </Link>
                                )}
                                <IfUserCan permission="hsm.update">
                                  <Link
                                    to={`/whatsapp-business-templates/${whatsappBusinessTemplate.id}/edit`}
                                    data-testid="edit-whatsappBusinessTemplate-button"
                                    className="dropdown-item"
                                  >
                                    <IconEdit fill={TextColor} width="28" height="28" />
                                    {t('common:ACTIONS_SUBMENU_EDIT')}
                                  </Link>
                                </IfUserCan>

                                <IfUserCan permission="hsm.destroy">
                                  <Link
                                    to={`/whatsapp-business-templates/${whatsappBusinessTemplate.id}/delete`}
                                    data-testid="delete-whatsappBusinessTemplate-button"
                                    className="dropdown-item"
                                  >
                                    <IconTrash fill={TextColor} width="28" height="28" />
                                    {t('common:ACTIONS_SUBMENU_DELETE')}
                                  </Link>
                                </IfUserCan>
                              </DropdownMenuActions>
                            </ButtonDropdownActions>
                          )}
                        />
                      )}
                    </TableCell>
                  </TableRow>
                )
              })}
            {!isLoading && whatsappBusinessTemplates.length < 1 && (
              <TableRow>
                <TableCell colSpan={6} className="text-center">
                  {t('common:NO_RESULTS_FOUND')}
                </TableCell>
              </TableRow>
            )}
            {whatsappBusinessTemplates.length === 0 && isLoading && <LoadingSpinner isLoading />}
          </TableBody>
        </Table>

        <TablePagination
          pagination={pagination}
          localPagination={localPagination}
          handlePaginationChange={handleLocalPaginationChange}
        />
      </Container>
      <Switch>
        <IfUserCanRoute
          permission="hsm.create"
          exact
          path={`${match.url}/create`}
          component={WhatsappBusinessTemplatesFormRoute}
        />
        <IfUserCanRoute
          permission="hsm.update"
          exact
          path={`${match.url}/:id/edit`}
          component={WhatsappBusinessTemplatesFormRoute}
        />
        <IfUserCanRoute
          permission="hsm.destroy"
          exact
          path={`${match.url}/:id/delete`}
          component={WhatsappBusinessTemplatesDeleteRoute}
        />
        <IfUserCanRoute
          permission="hsm.view"
          exact
          path={`${match.url}/:id`}
          component={WhatsappBusinessTemplatesShowRoute}
        />
      </Switch>
      <CardLoading isLoading={isLoadingRefreshTemplates} />
    </div>
  )
}

export default withRouter(WhatsappBusinessTemplatesIndex)
