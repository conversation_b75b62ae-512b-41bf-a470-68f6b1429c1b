import React from 'react'
import { Row, <PERSON> } from 'reactstrap'
import { useTranslation } from 'react-i18next'

// components
import { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import Switch from '../../../../common/unconnected/Switch'

interface TemplateBodyProps {
  handleSecurityRecomendation: () => void
  add_security_recommendation: boolean
}

const TemplateBody = ({ handleSecurityRecomendation, add_security_recommendation }: TemplateBodyProps) => {
  const { t } = useTranslation(['whatsappBusinessTemplatesPage'])

  return (
    <Row>
      <Col>
        <InputGroupWrapper
          id="add_security_recommendation"
          render={() => (
            <Switch
              className="d-flex justify-content-between"
              reverseSwitch
              label={t('CREATE_WHATSAPP_BUSINESS_LABEL_SECURITY_MESSAGE')}
              checked={add_security_recommendation}
              name="add_security_recommendation"
              id="add_security_recommendation"
              onChange={handleSecurityRecomendation}
            />
          )}
        />
      </Col>
    </Row>
  )
}

export default TemplateBody
