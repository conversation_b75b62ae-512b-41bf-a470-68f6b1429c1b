import React from 'react'
import { Col, Row } from 'reactstrap'
import { useTranslation } from 'react-i18next'

// components
import InputGroup, { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import Switch from '../../../../common/unconnected/Switch'

interface TemplateOTPFooterProps {
  footer: {
    active_code_expiration_minutes: boolean
    code_expiration_minutes: number
  }
  handleActiveTimeExpiration: () => void
  handleChangeTimeExpiration: () => void
  validation: string[]
}

const TemplateOTPFooter = ({
  footer,
  handleActiveTimeExpiration,
  handleChangeTimeExpiration,
  validation,
}: TemplateOTPFooterProps) => {
  const { t } = useTranslation(['whatsappBusinessTemplatesPage'])

  return (
    <Row>
      <Col>
        <InputGroupWrapper
          id="active_code_expiration_minutes"
          render={(id) => (
            <Switch
              className="d-flex justify-content-between"
              reverseSwitch
              label={t('CREATE_WHATSAPP_BUSINESS_LABEL_ACTIVE_EXPIRATION_MINUTES')}
              checked={footer?.active_code_expiration_minutes}
              name="active_code_expiration_minutes"
              id={id}
              onChange={handleActiveTimeExpiration}
            />
          )}
        />
      </Col>

      <Col>
        <InputGroup
          id="code_expiration_minutes"
          for="code_expiration_minutes"
          {...{ validation }}
          data-testid="templates-form-input-time_indicator"
          label={t('CREATE_WHATSAPP_BUSINESS_LABEL_EXPIRATION_MINUTES')}
          name="code_expiration_minutes"
          disabled={!footer?.active_code_expiration_minutes}
          required
          type="number"
          value={footer?.code_expiration_minutes}
          onChange={handleChangeTimeExpiration}
          max="90"
          min="1"
          maxlength="2"
        />
      </Col>
    </Row>
  )
}

export default TemplateOTPFooter
