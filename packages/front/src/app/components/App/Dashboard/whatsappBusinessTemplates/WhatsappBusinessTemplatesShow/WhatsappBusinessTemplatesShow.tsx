import React from 'react'
import { with<PERSON>out<PERSON> } from 'react-router-dom'
import { Row, Col } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { useFetchOneWhatsappBusinessTemplate } from '../../../../../resources/whatsappBusinessTemplate/requests'
import { getOptions } from '../WhatsappBusinessTemplatesForm/WhatsappBusinessTemplatesForm'
import useShowController from '../../../../../hooks/crud/useShowController'
import { getStatus, getQuality } from '../WhatsappBusinessTemplatesIndex/WhatsappBusinessTemplatesIndex'
import {
  Dialog,
  DialogContent,
  DialogClose,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../../../common/unconnected/ui/dialog'
import { Button } from '../../../../common/unconnected/ui/button'
import { Badge } from '../../../../common/unconnected/ui/badge'
import parse from 'html-react-parser'

export const getTemplateHealthDesctiption = (quality, t) =>
  ({
    LOW: {
      text: t('DESCRIPTION_TEMPLATE_HEALTH_LOW'),
    },
    MEDIUM: {
      text: t('DESCRIPTION_TEMPLATE_HEALTH_MEDIUM'),
    },
  })[quality] || { text: '' }

const WhatsappBusinessTemplatesShow = ({ match, history }) => {
  const { t } = useTranslation(['whatsappBusinessTemplatesPage', 'common'])
  const [categoryOptions, languageOptions, messageTypeOptions] = getOptions(t)

  const {
    model: template,
    isOpen,
    exit,
  } = useShowController({
    exitToPath: '/whatsapp-business-templates',
    useFetchOne: useFetchOneWhatsappBusinessTemplate,
    query: {
      attributes: ['id', 'name'],
      include: ['service'],
    },
  })

  if (!template) return null

  return (
    <Dialog open={isOpen} onOpenChange={exit}>
      <DialogContent style={{ maxWidth: '800px' }} data-testid="visual-template">
        <DialogHeader data-testid="visual_template">
          <DialogTitle data-testid="templates-modal-view-title">{t('TEMPLATE_DETAILS')}</DialogTitle>
        </DialogHeader>
        <main style={{ margin: '16px 0' }} data-testid="templates-modal-view-body">
          <Row className="mb-3">
            <Col sm="8">
              <span style={{ color: '#6E7A89' }}>{t('WHATSAPP_BUSINESS_TEMPLATES_SHOW_NAME')}</span>
              <div>{template.name}</div>
            </Col>
          </Row>
          <Row className="mb-3">
            <Col sm="4">
              <span style={{ color: '#6E7A89' }}>{t('WHATSAPP_BUSINESS_TEMPLATES_SHOW_SERVICE')}</span>
              <div>{template.service.name}</div>
            </Col>
            <Col sm="4">
              <span style={{ color: '#6E7A89' }}>{t('WHATSAPP_BUSINESS_TEMPLATES_SHOW_CATEGORY')}</span>
              <div>
                <Badge color="secondary">{categoryOptions.find((o) => o.value === template.category)?.label}</Badge>
              </div>
            </Col>
            <Col sm="4">
              <span style={{ color: '#6E7A89' }}>{t('WHATSAPP_BUSINESS_TEMPLATES_SHOW_MESSAGE_TYPE')}</span>
              <div>{messageTypeOptions.find((i) => i.value === template.messageType)?.label}</div>
            </Col>
          </Row>
          <Row className="mb-3">
            <Col sm="4">
              <span style={{ color: '#6E7A89' }}>{t('WHATSAPP_BUSINESS_TEMPLATES_SHOW_LANGUAGE')}</span>
              <div>{languageOptions.find((i) => i.value === template.language)?.label}</div>
            </Col>
            <Col sm="4">
              <span style={{ color: '#6E7A89' }}>{t('WHATSAPP_BUSINESS_TEMPLATES_SHOW_NAMESPACE')}</span>
              <div>{template.namespace}</div>
            </Col>
          </Row>
          <Row className="mb-3">
            <Col sm="4">
              <span style={{ color: '#6E7A89' }}>{t('WHATSAPP_BUSINESS_TEMPLATES_SHOW_STATUS')}</span>
              <div>
                <Badge variant={getStatus(template.status, t)?.variant}>{getStatus(template.status, t)?.text}</Badge>
              </div>
            </Col>
            <Col sm="4">
              <span style={{ color: '#6E7A89' }}>{t('WHATSAPP_BUSINESS_TEMPLATES_SHOW_TEMPLATE_HEALTH')}</span>
              <div>
                <Badge variant={getQuality(template.quality, t)?.variant}>
                  {getQuality(template.quality, t)?.text}
                </Badge>
              </div>
            </Col>
          </Row>
          {(template.quality === 'LOW' || template.quality === 'MEDIUM') && (
            <div className="mt-4 p-3 bg-light rounded border">
              <h6 style={{ color: '#6E7A89' }}>{t('DESCRIPTION_TEMPLATE_HEALTH')}</h6>
              <p>{parse(getTemplateHealthDesctiption(template.quality, t)?.text)}</p>
            </div>
          )}
        </main>
        <DialogFooter
          style={{
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <DialogClose asChild>
            <Button
              style={{ width: '50%' }}
              variant="outline"
              data-testid="templates-modal-view-close"
              id="template-button-modal-view-close"
            >
              {t('common:FORM_ACTION_CLOSE')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default withRouter(WhatsappBusinessTemplatesShow)
