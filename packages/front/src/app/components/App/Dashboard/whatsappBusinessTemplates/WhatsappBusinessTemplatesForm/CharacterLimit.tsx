import React from 'react'

interface CharacterLimitProps {
  message: string
  limit: number
  label: string
  rest?: React.ReactNode
}

const CharacterLimit = (props: CharacterLimitProps) => {
  const { message, limit, label = '', ...rest } = props
  return (
    <div style={{ color: 'gray', float: 'right', margin: '5px 5px 0 0' }} {...rest}>
      <label>{`${(message || '').length} / ${limit} ${label}`}</label>
    </div>
  )
}

export default CharacterLimit
