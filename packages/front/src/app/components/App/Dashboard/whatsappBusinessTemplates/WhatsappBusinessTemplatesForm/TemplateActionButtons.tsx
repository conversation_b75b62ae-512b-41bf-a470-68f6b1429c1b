import React from 'react'
import { But<PERSON> } from 'reactstrap'
import { useTranslation } from 'react-i18next'

interface TemplateActionButtonsProps {
  handleChangeButtonType: (string) => void
  buttonType: string
  buttonTypeOtp: string
  isEditable: string
  category: string
}

const TemplateActionButtons = ({
  handleChangeButtonType,
  buttonType,
  buttonTypeOtp,
  isEditable,
  category,
}: TemplateActionButtonsProps) => {
  const { t } = useTranslation(['whatsappBusinessTemplatesPage'])

  if (category === 'AUTHENTICATION') {
    return (
      <>
        <Button
          className="mr-1"
          size="sm"
          color="primary"
          outline
          data-testid="template-form-button-otp-copy-code"
          onClick={() => handleChangeButtonType('OTP_COPY_CODE')}
          active={buttonTypeOtp === 'COPY_CODE'}
          disabled={!isEditable}
        >
          {t('CREATE_WHATSAPP_BUSINESS_BUTTON_COPY_CODE')}
        </Button>

        <Button
          size="sm"
          color="primary"
          outline
          data-testid="template-form-button-otp-autofill"
          onClick={() => handleChangeButtonType('OTP_AUTOFILL')}
          active={buttonTypeOtp === 'ONE_TAP'}
          disabled={!isEditable}
        >
          Autofill
        </Button>
      </>
    )
  }

  return (
    <>
      <Button
        size="sm"
        color="primary"
        data-testid="button-quick_answer"
        disabled={!isEditable}
        outline
        active={buttonType === 'QUICK_REPLY'}
        style={{
          ...(buttonType === 'QUICK_REPLY' && { opacity: 1 }),
        }}
        className="mr-1"
        onClick={() => handleChangeButtonType('QUICK_REPLY')}
      >
        {t('CREATE_WHATSAPP_BUSINESS_BUTTON_QUICK_ANSWER')}
      </Button>

      <Button
        size="sm"
        color="primary"
        data-testid="templates-form-button-call_to_action"
        disabled={!isEditable}
        outline
        active={buttonType === 'CALL_TO_ACTION'}
        style={{
          ...(buttonType === 'CALL_TO_ACTION' && {
            opacity: 1,
          }),
        }}
        className="mr-1"
        onClick={() => handleChangeButtonType('CALL_TO_ACTION')}
      >
        {t('CREATE_WHATSAPP_BUSINESS_BUTTON_CALL_TO_ACTION')}
      </Button>
    </>
  )
}

export default TemplateActionButtons
