import React from 'react'
import { useTranslation } from 'react-i18next'

// componets
import InputGroup from '../../../../common/unconnected/InputGroup'

interface ButtonsProps {
  otp_type: string
  text: string
}

interface TemplateActionButtonsOTPPRops {
  buttons: ButtonsProps
  handleChangeButton: (object, string, string, boolean) => void
  isEditable: boolean
}

const TemplateActionButtonsOTP = ({ buttons, handleChangeButton, isEditable }: TemplateActionButtonsOTPPRops) => {
  const { t } = useTranslation(['whatsappBusinessTemplatesPage'])

  if (buttons.otp_type === 'COPY_CODE') {
    return (
      <div className="mb-3">
        <InputGroup
          label={t('CREATE_WHATSAPP_BUSINESS_LABEL_NAME_BUTTON')}
          name="text"
          value={buttons.text}
          disabled={!isEditable}
          onChange={(e) => handleChangeButton(e, 0, buttons)}
        />
      </div>
    )
  }

  return (
    <>
      <div className="mb-3">
        <InputGroup
          label={t('CREATE_WHATSAPP_BUSINESS_LABEL_NAME_BUTTON_ONE_TAP')}
          name="text"
          value={buttons.text}
          disabled={!isEditable}
          onChange={(e) => handleChangeButton(e, 0, buttons)}
        />
      </div>

      <div className="mb-3">
        <InputGroup
          label={t('CREATE_WHATSAPP_BUSINESS_LABEL_TEXT_BUTTON_ONE_TAP')}
          name="autofill_text"
          value={buttons.autofill_text}
          disabled={!isEditable}
          onChange={(e) => handleChangeButton(e, 0, buttons)}
        />
      </div>

      <div className="mb-3">
        <InputGroup
          label={t('CREATE_WHATSAPP_BUSINESS_LABEL_NAME_PACKAGE')}
          name="package_name"
          value={buttons.package_name}
          disabled={!isEditable}
          onChange={(e) => handleChangeButton(e, 0, buttons)}
        />
      </div>

      <div className="mb-3">
        <InputGroup
          label={t('CREATE_WHATSAPP_BUSINESS_LABEL_SIGNATURE_HASH')}
          name="signature_hash"
          value={buttons.signature_hash}
          disabled={!isEditable}
          onChange={(e) => handleChangeButton(e, 0, buttons)}
        />
      </div>
    </>
  )
}

export default TemplateActionButtonsOTP
