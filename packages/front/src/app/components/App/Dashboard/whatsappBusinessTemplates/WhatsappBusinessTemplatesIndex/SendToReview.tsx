import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import HsmModal from '../../../../common/unconnected/HsmModal'
import { useRequest } from '../../../../../hooks/useRequest'
import whatsappBusinessTemplateApi from '../../../../../resources/whatsappBusinessTemplate/api'
import toast from '../../../../../utils/toast'
import CardLoading from '../../../../common/unconnected/CardLoading'

const SendToReview = ({ template, onSend, setReviewId }) => {
  const [{ isLoading }, sendToReview] = useRequest(whatsappBusinessTemplateApi.sendToReview)
  const [, updateById] = useRequest(whatsappBusinessTemplateApi.updateById)
  const [, fetchById] = useRequest(whatsappBusinessTemplateApi.fetchById)

  const [defaultFileTemplate, setDefaultFileTemplate] = useState()

  const { t } = useTranslation(['whatsappBusinessTemplatesPage'])

  const close = () => setReviewId(null)

  const handleSendToReview = async (data) => {
    const dataHeader = data.parameters.find((p) => p.type === 'header')
    const dataBody = data.parameters.find((p) => p.type === 'body')
    const dataButtons = data.parameters.find((p) => p.type === 'button')

    let templateHeader = template.components.find((c) => c.type === 'HEADER')
    let templateBody = template.components.find((c) => c.type === 'BODY')
    const templateFooter = template.components.find((c) => c.type === 'FOOTER')
    let templateButtons = template.components.find((c) => c.type === 'BUTTONS')

    if (dataHeader) {
      templateHeader = {
        ...templateHeader,
        example: { header_text: dataHeader.parameters.map((i) => i.text) },
      }

      if (defaultFileTemplate) {
        await updateById(template.id, {
          ...template,
          fileExample: { ...defaultFileTemplate, isExample: true },
        })

        const response = await fetchById(template.id, {
          include: [
            {
              model: 'fileExample',
              order: [['createdAt', 'DESC']],
              limit: 1,
            },
          ],
        })

        const url = response.fileExample.url

        templateHeader.example = {
          header_handle: [url],
        }
      } else if (template.fileExample) {
        templateHeader.example = {
          header_handle: [template.fileExample.url],
        }
      }
    }
    if (dataBody) {
      templateBody = {
        ...templateBody,
        example: { body_text: [dataBody.parameters.map((i) => i.text)] },
      }
    }

    const dataUrlButtonParameter = dataButtons && dataButtons.parameters[0]

    if (dataButtons && dataUrlButtonParameter) {
      const buttons = templateButtons.buttons

      const index = buttons.findIndex((b) => b.type === 'URL')

      if (index !== -1) {
        buttons[index] = {
          ...buttons[index],
          example: [buttons[index].url.replace('{{1}}', dataUrlButtonParameter.text)],
        }

        templateButtons = {
          ...templateButtons,
          buttons,
        }
      }
    }

    const components = [templateHeader, templateBody, templateFooter, templateButtons].filter(Boolean)

    await updateById(template.id, { ...template, components })
    await sendToReview(template.id)
      .then((r) => {
        if (r.error) {
          toast.error(t('ERROR_SEND_TO_REVIEW'))
          return
        }
        onSend()
      })
      .catch((e) => {
        toast.error(e?.response?.data?.message || 'Unknow error!')
      })
  }

  return (
    <>
      <CardLoading isLoading={isLoading} />
      <HsmModal
        isOpen={!!template}
        toggle={close}
        serviceId={template?.serviceId}
        defaultTemplate={template}
        hsmSelectDisabled
        sendMessage={handleSendToReview}
        defaultFileTemplate={defaultFileTemplate}
        setDefaultFileTemplate={setDefaultFileTemplate}
        title={t('SEND_TEMPLATE_TO_REVIEW')}
      />
    </>
  )
}

export default SendToReview
