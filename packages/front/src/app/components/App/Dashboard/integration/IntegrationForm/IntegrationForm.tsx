import React, { memo, useState, useEffect } from 'react'
import Helmet from 'react-helmet'
import { Button, Form, ModalBody, ModalHeader, Row, Col } from 'reactstrap'
import SweetAlert from 'react-bootstrap-sweetalert'
import pick from 'lodash/pick'
import { useTranslation } from 'react-i18next'
import { required, isUrl } from '../../../../../utils/validator/validators'
import InputGroup, { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import useFormController from '../../../../../hooks/crud/useFormController'
import toast from '../../../../../utils/toast'
import {
  useCreateIntegration,
  useFetchOneIntegration,
  useUpdateIntegration,
} from '../../../../../resources/integration/requests'
import Select from '../../../../common/unconnected/Select'
import { TYPE_OPTIONS, ENABLE_SIZES } from '../constants'
import HelpPopover from '../../../../common/unconnected/HelpPopover/HelpPopover'
import IconSelect from '../../../../common/unconnected/IconSelect'
import useEventCallback from '../../../../../hooks/useEventCallback'

import { ModalDigisac, ModalFooter, GroupInput } from '../../../styles/common'
import ButtonClose from '../../../../common/unconnected/ButtonClose'

export const formatUrl = (url) => {
  if (url.includes('http://') || url.includes('https://')) {
    return url
  }

  return `http://${url}`
}

export const formatToApi = (integration) => ({
  ...pick(integration, ['type', 'text']),
  url: formatUrl(integration.url),
  type: integration.type.id,
  icon: integration.icon || '',
  size: integration.size ? integration.size.id : 'md',
})

export const formatFromApi = (integration) => ({
  ...pick(integration, ['type', 'url', 'icon', 'text', 'size']),
  type: integration && {
    id: integration.type,
    name: TYPE_OPTIONS[integration.type],
  },
  size: integration && {
    id: integration.size,
    name: ENABLE_SIZES[integration.size],
  },
})

const requiredValidation = [required, 'Campo obrigatório.']
const isUrlValidation = [isUrl, 'URL inválida.']

const validationRules = {
  type: [requiredValidation],
  text: [requiredValidation],
  url: [isUrlValidation],
}

const initialModel = {
  type: null,
  url: '',
  icon: '',
  text: '',
  size: null,
}

const typesList = [TYPE_OPTIONS.context_menu_button_contact_side_list, TYPE_OPTIONS.menu_button]

const typesAttendance = [TYPE_OPTIONS.event_ticket_transfer, TYPE_OPTIONS.event_ticket_closed]

function IntegrationForm() {
  const { t } = useTranslation(['integrationPage', 'common'])

  const typeOptions = Object.entries(TYPE_OPTIONS).map(([id, name]) => ({
    id,
    name: t(`label_${id}`),
  }))
  const sizeOptions = Object.entries(ENABLE_SIZES).map(([id, name]) => ({
    id,
    name: t(`label_${id}`),
  }))

  const {
    submit,
    exit,
    isModalOpen,
    isEditing,
    isAlertShowing,
    closeAlert,
    isLoading,
    error,
    model,
    bindInput,
    validation,
    setProperty,
  } = useFormController({
    exitToPath: '/integrations',
    initialModel,
    validationRules,
    formatToApi,
    formatFromApi,
    useCreateOne: useCreateIntegration,
    useUpdateOne: useUpdateIntegration,
    useFetchOne: useFetchOneIntegration,
  })

  const title = `${isEditing ? t('common:LABEL_EDITING') : t('common:LABEL_CREATING')} ${t('TITLE_INTEGRATION')}`

  const [showSize, setShowSize] = useState(false)
  useEffect(() => {
    const show = !typesList.includes(model.type ? model.type.name : '')
    setShowSize(show)
  }, [model])

  const [showIcon, setShowIcon] = useState(false)
  useEffect(() => {
    const show = !typesAttendance.includes(model.type ? model.type.name : '')
    setShowIcon(show)
  }, [model])

  const handleSubmit = useEventCallback(() => {
    validation.validateAll().then(
      (isValid) =>
        isValid &&
        submit().then((res) => {
          if (!res.success) {
            toast.warn(res.message)
            return
          }
          toast.success(t('INTEGRATION_TOAST_SUCCESS'))
          window.location.href = '/integrations'
        }),
    )
  })

  return (
    <>
      <Helmet title={title} />

      <Form onSubmit={handleSubmit} data-testid="create-integrations">
        <ModalDigisac isOpen={isModalOpen} toggle={exit} autoFocus={false}>
          <ModalHeader data-testid="integration-CreateTitle-text">
            {title}
            <ButtonClose onClick={exit} />
          </ModalHeader>
          <ModalBody>
            <InputGroupWrapper
              id="type"
              label={t('COLUMN_TABLE_TYPE')}
              {...{
                bindInput,
                validation,
                model,
                setProperty,
              }}
              required
              render={({ id }) => (
                <GroupInput data-testid="integration-CreateModal-type">
                  <Select
                    className="filter-type"
                    id={id}
                    value={model.type}
                    onChange={(value) => {
                      setProperty('type', value)
                      setShowSize(!typesList.includes(value.name))
                      setShowIcon(!typesAttendance.includes(value.name))
                    }}
                    options={typeOptions}
                    onBlur={() => validation.setTouched(id)}
                  />
                </GroupInput>
              )}
            />

            {showSize && (
              <InputGroupWrapper
                id="size"
                label={t('LABEL_FORM_SIZE')}
                {...{
                  bindInput,
                  validation,
                  model,
                  setProperty,
                }}
                render={({ id }) => (
                  <GroupInput data-testid="integration-CreateModal-size">
                    <Select
                      id={id}
                      value={model.size}
                      onChange={(value) => {
                        setProperty('size', value)
                      }}
                      options={sizeOptions}
                      onBlur={() => validation.setTouched(id)}
                    />
                  </GroupInput>
                )}
              />
            )}

            <InputGroup
              data-testid="integration-CreateModal-URL"
              id="url"
              label={t('LABEL_FORM_IFRAME_URL')}
              placeholder="https://iframe.dominio.com?contact_id={{contactId}}"
              required
              before={
                <span className="ml-2">
                  <HelpPopover
                    title={t('LABEL_FORM_AVAILABLE_VARIABLES')}
                    body={
                      <>
                        <div className="mb-3">
                          <b>{t('LABEL_CONTACT')}:</b>
                          <br />
                          <code>{'{{contactId}}'}</code>: {t('LABEL_CONTACT_IDENTIFIER')}
                          <br />
                          <code>{'{{ticketId}}'}</code>: {t('LABEL_CALLED_IDENTIFIER')}
                          <br />
                          <code>{'{{userId}}'}</code>: {t('LABEL_USER_IDENTIFIER')}
                        </div>
                        <div className="mb-3">
                          <b>{t('LABEL_MESSAGE')}:</b>
                          <br />
                          <code>{'{{messageId}}'}</code>: {t('LABEL_MESSAGE_IDENTIFIER')}
                          <br />
                          <code>{'{{contactId}}'}</code>: {t('LABEL_CONTACT_IDENTIFIER')}
                          <br />
                          <code>{'{{ticketId}}'}</code>: {t('LABEL_CALLED_IDENTIFIER')}
                          <br />
                          <code>{'{{userId}}'}</code>: {t('LABEL_USER_IDENTIFIER')}
                        </div>
                        <div className="mb-3">
                          <b>{t('LABEL_CALL_OPENING_TRANSFER_CLOSING')}</b>
                          <br />
                          <code>{'{{ticketId}}'}</code>: {t('LABEL_CALLED_IDENTIFIER')}
                          <br />
                          <code>{'{{contactId}}'}</code>: {t('LABEL_CONTACT_IDENTIFIER')}
                          <br />
                          <code>{'{{userId}}'}</code>: {t('LABEL_USER_IDENTIFIER')}
                        </div>
                        <div className="mb-3">
                          <b>{t('LABEL_MENU_TOP')}:</b>
                          <br />
                          <code>{'{{userId}}'}</code>: {t('LABEL_USER_IDENTIFIER')}
                        </div>
                      </>
                    }
                  />
                </span>
              }
              {...{ bindInput, validation }}
            />
            <InputGroup
              data-testid="integration-CreateModal-main"
              id="text"
              label={t('LABEL_TEXT_MENU')}
              required
              placeholder={t('LABEL_TEXT_MENU_PLACEHOLDER')}
              {...{ bindInput, validation }}
            />
            <Row>
              <Col>
                {showIcon && (
                  <InputGroupWrapper
                    id="icon"
                    label={t('COLUMN_TABLE_ICON')}
                    {...{
                      bindInput,
                      validation,
                      model,
                      setProperty,
                    }}
                    render={({ id }) => (
                      <GroupInput data-testid="integration-CreateModal-Icon">
                        <IconSelect id={id} value={model.icon} onChange={(value) => setProperty('icon', value)} />
                      </GroupInput>
                    )}
                  />
                )}
              </Col>
            </Row>
          </ModalBody>
          <ModalFooter>
            <Button className="cancel" type="button" onClick={exit} data-testid="cancel-account-button">
              {t('common:FORM_ACTION_CANCEL')}
            </Button>

            <Button
              data-testid="save-account-button"
              className="confirm"
              type="submit"
              onClick={handleSubmit}
              disabled={isLoading}
            >
              {t('common:FORM_ACTION_SAVE')}
            </Button>
          </ModalFooter>
        </ModalDigisac>
      </Form>

      {isAlertShowing && (
        <SweetAlert error title={t('common:MESSAGE_ATTENTION')} onConfirm={closeAlert}>
          {t('MODAL_ERROR')}
        </SweetAlert>
      )}
    </>
  )
}

export default memo(IntegrationForm)
