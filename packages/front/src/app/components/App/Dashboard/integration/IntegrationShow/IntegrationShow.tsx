import React, { memo } from 'react'
import Helmet from 'react-helmet'
import { <PERSON>dal, ModalBody, ModalHeader } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import useShowController from '../../../../../hooks/crud/useShowController'
import { useFetchOneIntegration } from '../../../../../resources/integration/requests'

import { TYPE_OPTIONS } from '../constants'

import { ModalDigisac } from '../../../styles/common'
import ButtonClose from '../../../../common/unconnected/ButtonClose'

function IntegrationShow() {
  const { t } = useTranslation(['integrationPage'])

  const {
    model: integration,
    isOpen,
    exit,
  } = useShowController({
    exitToPath: '/integrations',
    useFetchOne: useFetchOneIntegration,
  })

  if (!integration) return null

  return (
    <>
      <Helmet title={`${t('TITLE_INTEGRATION')} - ${integration.text}`} />

      <ModalDigisac isOpen={isOpen} toggle={exit} autoFocus={false}>
        <ModalHeader data-testid="integration-title-modalView">
          {t('TITLE_INTEGRATION')}
          <ButtonClose onClick={exit} />
        </ModalHeader>
        <ModalBody data-testid="integration-modalView-body">
          <b>{t('COLUMN_TABLE_TYPE')}:</b> {TYPE_OPTIONS[integration.type]} <br />
          <b>{t('COLUMN_TABLE_URL')}:</b> {integration.url}
          <br />
          <b>{t('COLUMN_TABLE_ICON')}:</b> {integration.icon}
          <br />
          <b>{t('COLUMN_TABLE_TEXT')}:</b> {integration.text}
        </ModalBody>
      </ModalDigisac>
    </>
  )
}

export default memo(IntegrationShow)
