import React, { memo } from 'react'
import Helmet from 'react-helmet'
import { Link, Route, Switch, useRouteMatch } from 'react-router-dom'
import { pickBy, identity } from 'lodash'
import { useTranslation } from 'react-i18next'
import IntegrationDeleteRoute from '../IntegrationDelete'
import IntegrationFormRoute from '../IntegrationForm'
import IntegrationShowRoute from '../IntegrationShow'
import Icon from '../../../../common/unconnected/Icon'
import IfUserCan from '../../../../common/connected/IfUserCan'
import Toggler from '../../../../common/unconnected/Toggler'
import IfUserCanRoute from '../../../../common/connected/IfUserCanRoute'
import TablePagination from '../../../../common/unconnected/TablePagination'
import CardLoading from '../../../../common/unconnected/CardLoading'
import Filters from './Filters'
import useIndexController from '../../../../../hooks/crud/useIndexController'
import { useFetchManyIntegration } from '../../../../../resources/integration/requests'
import { TYPE_OPTIONS } from '../constants'
import Button from '../../../../common/unconnected/Button'
import { options as IconOptionsDigisac } from '../../../../common/unconnected/IconSelect'

import {
  Table,
  TableHead,
  TableBody,
  TableColumn,
  TableRow,
  TableCell,
  TableOptions,
  ButtonRefresh,
  ButtonDropdownActions,
  DropdownItemActions,
  DropdownMenuActions,
  CardFilters,
} from '../../../styles/table'

import * as IconDigisac from '../../../../common/unconnected/IconDigisac'

import { PrimaryColor, TextColor } from '../../../styles/colors'
import Container from '../../../styles/container'

const buildQuery = ({ filters, localPagination }) =>
  pickBy(
    {
      attributes: ['id', 'text', 'url', 'icon', 'type'],
      where: pickBy(
        {
          text: filters.text && { $iLike: `%${filters.text}%` },
        },
        identity,
      ),
      order: [['text', 'ASC']],
      page: localPagination.page,
      perPage: localPagination.perPage,
    },
    identity,
  )

const initialFilters = {
  text: '',
}

function IntegrationIndex() {
  const {
    models: integration,
    pagination,
    isLoading,
    fetch,
    isFiltersShowing,
    toggleFilters,
    filters,
    handleFilterChange,
    localPagination,
    handleLocalPaginationChange,
  } = useIndexController({
    buildQuery,
    initialFilters,
    useFetchMany: useFetchManyIntegration,
  })

  const { t } = useTranslation(['integrationPage', 'common'])

  const match = useRouteMatch()

  const handleIconDigisac = (iconName) => {
    const IconOriginal = IconOptionsDigisac.find((icon) => icon.name === iconName)

    return IconOriginal?.icon ? <IconOriginal.icon fill={TextColor} width="25" height="25" /> : ''
  }

  return (
    <div>
      <Helmet title={t('TITLE_INTEGRATION')} />

      <Container>
        <div className="d-flex align-itens-center justify-content-between">
          <div className="title-page" data-testid="text-title-integration">
            <h2>{t('TITLE_INTEGRATION')}</h2>
          </div>

          <div className="d-flex">
            <IfUserCan permission="integrations.create">
              <Button background={PrimaryColor} size="xl" className="mr-2">
                <Link data-testid="integration-button-createNew" to="/integrations/create">
                  <IconDigisac.IconPlug fill="white" width="25" height="25" />
                  {t('BUTTON_NEW_INTEGRATION')}
                </Link>
              </Button>
            </IfUserCan>
            <Button
              data-testid="integration-button-filter"
              background={PrimaryColor}
              size="xl"
              className="mr-2"
              onClick={toggleFilters}
            >
              <IconDigisac.IconSearch fill="white" width="25" height="25" />
              {isFiltersShowing ? `${t('common:BUTTON_TEXT_HIDDEN')} ` : `${t('common:BUTTON_TEXT_SHOW')} `}
              {t('common:BUTTON_TEXT_FILTERS')}
            </Button>
            <ButtonRefresh
              data-testid="integration-button-refresh"
              loadIcon="sync-alt"
              color="default"
              isLoading={isLoading}
              onClick={fetch}
            >
              <Icon name="sync-alt" fixedWidth />
            </ButtonRefresh>
          </div>
        </div>

        <CardLoading isLoading={isLoading} />

        {isFiltersShowing && (
          <CardFilters>
            <Filters filters={filters} handleFilterChange={handleFilterChange} />
          </CardFilters>
        )}

        <Table className="mb-0" hover>
          <TableHead columns={5} data-testid="integration-table-titles">
            <TableColumn data-testid="integration-TitleColumn-text">{t('COLUMN_TABLE_TEXT')}</TableColumn>
            <TableColumn data-testid="integration-TitleColumn-type">{t('COLUMN_TABLE_TYPE')}</TableColumn>
            <TableColumn data-testid="integration-TitleColumn-url">{t('COLUMN_TABLE_URL')}</TableColumn>
            <TableColumn data-testid="integration-TitleColumn-icon">{t('COLUMN_TABLE_ICON')}</TableColumn>
            <TableColumn data-testid="integration-TitleColumn-actions" className="text-right">
              {t('common:ACTIONS_SUBMENU_TITLE')}
            </TableColumn>
          </TableHead>
          <TableBody data-testid="integration-tableBody-list">
            {integration &&
              integration.map((i) => (
                <TableRow cells={5} key={i.id}>
                  <TableCell>
                    <IconDigisac.IconPlug fill={PrimaryColor} width="25" height="25" className="mr-2" />
                    <Link to={`/integrations/${i.id}`}>{i.text}</Link>
                  </TableCell>
                  <TableCell>{TYPE_OPTIONS[i.type]}</TableCell>
                  <TableCell>{i.url}</TableCell>
                  <TableCell>{!!i.icon && handleIconDigisac(i.icon)}</TableCell>
                  <TableCell actions>
                    <Toggler
                      render={({ active, toggle }) => (
                        <ButtonDropdownActions group={false} isOpen={active} toggle={toggle}>
                          <TableOptions size="sm" color="primary" data-testid="integration-button-actions">
                            <IconDigisac.IconOptions className="icon-options" fill={PrimaryColor} />
                          </TableOptions>
                          <DropdownMenuActions>
                            <DropdownItemActions header>{t('common:ACTIONS_SUBMENU_TITLE')}</DropdownItemActions>
                            <Link
                              data-testid="integration-buttonAction-view"
                              to={`/integrations/${i.id}`}
                              className="dropdown-item"
                            >
                              <IconDigisac.IconShowPassword fill={TextColor} width="29" height="29" />
                              {t('common:ACTIONS_SUBMENU_VIEW')}
                            </Link>

                            <IfUserCan permission="integrations.update">
                              <Link
                                data-testid="integration-buttonAction-edit"
                                to={`/integrations/${i.id}/edit`}
                                className="dropdown-item"
                              >
                                <IconDigisac.IconEdit fill={TextColor} width="28" height="28" />
                                {t('common:ACTIONS_SUBMENU_EDIT')}
                              </Link>
                            </IfUserCan>

                            <IfUserCan permission="integrations.destroy">
                              <Link
                                data-testid="integration-buttonAction-delete"
                                to={`/integrations/${i.id}/delete`}
                                className="dropdown-item"
                              >
                                <IconDigisac.IconTrash fill={TextColor} width="28" height="28" />
                                {t('common:ACTIONS_SUBMENU_DELETE')}
                              </Link>
                            </IfUserCan>
                          </DropdownMenuActions>
                        </ButtonDropdownActions>
                      )}
                    />
                  </TableCell>
                </TableRow>
              ))}
            {integration.length < 1 && (
              <tr>
                <td colSpan={6} className="text-center">
                  {t('common:NO_RESULTS_FOUND')}
                </td>
              </tr>
            )}
          </TableBody>
        </Table>

        <TablePagination
          pagination={pagination}
          localPagination={localPagination}
          handlePaginationChange={handleLocalPaginationChange}
        />
      </Container>

      <Switch>
        <IfUserCanRoute
          permission="integrations.create"
          exact
          path={`${match.url}/create`}
          component={IntegrationFormRoute}
        />
        <IfUserCanRoute
          permission="integrations.update"
          exact
          path={`${match.url}/:id/edit`}
          component={IntegrationFormRoute}
        />
        <IfUserCanRoute
          permission="integrations.destroy"
          exact
          path={`${match.url}/:id/delete`}
          component={IntegrationDeleteRoute}
        />
        <Route path={`${match.url}/:id`} component={IntegrationShowRoute} />
      </Switch>
    </div>
  )
}

export default memo(IntegrationIndex)
