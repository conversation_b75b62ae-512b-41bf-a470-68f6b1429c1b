import React, { memo } from 'react'
import Helmet from 'react-helmet'
import { useTranslation } from 'react-i18next'
import SweetModal from '../../../../common/unconnected/SweetModal'
import { useDeleteIntegration, useFetchOneIntegration } from '../../../../../resources/integration/requests'
import useDeleteController from '../../../../../hooks/crud/useDeleteController'

function IntegrationsDelete() {
  const {
    isModalOpen,
    exit,
    model: integration,
    isLoading,
    submit,
    error,
  } = useDeleteController({
    exitToPath: '/integrations',
    useFetchOne: useFetchOneIntegration,
    useDeleteOne: useDeleteIntegration,
  })

  const { t } = useTranslation(['integrationPage', 'common'])

  const submitDelete = () => {
    submit()
    window.location.href = '/integrations'
  }

  if (!integration) return null

  return (
    <div data-testid="modal-delete">
      <Helmet title={`${t('TITLE_INTEGRATIONS')} - ${t('common:MODAL_DELETE_BUTTON_CONFIRM')} ${integration.type}?`} />

      <SweetModal
        type="warning"
        title={`${t('common:MODAL_DELETE_BUTTON_CONFIRM')} ${t('TITLE_INTEGRATIONS')}`}
        confirmBtnText={t('common:MODAL_DELETE_BUTTON_CONFIRM')}
        cancelBtnText={t('common:FORM_ACTION_CANCEL')}
        onConfirm={submitDelete}
        show={isModalOpen}
        onCancel={exit}
      >
        {t('common:MESSAGE_MODAL_DELETE')}
      </SweetModal>
    </div>
  )
}

export default memo(IntegrationsDelete)
