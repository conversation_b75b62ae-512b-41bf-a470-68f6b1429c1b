import React from 'react'
import { Col, Input, Row } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { IconPlug } from '../../../../common/unconnected/IconDigisac'
import { TextColor } from '../../../styles/colors'
import { GroupInputFilter } from '../../../styles/table'

function Filters({ filters, handleFilterChange }) {
  const { t } = useTranslation('integrationPage')

  return (
    <Row>
      <Col md="3">
        <GroupInputFilter>
          <IconPlug fill={TextColor} width="24" height="24" />
          <Input
            data-tesid="integration-Filter-inputName"
            type="text"
            value={filters.text}
            onChange={(e) => handleFilterChange('text', e.target.value)}
            placeholder={t('INPUT_SEARCH_PLACEHOLDER')}
          />
        </GroupInputFilter>
      </Col>
    </Row>
  )
}

export default Filters
