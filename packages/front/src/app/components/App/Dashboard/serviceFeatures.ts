/**
 * Map services avaialable features
 *
 * attachment - Role for send files.
 * emoji - Emoji avaialability
 * audio - Record audio
 * messageOption - Enable|Disable all Message options
 * status - status for client
 */

import { TFunction } from 'i18next'

type Service = {
  whatsapp: TService
  'whatsapp-remote': TService
  'whatsapp-business': TService
  'sms-wavy': TService
  telegram: TService
  webchat: TService
  email: TService
  'facebook-messenger': TService
  instagram: TService
  'google-business-message': TService
  'reclame-aqui': TService
}

type TService = {
  attachment: boolean
  audio: boolean
  messageOption: boolean
  messageOptionShowEmail: boolean
  messageOptionInfo: boolean
  messageOptionReply: boolean
  messageOptionReplyPrivate: boolean
  documentsSubtitles: boolean
  midiaSubtitles: boolean
  messageOptionDestroyMessage: boolean
  messageOptionForwardMessage: boolean
  emoji: boolean
  emojiReactions: boolean
  characterLeft: boolean
  createContact: boolean
  createGroup: boolean
  status: boolean
  sticker: boolean
}

export const serviceFeatures = <Service>{
  whatsapp: {
    attachment: true,
    audio: true,
    messageOption: true,
    messageOptionShowEmail: false,
    messageOptionInfo: true,
    messageOptionReply: true,
    messageOptionReplyPrivate: true,
    documentsSubtitles: false,
    midiaSubtitles: true,
    messageOptionDestroyMessage: true,
    messageOptionForwardMessage: true,
    emoji: true,
    emojiReactions: true,
    characterLeft: false,
    createContact: true,
    createGroup: true,
    status: false,
    sticker: true,
  },
  'whatsapp-remote': {
    attachment: true,
    audio: true,
    messageOption: true,
    messageOptionShowEmail: false,
    messageOptionInfo: true,
    messageOptionReply: true,
    messageOptionDestroyMessage: true,
    messageOptionForwardMessage: true,
    emoji: true,
    emojiReactions: false,
    characterLeft: false,
    createContact: true,
    createGroup: false,
    status: false,
    sticker: false,
  },
  'whatsapp-business': {
    attachment: true,
    audio: true,
    messageOption: true,
    messageOptionShowEmail: false,
    messageOptionInfo: true,
    messageOptionReply: false,
    messageOptionReplyPrivate: false,
    documentsSubtitles: false,
    midiaSubtitles: true,
    messageOptionDestroyMessage: false,
    messageOptionForwardMessage: true,
    emoji: true,
    emojiReactions: true,
    characterLeft: false,
    createContact: true,
    createGroup: false,
    status: false,
    sticker: true,
  },
  'facebook-messenger': {
    attachment: true,
    audio: true,
    messageOption: true,
    messageOptionShowEmail: false,
    messageOptionInfo: true,
    messageOptionReply: false,
    documentsSubtitles: false,
    midiaSubtitles: false,
    messageOptionDestroyMessage: false,
    messageOptionForwardMessage: true,
    emoji: true,
    emojiReactions: false, // Pode receber reações, mas não é possível enviar
    characterLeft: false,
    createContact: false,
    createGroup: false,
    status: false,
    sticker: false,
  },
  instagram: {
    attachment: true,
    audio: false,
    messageOption: true,
    messageOptionShowEmail: false,
    messageOptionInfo: true,
    messageOptionReply: false,
    documentsSubtitles: false,
    midiaSubtitles: false,
    messageOptionDestroyMessage: false,
    messageOptionForwardMessage: true,
    emoji: true,
    emojiReactions: true,
    characterLeft: false,
    createContact: false,
    createGroup: false,
    status: false,
    sticker: false,
  },
  'google-business-message': {
    attachment: true,
    audio: false,
    messageOption: true,
    messageOptionShowEmail: false,
    messageOptionInfo: true,
    messageOptionReply: false,
    documentsSubtitles: false,
    messageOptionDestroyMessage: true,
    messageOptionForwardMessage: true,
    emoji: true,
    emojiReactions: false,
    characterLeft: false,
    createContact: false,
    createGroup: false,
    status: false,
    sticker: false,
  },
  'sms-wavy': {
    attachment: false,
    audio: false,
    messageOption: false,
    messageOptionShowEmail: false,
    messageOptionInfo: true,
    documentsSubtitles: false,
    midiaSubtitles: true,
    messageOptionReply: true,
    messageOptionReplyPrivate: false,
    messageOptionDestroyMessage: false,
    messageOptionForwardMessage: true,
    emoji: false,
    emojiReactions: false,
    characterLeft: true,
    createContact: true,
    createGroup: false,
    status: false,
    sticker: false,
  },
  telegram: {
    attachment: true,
    audio: true,
    messageOption: true,
    messageOptionShowEmail: false,
    messageOptionInfo: true,
    messageOptionReply: true,
    messageOptionReplyPrivate: false,
    documentsSubtitles: true,
    midiaSubtitles: true,
    messageOptionDestroyMessage: true,
    messageOptionForwardMessage: true,
    emoji: true,
    emojiReactions: true,
    characterLeft: false,
    createContact: false,
    createGroup: false,
    status: false,
    sticker: false,
  },
  email: {
    attachment: true,
    audio: false,
    messageOption: true,
    messageOptionShowEmail: true,
    messageOptionInfo: true,
    documentsSubtitles: false,
    midiaSubtitles: false,
    messageOptionReply: false,
    messageOptionReplyPrivate: false,
    messageOptionForwardMessage: false,
    messageOptionDestroyMessage: false,
    emoji: false,
    emojiReactions: false,
    characterLeft: false,
    createContact: true,
    createGroup: false,
    status: false,
    sticker: false,
  },
  webchat: {
    attachment: true,
    audio: true,
    messageOption: true,
    messageOptionShowEmail: false,
    messageOptionInfo: true,
    messageOptionReply: true,
    messageOptionReplyPrivate: false,
    documentsSubtitles: true,
    midiaSubtitles: true,
    messageOptionDestroyMessage: false,
    messageOptionForwardMessage: true,
    emoji: true,
    emojiReactions: false,
    characterLeft: false,
    createContact: false,
    createGroup: false,
    status: true,
    sticker: false,
  },
  'reclame-aqui': {
    attachment: false,
    audio: false,
    messageOption: false,
    messageOptionShowEmail: false,
    messageOptionInfo: false,
    messageOptionReply: false,
    documentsSubtitles: true,
    midiaSubtitles: true,
    messageOptionDestroyMessage: false,
    messageOptionForwardMessage: false,
    emoji: false,
    emojiReactions: false,
    characterLeft: false,
    createContact: false,
    createGroup: false,
    status: false,
    sticker: false,
  },
}

export const servicesBy = (feature) =>
  Object.entries(serviceFeatures)
    .filter(([, val]) => val[feature])
    .map(([serviceName]) => serviceName)

export const mapWabaServiceHealthStatus = (translation: TFunction, status?: string): string => {
  if (status === 'AVAILABLE') return translation('MESSAGE_HEALTH_INFO_DESCRIPTION')
  if (status === 'LIMITED') return translation('MESSAGE_INFO_DESCRIPTION')
  if (status === 'BLOCKED') return translation('MESSAGE_BLOCK_INFO_DESCRIPTION')

  if (!status) return translation('MESSAGE_HEALTH_INFO_DESCRIPTION')
}
