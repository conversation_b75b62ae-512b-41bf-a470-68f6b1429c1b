import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, But<PERSON> } from 'reactstrap'
import { useHistory } from 'react-router-dom'
import { useTranslation } from 'react-i18next'

const PasswordExpireModal = ({ isOpen, toggle, daysLeft }) => {
  const { t } = useTranslation(['passwordExpires'])
  const history = useHistory()

  const handleExitWithoutUpdating = () => {
    toggle()
    history.goBack()
  }

  const handleUpdatePassword = () => {
    toggle()
  }

  return (
    <Modal isOpen={isOpen} toggle={toggle}>
      <ModalHeader toggle={toggle}>{t('PASSWORD_EXPIRES_ALERT_MODAL_TITLE')}</ModalHeader>
      <ModalBody>
        <p>{t('PASSWORD_EXPIRES_ALERT_MODAL_PASSWORD_WARNING', { daysLeft })}</p>
      </ModalBody>
      <ModalFooter>
        <Button color="secondary" onClick={handleExitWithoutUpdating}>
          {t('PASSWORD_EXPIRES_ALERT_EXIT_WITHOUT_UPDATING_BUTTON')}
        </Button>
        <Button color="primary" onClick={handleUpdatePassword}>
          {t('PASSWORD_EXPIRES_ALERT_UPDATE_PASSWORD_BUTTON')}
        </Button>
      </ModalFooter>
    </Modal>
  )
}

export default PasswordExpireModal
