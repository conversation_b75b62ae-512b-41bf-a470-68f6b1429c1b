import React, { memo, useCallback } from 'react'
import { useHistory } from 'react-router'
import { useDispatch } from 'react-redux'
import SweetAlert from 'react-bootstrap-sweetalert'
import { useTranslation } from 'react-i18next'
import { actions } from '../../../../modules/auth'

interface User {
  id: string
  language: string
}

interface Props {
  logout: any
  user: User
}

const PasswordExpired: React.FC<Props> = ({ user }) => {
  const { t } = useTranslation(['login'])
  const dispatch = useDispatch()
  const history = useHistory()

  const handleLogout = () => {
    if (user) {
      dispatch(actions.logout())
      return
    }
    history.push('/login')
  }

  return (
    <>
      <SweetAlert warning title={t('login:PASSWORD_EXPIRATION_TITLE')} onConfirm={handleLogout}>
        {t('login:PASSWORD_EXPIRATION_MESSAGE')}
      </SweetAlert>
    </>
  )
}

export default memo(PasswordExpired)
