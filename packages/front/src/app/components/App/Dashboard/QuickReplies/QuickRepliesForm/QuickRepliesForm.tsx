import React, { useState, useEffect } from 'react'
import Helmet from 'react-helmet'
import { Button, Form, Label } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import SweetAlert from 'react-bootstrap-sweetalert'
import { useHistory } from 'react-router-dom'
import { useRequest } from '../../../../../hooks/useRequest'
import { required } from '../../../../../utils/validator/validators'
import useFormController from '../../../../../hooks/crud/useFormController'
import InputGroup from '../../../../common/unconnected/InputGroup'
import LoadingButton from '../../../../common/unconnected/LoadingButton'
import DepartmentsSelect from '../../../../common/connected/DepartmentsSelect/DepartmentsSelectContainer'
import CategoriesSelect from '../../../../common/connected/CategoriesSelect'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>sa<PERSON><PERSON><PERSON><PERSON>, GroupInput } from '../../../styles/common'
import useEventCallback from '../../../../../hooks/useEventCallback'
import { IconClose, IconPDF } from '../../../../common/unconnected/IconDigisac'
import {
  useCreateQuickReply,
  useFetchOneQuickReply,
  useUpdateQuickReply,
} from '../../../../../resources/quickReply/requests'
import filesApi from '../../../../../resources/file/api'
import ShowFiles from './ShowFiles'
import * as S from '../styles'
import ModalExit from './ModalExit'
import toast from '../../../../../utils/toast'

export const formatToApi = (quickReply) => quickReply

export const formatFromApi = (quickReply) => quickReply

export const validFiles = ['image/png', 'image/jpeg', 'video/mp4', 'application/pdf']

const buildQuery = () => ({ include: ['departments', 'categories', 'files'] })

export default function QuickRepliesForm({
  initialModelProp = null,
  exitToPath = null,
  isModalOpenProp = null,
  handleEdit,
  handleReload,
}) {
  const { t } = useTranslation(['quickRepliesPage', 'common'])
  const requiredValidation = [required, t('common:REQUIRED_FIELD')]
  const [, createToUpload] = useRequest(filesApi.createToUpload)
  const [showError, setShowError] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [showFiles, setShowFiles] = useState(false)
  const [uploadingFiles, setUploadingFiles] = useState(false)
  const [fileIndex, setFileIndex] = useState(0)
  const [showModalExit, setShowModalExit] = useState(false)

  const validationRules = {
    text: [requiredValidation],
    title: [requiredValidation],
  }

  const initialModel = initialModelProp || {
    text: '',
    title: '',
    files: [],
    categories: [],
    departments: [],
  }

  const history = useHistory()

  const {
    submit,
    exit,
    isModalOpen,
    isEditing,
    isAlertShowing,
    closeAlert,
    isLoading,
    error,
    bindInput,
    validation,
    model,
    setModel,
  } = useFormController({
    buildQuery,
    exitToPath: exitToPath || '/quick-replies',
    initialModel,
    validationRules,
    formatToApi,
    formatFromApi,
    useCreateOne: useCreateQuickReply,
    useUpdateOne: useUpdateQuickReply,
    useFetchOne: useFetchOneQuickReply,
  })

  useEffect(() => {
    if (initialModelProp) {
      setModel(initialModelProp)
    }
  }, [initialModelProp])

  const title = `${isEditing ? t('common:LABEL_EDITING') : t('common:LABEL_CREATING')} ${t('TITLE_QUICK_REPLIES')}`

  const getErrorMessage = () => {
    if (error?.response?.status === 409) {
      return t('MESSAGE_DUPLICATE_TITLE')
    }
    return t('MODAL_MESSAGE_ERROR')
  }

  const handleChangeFiles = useEventCallback(async (e) => {
    setShowError(false)

    const newFiles = [...e?.target?.files]

    if (newFiles?.length + model?.files?.length > 10) {
      setShowError(true)
      setErrorMessage(t('MAX_FILES'))
      return
    }

    const tooLarge = newFiles?.length && newFiles.some((file) => file.size / 1024 / 1024 > 10) //10MB

    if (tooLarge) {
      setShowError(true)
      setErrorMessage(t('MAX_FILE_SIZE'))
      return
    }

    if (newFiles?.some((file) => !validFiles.includes(file?.type))) {
      setShowError(true)
      setErrorMessage(`${t('ERROR_FILE_EXTENSION')} ${t('SUPORTED_FILES')}`)
      return
    }

    const createdFiles = await Promise.all(
      newFiles?.map(async (blob) => ({
        blob: blob,
        url: URL.createObjectURL(blob),
        ...(await createToUpload({
          name: blob?.name,
          mimetype: blob?.type,
          attachedType: 'quickreply.file',
        })),
      })),
    )

    setModel({
      ...model,
      files: model?.files?.concat(createdFiles),
    })
  })

  const handleSubmit = async () => {
    setUploadingFiles(true)
    await Promise.all(
      model?.files
        ?.filter((file) => file?.urlToUpload)
        ?.map(async (file) =>
          fetch(file?.urlToUpload, {
            method: 'PUT',
            body: file?.blob,
            headers: {
              'Content-Type': file?.mimetype,
            },
          }),
        ),
    )
    setUploadingFiles(false)
    if (initialModelProp) {
      handleReload()
      handleEdit(false)
    }
    submit().then((res) => {
      if (res) {
        const message = isEditing ? t('MESSAGE_UPDATED_SUCCESSFUL') : t('MESSAGE_REGISTERED_SUCCESSFUL')
        toast.success(message)
        if (initialModelProp) {
          handleReload()
          handleEdit(false)
        }
        setTimeout(() => history.push({ pathname: exitToPath || '/quick-replies', state: { refetch: true } }), 500)
      }
    })
  }

  const removeFile = (index) => {
    setModel({ ...model, files: model?.files?.filter((_, i) => i !== index) })
  }

  const getElement = (file, key) => {
    if ((file?.blob?.type ?? file?.mimetype) === 'video/mp4') {
      return (
        <video onClick={() => handleShowFiles(key)}>
          <source src={file?.url} type="video/mp4" />
        </video>
      )
    }
    if ((file?.blob?.type ?? file?.mimetype) === 'application/pdf') {
      return (
        <div onClick={() => handleShowFiles(key)}>
          <IconPDF />
        </div>
      )
    }
    return <img src={file?.url} onClick={() => handleShowFiles(key)} />
  }

  const handleShowFiles = (key) => {
    setShowFiles(true)
    setFileIndex(key)
  }

  const handleExit = () => {
    setShowModalExit(true)
  }

  return (
    <div>
      <Helmet title={title} />

      <Form>
        <ModalDigisac
          size="mdplus"
          data-testid="quickReplies-title-create_edit"
          isOpen={isModalOpen || isModalOpenProp}
          toggle={handleExit}
          autoFocus
        >
          <ModalDigisacHeader data-testid="quickReplies-modal_header-create_edit" style={{ padding: '20px' }}>
            {title}
            <div>
              <span onClick={handleExit}>
                <IconClose fill="black" />
              </span>
            </div>
          </ModalDigisacHeader>
          <S.Body data-testid="quickReplies-modal_body-create_edit">
            <GroupInput>
              <Label>{t('TABLE_COLUMN_TITLE') + '*'}</Label>
              <InputGroup
                data-testid="quickReplies-title"
                id="title"
                type="input"
                placeholder={t('TABLE_COLUMN_TITLE')}
                {...{ bindInput, validation }}
              />
            </GroupInput>

            <GroupInput>
              <Label>{t('TABLE_COLUMN_TEXT') + '*'}</Label>
              <S.TextArea
                data-testid="quickReplies-text_area-create_edit"
                type="textarea"
                id="text"
                placeholder={t('TABLE_COLUMN_TEXT')}
                {...{ bindInput, validation }}
                rows="5"
              />
            </GroupInput>

            <GroupInput>
              <Label>{t('LABEL_CATEGORIES')}</Label>
              <CategoriesSelect
                data-testid="quickReplies-categories-create_edit"
                value={model?.categories ?? []}
                onChange={(event) => setModel({ ...model, categories: event })}
                formatCreateLabel={(value) => `+ ${t('CREATE_CATEGORY')} "${value}"`}
              />
            </GroupInput>

            <GroupInput marginTop="1rem">
              <Label>{t('TABLE_COLUMN_DEPARTMENT')}</Label>
              <DepartmentsSelect
                className="quick-reply-department-select"
                stateId="quickReplyDepartmentSelect"
                value={model?.departments ?? []}
                isMulti
                onChange={(event) => setModel({ ...model, departments: event })}
                hideArchived
              />
            </GroupInput>

            <S.Thumbnail>
              {model?.files?.map((file, key) => (
                <div className="thumbnail">
                  <div className="removeFile" onClick={() => removeFile(key)}>
                    <IconClose fill="white" width="20" height="20" viewBox="-1 0 34 34" />
                  </div>
                  {getElement(file, key)}
                </div>
              ))}
            </S.Thumbnail>

            {(model?.files?.length ?? 0) < 10 && (
              <S.AddFile>
                <label htmlFor="inputfile">
                  <input
                    id="inputfile"
                    name="inputfile"
                    type="file"
                    multiple
                    accept={validFiles.join(', ')}
                    onChange={handleChangeFiles}
                    style={{ display: 'none' }}
                    data-testid="form-input-choose_file"
                  />
                  <div>+ {t('BUTTON_ADD_FILES')}</div>
                </label>
              </S.AddFile>
            )}

            {showError && <S.Error>{errorMessage}</S.Error>}

            <S.DivTypeFiles>
              {t('SUPORTED_FILES')}
              <br />
              <div>{t('SUPORTED_FILES_SIZE')}</div>
            </S.DivTypeFiles>
          </S.Body>
          <S.Footer data-testid="quickReplies-modal_footer-create_edit">
            <Button data-testid="quickReplies-button-cancel" className="cancel" type="button" onClick={handleExit}>
              {t('BUTTON_DISCARD')}
            </Button>
            <LoadingButton
              data-testid="quickReplies-button-save"
              className="confirm"
              type="button"
              disabled={isLoading || uploadingFiles}
              onClick={handleSubmit}
              isLoading={isLoading || uploadingFiles}
            >
              {t('common:FORM_ACTION_SAVE')}
            </LoadingButton>
          </S.Footer>
        </ModalDigisac>
      </Form>
      {isAlertShowing && (
        <SweetAlert type="warning" error title={t('common:MESSAGE_ATTENTION')} onConfirm={closeAlert}>
          {getErrorMessage()}
        </SweetAlert>
      )}
      <ShowFiles model={model} isOpen={showFiles} setShowFiles={setShowFiles} index={fileIndex} />
      <ModalExit
        showModalExit={showModalExit}
        setShowModalExit={setShowModalExit}
        handleEdit={handleEdit}
        initialModelProp={initialModelProp}
        exit={exit}
      />
    </div>
  )
}
