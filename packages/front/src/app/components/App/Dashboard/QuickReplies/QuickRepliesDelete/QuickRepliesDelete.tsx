import React, { Component } from 'react'
import Helmet from 'react-helmet'
import { Redirect } from 'react-router-dom'
import { withTranslation } from 'react-i18next'
import toast from '../../../../../utils/toast'
import SweetModal from '../../../../common/unconnected/SweetModal'

class QuickRepliesDelete extends Component {
  constructor(props) {
    super(props)

    this.state = {
      submitted: false,
      redirect: false,
      isModalShowing: true,
      quickReply: props.quickReply,
    }

    this.fetch = this.fetch.bind(this)
    this.handleSubmit = this.handleSubmit.bind(this)
    this.close = this.close.bind(this)
  }

  UNSAFE_componentWillReceiveProps(newProps) {
    const { isLoading, error } = newProps

    if (this.state.submitted && !isLoading && !error) {
      this.close()
    }

    if (newProps.quickReply && newProps.quickReply !== this.state.quickReply) {
      this.setState({ quickReply: newProps.quickReply })
    }
  }

  fetch() {
    const {
      fetchOneQuickReply,
      match: {
        params: { id },
      },
    } = this.props

    fetchOneQuickReply({ id })
  }

  handleSubmit() {
    const { deleteOneQuickReply, quickReply } = this.props

    this.setState({
      submitted: true,
    })

    deleteOneQuickReply({ quickReply })
    const { t } = this.props
    toast.success(t('MESSAGE_QUICK_REPLY_DELETED'))
    this.close()
  }

  close() {
    this.setState({
      isModalShowing: false,
    })

    setTimeout(() => this.setState({ redirect: true }), 300)
  }

  render() {
    const { isModalShowing, redirect, quickReply } = this.state
    const { t } = this.props

    return (
      <div data-testid="quickReplies-modal-delete_quickReply">
        <Helmet
          title={`${t('TITLE_QUICK_REPLIES')} - ${t('common:MODAL_DELETE_BUTTON_CONFIRM')} ${quickReply.text}?`}
        />

        {redirect && <Redirect to={{ pathname: '/quick-replies', state: { refetch: true } }} />}

        <SweetModal
          type="warning"
          title={`${t('TITLE_QUICK_REPLY_DELETE')}`}
          confirmBtnText={t('common:MODAL_DELETE_BUTTON_CONFIRM')}
          cancelBtnText={t('common:FORM_ACTION_CANCEL')}
          show={isModalShowing}
          onCancel={this.close}
          onConfirm={this.handleSubmit}
        >
          {t('MESSAGE_MODAL_DELETE')}
        </SweetModal>
      </div>
    )
  }
}

export default withTranslation(['quickRepliesPage', 'common'])(QuickRepliesDelete)
