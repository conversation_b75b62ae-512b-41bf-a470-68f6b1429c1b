import React from 'react'
import { Col, Input, Row } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { IconTrigger } from '../../../../common/unconnected/IconDigisac'
import { TextColor } from '../../../styles/colors'
import { GroupInputFilter } from '../../../styles/table'

function Filters({ filters, handleFilterChange }) {
  const { t } = useTranslation(['quickRepliesPage'])
  return (
    <Row>
      <Col md="3">
        <GroupInputFilter>
          <IconTrigger fill={TextColor} width="24" height="24" />
          <Input
            data-testid="quickReplies-input_filter-text_quickReply"
            type="text"
            value={filters.text}
            onChange={(e) => handleFilterChange('text', e.target.value)}
            placeholder={t('INPUT_SEARCH_PLACEHOLDER')}
          />
        </GroupInputFilter>
      </Col>
    </Row>
  )
}

export default Filters
