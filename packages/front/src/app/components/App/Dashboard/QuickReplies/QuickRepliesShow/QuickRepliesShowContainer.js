import { connect } from 'react-redux'
import { withRouter } from 'react-router-dom'
import { selectors } from '../../../../../modules/quickReplies'
import { actions } from '../../../../../modules/quickReplies/modules/show'
import { actions as actionsDelete } from '../../../../../modules/quickReplies/modules/delete'
import QuickRepliesShow from './QuickRepliesShow'

const mapStateToProps = (
  state,
  {
    match: {
      params: { id },
    },
  },
) => ({
  quickReply: selectors.getById(state, id),
})

const actionCreators = {
  fetchOneQuickReply: actions.fetchOne,
  deleteOneQuickReply: actionsDelete.deleteOne,
}

export default withRouter(connect(mapStateToProps, actionCreators)(QuickRepliesShow))
