import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Table,
  TableHead,
  TableBody,
  TableColumn,
  TableRow,
  TableCell,
  TableOptions,
  ButtonDropdownActions,
  DropdownMenuActions,
} from '../../../../styles/table'
import { useFetchManyCategories } from '../../../../../../resources/categories/requests'
import TablePagination from '../../../../../common/unconnected/TablePagination'
import useIndexController from '../../../../../../hooks/crud/useIndexController'
import { IconOptions, IconEdit, IconTrash } from '../../../../../common/unconnected/IconDigisac'
import { PrimaryColor, TextColor } from '../../../../styles/colors'
import IfUserCan from '../../../../../common/connected/IfUserCan'
import Toggler from '../../../../../common/unconnected/Toggler'
import CategoriesForm from './CategoriesForm'
import CategoriesDelete from './CategoriesDelete'

export default function CategoriesTable({
  fetchCategories,
  toggleFetchCategories,
  filters,
  show,
  showDelete,
  toggle,
  toggleDelete,
}) {
  const [category, setCategory] = useState(null)

  useEffect(() => {
    handleFilterChange('title', filters?.title)
  }, [filters])

  useEffect(() => {
    if (fetchCategories) {
      handleFilterChange('title', filters?.title)
      toggleFetchCategories()
    }
  }, [fetchCategories])

  const { t } = useTranslation(['quickRepliesPage', 'common'])

  const initialFilters = { title: '' }

  const buildQuery = ({ filters, localPagination }) => ({
    order: [['title', 'ASC']],
    ...(filters?.title && { where: { title: { $iLike: `%${filters?.title}%` } } }),
    page: localPagination.page,
    perPage: localPagination.perPage,
  })

  const {
    models: categories,
    pagination,
    handleFilterChange,
    localPagination,
    handleLocalPaginationChange,
  } = useIndexController({
    buildQuery,
    initialFilters,
    useFetchMany: useFetchManyCategories,
  })

  const handleSubmitForm = (isDelete = false) => {
    !isDelete ? toggle() : toggleDelete()
    setCategory(null)
    handleFilterChange('title', filters?.title)
  }

  return (
    <>
      <Table data-testid="categories-table" className="mb-0">
        <TableHead columns={2}>
          <TableColumn data-testid="categories-label-title">{t('common:LABEL_TITLE')}</TableColumn>
          <TableColumn data-testid="categories-label-actions" className="text-right justify-content-end pr-4">
            {t('common:ACTIONS_SUBMENU_TITLE')}
          </TableColumn>
        </TableHead>
        <TableBody>
          {categories &&
            categories.map((category) => (
              <TableRow cells={2} key={category.id}>
                <TableCell>{category.title}</TableCell>

                <TableCell actions>
                  <Toggler
                    render={({ active, toggle: toggleActions }) => (
                      <ButtonDropdownActions group={false} isOpen={active} toggle={toggleActions}>
                        <TableOptions data-testid="categories-button-actions" size="sm" color="primary">
                          <IconOptions className="icon-options" fill={PrimaryColor} />
                        </TableOptions>
                        <DropdownMenuActions>
                          <IfUserCan permission="quickReplies.update">
                            <a
                              data-testid="categories-button-submenu_edit"
                              href="#"
                              className="dropdown-item"
                              onClick={() => {
                                setCategory(category)
                                toggle()
                                toggleActions()
                              }}
                            >
                              <IconEdit fill={TextColor} width="28" height="28" />
                              {t('common:ACTIONS_SUBMENU_EDIT')}
                            </a>
                          </IfUserCan>
                          <IfUserCan permission="quickReplies.destroy">
                            <a
                              data-testid="categories-button-submenu_delete"
                              href="#"
                              className="dropdown-item"
                              style={{ color: '#DB2727' }}
                              onClick={() => {
                                setCategory(category)
                                toggleDelete()
                                toggleActions()
                              }}
                            >
                              <IconTrash fill="#DB2727" width="28" height="28" />
                              {t('common:ACTIONS_SUBMENU_DELETE')}
                            </a>
                          </IfUserCan>
                        </DropdownMenuActions>
                      </ButtonDropdownActions>
                    )}
                  />
                </TableCell>
              </TableRow>
            ))}
          {categories?.length < 1 && (
            <TableRow>
              <TableCell className="text-center">{t('common:NO_RESULTS_FOUND')}</TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <TablePagination
        pagination={pagination}
        localPagination={localPagination}
        handlePaginationChange={handleLocalPaginationChange}
      />

      <CategoriesForm category={category} show={show} toggle={toggle} handleSubmitForm={handleSubmitForm} />

      <CategoriesDelete
        category={category}
        show={showDelete}
        toggle={toggleDelete}
        handleDeleteForm={handleSubmitForm}
      />
    </>
  )
}
