import identity from 'lodash/identity'
import { pickBy, debounce } from 'lodash'
import React from 'react'
import Helmet from 'react-helmet'
import { Link, Switch, Route } from 'react-router-dom'
import { withTranslation } from 'react-i18next'
import { ButtonGroup } from 'reactstrap'
import CardLoading from '../../../../common/unconnected/CardLoading'
import IfUserCan from '../../../../common/connected/IfUserCan'
import IfUserCanRoute from '../../../../common/connected/IfUserCanRoute'
import TablePagination from '../../../../common/unconnected/TablePagination'
import Icon from '../../../../common/unconnected/Icon'
import QuickRepliesDeleteRoute from '../QuickRepliesDelete'
import QuickRepliesFormRoute from '../QuickRepliesForm'
import QuickRepliesShowRoute from '../QuickRepliesShow'
import Filters from './Filters'
import CategoriesFilters from './Categories/CategoriesFilters'
import Join from '../../../../common/connected/Join'
import Button from '../../../../common/unconnected/Button'
import {
  Table,
  TableHead,
  TableBody,
  TableColumn,
  TableRow,
  TableCell,
  TableOptions,
  ButtonRefresh,
  ButtonDropdownActions,
  DropdownItemActions,
  DropdownMenuActions,
  CardFilters,
} from '../../../styles/table'
import {
  IconSearch,
  IconOptions,
  IconTrash,
  IconEdit,
  IconTrigger,
  IconShowPassword as IconEyes,
} from '../../../../common/unconnected/IconDigisac'
import { PrimaryColor, TextColor } from '../../../styles/colors'
import Container from '../../../styles/container'
import * as S from '../styles'
import CategoriesTable from './Categories/CategoriesTable'

class QuickRepliesIndex extends React.Component {
  constructor(props) {
    super(props)

    this.state = {
      filters: {
        text: '',
      },
      pagination: {
        page: 1,
        perPage: 15,
      },
      dropdowns: {},
      isFiltersShowing: false,
      tab: 'replies',
      isCategoryFormShowing: false,
      isCategoryDeleteShowing: false,
      fetchCategories: false,
    }

    this.fetch = debounce(this.fetch.bind(this), 400)
    this.handleFilterChange = this.handleFilterChange.bind(this)
    this.handlePaginationChange = this.handlePaginationChange.bind(this)
    this.toggleDropdown = this.toggleDropdown.bind(this)
    this.toggleFormCategory = this.toggleFormCategory.bind(this)
    this.toggleDeleteCategory = this.toggleDeleteCategory.bind(this)
    this.toggleFetchCategories = this.toggleFetchCategories.bind(this)
    this.toggleFilters = this.toggleFilters.bind(this)
  }

  componentDidMount() {
    this.initialFetch()
  }

  UNSAFE_componentWillReceiveProps(newProps) {
    const currentLocationState = this.props.location.state || {}
    const newLocationState = newProps.location.state || {}

    if (!currentLocationState.refetch && newLocationState.refetch) {
      this.fetch()
      this.props.history.replace({ state: { refetch: false } })
    }
  }

  fetch() {
    const { fetchManyQuickReplies } = this.props
    const { pagination, filters } = this.state

    const filter = filters.text
      ? {
          $or: [
            { text: filters.text && { $iLike: `%${filters.text}%` } },
            { title: filters.text && { $iLike: `%${filters.text}%` } },
          ],
        }
      : {}

    const query = pickBy(
      {
        attributes: ['id', 'text', 'title'],
        where: filter,
        include: [
          {
            model: 'departments',
            attributes: ['id', 'name'],
          },
        ],
        page: pagination.page,
        perPage: pagination.perPage,
      },
      identity,
    )

    fetchManyQuickReplies({ query })

    this.toggleFetchCategories()
  }

  initialFetch() {
    const { isLoading } = this.props

    if (!isLoading) return this.fetch()
  }

  handleFilterChange(key, value) {
    this.setState(
      (prevState) => ({
        filters: {
          ...prevState.filters,
          [key]: value,
        },
      }),
      this.fetch,
    )
  }

  handleTabChange(value) {
    this.setState((prevState) => ({
      ...prevState,
      tab: value,
    }))
  }

  handlePaginationChange(key, value) {
    this.setState(
      (prevState) => ({
        pagination: {
          ...prevState.pagination,
          [key]: value,
        },
      }),
      this.fetch,
    )
  }

  toggleDropdown(id) {
    this.setState((prevState) => ({
      dropdowns: {
        ...prevState,
        [id]: !prevState.dropdowns[id],
      },
    }))
  }

  toggleFilters() {
    this.setState((prevState) => ({
      isFiltersShowing: !prevState.isFiltersShowing,
    }))
  }

  toggleFormCategory() {
    this.setState((prevState) => ({
      isCategoryFormShowing: !prevState.isCategoryFormShowing,
    }))
  }

  toggleDeleteCategory() {
    this.setState((prevState) => ({
      isCategoryDeleteShowing: !prevState.isCategoryDeleteShowing,
    }))
  }

  toggleFetchCategories() {
    this.setState((prevState) => ({
      fetchCategories: !prevState.fetchCategories,
    }))
  }

  render() {
    const { quickReplies, pagination, match, isLoading, t } = this.props
    const { filters, isFiltersShowing, tab, isCategoryFormShowing, isCategoryDeleteShowing, fetchCategories } =
      this.state

    return (
      <div>
        <Helmet data-testid="quickReplies-label-title" title={t('TITLE_QUICK_REPLIES')} />

        <Container>
          <div className="d-flex justify-content-between" style={{ alignItems: 'flex-start' }}>
            <div className="title-page">
              <h2 data-testid="quickReplies-label-title">{t('TITLE_QUICK_REPLIES')}</h2>
            </div>
          </div>
          <div className="d-flex justify-content-between" style={{ alignItems: 'flex-start' }}>
            <div
              className="d-flex align-items-center justify-content-between"
              style={{ marginTop: '20px', width: '100%' }}
            >
              <IfUserCan permission="quickReplies.create">
                <S.WrapperStatus>
                  <ButtonGroup>
                    <S.ButtonStatus isActive={tab === 'replies'} onClick={() => this.handleTabChange('replies')}>
                      {t('TITLE_QUICK_REPLIES')}
                    </S.ButtonStatus>
                    <S.ButtonStatus isActive={tab === 'categories'} onClick={() => this.handleTabChange('categories')}>
                      {t('TABLE_COLUMN_CATEGORIES')}
                    </S.ButtonStatus>
                  </ButtonGroup>
                </S.WrapperStatus>
              </IfUserCan>
              <div className="d-flex align-items-center">
                {tab === 'replies' && (
                  <IfUserCan permission="quickReplies.create">
                    <Button background={PrimaryColor} size="xl" className="mr-2">
                      <Link data-testid="quickReplies-button-create_quickReplies" to="/quick-replies/create">
                        <IconTrigger fill="white" width="25" height="25" />
                        {t('NEW_TEXT_REPLY')}
                      </Link>
                    </Button>
                  </IfUserCan>
                )}

                {tab === 'categories' && (
                  <IfUserCan permission="quickReplies.create">
                    <Button background={PrimaryColor} size="xl" className="mr-2" onClick={this.toggleFormCategory}>
                      <a data-testid="quickReplies-button-create_categories" href="#">
                        {t('NEW_CATEGORY')}
                      </a>
                    </Button>
                  </IfUserCan>
                )}

                <Button
                  data-testid="quickReplies-header-filters_actived"
                  background={PrimaryColor}
                  size="xl"
                  onClick={this.toggleFilters}
                  className="mr-2"
                >
                  <IconSearch fill="white" width="25" height="25" />
                  {isFiltersShowing ? `${t('common:BUTTON_TEXT_HIDDEN')} ` : `${t('common:BUTTON_TEXT_SHOW')} `}
                  {t('common:BUTTON_TEXT_FILTERS')}
                </Button>

                <ButtonRefresh
                  data-testid="quickReplies-button-refresh"
                  loadIcon="sync-alt"
                  color="default"
                  isLoading={this.props.isLoading}
                  onClick={this.fetch}
                >
                  <Icon name="sync-alt" fixedWidth />
                </ButtonRefresh>
              </div>
            </div>
          </div>

          <CardLoading isLoading={isLoading} />

          {tab === 'replies' && isFiltersShowing && (
            <CardFilters>
              <Filters filters={filters} handleFilterChange={this.handleFilterChange} />
            </CardFilters>
          )}

          {tab === 'categories' && isFiltersShowing && (
            <CardFilters>
              <CategoriesFilters filters={filters} handleFilterChange={this.handleFilterChange} />
            </CardFilters>
          )}

          {tab === 'replies' && (
            <>
              <Table data-testid="quickReplies-table" className="mb-0">
                <TableHead columns={4}>
                  <TableColumn data-testid="quickReplies-label-title">{t('TABLE_COLUMN_TITLE')}</TableColumn>
                  <TableColumn data-testid="quickReplies-label-text">{t('TABLE_COLUMN_TEXT')}</TableColumn>
                  <TableColumn data-testid="quickReplies-label-departments">{t('TABLE_COLUMN_DEPARTMENT')}</TableColumn>
                  <TableColumn data-testid="quickReplies-label-actions" className="text-right justify-content-end pr-4">
                    {t('common:ACTIONS_SUBMENU_TITLE')}
                  </TableColumn>
                </TableHead>
                <TableBody>
                  {quickReplies &&
                    quickReplies.map((quickReply) => (
                      <TableRow cells={4} key={quickReply.id}>
                        <TableCell>
                          <Link data-testid="quickReplies-link-quickReply-title" to={`/quick-replies/${quickReply.id}`}>
                            {quickReply.title}
                          </Link>
                        </TableCell>

                        <TableCell>
                          <Link data-testid="quickReplies-link-quickReply-text" to={`/quick-replies/${quickReply.id}`}>
                            {quickReply.text}
                          </Link>
                        </TableCell>

                        <TableCell>
                          <Join
                            items={quickReply.departments}
                            render={(departments) => (
                              <Link data-testid="quickReplies-link-department" to={`/departments/${departments.id}`}>
                                {departments.name}
                              </Link>
                            )}
                          />
                        </TableCell>

                        <TableCell actions>
                          <ButtonDropdownActions
                            group={false}
                            isOpen={this.state.dropdowns[quickReply.id]}
                            toggle={() => this.toggleDropdown(quickReply.id)}
                          >
                            <TableOptions data-testid="quickReplies-button-actions" size="sm" color="primary">
                              <IconOptions className="icon-options" fill={PrimaryColor} />
                            </TableOptions>
                            <DropdownMenuActions>
                              <DropdownItemActions header>{t('common:ACTIONS_SUBMENU_TITLE')}</DropdownItemActions>
                              <Link
                                data-testid="quickReplies-button-submenu_view"
                                to={`/quick-replies/${quickReply.id}`}
                                className="dropdown-item"
                              >
                                <IconEyes fill={TextColor} width="29" height="29" />
                                {t('common:ACTIONS_SUBMENU_VIEW')}
                              </Link>

                              <IfUserCan permission="quickReplies.update">
                                <Link
                                  data-testid="quickReplies-button-submenu_edit"
                                  to={`/quick-replies/${quickReply.id}/edit`}
                                  className="dropdown-item"
                                >
                                  <IconEdit fill={TextColor} width="28" height="28" />
                                  {t('common:ACTIONS_SUBMENU_EDIT')}
                                </Link>
                              </IfUserCan>

                              <IfUserCan permission="quickReplies.destroy">
                                <Link
                                  data-testid="quickReplies-button-submenu_delete"
                                  to={`/quick-replies/${quickReply.id}/delete`}
                                  className="dropdown-item"
                                >
                                  <IconTrash fill={TextColor} width="28" height="28" />
                                  {t('common:ACTIONS_SUBMENU_DELETE')}
                                </Link>
                              </IfUserCan>
                            </DropdownMenuActions>
                          </ButtonDropdownActions>
                        </TableCell>
                      </TableRow>
                    ))}
                  {quickReplies?.length < 1 && (
                    <TableRow>
                      <TableCell className="text-center">{t('common:NO_RESULTS_FOUND')}</TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>

              <TablePagination
                pagination={pagination}
                localPagination={this.state.pagination}
                handlePaginationChange={this.handlePaginationChange}
              />
            </>
          )}

          {tab === 'categories' && (
            <CategoriesTable
              fetchCategories={fetchCategories}
              toggleFetchCategories={this.toggleFetchCategories}
              filters={filters}
              show={isCategoryFormShowing}
              showDelete={isCategoryDeleteShowing}
              toggle={this.toggleFormCategory}
              toggleDelete={this.toggleDeleteCategory}
            />
          )}
        </Container>

        <Switch>
          <IfUserCanRoute
            permission="quickReplies.create"
            exact
            path={`${match.url}/create`}
            component={QuickRepliesFormRoute}
          />
          <IfUserCanRoute
            permission="quickReplies.update"
            exact
            path={`${match.url}/:id/edit`}
            component={QuickRepliesFormRoute}
          />
          <IfUserCanRoute
            permission="quickReplies.destroy"
            exact
            path={`${match.url}/:id/delete`}
            component={QuickRepliesDeleteRoute}
          />
          <Route path={`${match.url}/:id`} component={QuickRepliesShowRoute} />
        </Switch>
      </div>
    )
  }
}

export default withTranslation(['quickRepliesPage', 'common'])(QuickRepliesIndex)
