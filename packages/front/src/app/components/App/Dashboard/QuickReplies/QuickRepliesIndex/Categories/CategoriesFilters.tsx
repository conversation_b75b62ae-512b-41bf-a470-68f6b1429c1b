import React from 'react'
import { Col, Row } from 'reactstrap'
import { Input } from '../../../../../common/unconnected/ui/input'
import { useTranslation } from 'react-i18next'

function CategoriesFilters({ filters, handleFilterChange }) {
  const { t } = useTranslation(['quickRepliesPage'])
  return (
    <Row>
      <Col md="3">
        <Input
          data-testid="quickReplies-input_filter-title-category"
          type="text"
          value={filters.title}
          onChange={(e) => handleFilterChange('title', e.target.value)}
          placeholder={t('INPUT_SEARCH_CATEGORY_PLACEHOLDER')}
          style={{ padding: '10px' }}
        />
      </Col>
    </Row>
  )
}

export default CategoriesFilters
