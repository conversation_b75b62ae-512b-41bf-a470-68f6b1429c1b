import React, { Component } from 'react'
import Helmet from 'react-helmet'
import { Card, CardBody } from 'reactstrap'
import QuickRepliesShowBody from './QuickRepliesShowBody'

class QuickRepliesShow extends Component {
  UNSAFE_componentWillMount() {
    this.fetch()
  }

  fetch() {
    const {
      fetchOneQuickReply,
      match: {
        params: { id },
      },
    } = this.props

    fetchOneQuickReply({ id })
  }

  render() {
    const { quickReply } = this.props

    return (
      <div>
        <Helmet title="Resposta Rápida" />

        <div className="container mt-4 mb-4">
          <Card>
            <CardBody>
              <QuickRepliesShowBody quickReply={quickReply} />
            </CardBody>
          </Card>
        </div>
      </div>
    )
  }
}

export default QuickRepliesShow
