import { connect } from 'react-redux'
import { withRouter } from 'react-router-dom'
import { actions, selectors } from '../../../../../modules/quickReplies/modules/list'
import QuickRepliesIndex from './QuickRepliesIndex'

const mapStateToProps = (state) => ({
  quickReplies: selectors.getCurrentPageQuickReplies(state),
  pagination: selectors.getPagination(state),
  isLoading: selectors.getIsLoading(state),
  error: selectors.getError(state),
})

const actionCreators = {
  fetchManyQuickReplies: actions.fetchMany,
}

export default withRouter(connect(mapStateToProps, actionCreators)(QuickRepliesIndex))
