import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { IconPDF, IconArrowOrderUp } from '../../../../common/unconnected/IconDigisac'
import * as S from '../styles'

export default function ShowFiles({ model, isOpen, setShowFiles, index, currentContact }) {
  const { t } = useTranslation(['quickRepliesPage'])
  const [files, setFiles] = useState([])

  useEffect(() => {
    setSelectedIndex(index)
  }, [index])

  useEffect(() => {
    if (model?.files?.length > 0) {
      const filteredFiles = model?.files?.filter((file) => {
        if (!currentContact) return true

        return !(
          ['instagram', 'facebook-messenger'].includes(currentContact?.service?.type) &&
          ['video/mp4', 'application/pdf'].includes(file?.mimetype)
        )
      })
      setFiles(filteredFiles)
    }
    setSelectedIndex(0)
  }, [currentContact, model])

  const [selectedIndex, setSelectedIndex] = useState(index)

  const getElement = (file) => {
    if ((file?.blob?.type ?? file?.mimetype) === 'video/mp4') {
      return <video src={file?.url} controls />
    }
    if ((file?.blob?.type ?? file?.mimetype) === 'application/pdf') {
      return (
        <S.DivPdf>
          <div className="flexColumn">
            <div>
              <IconPDF fill="black" width={'25'} height={'30'} />
            </div>
          </div>
          <div className="flexColumn">{file?.name}</div>
          <br />
          <div className="flexColumn error">{t('FILE_SHOW_ERROR')}</div>
        </S.DivPdf>
      )
    }
    return <img src={file?.url} />
  }

  const handleChangeIndex = (value) => {
    if (!files?.[selectedIndex + value]) return
    setSelectedIndex(selectedIndex + value)
  }

  return (
    <S.ModalDigisacSlide
      size="mdplus"
      data-testid="quickReplies-title-create_edit"
      isOpen={isOpen}
      toggle={() => setShowFiles(!isOpen)}
      autoFocus
    >
      <S.ButtonChangeFile className="prevButton" onClick={() => handleChangeIndex(-1)}>
        <IconArrowOrderUp fill="#353535" />
      </S.ButtonChangeFile>
      {getElement(files?.[selectedIndex])}
      <div className="divCloseButton">
        <S.ButtonClose onClick={() => setShowFiles(false)}>{t('BUTTON_CLOSE_FILES')}</S.ButtonClose>
      </div>
      <S.ButtonChangeFile className="nextButton" onClick={() => handleChangeIndex(1)}>
        <IconArrowOrderUp fill="#353535" />
      </S.ButtonChangeFile>
    </S.ModalDigisacSlide>
  )
}
