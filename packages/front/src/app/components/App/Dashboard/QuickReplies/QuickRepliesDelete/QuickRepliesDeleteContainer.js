import { connect } from 'react-redux'
import { withRouter } from 'react-router-dom'
import { selectors } from '../../../../../modules/quickReplies'
import { actions, selectors as deleteUiSelectors } from '../../../../../modules/quickReplies/modules/delete'
import { actions as showActions } from '../../../../../modules/quickReplies/modules/show'
import QuickRepliesDelete from './QuickRepliesDelete'

const mapStateToProps = (
  state,
  {
    match: {
      params: { id },
    },
  },
) => ({
  quickReply: selectors.getById(state, id),
  isLoading: deleteUiSelectors.getIsLoading(state),
  error: deleteUiSelectors.getError(state),
})

const actionCreators = {
  fetchOneQuickReply: showActions.fetchOne,
  deleteOneQuickReply: actions.deleteOne,
}

export default withRouter(connect(mapStateToProps, actionCreators)(QuickRepliesDelete))
