import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Input } from '../../../../../common/unconnected/ui/input'
import { Label } from '../../../../../common/unconnected/ui/label'
import { Button } from '../../../../../common/unconnected/ui/button'
import { useRequest } from '../../../../../../hooks/useRequest'
import categoryApi from '../../../../../../resources/categories/api'
import toast from '../../../../../../utils/toast'
import { ModalDigisac, ModalDigisacHeader } from '../../../../styles/common'
import { IconClose } from '../../../../../common/unconnected/IconDigisac'
import * as S from '../../styles'

export default function CategoriesForm({ category, show, toggle, handleSubmitForm }) {
  const [title, setTitle] = useState('')

  const { t } = useTranslation(['quickRepliesPage', 'common'])

  const [{ isLoading: isLoadingCreate }, create] = useRequest(categoryApi.create)
  const [{ isLoading: isLoadingUpdate }, updateById] = useRequest(categoryApi.updateById)

  useEffect(() => {
    setTitle(category?.title)
  }, [category])

  useEffect(() => {
    if (!show) {
      setTitle('')
    }
  }, [show])

  const handleSubmit = () => {
    if (!title) {
      return toast.warn(t('CATEGORY_NAME_REQUIRED'))
    }

    const handleSuccess = () => {
      toast.success(t('CATEGORY_SAVED_SUCCESS'))
      handleSubmitForm()
    }

    const handleError = (err) => {
      if (err?.response?.status === 409) {
        toast.error(t('CATEGORY_NAME_ALREADY_EXIST'))
      } else {
        toast.error(t('CATEGORY_SAVE_ERROR'))
      }
    }

    const saveCategory = category?.id ? updateById(category.id, { title }) : create({ title })

    saveCategory.then(handleSuccess).catch(handleError)
  }

  const titleForm = !category ? t('CREATE_CATEGORY') : t('UPDATE_CATEGORY')

  return (
    <ModalDigisac size="mdplus" isOpen={show} toggle={toggle} autoFocus>
      <ModalDigisacHeader style={{ padding: '20px' }}>
        {titleForm}
        <div>
          <span onClick={toggle}>
            <IconClose fill="black" />
          </span>
        </div>
      </ModalDigisacHeader>
      <S.Body>
        <Label style={{ marginBottom: '8px', fontWeight: '400', color: 'rgb(60, 73, 101)' }} htmlFor="bot-name">
          {t('CATEGORY_NAME')}
        </Label>
        <Input value={title} onChange={(e) => setTitle(e.target.value)} id="title" type="text" />
      </S.Body>
      <S.Footer>
        <Button className="cancel" type="button" onClick={toggle}>
          {t('BUTTON_DISCARD')}
        </Button>
        <Button className="confirm" type="button" onClick={handleSubmit}>
          {t('common:FORM_ACTION_SAVE')}
        </Button>
      </S.Footer>
    </ModalDigisac>
  )
}
