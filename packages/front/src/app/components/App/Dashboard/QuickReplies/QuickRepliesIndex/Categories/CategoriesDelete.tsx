import React from 'react'
import { useTranslation } from 'react-i18next'
import {
  <PERSON><PERSON>,
  DialogFooter,
  DialogClose,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '../../../../../common/unconnected/ui/dialog'
import { Button } from '../../../../../common/unconnected/ui/button'
import { useRequest } from '../../../../../../hooks/useRequest'
import categoryApi from '../../../../../../resources/categories/api'
import toast from '../../../../../../utils/toast'
import { TriangleAlert } from 'lucide-react'

export default function CategoriesDelete({ category, show, toggle, handleDeleteForm }) {
  const { t } = useTranslation(['quickRepliesPage', 'common'])

  const [{ isLoading }, deleteById] = useRequest(categoryApi.deleteById)

  const handleDelete = () => {
    const handleSuccess = () => {
      toast.success(t('CATEGORY_DELETED_SUCCESS'))
      handleDeleteForm(true)
    }

    const handleError = () => {
      toast.error(t('CATEGORY_DELETE_ERROR'))
    }

    deleteById(category.id).then(handleSuccess).catch(handleError)
  }

  return (
    <Dialog open={show} onOpenChange={toggle}>
      <DialogContent>
        <DialogHeader style={{ marginBottom: '30px' }}>
          <TriangleAlert style={{ color: '#B81D1D', width: '40px', height: '40px' }} />
          <DialogTitle>{t('TITLE_MODAL_DELETE_CATEGORY')}</DialogTitle>
        </DialogHeader>
        <main
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            textAlign: 'center',
            fontWeight: 400,
            fontSize: '16px',
            color: '#586171',
            lineHeight: '24px',
          }}
        >
          <p style={{ margin: 0 }}>{t('ANSWER_DELETE_CATEGORY')}</p>
          <p style={{ margin: 0 }}>{t('common:LABEL_DISCARD_CHANGES_ACTION')}</p>
        </main>
        <DialogFooter style={{ marginTop: '40px' }}>
          <DialogClose asChild>
            <Button
              type="button"
              style={{
                width: '100%',
              }}
              variant="outline"
            >
              {t('common:FORM_ACTION_CANCEL')}
            </Button>
          </DialogClose>
          <Button
            style={{
              width: '100%',
            }}
            onClick={handleDelete}
            disabled={isLoading}
            variant="destructive"
          >
            {t('common:ACTIONS_SUBMENU_DELETE')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
