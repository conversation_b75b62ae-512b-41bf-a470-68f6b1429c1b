import styled from 'styled-components'
import { ModalBody, DropdownMenu } from 'reactstrap'

import { ModalFooter } from '../../styles/common'
import { ModalDigisac } from '../../styles/common'
import InputGroup from '../../../common/unconnected/InputGroup'

export const Body = styled(ModalBody)<ModalBody>`
  padding: 20px;
`

export const Footer = styled(ModalFooter)<typeof ModalFooter>`
  Button {
    width: 100%;
  }

  .cancel {
    background: none;
    border: 1px solid #324b7d;
    color: #324b7d;
  }

  padding: 20px;
`

export const AddFile = styled.div`
  div {
    margin-top: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    color: #324b7d;
  }
`

export const DivTypeFiles = styled.div`
  margin-top: 20px;
  font-weight: 500;
  font-size: 12px;
  color: #6e7a89;

  div {
    margin-top: 5px;
  }
`

export const Thumbnail = styled.div`
  display: flex;
  margin-top: 15px;
  cursor: pointer;

  .thumbnail {
    width: 70px;
    height: 70px;
    border: 1px solid #d7dbe0;
    border-radius: 8px;
    padding: 2px;
    background: #f2f7fc;
    overflow: hidden;
    margin-right: 5px;

    video {
      object-fit: cover;
      width: 100%;
      height: 100%;
      border-radius: 6px;
    }

    iframe {
      object-fit: cover;
      width: 100%;
      height: 100%;
      border-radius: 6px;
    }

    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
      border-radius: 6px;
    }

    .removeFile {
      cursor: pointer;
      border-radius: 30px;
      background: #324b7d;
      position: absolute;
      color: white;
      width: 20px;
      height: 20px;
      text-align: center;
      font-size: 10px;
      float: right;
      margin-left: 50px;
      margin-top: -5px;
    }
  }

  .thumbnail_chat {
    width: 50px;
    height: 50px;
    margin-right: -20px;
    margin-top: -20px;
  }
`

export const Count = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 25px;
  background: ${(props) => props?.background ?? '#324B7D'};
  color: ${(props) => (!props?.background ? 'white' : 'rgb(62, 68, 78)')};
  border-radius: 30px;
  text-align: center;
  margin-left: ${(props) => props?.marginLeft};
  margin-top: ${(props) => props?.marginTop};
  margin-right: 20px;
  font-weight: 500;
  padding: 3px;
`

export const Error = styled.span`
  color: #db2727;
  font-size: 12px;
  font-weight: 500;
`

export const ModalDigisacSlide = styled(ModalDigisac)<typeof ModalDigisac>`
  .modal-content {
    background: transparent !important;
    height: 600px;

    .divCloseButton {
      display: flex;
      justify-content: center;
      margin-top: 10px;
    }

    .prevButton {
      margin-left: -60px;

      svg {
        transform: rotate(-90deg);
      }
    }

    .nextButton {
      margin-left: 100%;

      svg {
        transform: rotate(90deg);
      }
    }
  }
`

export const ButtonClose = styled.div`
  background: white;
  border-radius: 30px;
  width: 40%;
  text-align: center;
  color: #212e4a;
  padding: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
`

export const DivPdf = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 500px;
  width: 100%;
  background: white;
  border-radius: 30px;

  .flexColumn {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    color: #24272d;
    font-size: 14px;
    font-weight: 400;
  }

  .error {
    color: #586171;
    font-size: 14px;
    font-weight: 400;
  }
`

export const ButtonChangeFile = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: center;
  border-radius: 500px;
  background: white;
  width: 40px;
  height: 40px;
  top: 40%;
  position: absolute;
  cursor: pointer;

  svg {
    margin-top: 40%;
  }
`

export const Label = styled.div`
  font-size: 14px;
  color: #6e7a89;
  font-weight: 500;
`

export const Items = styled.div`
  display: flex;
  flex-direction: column;
`

export const Badge = styled.div`
  background: #edeef1;
  color: #3e444e;
  font-weight: 500;
  font-size: 14px;
  padding: 2px 8px 2px 8px;
  border-radius: 500px;
  margin-right: 10px;
  margin-top: ${(props) => props?.marginTop};
  margin-bottom: ${(props) => props?.marginBottom};
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  float: left;
`

export const DivInput = styled.div`
  display: flex;
  width: 100%;
  background: white;
  gap: 8px;

  .input-search {
    flex: 1;
  }

  input {
    border-radius: 25px;
    border: 1px solid rgba(82, 101, 140, 0.15);
  }

  .div_filters {
    display: flex;
    justify-content: center;
    flex-direction: column;
    cursor: pointer;
    border-radius: 25px;
    height: 40px;
    border: 1px solid rgba(82, 101, 140, 0.15);
    padding: 0 18px;
    color: #6e7a89;
    white-space: nowrap;
    background-color: #ffffff;

    svg {
      margin-left: 10px;
    }
  }
`

export const Title = styled.div`
  font-weight: bold;
`

export const ButtonQuickReply = styled.button`
  padding-top: 18px;
  display: flex;
  position: 'relative';
  padding: 0.75rem 1.25rem;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.125);
  margin-bottom: 8px;
  text-align: left;
  border-radius: 16px;
  height: 72px !important;
  width: 100% !important;

  .title_text {
    width: 70%;
    margin-top: -20px;
    padding-top: 20px;
  }

  .categories {
    display: flex;
    white-space: nowrap;
    margin-top: -20px;
    padding-top: 20px;
    padding-bottom: 40px;
  }

  .divAcao {
    position: absolute;
    right: 26px;
    top: 13px;
    white-space: nowrap;
  }

  .divAcao2 {
    padding: 10px;
    border-radius: 54px;
    &:hover {
      background-color: #e1edf8;
      border: 1px solid #e1edf8;
    }
  }
`

export const TextArea = styled(InputGroup)`
  border-radius: 20px 10px 10px 20px !important;
  resize: none;

  ::-webkit-scrollbar-track {
    border-radius: 0px 20px 20px 0px;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 0px 20px 20px 0px;
  }
`

export const NoResults = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 250px;
  background: #e5e5e5;
  color: #586171;
`

export const WrapperStatus = styled.div`
  margin-top: 2rem;
  margin-bottom: 10px;
  width: 70%;
`

export const ButtonStatus = styled.button.attrs({
  className: 'btn',
})`
  background: none;
  color: ${(props) => (props.isActive ? '#24272D' : '#586171')};
  font-size: 16px;
  font-weight: 600;
  border: 0px;
  border-bottom: 2px solid ${(props) => (props.isActive ? '#3C66B9' : '#B4BBC5')};
  border-radius: 0px;
`

export const ListLineQuickReply = styled.div`
  display: 'flex',
  justify-content: 'space-between',
  align-items: 'center',
  border-bottom: 1px solid #ccc,
  padding: 10px 10px 0px 0px,
  box-sizing: 'border-box',
  margin-bottom: 10px
`

export const AreaLineQuickReply = styled.div`
  background-color: '#ffffff';
  border-radius: '16px 16px 0 0';
  padding: '16px';
  display: 'flex';
  justify-content: 'space-between';
  align-items: 'left';
`
export const DropdownMenuJumper = styled(DropdownMenu)`
  padding: 0px;
  top: 12px !important;
  border-radius: '16px';
`
