import React, { Component } from 'react'
import { Redirect } from 'react-router-dom'
import Helmet from 'react-helmet'
import { ModalBody, Button } from 'reactstrap'
import { withTranslation } from 'react-i18next'
import toast from '../../../../../utils/toast'
import QuickRepliesShowBody from './QuickRepliesShowBody'
import { ModalDigisac, ModalDigisacHeader, ModalDigisacBody, ModalFooter } from '../../../styles/common'
import { IconClose, IconRedAlert } from '../../../../common/unconnected/IconDigisac'
import IfUserCan from '../../../../common/connected/IfUserCan'

class QuickRepliesShow extends Component {
  constructor(props) {
    super(props)

    this.state = {
      isModalShowing: true,
      isModalDeleteShowing: false,
      redirect: false,
    }

    this.toggleModal = this.toggleModal.bind(this)
    this.toggleDeleteModal = this.toggleDeleteModal.bind(this)
  }

  UNSAFE_componentWillMount() {
    this.fetch()
  }

  fetch() {
    const {
      fetchOneQuickReply,
      match: {
        params: { id },
      },
    } = this.props

    fetchOneQuickReply({ id })
  }

  delete() {
    const {
      deleteOneQuickReply,
      match: {
        params: { id },
      },
    } = this.props

    deleteOneQuickReply({ id })
  }

  toggleModal() {
    this.setState({
      isModalShowing: !this.state.isModalShowing,
    })

    setTimeout(() => this.props.history.push('/quick-replies'), 300)
  }

  toggleDeleteModal() {
    this.setState({
      isModalDeleteShowing: !this.state.isModalDeleteShowing,
    })
  }

  render() {
    const { isModalShowing, isModalDeleteShowing, redirect } = this.state
    const { quickReply, t } = this.props

    const handleDelete = () => {
      toast.success(t('MESSAGE_QUICK_REPLY_DELETED'))
      this.delete()
      setTimeout(() => this.setState({ redirect: true }), 300)
    }

    if (!quickReply) return false

    return (
      <div>
        {redirect && <Redirect to={{ pathname: '/quick-replies', state: { refetch: true } }} />}
        <Helmet title={t('TITLE_QUICK_REPLIES')} />

        <ModalDigisac
          data-testid="quickReplies-modal-view"
          isOpen={isModalShowing}
          toggle={this.toggleModal}
          autoFocus={false}
          size="mdplus"
        >
          <ModalDigisacHeader data-testid="quickReplies-header_modal-view">
            {t('TITLE_QUICK_REPLIES')}
            <div>
              <span onClick={this.toggleModal}>
                <IconClose fill="black" />
              </span>
            </div>
          </ModalDigisacHeader>
          <ModalBody data-testid="quickReplies-body_modal-view">
            <QuickRepliesShowBody quickReply={quickReply} />
            <ModalFooter style={{ marginTop: '30px' }}>
              <IfUserCan permission="quickReplies.destroy">
                <Button
                  data-testid="button-delete-quick-reply"
                  className="confirmWarning btn-footer"
                  type="button"
                  onClick={this.toggleDeleteModal}
                >
                  {t('common:ACTIONS_SUBMENU_DELETE')}
                </Button>
              </IfUserCan>
              <IfUserCan permission="quickReplies.update">
                <Button
                  data-testid="button-edit-quick-reply"
                  className="confirm btn-footer"
                  type="button"
                  onClick={() => this.props.history.push(`/quick-replies/${quickReply?.id}/edit`)}
                >
                  {t('common:ACTIONS_SUBMENU_EDIT')}
                </Button>
              </IfUserCan>
            </ModalFooter>
          </ModalBody>
        </ModalDigisac>

        <ModalDigisac isOpen={isModalDeleteShowing} style={{ top: '10%' }} size="md">
          <ModalDigisacHeader>
            <div>
              <span onClick={this.toggleDeleteModal}>
                <IconClose fill="black" />
              </span>
            </div>
          </ModalDigisacHeader>
          <ModalDigisacBody>
            <div className="icon-delete">
              <IconRedAlert width="45" height="35" />
            </div>
            <strong>{t('TITLE_QUICK_REPLY_DELETE')}</strong>
            <div className="div-message-delete">{t('MESSAGE_MODAL_DELETE')}</div>
            <div className="div-message-delete">{t('common:LABEL_DISCARD_CHANGES_ACTION')}</div>
          </ModalDigisacBody>
          <ModalFooter style={{ padding: '30px' }}>
            <Button
              data-testid="cancel-exit-quick-reply"
              className="cancelStroke btn-footer"
              type="button"
              onClick={this.toggleDeleteModal}
            >
              {t('common:FORM_ACTION_CANCEL')}
            </Button>
            <Button
              data-testid="confirm-exit-quick-reply"
              className="confirmWarning btn-footer"
              type="button"
              onClick={handleDelete}
            >
              {t('common:ACTIONS_SUBMENU_DELETE')}
            </Button>
          </ModalFooter>
        </ModalDigisac>
      </div>
    )
  }
}

export default withTranslation(['quickRepliesPage'])(QuickRepliesShow)
