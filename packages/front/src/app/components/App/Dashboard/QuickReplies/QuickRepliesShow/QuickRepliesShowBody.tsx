import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import * as S from '../styles'
import { IconPDF } from '../../../../common/unconnected/IconDigisac'
import ShowFiles from '../QuickRepliesForm/ShowFiles'

const QuickRepliesShowBody = ({ quickReply }) => {
  const { t } = useTranslation(['quickRepliesPage'])

  const [fileIndex, setFileIndex] = useState(0)
  const [showFiles, setShowFiles] = useState(false)

  const handleShowFiles = (key) => {
    setFileIndex(key)
    setShowFiles(true)
  }

  const getElement = (file, key) => {
    if ((file?.blob?.type ?? file?.mimetype) === 'video/mp4') {
      return (
        <video onClick={() => handleShowFiles(key)}>
          <source src={file?.url} type="video/mp4" />
        </video>
      )
    }
    if ((file?.blob?.type ?? file?.mimetype) === 'application/pdf') {
      return (
        <div onClick={() => handleShowFiles(key)}>
          <IconPDF />
        </div>
      )
    }
    return <img src={file?.url} onClick={() => handleShowFiles(key)} />
  }

  return (
    <div data-testid="quickReplies-div-content_quickReply">
      <S.Label>{t('TABLE_COLUMN_TITLE')}</S.Label> {quickReply?.title}
      <br />
      <br />
      <S.Label>{t('TABLE_COLUMN_TEXT')}</S.Label> {quickReply?.text}
      <br />
      <br />
      <S.Items>
        {quickReply?.categories?.length > 0 && (
          <>
            <S.Label>{t('TABLE_COLUMN_CATEGORIES')}</S.Label>
            <div>
              {quickReply?.categories?.map((category) => (
                <S.Badge
                  title={category.title}
                  marginBottom="5px"
                  marginTop="5px"
                  data-testid="quickReplies-link-department_modal_view"
                >
                  {category.title}
                </S.Badge>
              ))}
            </div>
            <br />
          </>
        )}
        {quickReply?.departments?.length > 0 && (
          <>
            <S.Label>{t('TABLE_COLUMN_DEPARTMENT')}</S.Label>
            <div>
              {quickReply?.departments?.map((departments) => (
                <S.Badge title={departments.name} marginBottom="5px" marginTop="5px">
                  {departments.name}
                </S.Badge>
              ))}
            </div>
            <br />
          </>
        )}
        {quickReply?.files?.length > 0 && (
          <>
            <S.Label>{t('LABEL_ADDED_FILES')}</S.Label>
            <S.Thumbnail style={{ marginTop: '0px' }}>
              {quickReply?.files?.map((file, key) => <div className="thumbnail">{getElement(file, key)}</div>)}
            </S.Thumbnail>
          </>
        )}
      </S.Items>
      <ShowFiles model={quickReply} isOpen={showFiles} setShowFiles={setShowFiles} index={fileIndex} />
    </div>
  )
}

export default QuickRepliesShowBody
