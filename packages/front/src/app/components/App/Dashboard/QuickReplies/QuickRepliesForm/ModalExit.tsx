import React from 'react'
import { useTranslation } from 'react-i18next'
import { Button as ReactButton } from 'reactstrap'
import { IconRedAlert, IconClose } from '../../../../common/unconnected/IconDigisac'
import { ModalDigisac, ModalDigisac<PERSON>eader, ModalDigisacBody, ModalFooter } from '../../../../App/styles/common'

export default function ModalExit({ showModalExit, setShowModalExit, exit, handleEdit, initialModelProp = null }) {
  const { t } = useTranslation(['quickRepliesPage', 'common'])

  const confirmExit = () => {
    setShowModalExit(false)
    if (initialModelProp) {
      handleEdit(false)
    }
    exit()
  }

  return (
    <ModalDigisac isOpen={showModalExit} style={{ top: '10%' }} size="md">
      <ModalDigisacHeader>
        <div>
          <span onClick={() => setShowModalExit(false)}>
            <IconClose fill="black" />
          </span>
        </div>
      </ModalDigisacHeader>
      <ModalDigisacBody>
        <div className="icon-delete">
          <IconRedAlert width="45" height="35" />
        </div>
        <strong>{t('common:TITLE_DISCARD_CHANGES')}</strong>
        <div className="div-message-delete">{t('common:LABEL_ANSWER_DISCARD_CHANGES')}</div>
        <div className="div-message-delete">{t('common:LABEL_DISCARD_CHANGES_ACTION')}</div>
      </ModalDigisacBody>
      <ModalFooter style={{ padding: '30px' }}>
        <ReactButton
          data-testid="cancel-exit-quick-reply"
          className="cancelStroke btn-footer"
          type="button"
          onClick={() => setShowModalExit(false)}
        >
          {t('common:FORM_ACTION_BACK')}
        </ReactButton>
        <ReactButton
          data-testid="confirm-exit-quick-reply"
          className="confirmWarning btn-footer"
          type="button"
          onClick={confirmExit}
        >
          {t('BUTTON_DISCARD')}
        </ReactButton>
      </ModalFooter>
    </ModalDigisac>
  )
}
