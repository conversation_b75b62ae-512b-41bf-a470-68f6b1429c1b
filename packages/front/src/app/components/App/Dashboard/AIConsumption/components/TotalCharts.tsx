import React, { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import * as S from '../styles'
import TableServices from '../components/TableServices'
import ChartAi from '../charts/ChartAi'

interface TotalChartsProps {
  data: any
  date: any
  dataDefault: any
  handleCalculate: (data: any) => void
  serviceSelected: string[]
}

const TotalCharts: React.FC<TotalChartsProps> = ({ data, date, dataDefault, handleCalculate, serviceSelected }) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', width: '100%', height: '430px' }}>
      <S.AreaChartWide>
        <ChartAi
          componentName={data.component}
          data={data.data}
          dataDefault={dataDefault}
          aiServices={data?.services?.map((item: { id: string }) => item.id) || []}
          title={data.titleTotal}
          showTotalCharts={true}
          showTotal={false}
          handleCalculate={handleCalculate}
          useTime={data.useTime}
        />
      </S.AreaChartWide>
      <TableServices date={date} serviceSelected={serviceSelected} serviceType={data.component} />
    </div>
  )
}

export default TotalCharts
