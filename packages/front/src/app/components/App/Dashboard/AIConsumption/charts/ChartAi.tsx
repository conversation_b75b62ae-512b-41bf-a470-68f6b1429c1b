import React, { useState, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'
import { ArrowRight } from 'lucide-react'
import { Badge } from '../../../../common/unconnected/ui/badge'
import { Button } from '../../../../common/unconnected/ui/button'
import * as S from '../styles'
import moment from 'moment'

const colors = [
  '#3C66B9',
  '#A5CBEB',
  '#373C43',
  '#01350c',
  '#044191',
  '#020d41',
  '#3633ff',
  '#5a0316',
  '#313d38',
  '#91662f',
  '#5733FF',
  '#074675',
  '#817f09',
]

function ChartAi({
  data,
  handleCalculate,
  dataDefault,
  aiServices = [],
  showTotalCharts = false,
  serviceSelected = [],
  title = '',
  componentName = '',
  showTotal = true,
  handleShowTotal = (componentName?: string) => {},
  useTime = false,
}) {
  const { t } = useTranslation(['aIConsumption', 'common'])
  const [consolidatedArray, setConsolidatedArray] = useState<any[]>([])
  const [bottomChart, setBottomChart] = useState(30)
  const [dailyAverageTooltip, setDailyAverageTooltip] = useState(0)
  const [dataFiltered, setDataFiltered] = useState([])

  const services = useMemo(() => {
    return [...new Set(dataFiltered?.map((item) => item?.name))].filter(Boolean)
  }, [dataFiltered])

  const getServices = useMemo(() => {
    if (serviceSelected?.length > 0) {
      return aiServices
    }

    return aiServices?.slice(0, 5)
  }, [serviceSelected.length, aiServices])

  useEffect(() => {
    setDataFiltered(data.filter((item) => getServices?.includes(item.serviceId)))
  }, [data, getServices])

  useEffect(() => {
    const dateMap = {}
    const consolidated = []
    for (const { date, outs, name } of dataFiltered) {
      const [year, month, day] = date.split('-')
      const formattedDate = new Date(year, month - 1, day).toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      })

      if (dateMap[formattedDate]) {
        dateMap[formattedDate][name] = (dateMap[formattedDate][name] || 0) + Number(outs)
      } else {
        const newEntry = { date: formattedDate, [name]: Number(outs) }
        dateMap[formattedDate] = newEntry
        if (outs != 0) {
          consolidated.push(newEntry)
        }
      }
    }

    if (consolidated.length > 0) {
      consolidated.forEach((entry) => {
        services.forEach((service) => {
          if (!(service in entry)) {
            entry[service] = 0
          }
        })
      })

      setConsolidatedArray(consolidated)
      setBottomChart(30)
    } else {
      setBottomChart(76)
    }
  }, [dataFiltered, setConsolidatedArray, setBottomChart])

  const dailyAverage = handleCalculate(dataFiltered)

  const handleTooltip = async (value) => {
    setDailyAverageTooltip(value)
  }

  const getTitle = () => {
    return !showTotalCharts ? title : title
  }

  const formatTime = (amount, location) => {
    const time = moment.utc(moment.duration(amount, 'seconds').asMilliseconds())
    if (location == 'grid') {
      return (
        (Number(time.format('H')) > 0 ? `${time.format('H')}h ` : ``) +
        (Number(time.format('m')) > 0 ? `${time.format('m')}m ` : `0m`)
      )
    } else {
      return (
        (Number(time.format('H')) > 0 ? `${time.format('H')}h ` : ``) +
        (Number(time.format('m')) > 0 ? `${time.format('m')}m ` : ``) +
        (Number(time.format('s')) > 0 ? `${time.format('s')}s ` : `0s`)
      )
    }
  }

  const formatTextTooltip = (useTime, credit) => {
    if (useTime) {
      return formatTime(credit, 'tooltip')
    }
    return credit.toString() + ' ' + (parseInt(credit) > 1 ? t('CREDITS') : t('CREDIT'))
  }

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '99%' }}>
        <p style={{ fontSize: '16px', margin: 0, fontWeight: '600' }}>{getTitle()}</p>
        {consolidatedArray.length > 0 && (
          <Badge variant="neutral-soft">
            {t('LABEL_DAY_CONSUMPTION')}: {useTime ? formatTime(dailyAverage, 'media') : dailyAverage}
          </Badge>
        )}
      </div>
      <ResponsiveContainer width="99%" height="100%">
        <LineChart
          width={500}
          height={300}
          data={consolidatedArray.length > 0 ? consolidatedArray : dataDefault}
          margin={{
            top: 30,
            right: 0,
            left: 0,
            bottom: bottomChart,
          }}
        >
          <CartesianGrid vertical={false} strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis
            tickFormatter={(value) => `${useTime ? formatTime(value, 'grid') : value}`}
            domain={consolidatedArray.length > 0 ? undefined : [0, 10]}
          />
          <Tooltip
            formatter={(value) => `${value}`}
            content={({ payload }) => {
              if (payload && payload.length && payload?.[dailyAverageTooltip]) {
                const { name, value } = payload?.[dailyAverageTooltip] ?? {}
                return (
                  <div
                    className="custom-tooltip"
                    style={{ backgroundColor: 'white', padding: '10px', border: '1px solid #ccc' }}
                  >
                    <span style={{ color: '#586171' }}>{`${payload?.[dailyAverageTooltip]?.payload?.date}`}</span>
                    <br />
                    <strong>{formatTextTooltip(useTime, value)}</strong>
                    <br />
                    <span style={{ color: '#586171' }}>{`${name}`}</span>
                    <br />
                  </div>
                )
              }
              return null
            }}
          />
          <Legend
            formatter={(value, entry) => (
              <S.TextLegendChart title={value} style={{ color: entry.color }}>
                {value}
              </S.TextLegendChart>
            )}
            iconType="circle"
            iconSize={12}
            wrapperStyle={{ width: '80%', maxHeight: '120px', marginLeft: '20px' }}
            align="left"
          />
          {consolidatedArray.length > 0 &&
            services.map((service, index) => (
              <Line
                type="monotone"
                key={service}
                dataKey={service}
                stroke={colors[index % colors.length]}
                dot={false}
                strokeWidth={2}
                onMouseLeave={() => handleTooltip(index)}
              />
            ))}
          {consolidatedArray.length === 0 && (
            <text x="50%" y="50%" textAnchor="middle" dominantBaseline="middle">
              {t('NO_DATA_AVAILABLE')}
            </text>
          )}
        </LineChart>
      </ResponsiveContainer>
      {aiServices?.length > 5 && consolidatedArray?.length > 0 && showTotal && (
        <Button
          variant="ghost"
          onClick={() => handleShowTotal(componentName)}
          style={{ float: 'right', marginTop: '-60px', marginRight: '-20px', position: 'relative', zIndex: '100' }}
          data-testid={`see-all-${componentName}`}
        >
          {t('BUTTON_SEE_ALL')}
          <ArrowRight color="#324b7d" size={16} style={{ marginLeft: '10px' }} />
        </Button>
      )}
    </>
  )
}

export default ChartAi
