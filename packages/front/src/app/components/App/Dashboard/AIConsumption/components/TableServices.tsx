import React, { useEffect, useState, useCallback } from 'react'
import { ChevronsUpDown, ChevronUp, ChevronDown } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../../../common/unconnected/ui/table'
import TablePagination from '../../../../common/unconnected/TablePagination'
import useIndexController from '../../../../../hooks/crud/useIndexController'
import { useFetchManyCreditMovementsServices } from '../../../../../resources/creditMovement/requests'

interface TableServicesProps {
  date: { startDate?: string; endDate?: string }
  serviceSelected: Array<any>
  serviceType: string
}

const TableServices: React.FC<TableServicesProps> = ({ date, serviceSelected = [], serviceType }) => {
  const { t } = useTranslation(['aIConsumption'])

  const [orderTotal, setOrderTotal] = useState([])
  const [orderName, setOrderName] = useState([])

  useEffect(() => {
    if (date) fetch()
  }, [date, orderTotal, orderName, serviceSelected])

  const buildQuery = ({ localPagination }) => ({
    filters: {
      ...date,
      serviceType,
      order: [orderTotal.join(' '), orderName.join(' ')],
    },
    page: localPagination.page || 1,
    perPage: localPagination.perPage,
  })

  const { models, pagination, localPagination, handleLocalPaginationChange, fetch } = useIndexController({
    initialPagination: {
      page: 1,
      perPage: 5,
    },
    buildQuery,
    useFetchMany: useFetchManyCreditMovementsServices,
  })

  const handleChangeOrderTotal = useCallback(() => {
    const order = orderTotal?.[1] === 'DESC' ? 'ASC' : 'DESC'
    setOrderTotal(['"total"', order])
    setOrderName([])
  }, [orderTotal])

  const getTotalIcon = () => {
    if (orderTotal?.[1] === 'DESC') return <ChevronDown size={16} />
    if (orderTotal?.[1] === 'ASC') return <ChevronUp size={16} />
    return <ChevronsUpDown size={16} />
  }

  const handleChangeOrderName = () => {
    const order = orderName?.[1] === 'DESC' ? 'ASC' : 'DESC'
    setOrderName(['name', order])
    setOrderTotal([])
  }

  const getNameIcon = () => {
    if (orderName?.[1] === 'DESC') return <ChevronDown size={16} />
    if (orderName?.[1] === 'ASC') return <ChevronUp size={16} />
    return <ChevronsUpDown size={16} />
  }

  return (
    <div
      style={{
        border: '1px solid #D7DBE0',
        borderRadius: '8px',
        background: 'white',
        marginTop: '10px',
      }}
    >
      <Table style={{ flex: 1, overflowY: 'auto' }}>
        <TableHeader>
          <TableRow>
            <TableHead style={{ width: '50%' }}>
              {t('EXPORT_MODAL_LABEL_SERVICE')}
              <a href="#" style={{ marginLeft: '10px' }} onClick={handleChangeOrderName}>
                {getNameIcon()}
              </a>
            </TableHead>
            <TableHead>
              {t('LABEL_TOTAL_USED')}
              <a href="#" style={{ marginLeft: '10px' }} onClick={handleChangeOrderTotal}>
                {getTotalIcon()}
              </a>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {models.map((service, index) => (
            <TableRow key={index}>
              <TableCell>{service?.name}</TableCell>
              <TableCell>{service?.total}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <TablePagination
        pagination={pagination}
        localPagination={localPagination}
        handlePaginationChange={handleLocalPaginationChange}
        simplePagination
        style={{ border: 'none' }}
      />
    </div>
  )
}

export default TableServices
