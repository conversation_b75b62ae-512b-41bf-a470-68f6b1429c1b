import React, { useState, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import moment from 'moment'
import * as S from './styles'
import { Progress } from 'reactstrap'
import convertSecondsToTimeString from '../../../../utils/convertSecondsToTimeString'

interface ConsumeBoxProps {
  icon: React.ComponentType
  title: string
  plan?: number
  credits: {
    balance: number
    outs: number
    ins: number
    used: number
    contractedCredits?: number
    additionalCredits?: number
    realConsumption?: number
  }
  renewDate?: string
  useCredits: boolean
  serviceType: string
}

const ConsumeBox: React.FC<ConsumeBoxProps> = ({ icon: Icon, title, plan = 0, credits, renewDate, useCredits }) => {
  const { t } = useTranslation(['aIConsumption', 'common'])
  const [creditsBalance, setCreditsBalance] = useState(0)
  const [creditsConsumed, setCreditsConsumed] = useState(0)
  const [creditsAdded, setCreditsAdded] = useState(0)
  const [creditsConsumedBar, setCreditsConsumedBar] = useState(0)
  const [extraCreditsConsumedBar, setExtraCreditsConsumedBar] = useState(0)
  const [hasExtraCredits, setHasExtraCredits] = useState(false)

  useEffect(() => {
    if (credits) {
      setCreditsBalance(
        credits.balance <= 0 ? 0 : credits.contractedCredits + credits.additionalCredits - credits.realConsumption,
      )

      setCreditsConsumed(credits.realConsumption)

      const totalCredits = (credits.contractedCredits || 0) + (credits.additionalCredits || 0)
      setCreditsAdded(totalCredits)
    }
  }, [credits, plan])

  useEffect(() => {
    const contractedAmount = credits.contractedCredits

    setCreditsConsumedBar(
      credits.balance <= 0 ? 100 : 100 * (Math.min(creditsConsumed, contractedAmount) / creditsAdded),
    )
    setExtraCreditsConsumedBar(
      credits.balance <= 0 ? 0 : 100 * (Math.max(creditsConsumed - contractedAmount, 0) / creditsAdded),
    )

    setHasExtraCredits((credits.additionalCredits || 0) > 0)
  }, [creditsConsumed, creditsAdded, credits, plan])

  const getAvailableCreditsText = useMemo((): string => {
    const creditsText = useCredits ? creditsBalance : convertSecondsToTimeString(creditsBalance)

    return `${t('common:AVAILABLE_LABEL')}: ${creditsText}`
  }, [useCredits, creditsBalance, hasExtraCredits, convertSecondsToTimeString])

  const getContractedText = useMemo((): string => {
    const contractedAmount = credits?.contractedCredits
    const value = useCredits ? contractedAmount?.toLocaleString('pt-BR') : Math.floor(contractedAmount / 3600)
    const unit = useCredits
      ? t('MONTH_CREDITS')
      : `${t('MONTH_HOURS')} (${Math.floor(contractedAmount / 60)} ${t('common:LABEL_INFO_MINUTES').slice(0, 3)})`
    return `${t('CONTRACTED_LABEL')}: ${value} ${unit}`
  }, [plan, useCredits, t, credits])

  const getAdditionalCredits = useMemo((): string => {
    const additionalAmount = credits?.additionalCredits || 0
    const value = useCredits ? additionalAmount : Math.floor(additionalAmount / 60)
    const unit = useCredits ? t('CREDITS').toLowerCase() : t('common:LABEL_INFO_MINUTES')
    return `${t('EXTRA_CREDITS')}: ${value > 0 ? value?.toLocaleString('pt-BR') : 0} ${unit}`
  }, [credits, plan, useCredits, t])

  if (plan <= 0) return null

  return (
    <S.ConsumeBox>
      <S.ConsumeBoxHeader>
        <Icon />
        {t('RENEW_IN')}: {renewDate ? moment(renewDate).add(1, 'M').format('DD/MM/YYYY') : ''}
      </S.ConsumeBoxHeader>

      <div className="consumptionAvailable">
        <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
          <div>
            <p className="consumptionSpentLabel">
              {useCredits ? creditsConsumed?.toLocaleString('pt-BR') : convertSecondsToTimeString(creditsConsumed)}
            </p>
          </div>
          <div style={{ display: 'flex', alignItems: 'flex-end', justifyContent: 'flex-end' }}>
            <p className="consumptionAvailableLabel">{getAvailableCreditsText}</p>
          </div>
        </div>
        {hasExtraCredits ? (
          <S.DoubleProgressBar multi style={{ height: '4px', marginTop: '8px' }}>
            <Progress bar style={{ background: '#3c66b9' }} value={creditsConsumedBar} />
            <Progress bar style={{ background: '#7ab0e0' }} value={extraCreditsConsumedBar} />
          </S.DoubleProgressBar>
        ) : (
          <S.ProgressBar style={{ marginTop: '8px' }} value={creditsConsumedBar} />
        )}
      </div>

      <div className="consumptionInfo">
        <div className="descriptionLabel">{title}</div>
        <div className="contractedLabel">
          <S.Dot /> {getContractedText}
        </div>
        <div className="contractedLabel">
          <S.Dot color="#7ab0e0" /> {getAdditionalCredits}
        </div>
      </div>
    </S.ConsumeBox>
  )
}

export default ConsumeBox
