import React, { useState, useEffect, useCallback } from 'react'
import { Col, Row } from 'reactstrap'
import { useSelector } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { addDays } from 'date-fns'
import { orderBy, omit } from 'lodash'
import { ChevronLeft } from 'lucide-react'
import { DateRange } from 'react-day-picker'
import { useRequest } from '../../../../hooks/useRequest'
import { selectors as authSelectors } from '../../../../modules/auth'
import creditMovementApi from '../../../../resources/creditMovement/api'
import Container from '../../styles/container'
import ChartAi from './charts/ChartAi'
import ConsumeBoxContainer from './ConsumeBoxContainer'
import ExportAIConsumeDialog from './components/ExportAIConsumeDialog'
import History from './History'
import Actions from './components/Actions'
import TotalCharts from './components/TotalCharts'
import * as S from './styles'
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '../../../common/unconnected/ui/tabs'
import * as accountsFlags from '../../../App/Dashboard/Admin/accounts/AccountsFlags'

const defaultPlan = {
  transcription: 0,
  summary: 0,
  magicText: 0,
  copilot: 0,
  csat: 0,
  agent: 0,
}

const extractServiceData = (data) =>
  Object.keys(omit(data, ['all', 'history', null])).map((key) => ({
    service: key,
    ...data[key],
  }))

const AIConsumption: React.FC = () => {
  const { t } = useTranslation(['aIConsumption', 'common'])
  const user = useSelector(authSelectors.getUser)

  const [dataDefault, setDataDefault] = useState([])
  const [date, setDate] = React.useState<DateRange>({
    from: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    to: addDays(new Date(new Date().getFullYear(), new Date().getMonth(), 1), 30),
  })
  const [plans, setPlans] = useState(defaultPlan)
  const [serviceSelected, setServiceSelected] = useState([])
  const [renewDate, setRenewDate] = useState('')
  const [showTotalCharts, setShowTotalCharts] = useState(false)
  const [dataTotalCharts, setDataTotalCharts] = useState<any[]>([])
  const [typeTotalChart, setTypeTotalChart] = useState<
    'transcription' | 'summary' | 'magic-text' | 'copilot' | 'agent'
  >(null)
  const [isExportModalOpen, setIsExportModalOpen] = useState(false)
  const [activeCharts, setActiveCharts] = useState([])

  const [{ isLoading: isLoadingCharts, response: chartsResponse }, getCharts] = useRequest(creditMovementApi.balancesV2)

  useEffect(() => {
    if (user?.account?.plan) {
      const {
        transcription,
        summary,
        'magic-text': magicText,
        agent: agent,
        copilot,
        csat,
      } = user?.account?.plan?.ai ?? {}

      setPlans({
        transcription: transcription,
        summary: summary,
        magicText: magicText,
        copilot: copilot,
        csat: csat,
        agent: agent,
      })

      setRenewDate(user?.account?.plan?.renewDate ?? '')
    }
  }, [user?.account?.plan])

  useEffect(() => {
    if (!isLoadingCharts && chartsResponse) {
      const summary = extractServiceData(chartsResponse?.summary)
      const transcription = extractServiceData(chartsResponse?.transcription)
      const magicText = extractServiceData(chartsResponse?.['magic-text'])
      const copilot = extractServiceData(chartsResponse?.copilot)
      const csat = extractServiceData(chartsResponse?.csat)
      const agent = extractServiceData(chartsResponse?.agent)

      const activeChartsArray = []
      if (accountsFlags.isEnable(user?.account?.settings?.flags || {}, 'enable-audio-transcription') === true) {
        activeChartsArray.push({
          component: 'transcription',
          data: chartsResponse?.transcription?.history,
          services: orderBy(transcription, ['outs'], ['desc']),
          plan: plans.transcription,
          title: t('TITLE_CHARTS_TRANSCRIPTION_PLURAL'),
          titleTotal: t('TITLE_CHARTS_TRANSCRIPTION'),
          useTime: true,
        })
      }
      if (accountsFlags.isEnable(user?.account?.settings?.flags || {}, 'enable-smart-summary') === true) {
        activeChartsArray.push({
          component: 'summary',
          data: chartsResponse?.summary?.history,
          services: orderBy(summary, ['outs'], ['desc']),
          plan: plans.summary,
          title: t('TITLE_CHARTS_SUMMARY_PLURAL'),
          titleTotal: t('TITLE_CHARTS_SUMMARY'),
        })
      }
      if (accountsFlags.isEnable(user?.account?.settings?.flags || {}, 'enable-magic-text') === true) {
        activeChartsArray.push({
          component: 'magic-text',
          data: chartsResponse?.['magic-text']?.history,
          services: orderBy(magicText, ['outs'], ['desc']),
          plan: plans.magicText,
          title: t('TITLE_CHARTS_MAGIC_TEXT_PLURAL'),
          titleTotal: t('TITLE_CHARTS_MAGIC_TEXT'),
        })
      }
      if (accountsFlags.isEnable(user?.account?.settings?.flags || {}, 'enable-copilot') === true) {
        activeChartsArray.push({
          component: 'copilot',
          data: chartsResponse?.copilot?.history,
          services: orderBy(copilot, ['outs'], ['desc']),
          plan: plans.copilot,
          title: t('TITLE_CHARTS_COPILOT_PLURAL'),
          titleTotal: t('TITLE_CHARTS_COPILOT'),
        })
      }
      if (accountsFlags.isEnable(user?.account?.settings?.flags || {}, 'enable-smart-csat-score') === true) {
        activeChartsArray.push({
          component: 'csat',
          data: chartsResponse?.csat?.history,
          services: orderBy(csat, ['outs'], ['desc']),
          plan: plans.csat,
          title: t('TITLE_CSAT'),
          titleTotal: t('TITLE_CSAT'),
        })
      }
      if (accountsFlags.isEnable(user?.account?.settings?.flags || {}, 'enable-bots-v3-ai-node') === true) {
        activeChartsArray.push({
          component: 'agent',
          data: chartsResponse?.agent?.history,
          services: orderBy(agent, ['outs'], ['desc']),
          plan: plans.agent,
          title: t('TITLE_CHARTS_AGENT'),
          titleTotal: t('TITLE_CHARTS_AGENT'),
        })
      }
      setActiveCharts(activeChartsArray.filter((chart) => chart.data && (chart.plan ?? 0) > 0))
    }
  }, [isLoadingCharts, chartsResponse, plans, showTotalCharts, typeTotalChart])

  useEffect(() => {
    if (activeCharts.length <= 0) return

    if (showTotalCharts && typeTotalChart) {
      setDataTotalCharts(activeCharts.find((chart) => chart.component === typeTotalChart))
    }
  }, [activeCharts, showTotalCharts, typeTotalChart])

  const generateEmptyDateRangeData = useCallback(() => {
    const dateArray = []
    const startDate = new Date(date.from)
    const endDate = new Date(date.to)

    while (startDate <= endDate) {
      dateArray.push({ date: startDate.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' }), value: 0 })
      startDate.setDate(startDate.getDate() + 1)
    }

    setDataDefault(dateArray)
  }, [date])

  useEffect(() => {
    generateEmptyDateRangeData()

    try {
      getCharts({
        from: date.from,
        to: date.to,
        serviceId: { $in: serviceSelected?.map((service) => service.id) },
      })
    } catch (error) {
      console.error('Error fetching data:', error)
    }
  }, [date, serviceSelected])

  const handleCalculate = (data) => {
    if (date == undefined) {
      return 0
    }
    const groupedByDate = data.reduce((acc, item) => {
      const creditDate = item.date.split('T')[0]
      acc[creditDate] = (acc[creditDate] || 0) + parseFloat(item.outs)
      return acc
    }, {})

    const startDate = new Date(date.from)
    const endDate = new Date(date.to)
    const numberOfDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
    const totalAmount = Object.values(groupedByDate).reduce((sum, value) => Number(sum) + Number(value), 0) || 0
    const dailyAverage = (Number(totalAmount) / Number(numberOfDays)).toFixed(2)

    return dailyAverage || 0
  }

  const handleFilterService = async (services) => {
    if (services?.length === 0) {
      setServiceSelected(null)
    } else {
      setServiceSelected(services)
    }
  }

  const handleBackIndex = () => {
    setServiceSelected(null)
    setShowTotalCharts(false)
  }

  const handleShowTotal = (type) => {
    const activeChart = activeCharts.find((chart) => chart.component === type)

    setTypeTotalChart(type)
    setShowTotalCharts(true)
    setDataTotalCharts(activeChart)
    setServiceSelected(activeChart.services?.slice(0, 5).map((service) => ({ id: service.id, name: service.service })))
  }

  const getGridStyle = useCallback(
    (index) => {
      const flexStyle =
        activeCharts.length === 1 || activeCharts.length === 2
          ? { flexBasis: '100%', flexGrow: 1, flexShrink: 0 } // Full width for 1 chart
          : activeCharts.length % 2 === 0
            ? { flexBasis: 'calc(50% - 0.75rem)', flexGrow: 0, flexShrink: 0 } // Two columns for even numbers of charts, accounting for gap
            : activeCharts.length === 3 && index === 0
              ? { flexBasis: '100%', flexGrow: 1, flexShrink: 0 } // Full width for the first chart when there are 3 charts
              : { flexBasis: 'calc(50% - 0.75rem)', flexGrow: 0, flexShrink: 0 } // Default to two columns for other cases, accounting for gap

      return flexStyle
    },
    [activeCharts],
  )

  const getSizeChart = (chatsLength, index) => {
    if (
      chatsLength === 1 ||
      chatsLength === 2 ||
      (chatsLength === 3 && index === 0) ||
      (chatsLength === 5 && index === 4)
    ) {
      return '12'
    }
    return '6'
  }

  const getTitle = useCallback(() => {
    if (!showTotalCharts) return t('TITLE')

    return (
      <>
        <button
          onClick={() => handleBackIndex()}
          style={{
            marginLeft: '-10px',
            marginRight: '20px',
            background: 'none',
            border: 'none',
            cursor: 'pointer',
          }}
        >
          <ChevronLeft size={20} />
        </button>
        {typeTotalChart === 'summary'
          ? t('TITLE_CHARTS_SUMMARY')
          : typeTotalChart === 'magic-text'
            ? t('TITLE_CHARTS_MAGIC_TEXT')
            : typeTotalChart === 'transcription'
              ? t('TITLE_CHARTS_TRANSCRIPTION')
              : t('TITLE')}
      </>
    )
  }, [showTotalCharts, typeTotalChart, t])

  return (
    <Container>
      <h2 style={{ marginBottom: '0px', color: '#324B7D', fontWeight: '600', fontSize: '32px' }}>{getTitle()}</h2>
      <Tabs defaultValue="dashboard">
        <div className={'d-flex flex-column'} style={{ gap: '40px' }}>
          {!showTotalCharts && (
            <div className={'d-flex flex-column'} style={{ gap: '24px' }}>
              <div>
                <TabsList>
                  <TabsTrigger value="dashboard">{t('TAB_DASHBOARD')}</TabsTrigger>
                  <TabsTrigger value="history">{t('TAB_HISTORY')}</TabsTrigger>
                </TabsList>
              </div>
              <TabsContent value="dashboard">
                <ConsumeBoxContainer plans={plans} renewDate={renewDate} account={user?.account} />
              </TabsContent>
            </div>
          )}
          <TabsContent value="dashboard">
            {!isLoadingCharts && (
              <div>
                <Actions
                  date={
                    date?.from && date?.to ? { from: date.from, to: date.to } : { from: new Date(), to: new Date() }
                  }
                  setDate={setDate}
                  handleFilterService={handleFilterService}
                  setIsExportModalOpen={setIsExportModalOpen}
                  showServicesFilter={showTotalCharts}
                  serviceSelected={serviceSelected}
                />
                {!showTotalCharts && (
                  <Row>
                    {activeCharts.map((chart, index) => (
                      <Col
                        xs="12"
                        sm="12"
                        md="12"
                        lg={getSizeChart(activeCharts.length, index)}
                        xl={getSizeChart(activeCharts.length, index)}
                        style={{ marginTop: '20px' }}
                      >
                        <div key={chart.component} style={getGridStyle(index)}>
                          <S.AreaChartWide key={chart.component}>
                            <ChartAi
                              componentName={chart.component}
                              data={chart.data}
                              dataDefault={dataDefault}
                              aiServices={chart.services?.map((item: { id: string }) => item.id) || []}
                              title={chart.title}
                              handleCalculate={handleCalculate}
                              handleShowTotal={handleShowTotal}
                              useTime={chart.useTime}
                            />
                          </S.AreaChartWide>
                        </div>
                      </Col>
                    ))}
                  </Row>
                )}

                {showTotalCharts && (
                  <TotalCharts
                    data={dataTotalCharts}
                    dataDefault={dataDefault}
                    handleCalculate={handleCalculate}
                    serviceSelected={serviceSelected}
                    date={date}
                  />
                )}
              </div>
            )}
          </TabsContent>
          <TabsContent value="history">
            <History />
          </TabsContent>
        </div>
      </Tabs>
      <ExportAIConsumeDialog
        isOpen={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
      ></ExportAIConsumeDialog>
    </Container>
  )
}

export default AIConsumption
