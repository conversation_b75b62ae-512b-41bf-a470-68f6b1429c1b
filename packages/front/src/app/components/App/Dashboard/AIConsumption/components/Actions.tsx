import React from 'react'
import { useTranslation } from 'react-i18next'
import { ArrowUpRight, Download, RotateCw } from 'lucide-react'
import { addDays } from 'date-fns'
import ServicesSelect from '../../../../common/connected/ServicesSelect'
import { DateRangePicker } from '../../../../common/unconnected/ui/date-range-picker'
import { GroupInput } from '../../../styles/common'
import { Button } from '../../../../common/unconnected/ui/button'
import * as S from '../styles'
import config from '../../../../../../../config'

interface ActionsProps {
  date: { from: Date; to: Date }
  setDate: (date: { from: Date; to: Date }) => void
  handleFilterService: (services: any[]) => void
  setIsExportModalOpen: (isOpen: boolean) => void
  showServicesFilter?: boolean
  serviceSelected?: any[]
}

const Actions: React.FC<ActionsProps> = ({
  date,
  setDate,
  handleFilterService,
  setIsExportModalOpen,
  showServicesFilter = false,
  serviceSelected = [],
}) => {
  const { t } = useTranslation(['aIConsumption', 'common'])

  const handleFilterServiceChange = (e) => {
    if (e?.length > 5) return
    handleFilterService(e)
  }

  const resetFilters = () => {
    handleFilterService([])
    setDate({
      from: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
      to: addDays(new Date(new Date().getFullYear(), new Date().getMonth(), 1), 30),
    })
  }

  return (
    <S.Actions>
      <S.ActionFilters>
        <S.FiltersFormGroup>
          <GroupInput style={{ width: '300px' }} data-testid="date-range_charts">
            <DateRangePicker dateRange={date} setDateRange={setDate} />
          </GroupInput>
          {showServicesFilter && (
            <>
              <GroupInput style={{ minWidth: '300px' }} data-testid="select-aiconsumption-service">
                <ServicesSelect
                  id="actions-services-select"
                  hideArchived
                  isMulti
                  onChange={(e) => handleFilterServiceChange(e)}
                  value={serviceSelected}
                />
              </GroupInput>
              <Button variant="ghost" id="reset-filters" data-testid="reset-filters" onClick={resetFilters}>
                <RotateCw size={5} color="#324B7D" style={{ transform: 'scaleX(-1)', marginRight: '10px' }} />{' '}
                {t('RESET_FILTERS')}
              </Button>
            </>
          )}
        </S.FiltersFormGroup>
      </S.ActionFilters>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '16px',
        }}
      >
        <a href={config('creditPurchaseUrl')} target="_blank" data-testid="raise-limit-link">
          <Button variant="ghost">
            <span>{t('LINK_RAISE_LIMIT')}</span>
            <ArrowUpRight
              style={{
                marginLeft: '4px',
              }}
              color="#324B7D"
            />
          </Button>
        </a>
        <Button variant="ghost" data-testid="raise-export" onClick={() => setIsExportModalOpen(true)}>
          <Download
            style={{
              marginRight: '4px',
            }}
            color="#324B7D"
          />
          <span>{t('LINK_EXPORT')}</span>
        </Button>
      </div>
    </S.Actions>
  )
}

export default Actions
