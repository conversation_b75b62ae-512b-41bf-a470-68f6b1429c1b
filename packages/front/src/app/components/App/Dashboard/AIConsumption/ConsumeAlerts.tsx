import React from 'react'
import { Alert, AlertDescription, AlertLink } from '../../../common/unconnected/ui/alert'
import { TriangleAlertIcon, AlertOctagonIcon, ArrowUpRightIcon } from 'lucide-react'
import { useTranslation, Trans } from 'react-i18next'
import config from '../../../../../../config'

const ConsumeAlerts = ({
  flags,
  transcriptionProgress,
  smartSummaryProgress,
  magicTextProgress,
  copilotProgress,
  csatProgress,
  agentProgress,
}) => {
  const { t } = useTranslation(['aIConsumption', 'common'])

  const features = []
  if (flags?.['enable-audio-transcription']) features.push(transcriptionProgress)
  if (flags?.['enable-smart-summary']) features.push(smartSummaryProgress)
  if (flags?.['enable-magic-text']) features.push(magicTextProgress)
  if (flags?.['enable-copilot']) features.push(copilotProgress)
  if (flags?.['enable-smart-csat-score']) features.push(csatProgress)
  if (flags?.['enable-bots-v3-ai-node']) features.push(agentProgress)

  const limitsExceeded = Object.values(features).filter((progress) => progress >= 100).length

  const limitsToExceed = Object.values(features).filter((progress) => progress >= 75).length

  const getServiceName = () => {
    const percentual = limitsExceeded >= 1 ? 100 : 75
    if (transcriptionProgress >= percentual) return t('AUDIO_TRANSCRIPTION')
    if (smartSummaryProgress >= percentual) return t('SMART_SUMMARY')
    if (magicTextProgress >= percentual) return t('MAGIC_TEXT')
    if (csatProgress >= percentual) return t('CSAT')
    if (agentProgress >= percentual) return t('AGENT')
    return t('COPILOT')
  }

  return (
    <>
      {(limitsExceeded > 0 && (
        <Alert variant="destructive">
          <AlertOctagonIcon />

          {limitsExceeded === 1 ? (
            <AlertDescription>
              <Trans
                t={t}
                i18nKey="SERVICE_LIMIT_EXCEEDED"
                values={{ service: getServiceName() }}
                components={{ bold: <strong /> }}
              />
            </AlertDescription>
          ) : (
            <AlertDescription>{t('SERVICES_LIMIT_EXCEEDED')}</AlertDescription>
          )}

          <AlertLink>
            <a href={config('creditPurchaseUrl')} target="_blank" data-testid="raise-limit-link">
              <span>{t('LINK_RAISE_LIMIT')}</span>
              <ArrowUpRightIcon
                color="#324B7D"
                style={{ marginLeft: '8px', marginRight: '8px', width: '16px', height: '16px' }}
              />
            </a>
          </AlertLink>
        </Alert>
      )) ||
        (limitsToExceed > 0 && (
          <Alert variant="warning">
            <TriangleAlertIcon />

            {limitsToExceed === 1 ? (
              <AlertDescription>
                <Trans
                  t={t}
                  i18nKey="SERVICE_LIMIT_TO_EXCEED"
                  values={{ service: getServiceName() }}
                  components={{ bold: <strong /> }}
                />
              </AlertDescription>
            ) : (
              <AlertDescription>{t('SERVICES_LIMIT_TO_EXCEED')}</AlertDescription>
            )}
          </Alert>
        ))}
    </>
  )
}

export default ConsumeAlerts
