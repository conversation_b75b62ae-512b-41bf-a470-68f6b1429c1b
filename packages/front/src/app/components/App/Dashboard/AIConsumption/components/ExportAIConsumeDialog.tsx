import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../../../common/unconnected/ui/dialog'
import api from '../../../../../resources/creditMovement/api'
import { useRequest } from '../../../../../hooks/useRequest'
import useEventCallback from '../../../../../hooks/useEventCallback'
import formatDate from '../../../../../utils/date/format'
import { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import ServicesSelect from '../../../../common/connected/ServicesSelect'
import { DateRangePicker } from '../../../../common/unconnected/ui/date-range-picker'
import moment from 'moment'
import { But<PERSON> } from '../../../../common/unconnected/ui/button'

interface ExportModalProps {
  isOpen: boolean
  onClose: () => void
}

interface FiltersProps {
  from?: Date
  to?: Date
  service?: { id: string } | null
}

const initialFilters: FiltersProps = {
  from: new Date(),
  to: new Date(),
  service: null,
}

const ExportAIConsumeDialog: React.FC<ExportModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation('aIConsumption')

  const [filters, setFilters] = useState<FiltersProps>(initialFilters)
  const [{ response: responseExport, isLoading: isLoadingExport }, getExport] = useRequest(api.export)

  useEffect(() => {
    if (responseExport) {
      exportCSV()
    }
  }, [responseExport])

  const buildQuery = () => {
    const query = {
      where: {
        ...(filters.service?.id && { serviceId: filters.service.id }),
        serviceType: {
          $in: ['transcription', 'summary', 'magic-text', 'copilot', 'csat', 'agent'],
        },
        $and: [],
      },
    }

    if (filters.from) {
      query.from = moment(filters.from).startOf('day').toISOString()
    }

    if (filters.to) {
      query.to = moment(filters.to).endOf('day').toISOString()
    }

    return { query: JSON.stringify(query) }
  }

  const handleExport = useEventCallback(() => {
    if (isLoadingExport) return

    getExport(buildQuery()).catch((error) => {
      console.error('Export failed:', error)
    })
  })

  const exportCSV = () => {
    const blob = new Blob([responseExport], {
      type: 'vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
    })
    const link = document.createElement('a')

    link.download = `${formatDate(new Date())}_${t('EXPORT_FILENAME')}.xls`
    link.href = URL.createObjectURL(blob)
    link.click()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        style={{
          overflow: 'initial',
          gap: '40px',
        }}
      >
        <DialogHeader>
          <DialogTitle>{t('EXPORT_MODAL_TITLE')}</DialogTitle>
          <DialogDescription>{t('EXPORT_MODAL_SUBTITLE')}</DialogDescription>
        </DialogHeader>
        <main style={{ marginBottom: '-1rem' }}>
          <InputGroupWrapper
            label={t('EXPORT_MODAL_LABEL_PERIOD')}
            render={() => (
              <DateRangePicker
                dateRange={{
                  from: filters.from,
                  to: filters.to,
                }}
                setDateRange={(dates) => {
                  setFilters({ ...filters, ...dates })
                }}
                id="aiconsumption-export-date-picker"
                data-testid="aiconsumption-export-date-picker"
                selectStyles={{
                  control: (styles) => ({
                    ...styles,
                    borderColor: 'hsl(0,0%,80%) !important',
                  }),
                  placeholder: (styles) => ({
                    ...styles,
                    color: 'hsl(0,0%,20%) !important',
                  }),
                }}
              />
            )}
          />
          <InputGroupWrapper
            label={t('EXPORT_MODAL_LABEL_SERVICE')}
            render={() => (
              <ServicesSelect
                id="aiconsumption-export-service-select"
                className="select-service"
                value={filters.service}
                onChange={(value) => setFilters({ ...filters, service: value })}
                data-testid="aiconsumption-export-service-select"
                styles={{
                  control: (styles) => ({
                    ...styles,
                    borderRadius: '25px',
                    padding: '0px 8px 0px 4px',
                  }),
                }}
              />
            )}
          />
        </main>
        <DialogFooter>
          <DialogClose asChild>
            <Button style={{ width: '100%' }} variant="outline" data-testid="export-consumption-back-button">
              {t('EXPORT_MODAL_BUTTON_BACK')}
            </Button>
          </DialogClose>
          <Button
            style={{ width: '100%' }}
            onClick={handleExport}
            disabled={isLoadingExport}
            id="aiconsumption-export-export-button"
            data-testid="aiconsumption-export-export-button"
          >
            {t('EXPORT_MODAL_BUTTON_EXPORT')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default ExportAIConsumeDialog
