import React, { useEffect, useState } from 'react'
import * as S from './styles'
import TablePagination from '../../../common/unconnected/TablePagination'
import { useTranslation } from 'react-i18next'
import { GroupInput } from '../../styles/common'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '../../../common/unconnected/ui/select'
import { DateRangePicker } from '../../../common/unconnected/ui/date-range-picker'
import { addDays } from 'date-fns'
import formatDate from '../../../../utils/date/format'
import { useFetchManyPlanAiHistory } from '../../../../resources/planAiHistory/requests'
import useIndexController from '../../../../hooks/crud/useIndexController'
import { pickBy } from 'lodash'
import identity from 'lodash/identity'
import { DateRange } from 'react-day-picker'

const buildQuery = ({ filters, localPagination }) => ({
  query: JSON.stringify({
    where: {
      ...pickBy(
        {
          summary: typeof filters.summary === 'boolean' ? { $eq: filters.summary } : undefined,
          magicText: typeof filters.magicText === 'boolean' ? { $eq: filters.magicText } : undefined,
          transcription: typeof filters.transcription === 'boolean' ? { $eq: filters.transcription } : undefined,
          csat: typeof filters.csat === 'boolean' ? { $eq: filters.csat } : undefined,
          copilot: typeof filters.copilot === 'boolean' ? { $eq: filters.copilot } : undefined,
          agent: typeof filters.agent === 'boolean' ? { $eq: filters.agent } : undefined,
          $and: [
            { ...(filters.from && { createdAt: { $gte: filters.from } }) },
            { ...(filters.to && { createdAt: { $lte: filters.to } }) },
          ],
        },
        identity,
      ),
    },
    order: [['createdAt', 'desc']],
    page: localPagination.page,
    perPage: localPagination.perPage,
  }),
})

const initialPagination = {
  page: 1,
  perPage: 7,
}

const AI_SERVICES = ['magicText', 'summary', 'transcription', 'copilot', 'csat', 'agent']

const History = () => {
  const {
    models: planAiHistory,
    pagination,
    localPagination,
    handleLocalPaginationChange,
    fetch,
    handleFilterChange,
    handleClearFilters,
  } = useIndexController({
    buildQuery,
    useFetchMany: useFetchManyPlanAiHistory,
    initialPagination,
  })

  const { t } = useTranslation(['aIConsumption', 'common'])

  const activityMap = {
    change: t('PLAN_CHANGE_LABEL'),
    renewal: t('CONTRACT_RENEW_LABEL'),
    addition: t('ADD_CREDIT_LABEL'),
  }

  const servicesAiMap = {
    magicText: t('MAGIC_TEXT'),
    summary: t('SMART_SUMMARY'),
    transcription: t('AUDIO_TRANSCRIPTION'),
    copilot: t('COPILOT'),
    csat: t('CSAT'),
    agent: t('AGENT'),
  }

  const [date, setDate] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    to: addDays(new Date(new Date().getFullYear(), new Date().getMonth(), 1), 30),
  })

  const handleDateChange = () => {
    handleFilterChange('from', date?.from?.setHours(0, 0, 0, 0))
    handleFilterChange('to', date?.to?.setHours(23, 59, 59, 999))
  }

  useEffect(() => {
    fetch()
  }, [location, history])

  useEffect(() => {
    handleDateChange()
  }, [date])

  const getAiServices = (data) => {
    const aiServices = []
    const { magicText, summary, transcription, copilot, csat, agent } = data

    if (magicText) aiServices.push(t('MAGIC_TEXT'))
    if (summary) aiServices.push(t('SMART_SUMMARY'))
    if (transcription) aiServices.push(t('AUDIO_TRANSCRIPTION'))
    if (copilot) aiServices.push(t('COPILOT'))
    if (csat) aiServices.push(t('CSAT'))
    if (agent) aiServices.push(t('AGENT'))

    return aiServices.join(', ')
  }

  const applyFilterChange = (aiService) => {
    handleClearFilters()
    handleDateChange()
    if (AI_SERVICES.includes(aiService)) handleFilterChange(aiService, true)
  }

  return (
    <div style={{ display: 'grid', gap: '24px' }}>
      <S.ActionFilters>
        <S.FiltersFormGroup>
          <GroupInput
            style={{ width: '300px', borderRadius: '21px', border: '1px solid rgb(180, 187, 197)' }}
            data-testid="aiconsumption-history-date-picker"
            id="aiconsumption-history-date-picker"
          >
            <DateRangePicker dateRange={date} setDateRange={setDate} />
          </GroupInput>
          <GroupInput data-testid="aiconsumption-history-service" id="aiconsumption-history-service">
            <Select onValueChange={applyFilterChange}>
              <SelectTrigger style={{ width: '300px' }}>
                <SelectValue placeholder={t('LABEL_SERVICE_AI')} />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>{t('LABEL_SERVICE_AI')}</SelectLabel>
                  <SelectItem key="ALL" value="all">
                    {t('aIConsumption:LABEL_ALL')}
                  </SelectItem>
                  {AI_SERVICES.map((service) => (
                    <SelectItem value={service}>{servicesAiMap[service]}</SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          </GroupInput>
        </S.FiltersFormGroup>
      </S.ActionFilters>
      <S.TableContainer>
        <S.Table>
          <S.TableHead>
            <tr>
              <S.TableHeader>{t('ACTIVITY_LABEL')}</S.TableHeader>
              <S.TableHeader>{t('AI_SERVICE_LABEL')}</S.TableHeader>
              <S.TableHeader>{t('DATE_LABEL')}</S.TableHeader>
            </tr>
          </S.TableHead>
          <S.TableBody>
            {planAiHistory?.length === 0 ? (
              <>
                <S.TableRow>
                  <S.TableCell colSpan={3} style={{ textAlign: 'center', height: '400px' }}>
                    {t('NO_RESULTS_FOUND_1')}
                    <br />
                    {t('NO_RESULTS_FOUND_2')}
                  </S.TableCell>
                </S.TableRow>
              </>
            ) : (
              planAiHistory?.map((row, index) => (
                <S.TableRow key={index}>
                  <S.TableCell className="activity">{activityMap[row.activity]}</S.TableCell>
                  <S.TableCell>{getAiServices(row)}</S.TableCell>
                  <S.TableCell>{formatDate(row.createdAt, 'dd/MM/yyyy')}</S.TableCell>
                </S.TableRow>
              ))
            )}
          </S.TableBody>
        </S.Table>
        <TablePagination
          newStyle={true}
          style={{
            border: 'none',
            borderRadius: '0',
            height: '44px',
          }}
          pagination={pagination}
          localPagination={localPagination}
          handlePaginationChange={handleLocalPaginationChange}
          simplePagination
        />
      </S.TableContainer>
    </div>
  )
}

export default History
