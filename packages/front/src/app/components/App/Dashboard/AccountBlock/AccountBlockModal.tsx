import React, { memo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { <PERSON><PERSON>, <PERSON>dal<PERSON>eader, ModalBody, Modal<PERSON>ooter, <PERSON><PERSON> } from 'reactstrap'
import config from '../../../../../../config'

type Props = {
  isOpen: boolean
}

function AccountBlockModal(props: Props) {
  const [modal, setModal] = useState(true)

  const { t } = useTranslation(['accountPage', 'common'])

  const handleSubmit = () => {
    window.location.reload()
  }

  const toggle = () => {
    setModal(!modal)
    handleSubmit()
  }

  const { isOpen } = props

  return (
    <Modal isOpen={isOpen} toggle={toggle}>
      <ModalHeader toggle={toggle}>{t('common:LABEL_CONFIRMATION')}</ModalHeader>

      <ModalBody>
        <p>{t('ACCESS_RELEASED')}</p>

        <p>{t('REMINDER')}</p>
      </ModalBody>

      <ModalFooter>
        <Button color="primary" onClick={() => handleSubmit()}>
          {t('ACCESS_THE')} {config('whitelabel.appName')}
        </Button>
      </ModalFooter>
    </Modal>
  )
}

export default memo(AccountBlockModal)
