import React, { useState, useEffect } from 'react'
import { connect } from 'react-redux'
import { <PERSON><PERSON>, Card, CardBody, CardFooter, Col, Row } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import AccountBlockModal from './AccountBlockModal'
import { useRequest } from '../../../../hooks/useRequest'
import SwitchLanguage from '../../../common/unconnected/SwitchLanguage'
import accountApi from '../../../../modules/account/services/acountApi'
import { logout } from '../../../../modules/auth/actions'

function AccountBlock({ logout, expired }) {
  const [modal, setModal] = useState(false)

  const [{ response }, accountBlock] = useRequest(accountApi.accountBlock)

  const { t } = useTranslation(['accountPage', 'common'])

  const handleAccess = () => {
    accountBlock()
  }

  useEffect(() => {
    if (response) {
      setModal(true)
    }
  }, [response])

  return (
    <>
      <div className="full-height d-flex align-items-center" style={{ backgroundColor: '#EEE' }}>
        <div className="container">
          <Row className="justify-content-center align-middle">
            <Col md={7}>
              <Card>
                <CardBody>
                  {expired > 0 ? (
                    <>
                      <p>{t('BLOCKED_ACCOUNT')}</p>
                      <p>{t('BLOCKED_ACCOUNT_PART_2')}</p>
                    </>
                  ) : (
                    <p>{t('BLOCKED_ACCOUNT_EXPIRED')}</p>
                  )}
                </CardBody>
                <CardFooter>
                  {expired <= 0 ? (
                    <Button color="primary" type="submit" onClick={logout} className="ml-0">
                      {t('common:BUTTON_LOGIN_WITH_OTHER_ACCOUNT')}
                    </Button>
                  ) : (
                    <Button color="primary" type="submit" onClick={() => handleAccess()} className="ml-0">
                      {t('BUTTON_FREE_ACCESS_24')}
                    </Button>
                  )}
                  <SwitchLanguage renderName data-testid="expired-list-language" />
                </CardFooter>
              </Card>
            </Col>
          </Row>
        </div>
      </div>
      <AccountBlockModal isOpen={modal} />
    </>
  )
}

export default connect(() => ({}), {
  logout,
})(AccountBlock)
