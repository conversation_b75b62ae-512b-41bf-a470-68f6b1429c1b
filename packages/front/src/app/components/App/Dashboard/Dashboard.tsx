import React from 'react'
import { with<PERSON>out<PERSON>, Switch, Route } from 'react-router-dom'
import { Redirect } from 'react-router'
import Terms from './terms/Terms'
import AbsenceModal from './absenceModal/AbsenceModal'
import Header from './Header'
import Error404 from '../Error404'
import ChatRoute from './Chat'
import CampaignsIndexRoute from './campaigns/CampaignsIndex'
import CampaignsShowPageRoute from './campaigns/CampaignsShow'
import CampaignsWhatsappBusinessRoute from './campaigns/CampaignsForm/WhatsappBusiness'
import ContactsImportRoute from './contacts/ContactsImportCsv/ContactsImportCsv'
import ContactBlockListsRoute from './contacts/ContactBlock'
import QuickRepliesIndexRoute from './QuickReplies/QuickRepliesIndex'
import AIConsumptionRoute from './AIConsumption'
import ContactsIndexRoute from './contacts/ContactsIndex'
import ServicesIndexRoute from './services/ServicesIndex'
import ServicesFormScreenRoute from './services/ServicesFormScreen'
import BotsIndexRoute from './bots/BotsIndex'
import TutorialsRoute from './Tutorials'
import BotsFormRoute from './bots/BotsForm'
import CompaniesIndexRoute from './Account'
import SettingsRoute from './Settings'
import PeopleIndexRoute from './people/PeopleIndex'
import ScheduleIndexRoute from './schedule/ScheduleIndex'
import NowRoute from './overview/Now'
import TicketsHistoryRoute from './overview/TicketHistory'
import { TicketReportsRoute, TickerReportsOldVersionRoute } from './overview/Stats'
import HsmStatsRoute from './overview/Hsm'
import TicketsConversationRoute from './overview/Conversation'
import DistributionIndex from './Distribution/DistributionIndex'
import EvaluationReportsRoute from './overview/Evaluation'
import AbsenceControlRoute from './overview/AbsenceControl'
import AdminRoute from './Admin'
import AboutRoute from './About'
import Plan from './Plan'
import IntegrationsIndexRoute from './integration/IntegrationIndex'
import config from '../../../../../config'
import BetaRibbon from '../../common/unconnected/BetaRibbon'
import IfUserCanRoute from '../../common/connected/IfUserCanRoute/IfUserCanRoute'
import LoadingSpinner from '../../common/unconnected/LoadingSpinner'
import BotsShowRoute from './bots/BotsShow'
import Takeover from './Takeover'
import ShowOutOfHours from './ShowOutOfHours'
import AccountBlock from './AccountBlock'
import BlockedIp from './BlockedIp'
import AccountExpired from './AccountExpired'
import EvaluationIndex from './Evaluation/EvaluationIndex'
import timetableRoute from './timetable/TimetableIndex'
import whatsappBusinessTemplatesRoute from './whatsappBusinessTemplates/WhatsappBusinessTemplatesIndex'
import interactiveMessagesIndex from './interactiveMessages/InteractiveMessagesIndex'
import AcceptanceTerm from './AcceptanceTerm/AcceptanceTermIndex'
import useCanEnter, { AlertUser } from '../../../hooks/useCanEnter'
import useActiveUser from '../../../hooks/useActivityUser'
import BotProvider from './botsv2/Container'
import BotsV2ShowRoute from './botsv2/show'
import BotsV2FormRoute from './botsv2/form'
import BotsV3ShowRoute from './botsv3/Flow'
import MessageSearch from './Chat/MessageSearch'
import * as S from './styles'
import HolidayRoute from './Holiday'
import InternalChat from '../../InternalChat'
import * as accountsFlags from './Admin/accounts/AccountsFlags'
import { PipelinesRoutes } from './Pipelines/Routes'
import { KnowledgeBaseRoutes } from './KnowledgeBase/Routes'
import PasswordExpired from './PasswordExpired'
import AuthHistoryRoute from './AuthHistory/AuthHistoryIndex/AuthHistory'
import SetupSecret from '../TwoFactorAuthentication/SetupSecret'
import ArchivedUser from './ArchivedUser'
import { TagsRoutes } from '../../../../routes/tags'
import { DepartmentsRoutes } from '../../../../routes/departments'
import { UsersRoutes } from '../../../../routes/users'
import { RolesRoutes } from '../../../../routes/roles'
import { CustomFieldsRoutes } from '../../../../routes/custom-fields'
import { CreditsRoutes } from '../../../../routes/credits'
import { OrganizationsRoutes } from '../../../../routes/organizations'
import { TicketTopicsRoutes } from '../../../../routes/ticket-topics'
import { BotsRoutes } from '../../../../routes/bots'
import Marketplace from './Marketplace'

const Dashboard = ({
  user,
  isTookOver,
  isArchived,
  displayTerms,
  setDisplayTerms,
  displayAbsenceModal,
  setDisplayAbsenceModal,
  isImpersonating,
  displayIpMessage,
  displayHourMessage,
  hasPasswordExpired,
}) => {
  const hideTutorials = config('whitelabel.hideTutorials')

  if (user && isArchived) {
    return <ArchivedUser />
  }

  if (!user && displayIpMessage) {
    return <BlockedIp />
  }

  if (!user && displayHourMessage) {
    return <ShowOutOfHours />
  }

  if (!user) {
    return <LoadingSpinner isLoading />
  }

  if (user && !user.account?.isActive && !user.account?.plan?.isOnGracePeriod) {
    return <AccountExpired />
  }

  if (user && user.account?.plan?.isOnGracePeriod && user.account?.plan?.isGracePeriodOnHold) {
    return <AccountBlock expired={user.account?.plan?.gracePeriodExtendTimesRemaining} />
  }

  if (hasPasswordExpired) {
    return <PasswordExpired user={user} />
  }

  if (user && isTookOver) {
    return <Takeover user={user} />
  }

  if (user && user.isClientUser) {
    return <Redirect to="/client/ticket-history" />
  }

  if (
    !hideTutorials &&
    user &&
    user.language === 'pt-BR' &&
    user.account.wizardProgress &&
    user.account.wizardProgress !== 'finished'
  ) {
    return <Redirect to="/wizard" />
  }

  const [warning] = useCanEnter(user)

  if (
    user &&
    !user.otpAuthActive &&
    user.account?.settings?.twoFactorAuthMandatory &&
    new Date(user.account?.settings?.twoFactorAuthMandatorySchedule) <= new Date() &&
    !isImpersonating
  ) {
    return <SetupSecret notGoBack={true} expireSession={true} />
  }

  return (
    <S.Dashboard id="dashboard" className="d-flex flex-column align-items-stretch">
      {warning && <AlertUser minutes={user.timetable?.previousTimeToNotification || 0} />}

      {displayTerms && !isImpersonating && !user.isSuperAdmin && (
        <Terms user={user} displayTerms={displayTerms} setDisplayTerms={setDisplayTerms} />
      )}

      {accountsFlags.isEnable(user.account.settings?.flags || {}, 'absence-management') &&
        !isImpersonating &&
        !user.isSuperAdmin &&
        (user.account.settings?.absence || {}).enabled && (
          <AbsenceModal
            displayAbsenceModal={displayAbsenceModal}
            setDisplayAbsenceModal={setDisplayAbsenceModal}
            absenceSettings={user.account.settings?.absence}
          />
        )}

      {!!config('isBeta') && <BetaRibbon />}

      <Header />

      {user && accountsFlags.isEnable(user.account.settings.flags || {}, 'enable-sales-funnel') === true && (
        <PipelinesRoutes />
      )}

      <Switch>
        <IfUserCanRoute
          permission="contacts.create"
          path="/contacts/import/csv"
          component={ContactsImportRoute}
          exact
        />

        <IfUserCanRoute permission="tags.view" path="/tags" component={TagsRoutes} />

        {user.account.settings.ticketsEnabled && (
          <IfUserCanRoute permission="departments.view" path="/departments" component={DepartmentsRoutes} />
        )}

        <IfUserCanRoute permission="contacts.block" path="/contacts/block-lists" component={ContactBlockListsRoute} />

        <IfUserCanRoute permission="contacts.view" path="/contacts" component={ContactsIndexRoute} />

        <IfUserCanRoute permission="users.view" path="/users" component={UsersRoutes} />

        {user.account.isCampaignActive &&
          user.account.settings.campaign &&
          Object.values(user.account.settings.campaign).some((channel) => channel) && (
            <IfUserCanRoute permission="campaigns.view" path="/campaigns" component={CampaignsIndexRoute} />
          )}

        {user.account.isCampaignActive &&
          user.account.settings.campaign &&
          Object.values(user.account.settings.campaign).some((channel) => channel) && (
            <IfUserCanRoute
              permission="campaigns.view"
              path="/new-campaign/:id"
              component={CampaignsShowPageRoute}
              exact
            />
          )}

        {user.account.isCampaignActive &&
          user.account.settings.campaign &&
          user.account.settings.campaign['whatsapp-business'] && (
            <IfUserCanRoute
              permission="campaigns.create"
              path="/new-campaign/create/whatsapp-business"
              component={CampaignsWhatsappBusinessRoute}
              exact
            />
          )}

        {user.account.isCampaignActive &&
          user.account.settings.campaign &&
          user.account.settings.campaign['whatsapp-business'] && (
            <IfUserCanRoute
              permission="campaigns.update"
              path="/new-campaign/whatsapp-business/:id/edit"
              component={CampaignsWhatsappBusinessRoute}
              exact
            />
          )}

        <IfUserCanRoute
          permission="services.create"
          path="/services/create/webchat"
          exact
          component={ServicesFormScreenRoute}
        />

        <IfUserCanRoute
          permission="services.create"
          path="/services/:id/edit/webchat"
          exact
          component={ServicesFormScreenRoute}
        />

        <IfUserCanRoute permission="services.view" path="/services" component={ServicesIndexRoute} />

        <IfUserCanRoute permission="overview.now.view" path="/now" component={NowRoute} />

        {user.account.settings.enableHsmStats && (
          <IfUserCanRoute permission="overview.hsm.statistic.view" path="/hsm-stats" component={HsmStatsRoute} />
        )}

        {user.account.settings.ticketsEnabled && (
          <IfUserCanRoute permission="overview.now.view" path="/now" component={NowRoute} />
        )}

        {user.account.settings.ticketsEnabled && (
          <IfUserCanRoute
            permissions={['overview.history.view']}
            any
            path="/ticket-history"
            component={TicketsHistoryRoute}
          />
        )}

        {user.account.settings.ticketsEnabled && (
          <IfUserCanRoute
            permission="overview.history.view"
            path="/ticket-reports"
            component={TickerReportsOldVersionRoute}
          />
        )}

        {user.account.settings.ticketsEnabled && (
          <IfUserCanRoute permission="overview.history.view" path="/ticket-reports-v2" component={TicketReportsRoute} />
        )}

        {user.account.settings.ticketsEnabled && (
          <IfUserCanRoute
            permission="overview.history.view"
            path="/ticket-conversation"
            component={TicketsConversationRoute}
          />
        )}

        <IfUserCanRoute
          permission="overview.evaluation.view"
          path="/evaluation-reports"
          component={EvaluationReportsRoute}
        />

        <IfUserCanRoute permission="overview.absence.view" path="/absence-control" component={AbsenceControlRoute} />

        <IfUserCanRoute permission="roles.view" path="/roles" component={RolesRoutes} />

        <Route path="/settings" component={SettingsRoute} />

        {user.isSuperAdmin && <Route path="/admin" component={AdminRoute} />}

        <IfUserCanRoute permission="myAccount.view" path="/company" component={CompaniesIndexRoute} />

        <IfUserCanRoute permission="holidays.view" path="/holiday" component={HolidayRoute} />

        <IfUserCanRoute permission="authHistory.view" path="/auth-history" component={AuthHistoryRoute} />

        <IfUserCanRoute permission="quickReplies.view" path="/quick-replies" component={QuickRepliesIndexRoute} />

        {(accountsFlags.isEnable(user.account.settings.flags || {}, 'enable-magic-text') === true ||
          accountsFlags.isEnable(user.account.settings.flags || {}, 'enable-smart-summary') === true ||
          accountsFlags.isEnable(user.account.settings.flags || {}, 'enable-audio-transcription') === true ||
          accountsFlags.isEnable(user.account.settings.flags || {}, 'enable-smart-csat-score') === true ||
          accountsFlags.isEnable(user.account.settings.flags || {}, 'enable-copilot') === true ||
          accountsFlags.isEnable(user.account.settings.flags || {}, 'enable-bots-v3-ai-node') === true) && (
          <IfUserCanRoute permission="aIConsumption.view" path="/ai-consumption" component={AIConsumptionRoute} />
        )}

        {(accountsFlags.isEnable(user.account.settings.flags || {}, 'enable-copilot') === true ||
          accountsFlags.isEnable(user.account.settings.flags || {}, 'enable-bots-v3-ai-node') === true) && (
          <IfUserCanRoute
            permission="knowledgeBase.view"
            path="/knowledge-base"
            component={KnowledgeBaseRoutes}
            user={user}
          />
        )}

        <IfUserCanRoute permission="evaluation.view" path="/evaluation" component={EvaluationIndex} />

        <IfUserCanRoute permission="ticketTopics.view" path="/ticket-topics" component={TicketTopicsRoutes} />

        <IfUserCanRoute permission="distribution.view" path="/distribution" component={DistributionIndex} />

        <IfUserCanRoute permission="people.view" path="/people" component={PeopleIndexRoute} />

        <IfUserCanRoute permission="acceptanceTerms.view" path="/acceptanceTerms" component={AcceptanceTerm} />

        <Route path="/integrations" component={IntegrationsIndexRoute} />

        <IfUserCanRoute permission="schedule.view" path="/schedules" component={ScheduleIndexRoute} />

        <IfUserCanRoute permission="organizations.view" path="/organizations" component={OrganizationsRoutes} />

        <IfUserCanRoute
          permission="hsm.view"
          path="/whatsapp-business-templates"
          component={whatsappBusinessTemplatesRoute}
        />
        <IfUserCanRoute
          permission="interactive-messages.view"
          path="/interactive-messages"
          component={interactiveMessagesIndex}
        />

        <IfUserCanRoute permission="customFields.view" path="/custom-fields" component={CustomFieldsRoutes} />

        <IfUserCanRoute permission="timetables.view" path="/timetables" component={timetableRoute} />

        {!hideTutorials && user.language === 'pt-BR' && <Route path="/tutorials" component={TutorialsRoute} />}

        <Route path="/about" component={AboutRoute} />

        <IfUserCanRoute permission="myAccount.myPlan" path="/plan" component={Plan} />

        <Route path="/menu-integration/:id" render={() => <div />} />

        <Route path="/marketplace" component={Marketplace} />

        <Route path="/credits" component={CreditsRoutes} />

        <IfUserCanRoute permission="chat" path="/" component={ChatRoute} exact />

        <IfUserCanRoute permission="chat" path="/message-search" render={() => <MessageSearch isOpen />} />

        <IfUserCanRoute permission="bots.view" path="/bots" component={BotsRoutes} />

        {/* Bot V1 */}
        <IfUserCanRoute permission="bots.create" path="/bots-v1/create" component={BotsFormRoute} />
        <IfUserCanRoute permission="bots.update" path="/bots-v1/:id/edit" exact component={BotsFormRoute} />
        <IfUserCanRoute permission="bots.view" path="/bots-v1/:id" component={BotsShowRoute} exact />

        {/* Bot V3 (Não inverter com V2) */}
        <IfUserCanRoute permission="bots.create" path="/bots-v3/create" component={BotsV3ShowRoute} exact />
        <IfUserCanRoute permission="bots.update" path="/bots-v3/:id/edit" component={BotsV3ShowRoute} exact />
        <IfUserCanRoute permission="bots.update" path="/bots-v3/:id/simulator" component={BotsV3ShowRoute} exact />
        <IfUserCanRoute permission="bots.view" path="/bots-v3/:id" component={BotsV3ShowRoute} exact />

        {/*Bot V2*/}
        <BotProvider>
          <IfUserCanRoute permission="bots.view" path="/bots-v2/:id" component={BotsV2ShowRoute} exact />
          <IfUserCanRoute permission="bots.update" path="/bots-v2/:id/:context" component={BotsV2FormRoute} exact />
        </BotProvider>

        <Route component={Error404} />
      </Switch>

      <InternalChat />
    </S.Dashboard>
  )
}

export default withRouter((props) => {
  if (props.user) {
    useActiveUser(props.user, props.isTookOver, props.isImpersonating)
  }

  return <Dashboard {...props} />
})
