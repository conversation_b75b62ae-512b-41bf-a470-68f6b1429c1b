import { connect } from 'react-redux'
import { withRouter } from 'react-router-dom'
import Dashboard from './Dashboard'
import { selectors, actions } from '../../../modules/auth'
import { selectors as impersonateSelector } from '../../../modules/admin/modules/users/modules/impersonate'

const mapStateToProps = (state) => ({
  user: selectors.getUser(state),
  isTookOver: selectors.getIsTookOver(state),
  isArchived: selectors.getIsArchived(state),
  displayTerms: selectors.getDisplayTerms(state),
  displayAbsenceModal: selectors.getDisplayAbsenceModal(state),
  isAccountExpired: selectors.getIsAccountExpired(state),
  isImpersonating: impersonateSelector.getIsImpersonating(state),
  displayIpMessage: selectors.getDisplayIpMessage(state),
  displayHourMessage: selectors.getDisplayHourMessage(state),
  hasPasswordExpired: selectors.getHasPasswordExpired(state),
})

const actionCreators = {
  setDisplayTerms: actions.displayTerms,
  setDisplayAbsenceModal: actions.displayAbsenceModal,
}

export default withRouter(connect(mapStateToProps, actionCreators)(Dashboard))
