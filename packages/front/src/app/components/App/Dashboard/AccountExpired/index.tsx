import React, { memo } from 'react'
import { <PERSON><PERSON>, <PERSON>, CardBody, <PERSON><PERSON><PERSON>er, <PERSON>, Row } from 'reactstrap'
import { connect } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { logout } from '../../../../modules/auth/actions'
import { getUser } from '../../../../modules/auth/selectors'
import Icon from '../../../common/unconnected/Icon'
import config from '../../../../../../config'
import { useHistory } from 'react-router'

interface Account {
  agnusSignatureKey: string
}

interface User {
  id: string
  account: Account
  roles: Array<number>
}

interface Props {
  user: User
}

const AccountExpired: React.FC<Props> = ({ logout, user }) => {
  const { t } = useTranslation(['common', 'chatPage'])

  const history = useHistory()

  const handleLogout = () => {
    if (user) {
      logout()
      return
    }
    return history.push('/login')
  }

  return (
    <div className="full-height d-flex align-items-center" style={{ backgroundColor: '#EEE' }}>
      <div className="container">
        <Row className="justify-content-center align-middle">
          <Col md={7}>
            <Card>
              <CardBody>{t('common:INACTIVE_ACCOUNT')}</CardBody>
              <CardFooter>
                <Button color="link" type="submit" onClick={handleLogout} className="float-left">
                  {t('common:BUTTON_LOGIN_WITH_OTHER_ACCOUNT')}
                </Button>

                <Button
                  color="primary"
                  className="float-right"
                  target="__blank"
                  href={`https://api.whatsapp.com/send?phone=${config('whitelabel.supportWhatsapp')}`}
                >
                  <Icon name="phone" /> {t('common:LABEL_CONTACT_US', { appName: config('whitelabel.appName') })}
                </Button>
              </CardFooter>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  )
}

const mapStateToProps = (state) => ({
  user: getUser(state),
  logout,
})

const actionCreators = {
  logout,
}

export default connect(mapStateToProps, actionCreators)(memo(AccountExpired))
