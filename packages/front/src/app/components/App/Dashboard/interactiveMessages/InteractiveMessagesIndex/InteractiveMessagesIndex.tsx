import React from 'react'
import Helmet from 'react-helmet'
import { Link, Switch } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { pickBy, identity } from 'lodash'
import { withRouter } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import LoadingSpinner from '../../../../common/unconnected/LoadingSpinner'
import Icon from '../../../../common/unconnected/Icon'
import Toggler from '../../../../common/unconnected/Toggler'
import TablePagination from '../../../../common/unconnected/TablePagination'
import IfUserCan from '../../../../common/connected/IfUserCan'
import IfUserCanRoute from '../../../../common/connected/IfUserCanRoute'
import Join from '../../../../common/connected/Join'
import { useFetchManyInteractiveMessages } from '../../../../../resources/interactiveMessages/requests'
import useIndexController from '../../../../../hooks/crud/useIndexController'
import InteractiveMessagesDeleteRoute from '../InteractiveMessagesDelete'
import InteractiveMessagesFormRoute from '../InteractiveMessagesForm'
import InteractiveMessagesShowRoute from '../InteractiveMessagesShow'
import Filters from './Filters'
import Button from '../../../../common/unconnected/Button'

import {
  Table,
  TableHead,
  TableBody,
  TableColumn,
  TableRow,
  TableCell,
  TableOptions,
  ButtonRefresh,
  ButtonDropdownActions,
  DropdownItemActions,
  DropdownMenuActions,
  CardFilters,
} from '../../../styles/table'
import { hasPermission } from '../../../../common/connected/IfUserCan/IfUserCan'
import { getUser } from '../../../../../modules/auth/selectors'

import {
  IconSearch,
  IconOptions,
  IconTrash,
  IconEdit,
  IconShowPassword as IconEyes,
} from '../../../../common/unconnected/IconDigisac'

import { PrimaryColor, TextColor } from '../../../styles/colors'
import Container from '../../../styles/container'
import * as S from './styles'

const buildQuery = ({ filters, user, localPagination }) => ({
  query: JSON.stringify({
    ...pickBy({
      include: [
        {
          model: 'departments',
          attributes: ['id', 'name'],
          where: {
            archivedAt: { $eq: null },

            ...(!hasPermission(user.permissions, 'interactive-messages.management.all.departments') && {
              id: {
                $in: (user.departments || []).map((d) => d.id),
              },
            }),
          },
        },
      ],
      where: {
        ...(filters.name && {
          name: {
            $iLike: `%${filters.name}%`,
          },
        }),
        archivedAt: { $eq: null },
      },
      page: localPagination.page,
      perPage: localPagination.perPage,
      order: [['createdAt', 'DESC']],
    }),
    identity,
  }),
})

const initialFilters = {
  name: '',
}

export const getType = (type, t) =>
  ({
    button: t('SELECT_TYPE_OPTION_BUTTON'),
    list: t('SELECT_TYPE_OPTION_LIST'),
  })[type]

const InteractiveMessagesIndex = ({ match }) => {
  const { t } = useTranslation(['interactiveMessagesPage', 'common'])
  const user = useSelector(getUser)

  // hasPermission
  const {
    models: interactiveMessages,
    pagination,
    isLoading,
    fetch,
    localPagination,
    handleLocalPaginationChange,
    handleFilterChange,
    filters,
    toggleFilters,
    isFiltersShowing,
  } = useIndexController({
    buildQuery: (params) => buildQuery({ ...params, user }),
    initialFilters,
    useFetchMany: useFetchManyInteractiveMessages,
  })

  return (
    <div>
      <Helmet title={t('TITLE_INTERACTIVE_MESSAGES')} />

      <Container data-testid="interactive-message-list-container">
        <div className="d-flex align-itens-center justify-content-between">
          <div className="title-page" data-testid="interactiveMessage-text">
            <h2 data-testid="interactive-message-list-title">{t('TITLE_INTERACTIVE_MESSAGES')}</h2>
          </div>

          <div className="d-flex align-items-center">
            <IfUserCan permission="interactive-messages.create">
              <Button
                background={PrimaryColor}
                size="xll"
                className="mr-2"
                data-testid="interactive-message-list-create-button"
              >
                <Link to="/interactive-messages/create" data-testid="create-interactiveMessages-button">
                  <Icon
                    name="plus"
                    fixedWidth
                    className="mr-1"
                    data-testid="interactive-message-list-create-button-icon"
                  />
                  {t('NEW_INTERACTIVE_MESSAGE')}
                </Link>
              </Button>
            </IfUserCan>

            <Button
              background={PrimaryColor}
              size="xl"
              onClick={toggleFilters}
              data-testid="interactive-message-list-show-filter-button"
              className="mr-2"
            >
              <IconSearch
                fill="white"
                width="25"
                height="25"
                data-testid="interactive-message-list-show-filter-button-icon"
              />
              {isFiltersShowing ? `${t('common:BUTTON_TEXT_HIDDEN')} ` : `${t('common:BUTTON_TEXT_SHOW')} `}
              {t('common:BUTTON_TEXT_FILTERS')}
            </Button>

            <ButtonRefresh onClick={fetch} data-testid="interactive-message-list-refresh-button">
              <Icon name="sync-alt" fixedWidth data-testid="interactive-message-list-refresh-button-icon" />
            </ButtonRefresh>
          </div>
        </div>

        <S.AlertInteractiveMessages>
          <h5 data-testid="interactive-message-list-subtitle">{t('SUBTITLE_INTERACTIVE_MESSAGES')}</h5>
        </S.AlertInteractiveMessages>

        {isFiltersShowing && (
          <CardFilters>
            <Filters
              data-testid="interactive-message-list-filters"
              filters={filters}
              handleFilterChange={handleFilterChange}
            />
          </CardFilters>
        )}

        <Table>
          <TableHead gridColumns="50% 25% 20% 5%" disableGap>
            <TableColumn data-testid="interactive-messages-table-column-name">{t('TABLE_COLUMN_NAME')}</TableColumn>
            <TableColumn data-testid="interactive-messages-table-column-type">{t('TABLE_COLUMN_TYPE')}</TableColumn>
            <TableColumn data-testid="interactive-messages-table-column-departments">
              {t('TABLE_COLUMN_DEPARTMENTS')}
            </TableColumn>
            <TableColumn
              data-testid="interactive-messages-table-column-actions"
              className="text-right justify-content-end pr-4"
            >
              {t('common:ACTIONS_SUBMENU_TITLE')}
            </TableColumn>
          </TableHead>
          <TableBody data-testid="interactive-message-table-body">
            {interactiveMessages &&
              interactiveMessages.map((interactiveMessage) => {
                return (
                  <TableRow
                    data-testid="interactive-message-table-name-row"
                    gridColumns="50% 25% 20% 5%"
                    disableGap
                    key={interactiveMessage.id}
                  >
                    <TableCell data-testid="interactive-message-table-name-cell">
                      <Link
                        data-testid="interactive-message-table-name-link"
                        to={`/interactive-messages/${interactiveMessage.id}`}
                        data-testid="Text-name-interactiveMessages"
                      >
                        {interactiveMessage.name}
                      </Link>
                    </TableCell>
                    <TableCell className="pr-3" data-testid="interactive-message-table-type-cell">
                      {getType(interactiveMessage.interactive.type, t)}
                    </TableCell>
                    <TableCell className="pr-3" data-testid="interactive-message-table-departments-cell">
                      <Join
                        data-testid="interactive-message-table-departments-join"
                        items={interactiveMessage.departments}
                        render={(department) => (
                          <Link
                            to={`/departments/${department.id}`}
                            data-testid="interactive-message-table-departments-link"
                          >
                            {department.name}
                          </Link>
                        )}
                      />
                    </TableCell>
                    <TableCell actions data-testid="interactive-message-table-action-cell">
                      <Toggler
                        data-testid="interactive-message-table-action-toggle"
                        render={({ active, toggle }) => (
                          <ButtonDropdownActions
                            data-testid="interactive-message-table-action-dropdown"
                            group={false}
                            isOpen={active}
                            toggle={toggle}
                          >
                            <TableOptions
                              size="sm"
                              color="primary"
                              data-testid="interactive-message-table-action-table-option"
                            >
                              <IconOptions
                                data-testid="interactive-message-table-action-table-option-icon"
                                className="icon-options"
                                fill={PrimaryColor}
                              />
                            </TableOptions>
                            <DropdownMenuActions data-testid="interactive-message-table-title-dropdown">
                              <DropdownItemActions
                                header
                                data-testid="interactive-message-table-action-dropdown-title-action"
                              >
                                {t('common:ACTIONS_SUBMENU_TITLE')}
                              </DropdownItemActions>
                              <Link
                                data-testid="interactive-message-table-dropdown-view"
                                to={`/interactive-messages/${interactiveMessage.id}`}
                                className="dropdown-item"
                              >
                                <IconEyes
                                  data-testid="interactive-message-table-dropdown-view-icon"
                                  fill={TextColor}
                                  width="29"
                                  height="29"
                                />
                                {t('common:ACTIONS_SUBMENU_VIEW')}
                              </Link>

                              <IfUserCan permission="interactive-messages.update">
                                <Link
                                  data-testid="interactive-message-table-dropdown-edit"
                                  to={`/interactive-messages/${interactiveMessage.id}/edit`}
                                  data-testid="edit-interactiveMessages-button"
                                  className="dropdown-item"
                                >
                                  <IconEdit
                                    data-testid="interactive-message-table-dropdown-edit-icon"
                                    fill={TextColor}
                                    width="28"
                                    height="28"
                                  />
                                  {t('common:ACTIONS_SUBMENU_EDIT')}
                                </Link>
                              </IfUserCan>

                              <IfUserCan permission="interactive-messages.destroy">
                                <Link
                                  data-testid="interactive-message-table-dropdown-destroy"
                                  to={`/interactive-messages/${interactiveMessage.id}/delete`}
                                  className="dropdown-item"
                                >
                                  <IconTrash
                                    data-testid="interactive-message-table-dropdown-destroy-icon"
                                    fill={TextColor}
                                    width="28"
                                    height="28"
                                  />
                                  {t('common:ACTIONS_SUBMENU_DELETE')}
                                </Link>
                              </IfUserCan>
                            </DropdownMenuActions>
                          </ButtonDropdownActions>
                        )}
                      />
                    </TableCell>
                  </TableRow>
                )
              })}
            {!isLoading && interactiveMessages.length < 1 && (
              <TableRow>
                <TableCell colSpan={6} className="text-center" data-testid="interactive-message-table-no-results-found">
                  {t('common:NO_RESULTS_FOUND')}
                </TableCell>
              </TableRow>
            )}
            {interactiveMessages.length === 0 && isLoading && (
              <LoadingSpinner isLoading data-testid="interactive-message-table-loading" />
            )}
          </TableBody>
        </Table>

        <TablePagination
          data-testid="interactive-message-table-pagination"
          pagination={pagination}
          localPagination={localPagination}
          handlePaginationChange={handleLocalPaginationChange}
        />
      </Container>
      <Switch>
        <IfUserCanRoute
          permission="interactive-messages.create"
          exact
          path={`${match.url}/create`}
          component={InteractiveMessagesFormRoute}
        />
        <IfUserCanRoute
          permission="interactive-messages.update"
          exact
          path={`${match.url}/:id/edit`}
          component={InteractiveMessagesFormRoute}
        />
        <IfUserCanRoute
          permission="interactive-messages.destroy"
          exact
          path={`${match.url}/:id/delete`}
          component={InteractiveMessagesDeleteRoute}
        />
        <IfUserCanRoute
          permission="interactive-messages.view"
          exact
          path={`${match.url}/:id`}
          component={InteractiveMessagesShowRoute}
        />
      </Switch>
    </div>
  )
}

export default withRouter(InteractiveMessagesIndex)
