import React from 'react'
import Helmet from 'react-helmet'
import <PERSON><PERSON><PERSON><PERSON> from 'react-bootstrap-sweetalert'
import { with<PERSON>out<PERSON> } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import SweetModal from '../../../../common/unconnected/SweetModal'
import {
  useDeleteInteractiveMessages,
  useFetchOneInteractiveMessages,
} from '../../../../../resources/interactiveMessages/requests'
import useDeleteController from '../../../../../hooks/crud/useDeleteController'

const InteractiveMessagesDelete = () => {
  const { t } = useTranslation(['interactiveMessagesPage', 'common'])

  const {
    isModalOpen,
    exit,
    error,
    isAlertShowing,
    closeAlert,
    isLoading,
    model: interactiveMessage,
    submit,
  } = useDeleteController({
    exitToPath: '/interactive-messages',
    useFetchOne: useFetchOneInteractiveMessages,
    useDeleteOne: useDeleteInteractiveMessages,
  })

  if (!interactiveMessage || isLoading) return null

  return (
    <div data-testid="interactive-message-modal-delete-div">
      <Helmet
        title={`${t('TITLE_INTERACTIVE_MESSAGES')} - ${t('common:MODAL_DELETE_BUTTON_CONFIRM')} ${
          interactiveMessage.name
        }?`}
      />

      <SweetModal
        data-testid="interactive-message-modal-delete"
        type="warning"
        title={`${t('common:MODAL_DELETE_BUTTON_CONFIRM')} ${t('TITLE_INTERACTIVE_MESSAGE')}`}
        confirmBtnText={t('common:MODAL_DELETE_BUTTON_CONFIRM')}
        cancelBtnText={t('common:FORM_ACTION_CANCEL')}
        onConfirm={submit}
        show={isModalOpen}
        onCancel={exit}
      >
        {t('interactiveMessagesPage:MESSAGE_MODAL_DELETE')}
      </SweetModal>
      {isAlertShowing && error && (
        <SweetAlert
          data-testid="interactive-message-modal-delete-alert"
          error
          title={t('ERROR_MESSAGE_MODAL')}
          onConfirm={closeAlert}
        >
          {error?.response?.data?.message}
        </SweetAlert>
      )}
    </div>
  )
}

export default withRouter(InteractiveMessagesDelete)
