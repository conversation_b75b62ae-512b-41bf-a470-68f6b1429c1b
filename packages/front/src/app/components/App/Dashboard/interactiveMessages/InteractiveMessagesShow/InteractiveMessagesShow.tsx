import React from 'react'
import { <PERSON> } from 'react-router-dom'
import Helmet from 'react-helmet'
import { with<PERSON><PERSON><PERSON> } from 'react-router-dom'
import { ModalBody, ModalHeader } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { useFetchOneInteractiveMessages } from '../../../../../resources/interactiveMessages/requests'
import useShow<PERSON>ontroller from '../../../../../hooks/crud/useShowController'
import { ModalDigisac } from '../../../styles/common'
import ButtonClose from '../../../../common/unconnected/ButtonClose'
import { getType } from '../InteractiveMessagesIndex/InteractiveMessagesIndex'
import Join from '../../../../common/connected/Join'

const InteractiveMessagesShow = ({ match, history }) => {
  const { t } = useTranslation(['interactiveMessagesPage', 'common'])

  const {
    model: interactiveMessage,
    isOpen,
    exit,
  } = useShowController({
    exitToPath: '/interactive-messages',
    useFetchOne: useFetchOneInteractiveMessages,
    query: {
      attributes: ['id', 'name', 'interactive'],
      include: ['departments'],
    },
  })

  if (!interactiveMessage) return null

  return (
    <div data-testid="visual-interactive-message">
      <Helmet title={`${t('TITLE_INTERACTIVE_MESSAGE')} - ${interactiveMessage.name}`} />

      <ModalDigisac
        data-testid="interactive-message-view-modal"
        isOpen={isOpen}
        toggle={exit}
        autoFocus={false}
        size="ml"
      >
        <ModalHeader data-testid="interactive-message-modal-view-title">
          {interactiveMessage.name}
          <ButtonClose onClick={exit} />
        </ModalHeader>
        <ModalBody data-testid="interactive-message-modal-view-body">
          <b data-testid="interactive-message-view-name">{t('INTERACTIVE_MESSAGES_SHOW_NAME')}</b>
          {interactiveMessage.name}
          <br />
          <b data-testid="interactive-message-view-type">{t('INTERACTIVE_MESSAGES_SHOW_TYPE')}</b>
          {getType(interactiveMessage.interactive.type, t)}
          <br />
          <b data-testid="interactive-message-view-departments">{t('TABLE_COLUMN_DEPARTMENTS')}:&nbsp;</b>
          <Join
            data-testid="interactive-message-view-departments-join"
            items={interactiveMessage.departments}
            render={(department) => <Link to={`/departments/${department.id}`}>{department.name}</Link>}
          />
        </ModalBody>
      </ModalDigisac>
    </div>
  )
}

export default withRouter(InteractiveMessagesShow)
