import React from 'react'
import { Col, Input, Row } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { IconTimeTable } from '../../../../common/unconnected/IconDigisac'
import { TextColor } from '../../../styles/colors'
import { GroupInputFilter } from '../../../styles/table'

function Filters({ filters, handleFilterChange }) {
  const { t } = useTranslation('interactiveMessagesPage')
  return (
    <Row data-testid="interactive-message-filter-name-row">
      <Col md="4" data-testid="interactive-message-filter-name-col">
        <GroupInputFilter data-testid="interactive-message-filter-name-group-input">
          <IconTimeTable
            data-testid="interactive-message-filter-name-icon"
            fill={TextColor}
            width="25"
            height="25"
            className="mr-2"
          />
          <Input
            data-testid="interactive-message-filter-name-input"
            type="text"
            value={filters.name}
            onChange={(e) => handleFilterChange('name', e.target.value)}
            placeholder={t('INPUT_SEARCH_PLACEHOLDER')}
          />
        </GroupInputFilter>
      </Col>
    </Row>
  )
}

export default Filters
