import React, { useEffect, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, useMemo, useState, useRef } from 'react'
import Helmet from 'react-helmet'
import { v4 as uuid } from 'uuid'
import assignDeep from 'lodash/merge'
import { useSelector } from 'react-redux'
import {
  Form,
  ModalBody,
  ModalHeader,
  FormGroup,
  Card,
  CardHeader,
  CardBody,
  Row,
  Col,
  Popover,
  PopoverHeader,
  PopoverBody,
} from 'reactstrap'
import SweetAlert from 'react-bootstrap-sweetalert'
import { withRouter } from 'react-router-dom'
import pick from 'lodash/pick'
import omit from 'lodash/omit'
import { useTranslation } from 'react-i18next'
import { required } from '../../../../../utils/validator/validators'
import { isAllBlankSpace } from '../../../../../utils/isAllBlankSpace'
import InputGroup, { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import useFormController from '../../../../../hooks/crud/useFormController'
import {
  useCreateInteractiveMessages,
  useFetchOneInteractiveMessages,
  useUpdateInteractiveMessages,
} from '../../../../../resources/interactiveMessages/requests'
import templateImage from '../../../../../assets/logos/template-image.svg'
import { ModalDigisac, ModalFooter, GroupInput } from '../../../styles/common'
import * as HsmStyles from '../../../../common/unconnected/HsmModal/styles'
import * as S from '../../../styles/common'
import { IconDepartment, IconMessageList } from '../../../../common/unconnected/IconDigisac'
import Select from '../../../../common/unconnected/Select'
import ButtonClose from '../../../../common/unconnected/ButtonClose'
import Icon from '../../../../common/unconnected/Icon'

import { TextColor } from '../../../styles/colors'
import { Media } from '../../../../common/unconnected/HsmModal/styles'
import InteractiveMessagesSelect from '../../../../common/connected/InteractiveMessagesSelect'
import DepartmentsSelect from '../../../../common/connected/DepartmentsSelect'
import { getUser } from '../../../../../modules/auth/selectors'
import IfUserCan from '../../../../common/connected/IfUserCan'
import readFileAsDataURL from '../../../../../utils/readFileAsDataURL'
import { hasPermission } from '../../../../common/connected/IfUserCan/IfUserCan'
import { Button } from '../../../../common/unconnected/ui/button'
import { Label } from '../../../../common/unconnected/ui/label'
import { File, Info, Play, PlusCircle, Trash2 } from 'lucide-react'
import { Textarea } from '../../../../common/unconnected/ui/textarea'
// import { PreviewMessage } from './preview-message'
import { Input } from '../../../../common/unconnected/ui/input'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../../../common/unconnected/ui/dialog'
import { HoverCard, HoverCardContent, HoverCardTrigger } from '../../../../common/unconnected/ui/hover-card'
import { customStyles } from '../../../../common/unconnected/GenericResourceSelect'
import {
  handleAutocompleteSelect,
  TEMPLATE_VARIABLES,
  TextareaAutocomplete,
} from '../../../../common/unconnected/TextareaAutocomplete'

const interpolate = (text = '') => {
  const replaceValue = 'XXXXXXXXXXXXXXX' // 15 chars

  const criterias = ['{{contact_name}}', '{{protocol_number}}', '{{contact_number}}']

  let newText = text

  criterias.forEach((searchValue) => {
    newText = newText.replaceAll(searchValue, replaceValue)
  })

  return newText
}

export const CharacterLimit = (props) => {
  const { message, limit, label = '', t, ...rest } = props
  return (
    <div
      data-testid="interactive-message-component-char-limit-div"
      style={{
        color: 'gray',
        float: 'right',
        width: '100%',
        margin: '4px 0px 0px 0px',
        display: 'flex',
        ...props.style,
      }}
      {...rest}
    >
      <label
        data-testid="interactive-message-component-char-limit-label"
        style={{
          width: '100%',
          margin: 0,
        }}
      >{`${(message || '').length} / ${limit} ${label}`}</label>
      {props.children}
    </div>
  )
}

const Component = (props) => {
  const {
    handleSelectInteractiveMessage,
    handleChangeListSectionName,
    handleChangeButton,
    handleChangeListItemTitle,
    handleChangeListSectionItemTitle,
    handleRemoveButton,
    handleAddButton,
    handleChangeActionType,
    handleChangeFooter,
    handleChangeBody,
    handleChangeHeaderFile,
    handleChangeHeaderText,
    handleChangeHeaderType,
    t,
    isEditable,
    interactive,
    header,
    body,
    footer,
    action,
    buttons,
    simpleListSection,
    simpleListRows,
    model,
    setProperty,
    bindInput,
    validation,
    user,
    openedBy,
    botVersion,
    setShowSidebarHelper,
    isHeaderPopoverShowing,
    toggleHeaderPopover,
    isBodyPopoverShowing,
    toggleBodyPopover,
    isFooterPopoverShowing,
    toggleFooterPopover,
    allDisabled,
  } = props

  if (!user) return

  return (
    <>
      <main
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '24px',
        }}
      >
        <IfUserCan
          permissions={['interactive-messages.send']}
          render={() => (
            <>
              <IfUserCan
                permissions={['interactive-messages.view']}
                render={() => (
                  <FormGroup
                    data-testid="interactive-message-component-interactive-message-form-group"
                    style={{ marginBottom: 0 }}
                  >
                    <Label
                      style={{
                        marginBottom: '4px',
                      }}
                    >
                      {`${t('TITLE_INTERACTIVE_MESSAGE')} (${t('FILL_IN_FROM_AN_EXISTING')})`}
                    </Label>
                    <InputGroupWrapper
                      id="interactiveMessage"
                      data-testid="interactive-message-component-interactive-message-input-group-wrapper"
                      classNameInputGroupWrapper="mb-0"
                      render={(input) => (
                        <S.GroupInput
                          data-testid="interactive-message-component-interactive-message-group-input"
                          withIcon
                        >
                          <InteractiveMessagesSelect
                            data-testid="interactive-message-component-interactive-message-select"
                            id={input.id}
                            isPaged
                            placeholder={t('common:SELECT_LOADING_PLACEHOLDER')}
                            value={model.interactiveMessage}
                            onChange={handleSelectInteractiveMessage}
                            extraQuery={{
                              include: [
                                {
                                  model: 'departments',
                                  where: {
                                    ...(!hasPermission(
                                      user.permissions,
                                      'interactive-messages.management.all.departments',
                                    ) && {
                                      id: {
                                        $in: (user.departments || []).map((d) => d.id),
                                      },
                                    }),
                                    archivedAt: { $eq: null },
                                  },
                                },
                                'file',
                              ],
                            }}
                            disabled={openedBy === 'bot' && allDisabled}
                          />
                        </S.GroupInput>
                      )}
                    />
                  </FormGroup>
                )}
              />

              {openedBy !== 'bot' && (
                <>
                  <FormGroup
                    data-testid="interactive-message-component-name-form-group"
                    style={{
                      marginBottom: 0,
                    }}
                  >
                    <InputGroup
                      data-testid="interactive-message-component-name-input-group"
                      id="name"
                      classNameInputGroupWrapper="mb-0"
                      label={`${t('TABLE_COLUMN_NAME')} (${t('REQUIRED_FOR_CREATION_ONLY')})`}
                      {...{ bindInput, validation }}
                    />
                  </FormGroup>

                  <IfUserCan
                    permissions={[
                      'interactive-messages.create',
                      'interactive-messages.view',
                      'interactive-messages.update',
                    ]}
                    any
                    render={() => (
                      <>
                        <InputGroupWrapper
                          classNameInputGroupWrapper="mb-0"
                          data-testid="interactive-message-component-departments-input-group"
                          id="departments"
                          label={`${t('TABLE_COLUMN_DEPARTMENTS')} (${t('REQUIRED_FOR_CREATION_ONLY')})`}
                          {...{
                            bindInput,
                            validation,
                            model,
                            setProperty,
                          }}
                          render={(input) => (
                            <S.GroupInput data-testid="interactive-message-component-departments-group-input" withIcon>
                              <DepartmentsSelect
                                data-testid="interactive-message-component-departments-select"
                                isMulti
                                isPaged
                                icon={
                                  <IconDepartment
                                    data-testid="interactive-message-component-departments-icon"
                                    fill={TextColor}
                                    width="30"
                                    height="30"
                                  />
                                }
                                id={input.id}
                                value={input.model[input.id]}
                                onChange={(value) => input.setProperty(input.id, value)}
                                onBlur={() => input.validation.setTouched(input.id)}
                                hideArchived
                                extraQuery={{
                                  ...(user?.account?.settings?.ticketsEnabled &&
                                    !hasPermission(
                                      user.permissions,
                                      'interactive-messages.management.all.departments',
                                    ) && {
                                      where: {
                                        id: {
                                          $in: (user.departments || []).map((d) => d.id),
                                        },
                                      },
                                    }),
                                }}
                              />
                            </S.GroupInput>
                          )}
                        />
                      </>
                    )}
                  />
                </>
              )}
            </>
          )}
        />
        <div data-testid="interactive-message-component-action-type-col">
          <Label
            style={{
              marginBottom: '4px',
            }}
          >
            {t('ACTION_TYPE')}
          </Label>
          <InputGroupWrapper
            data-testid="interactive-message-component-action-type-input-group"
            id="actionType"
            classNameInputGroupWrapper="mb-0"
            required
            {...{
              bindInput,
              validation,
              model,
              setProperty,
            }}
            render={(input) => (
              <GroupInput data-testid="interactive-message-component-action-type-group-input">
                <Select
                  data-testid="interactive-message-component-action-type-select"
                  id={input.id}
                  value={input?.model?.[input?.id]}
                  onChange={(value) => {
                    handleChangeActionType(value?.value)
                    return input.setProperty(input.id, value)
                  }}
                  styles={customStyles}
                  disabled={!isEditable}
                  options={getActionTypeOptions(t)}
                  instanceId={input.id}
                  getOptionValue={(o) => o.value}
                  getOptionLabel={(o) => o.label}
                  searchPromptText={t('common:SELECT_SEARCH_PROMPT_TEXT')}
                  loadingPlaceholder={t('common:TABLE_LOADING')}
                  placeholder={t('common:SELECT_LOADING_PLACEHOLDER')}
                  noResultsText={t('common:NO_RESULTS_FOUND')}
                  clearValueText={t('common:MESSAGE_CLEAR')}
                  clearAllText={t('common:MESSAGE_CLEAR_ALL')}
                />
              </GroupInput>
            )}
          />
        </div>
        {openedBy === 'bot' && botVersion === 'v3' && (
          <div style={{ fontSize: 14, color: '#586171' }}>
            {t('botsPage:MESSAGE_EDIT_VARIABLES_HINT')} <br />
            {t('botsPage:MESSAGE_EDIT_VARIABLES_HELP')}
            <button
              type="button"
              onClick={() => setShowSidebarHelper(true)}
              style={{
                background: 'none',
                border: 'none',
                padding: '0px 4px',
                fontWeight: '600',
                color: '#324B7D',
                textDecoration: 'underline',
              }}
            >
              {t('botsPage:MESSAGE_EDIT_VARIABLES_HELP_BUTTON')}
            </button>
          </div>
        )}
        <div data-testid="interactive-message-component-header-card">
          <Label
            style={{
              marginBottom: '4px',
            }}
          >
            {t('CONFIGURE_MESSAGE_HEADER')}
          </Label>
          <div
            data-testid="interactive-message-component-header-card-header"
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '8px 0',
            }}
          >
            <Button
              data-testid="interactive-message-component-header-text-button"
              size="xs"
              color="primary"
              disabled={!isEditable}
              variant={header?.type === 'text' ? 'default' : 'outline'}
              onClick={() => handleChangeHeaderType('text')}
            >
              {t('SELECT_HEADER_TYPE_OPTION_TEXT')}
            </Button>
            <Button
              data-testid="interactive-message-component-header-image-button"
              size="xs"
              color="primary"
              disabled={!isEditable || model.actionType?.value !== 'button'}
              variant={header?.type === 'image' ? 'default' : 'outline'}
              onClick={() => handleChangeHeaderType('image')}
            >
              {t('SELECT_HEADER_TYPE_OPTION_IMAGEM')}
            </Button>
            <Button
              data-testid="interactive-message-component-header-document-button"
              size="xs"
              disabled={!isEditable || model.actionType?.value !== 'button'}
              color="primary"
              variant={header?.type === 'document' ? 'default' : 'outline'}
              onClick={() => handleChangeHeaderType('document')}
            >
              {t('SELECT_HEADER_TYPE_OPTION_DOCUMENT')}
            </Button>
            <Button
              data-testid="interactive-message-component-header-video-button"
              size="xs"
              disabled={!isEditable || model.actionType?.value !== 'button'}
              color="primary"
              variant={header?.type === 'video' ? 'default' : 'outline'}
              onClick={() => handleChangeHeaderType('video')}
            >
              {t('SELECT_HEADER_TYPE_OPTION_VIDEO')}
            </Button>
          </div>
          <div data-testid="interactive-message-component-header-card-body">
            {header &&
              (header?.type === 'text' ? (
                <div>
                  {openedBy === 'bot' && botVersion === 'v3' ? (
                    <TextareaAutocomplete
                      data-testid="interactive-message-component-header-card-body-input-group"
                      {...{
                        bindInput,
                        validation,
                      }}
                      id="headerInteractiveInput"
                      required={false}
                      disabled={!isEditable}
                      placeholder={t('INPUT_HEADER_PLACEHOLDER_TEXT')}
                      value={header?.text || ''}
                      onChange={handleChangeHeaderText}
                      maxLength={60 + ((header?.text?.length || 0) - interpolate(header?.text).length)}
                      trigger="{{"
                      options={TEMPLATE_VARIABLES}
                      changeOnSelect={handleAutocompleteSelect}
                      style={{ maxHeight: 400 }}
                    />
                  ) : (
                    <Textarea
                      data-testid="interactive-message-component-header-card-body-input-group"
                      {...{
                        bindInput,
                        validation,
                      }}
                      id="headerInteractiveInput"
                      required={false}
                      disabled={!isEditable}
                      placeholder={t('INPUT_HEADER_PLACEHOLDER_TEXT')}
                      value={header?.text || ''}
                      onChange={handleChangeHeaderText}
                      maxLength={60 + ((header?.text?.length || 0) - interpolate(header?.text).length)}
                    />
                  )}
                  <CharacterLimit message={interpolate(header?.text)} limit={60} label={t('INPUT_PLACEHOLDER_CHARS')}>
                    <HoverCard closeDelay={100}>
                      <HoverCardTrigger>
                        <Info color="#324b7d" size={16} />
                      </HoverCardTrigger>
                      <HoverCardContent side="top" align="end">
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                          <strong>{t('AVAILABLE_INTERPOLATION_PARAMS')}</strong>
                          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                            <p
                              style={{
                                margin: 0,
                              }}
                            >
                              <span
                                style={{
                                  fontWeight: 'bold',
                                }}
                              >{`{{contact_name}}`}</span>
                              {`: ${t('CONTACT_NAME')}`}
                            </p>
                            <p
                              style={{
                                margin: 0,
                              }}
                            >
                              <span
                                style={{
                                  fontWeight: 'bold',
                                }}
                              >{`{{contact_number}}`}</span>
                              {`: ${t('CONTACT_NUMBER')}`}
                            </p>
                            <p
                              style={{
                                margin: 0,
                              }}
                            >
                              <span
                                style={{
                                  fontWeight: 'bold',
                                }}
                              >{`{{protocol_number}}`}</span>
                              {`: ${t('PROTOCOL_NUMBER')}`}
                            </p>
                          </div>
                          <span>{t('INTERPOLATION_DESCRIPTION')}</span>
                        </div>
                      </HoverCardContent>
                    </HoverCard>
                  </CharacterLimit>
                </div>
              ) : (
                <div
                  data-testid="interactive-message-component-header-card-body-div"
                  style={{
                    marginTop: '8px',
                  }}
                >
                  {header.type === 'image' ? (
                    <img
                      data-testid="interactive-message-component-header-card-body-img"
                      src={model.file?.base64Url || model.file?.url || templateImage}
                      alt="header"
                      style={{
                        margin: 'auto',
                        width: '100%',
                        maxHeight: '300px',
                        objectFit: 'contain',
                        borderRadius: '10px',
                      }}
                    />
                  ) : model.file?.base64Url || model.file?.url ? (
                    header.type === 'video' ? (
                      <video
                        data-testid="interactive-message-component-header-card-body-video"
                        width="100%"
                        height="300"
                        controls
                        src={model.file?.base64Url || model.file?.url}
                      >
                        <track kind="captions" />
                      </video>
                    ) : (
                      <div
                        style={{ padding: '10px 5px 20px 0' }}
                        data-testid="interactive-message-component-header-card-body-document"
                      >
                        <a
                          data-testid="interactive-message-component-header-card-body-a"
                          href={model.file?.base64Url || model.file?.url}
                          download={model.file?.base64Url && model.file?.fileName}
                          target="_blank"
                          rel="noreferrer"
                        >
                          {model.file?.fileName || model.file?.name || t('SELECT_HEADER_TYPE_OPTION_DOCUMENT')}
                        </a>
                      </div>
                    )
                  ) : (
                    <Media
                      format={header.type}
                      data-testid="interactive-message-component-header-card-body-default-media"
                    >
                      {header.type === 'video' ? (
                        <Play
                          color="#324b7d"
                          data-testid="interactive-message-component-header-card-body-default-icon"
                        />
                      ) : (
                        <File
                          color="#324b7d"
                          data-testid="interactive-message-component-header-card-body-default-icon"
                        />
                      )}
                    </Media>
                  )}
                  <label
                    data-testid="interactive-message-component-header-card-body-input-file"
                    htmlFor="inputfile"
                    style={{
                      ...(!isEditable && { opacity: 0.65 }),
                      width: '100%',
                    }}
                  >
                    <InputGroup
                      data-testid="interactive-message-component-header-card-body-input-group"
                      id="inputfile"
                      name="inputfile"
                      type="file"
                      disabled={!isEditable}
                      accept={(header.type === 'image' && 'image/*') || (header.type === 'video' && 'video/*') || '*'}
                      onChange={handleChangeHeaderFile}
                      style={{
                        display: 'none',
                      }}
                    />
                    <HsmStyles.ButtonFile
                      data-testid="interactive-message-component-header-card-body-button-file"
                      style={{
                        width: '100%',
                      }}
                    >
                      <File
                        style={{
                          marginRight: '4px',
                          height: '16px',
                          width: '16px',
                        }}
                        data-testid="interactive-message-component-header-card-body-icon"
                      />
                      {t('BUTTON_HEADER_CHOOSE_FILE')}
                    </HsmStyles.ButtonFile>
                  </label>
                  {model.file?.fileName && (
                    <div data-testid="interactive-message-component-header-card-body-filename-div">
                      <label
                        style={{ padding: '10px 5px 6px 3px' }}
                        data-testid="interactive-message-component-header-card-body-filename-label"
                      >
                        {model.file?.fileName}
                      </label>
                    </div>
                  )}
                </div>
              ))}
          </div>
        </div>
        <div data-testid="interactive-message-component-body-card">
          <Label
            data-testid="interactive-message-component-body-label"
            style={{
              marginBottom: '4px',
            }}
          >
            {t('CONFIGURE_MESSAGE_BODY')}
          </Label>
          {openedBy === 'bot' && botVersion === 'v3' ? (
            <TextareaAutocomplete
              data-testid="interactive-message-component-card-body-input-group"
              {...{
                bindInput,
                validation,
              }}
              id="bodyInteractiveInput"
              required
              disabled={!isEditable}
              placeholder={t('INPUT_BODY_PLACEHOLDER_TEXT')}
              value={body?.text}
              onChange={handleChangeBody}
              maxLength={1024 + ((body?.text?.length || 0) - interpolate(body?.text).length)}
              trigger="{{"
              options={TEMPLATE_VARIABLES}
              changeOnSelect={handleAutocompleteSelect}
              style={{ maxHeight: 400 }}
            />
          ) : (
            <Textarea
              data-testid="interactive-message-component-card-body-input-group"
              {...{
                bindInput,
                validation,
              }}
              id="bodyInteractiveInput"
              required
              disabled={!isEditable}
              placeholder={t('INPUT_BODY_PLACEHOLDER_TEXT')}
              value={body?.text}
              onChange={handleChangeBody}
              maxLength={1024 + ((body?.text?.length || 0) - interpolate(body?.text).length)}
            />
          )}
          <CharacterLimit
            data-testid="interactive-message-component-card-body-char-limit"
            message={interpolate(body?.text)}
            limit={1024}
            label={t('INPUT_PLACEHOLDER_CHARS')}
          >
            <HoverCard closeDelay={100}>
              <HoverCardTrigger>
                <Info color="#324b7d" size={16} />
              </HoverCardTrigger>
              <HoverCardContent side="top" align="end">
                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                  <strong>{t('AVAILABLE_INTERPOLATION_PARAMS')}</strong>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                    <p
                      style={{
                        margin: 0,
                      }}
                    >
                      <span
                        style={{
                          fontWeight: 'bold',
                        }}
                      >{`{{contact_name}}`}</span>
                      {`: ${t('CONTACT_NAME')}`}
                    </p>
                    <p
                      style={{
                        margin: 0,
                      }}
                    >
                      <span
                        style={{
                          fontWeight: 'bold',
                        }}
                      >{`{{contact_number}}`}</span>
                      {`: ${t('CONTACT_NUMBER')}`}
                    </p>
                    <p
                      style={{
                        margin: 0,
                      }}
                    >
                      <span
                        style={{
                          fontWeight: 'bold',
                        }}
                      >{`{{protocol_number}}`}</span>
                      {`: ${t('PROTOCOL_NUMBER')}`}
                    </p>
                  </div>
                  <span>{t('INTERPOLATION_DESCRIPTION')}</span>
                </div>
              </HoverCardContent>
            </HoverCard>
          </CharacterLimit>
        </div>
        <div data-testid="interactive-message-component-footer-card">
          <Label
            data-testid="interactive-message-component-footer-label"
            style={{
              marginBottom: '4px',
            }}
          >
            {t('CONFIGURE_MESSAGE_FOOTER')}
          </Label>
          {openedBy === 'bot' && botVersion === 'v3' ? (
            <TextareaAutocomplete
              data-testid="interactive-message-component-footer-card-body-input-group"
              {...{
                bindInput,
                validation,
              }}
              id="footerInteractiveInput"
              disabled={!isEditable}
              placeholder={t('INPUT_FOOTER_PLACEHOLDER_TEXT')}
              value={footer?.text}
              onChange={handleChangeFooter}
              maxLength={60 + ((footer?.text?.length || 0) - interpolate(footer?.text).length)}
              trigger="{{"
              options={TEMPLATE_VARIABLES}
              changeOnSelect={handleAutocompleteSelect}
              style={{ maxHeight: 400 }}
            />
          ) : (
            <Textarea
              data-testid="interactive-message-component-footer-card-body-input-group"
              {...{
                bindInput,
                validation,
              }}
              id="footerInteractiveInput"
              disabled={!isEditable}
              placeholder={t('INPUT_FOOTER_PLACEHOLDER_TEXT')}
              value={footer?.text}
              onChange={handleChangeFooter}
              maxLength={60 + ((footer?.text?.length || 0) - interpolate(footer?.text).length)}
            />
          )}
          <CharacterLimit
            data-testid="interactive-message-component-footer-card-body-char-limit"
            message={interpolate(footer?.text)}
            limit={60}
            label={t('INPUT_PLACEHOLDER_CHARS')}
          >
            <HoverCard closeDelay={100}>
              <HoverCardTrigger>
                <Info color="#324b7d" size={16} />
              </HoverCardTrigger>
              <HoverCardContent side="top" align="end">
                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                  <strong>{t('AVAILABLE_INTERPOLATION_PARAMS')}</strong>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                    <p
                      style={{
                        margin: 0,
                      }}
                    >
                      <span
                        style={{
                          fontWeight: 'bold',
                        }}
                      >{`{{contact_name}}`}</span>
                      {`: ${t('CONTACT_NAME')}`}
                    </p>
                    <p
                      style={{
                        margin: 0,
                      }}
                    >
                      <span
                        style={{
                          fontWeight: 'bold',
                        }}
                      >{`{{contact_number}}`}</span>
                      {`: ${t('CONTACT_NUMBER')}`}
                    </p>
                    <p
                      style={{
                        margin: 0,
                      }}
                    >
                      <span
                        style={{
                          fontWeight: 'bold',
                        }}
                      >{`{{protocol_number}}`}</span>
                      {`: ${t('PROTOCOL_NUMBER')}`}
                    </p>
                  </div>
                  <span>{t('INTERPOLATION_DESCRIPTION')}</span>
                </div>
              </HoverCardContent>
            </HoverCard>
          </CharacterLimit>
        </div>

        {!!model.actionType && (
          <>
            {model.actionType?.value === 'simpleList' && (
              <>
                <div>
                  <Label htmlFor="listSectionName" data-testid="interactive-message-component-list-section-name-label">
                    {t('CHOOSE_LIST_NAME')}
                  </Label>
                  <Input
                    data-testid="interactive-message-component-list-section-name-input-group"
                    id="listSectionName"
                    type="text"
                    required
                    disabled={!isEditable}
                    placeholder={t('LABEL_LIST_NAME_PLACEHOLDER')}
                    value={action.button}
                    onChange={handleChangeListSectionName}
                    maxLength={20}
                  />

                  <CharacterLimit
                    limit={20}
                    message={action.button}
                    data-testid="interactive-message-component-list-section-name-char-limit"
                  />
                </div>
                <div>
                  <Label
                    htmlFor="listSectionItemTitle0"
                    data-testid="interactive-message-component-list-section-item-title-0"
                    style={{
                      marginBottom: '4px',
                    }}
                  >
                    {t('LABEL_LIST_TITLE')}
                  </Label>
                  <Input
                    data-testid="interactive-message-component-list-section-item-title-0-input-group"
                    id="listSectionItemTitle0"
                    type="text"
                    required
                    disabled={!isEditable}
                    placeholder={t('INPUT_LIST_ITEM_SECTION_TITLE')}
                    value={simpleListSection?.title}
                    onChange={handleChangeListSectionItemTitle}
                    maxLength={24}
                  />
                  <CharacterLimit
                    limit={24}
                    message={simpleListSection?.title}
                    data-testid="interactive-message-component-list-section-item-title-0-char-limit"
                  />
                </div>
                <div>
                  <Label
                    style={{
                      marginBottom: '4px',
                    }}
                    data-testid="interactive-message-component-items-list"
                  >
                    {t('LABEL_ITEMS_LIST')}
                  </Label>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    {simpleListRows.map((row, index) => (
                      <div>
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '16px',
                          }}
                          key={index}
                          data-testid={`interactive-message-component-items-list-${index}-row`}
                        >
                          <div
                            style={{
                              width: '100%',
                            }}
                            data-testid={`interactive-message-component-items-list-${index}-col`}
                          >
                            <Input
                              data-testid={`interactive-message-component-items-list-${index}-input-group`}
                              id={`listItemTitle${index}`}
                              type="text"
                              required
                              disabled={!isEditable}
                              placeholder={t('ENTER_ITEM_NAME')}
                              value={row.title}
                              onChange={(e) => handleChangeListItemTitle(e, index)}
                              maxLength={24}
                            />
                          </div>
                          <div data-testid={`interactive-message-component-items-list-${index}-col-trash-button`}>
                            <Button
                              data-testid={`interactive-message-component-items-list-${index}-trash-button`}
                              className="float-right"
                              size="icon-sm"
                              variant="destructive"
                              disabled={!isEditable || simpleListRows.length <= 1}
                              onClick={() => handleRemoveButton(index)}
                            >
                              <Trash2 data-testid={`interactive-message-component-items-list-${index}-icon`} />
                            </Button>
                          </div>
                        </div>
                        <CharacterLimit
                          limit={24}
                          message={row.title}
                          data-testid={`interactive-message-component-items-list-${index}-char-limit`}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
            {model.actionType?.value === 'button' && (
              <div>
                <Label
                  style={{
                    marginBottom: '4px',
                  }}
                >
                  {t('CONFIGURE_MESSAGE_BUTTONS')}
                </Label>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                  {buttons.map((button, index) => (
                    <div>
                      <div
                        key={index}
                        data-testid={`interactive-message-component-buttons-${index}-row`}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '16px',
                        }}
                      >
                        <div
                          style={{
                            width: '100%',
                          }}
                          data-testid={`interactive-message-component-buttons-${index}-col`}
                        >
                          <Input
                            data-testid={`interactive-message-component-buttons-${index}-input-group`}
                            id={`buttonsInteractiveInput${index}`}
                            type="text"
                            required
                            disabled={!isEditable}
                            placeholder={t('ENTER_BUTTON_TEXT')}
                            value={button.reply.title}
                            onChange={(e) => handleChangeButton(e, index)}
                            maxLength={20}
                          />
                        </div>
                        <div data-testid={`interactive-message-component-buttons-${index}-col-trash-button`}>
                          <Button
                            data-testid={`interactive-message-component-buttons-${index}-trash-button`}
                            className="float-right"
                            size="icon-sm"
                            variant="destructive"
                            disabled={
                              !isEditable || (interactive.type === 'list' ? simpleListRows : buttons).length <= 1
                            }
                            onClick={() => handleRemoveButton(index)}
                          >
                            <Trash2 data-testid={`interactive-message-component-buttons-${index}-icon`} />
                          </Button>
                        </div>
                      </div>
                      <CharacterLimit
                        limit={20}
                        message={button.reply.title}
                        data-testid={`interactive-message-component-buttons-${index}-char-limit`}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
            <div
              style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
              data-testid="interactive-message-component-add-button-card-header"
            >
              <Button
                data-testid="interactive-message-component-add-button-button"
                variant="ghost"
                onClick={handleAddButton}
                disabled={
                  !isEditable || (interactive.type === 'list' ? simpleListRows?.length >= 10 : buttons.length >= 3)
                }
              >
                <PlusCircle
                  style={{
                    marginRight: '4px',
                  }}
                />
                {interactive.type === 'list' ? t('ADD_ITEM') : t('ADD_BUTTON')}
              </Button>
            </div>
          </>
        )}
        {/* {!!model.actionType && <PreviewMessage model={model} />} */}
      </main>
    </>
  )
}

const getActionTypeOptions = (t) => [
  {
    value: 'simpleList',
    label: t('BUTTON_SIMPLE_LIST'),
  },
  {
    value: 'button',
    label: t('BUTTON_BUTTONS'),
  },
]

export const formatToApi = (interactiveMessage) => ({
  ...pick(interactiveMessage, ['name', 'file', 'interactive', 'departments']),
  interactive: {
    ...omit(interactiveMessage.interactive, ['header']),
    ...(interactiveMessage.interactive.header?.type === 'text'
      ? interactiveMessage.interactive.header?.text?.trim()?.length && {
          header: interactiveMessage.interactive.header,
        }
      : interactiveMessage.file && {
          header: interactiveMessage.interactive.header,
        }),
  },
})

export const formatFromApi = (interactiveMessage, t) => {
  const auxModel = {
    name: '',
    file: null,
    actionType: null,
    departments: interactiveMessage?.departments || [],
    interactive: {
      header: {
        type: 'text',
      },
    },
  }

  const response = {
    ...assignDeep(auxModel, interactiveMessage),
    actionType: getActionTypeOptions(t).find(
      (i) =>
        interactiveMessage?.interactive &&
        i.value ===
          (interactiveMessage.interactive.type === 'list' ? 'simpleList' : interactiveMessage.interactive.type),
    ),
  }
  return response
}

const InteractiveMessagesForm = forwardRef((props, ref) => {
  const dialogContentRef = useRef(null)

  const {
    allDisabled,
    match,
    history,
    exitToPath = '/interactive-messages',
    openedBy = 'form',
    botVersion,
    setShowSidebarHelper,
    withModal = true,
    externalModel,
  } = props

  const initialModel = useMemo(
    () => ({
      name: '',
      file: null,
      actionType: null,
      departments: [],
      interactive: {
        header: {
          type: 'text',
        },
      },
    }),
    [],
  )

  const user = useSelector(getUser)

  const { t } = useTranslation(['interactiveMessagesPage', 'common', 'botsPage'])

  const blankSpaceValidation = [isAllBlankSpace, t('common:INVALID_FIELD')]

  const getSimpleListItemsTitle = () => {
    const listItemValidation = {}

    for (let i = 0; i < 10; i++) {
      listItemValidation[`listItemTitle${i}`] = [
        [
          (value) => {
            if (model.actionType?.value !== 'simpleList') return true

            const rows = value.inputData.interactive.action?.sections?.[0]?.rows

            if (!rows?.[i]) return true

            const text = rows?.[i]?.title?.trim() || ''

            return text.length >= 1 && text.length <= 24
          },
          t('REQUIRED_TEXT_BETWEEN', { min: 1, max: 24 }),
        ],
      ]
    }
    return listItemValidation
  }

  const getButtonInputsTitle = () => {
    const buttonValidation = {}

    for (let i = 0; i < 3; i++) {
      buttonValidation[`buttonsInteractiveInput${i}`] = [
        [
          (value) => {
            if (model.actionType?.value !== 'button') return true

            const buttons = value.inputData.interactive.action?.buttons

            if (!buttons?.[i]) return true

            const text = buttons?.[i]?.reply?.title?.trim() || ''

            return text.length >= 1 && text.length <= 24
          },
          t('REQUIRED_TEXT_BETWEEN', { min: 1, max: 24 }),
        ],
        [
          (value) => {
            if (model.actionType?.value !== 'button') return true

            const buttons = value.inputData.interactive.action?.buttons

            if (!buttons?.[i]) return true

            const textButtons = buttons?.map((bt) => bt?.reply?.title?.trim() || '')

            const text = textButtons?.[i]

            return !textButtons.some((item, index) => item === text && index !== i)
          },
          t('REQUIRED_DIFF_BUTTONS'),
        ],
      ]
    }
    return buttonValidation
  }

  const validationRules = {
    ...(props.openedBy !== 'bot' && {
      name: [
        [required, props.openedBy === 'form' ? t('common:REQUIRED_FIELD') : t('REQUIRED_FOR_CREATION_ONLY')],
        blankSpaceValidation,
      ],
      departments: [
        [
          (input) => input.inputData.departments?.length > 0,
          props.openedBy === 'form' ? t('common:REQUIRED_FIELD') : t('REQUIRED_FOR_CREATION_ONLY'),
        ],
      ],
    }),

    headerInteractiveInput: [
      [
        (value) => {
          const text = value.inputData.interactive.header?.text?.trim() || ''

          if (!text.length) return true

          const interpolatedText = interpolate(text)

          return interpolatedText.length >= 1 && interpolatedText.length <= 60
        },
        t('REQUIRED_TEXT_BETWEEN', { min: 0, max: 60 }),
      ],
    ],

    bodyInteractiveInput: [
      [
        (value) => {
          const text = value.inputData.interactive.body?.text?.trim() || ''

          const interpolatedText = interpolate(text)

          return interpolatedText.length >= 1 && interpolatedText.length <= 1024
        },
        t('REQUIRED_TEXT_BETWEEN', { min: 1, max: 1024 }),
      ],
    ],
    footerInteractiveInput: [
      [
        (value) => {
          const text = value.inputData.interactive.footer?.text?.trim() || ''

          if (!text.length) return true

          const interpolatedText = interpolate(text)

          return interpolatedText.length >= 1 && interpolatedText.length <= 60
        },
        t('REQUIRED_TEXT_BETWEEN', { min: 0, max: 60 }),
      ],
    ],
    actionType: [[(value) => !!model.actionType, t('common:REQUIRED_FIELD')]],
    listSectionName: [
      [
        (value) => {
          if (model.actionType?.value !== 'simpleList') return true

          const text = value.inputData.interactive.action?.button?.trim() || ''

          return text.length >= 1 && text.length <= 20
        },
        t('REQUIRED_TEXT_BETWEEN', { min: 1, max: 20 }),
      ],
    ],
    listSectionItemTitle0: [
      [
        (value) => {
          if (model.actionType?.value !== 'simpleList') return true

          const text = value.inputData.interactive.action?.sections?.[0]?.title?.trim() || ''

          return text.length >= 1 && text.length <= 24
        },
        t('REQUIRED_TEXT_BETWEEN', { min: 1, max: 24 }),
      ],
    ],
    ...getSimpleListItemsTitle(),
    ...getButtonInputsTitle(),
  }

  const buildQuery = () => ({
    query: JSON.stringify({
      include: ['departments', 'file'],
    }),
  })

  const useController = ({ match, history }) => {
    const { id } = match.params

    return useFormController({
      id,
      exitToPath,
      initialModel,
      validationRules,
      buildQuery,
      formatToApi,
      formatFromApi: (params) => formatFromApi(params, t),
      useCreateOne: useCreateInteractiveMessages,
      useUpdateOne: useUpdateInteractiveMessages,
      useFetchOne: useFetchOneInteractiveMessages,
    })
  }

  const {
    submit,
    exit,
    isModalOpen,
    isEditing,
    model,
    setProperty,
    setModel,
    updateModel,
    isAlertShowing,
    closeAlert,
    isLoading,
    error,
    bindInput,
    validation,
  } = {
    ...useController({ match, history }),
    ...('isModalOpen' in props && { isModalOpen: !!props.isModalOpen }),
    ...(props.exit && {
      exit: async () => {
        setModel(formatFromApi(initialModel, t))
        props.exit()
      },
    }),
  }

  const isEditable = !allDisabled

  const interactive = model?.interactive
  const header = interactive?.header
  const body = interactive?.body
  const footer = interactive?.footer
  const action = interactive?.action
  const sections = action?.sections || []
  const buttons = action?.buttons || []
  const simpleListSection = sections?.[0]
  const simpleListRows = simpleListSection?.rows || []

  useEffect(() => {
    if (externalModel) {
      setModel(formatFromApi(assignDeep(initialModel, externalModel), t))
    }
  }, [])

  useEffect(() => {
    if (props.onChange && model) {
      props.onChange(model)
    }
  }, [model])

  const title = `${
    isEditing ? t('common:LABEL_EDITING') : openedBy === 'chat' ? t('common:LABEL_SUBMIT') : t('common:LABEL_CREATING')
  } ${t('TITLE_INTERACTIVE_MESSAGE')}`

  const errorCode = error?.response?.status

  const handleSubmit = async (type) => {
    await validation.setTouched('actionType')
    const valid = await validation.validateAll()

    if (type === 'saveAndSend') {
      if (!valid) return
      await submit(model)
    }

    if (
      !valid &&
      (Object.keys(validation.errors).some((e) => e !== 'name' && e !== 'departments') ||
        !model.interactive.body?.text ||
        !model.actionType)
    )
      return

    await props.submit({
      type: 'chat',
      interactiveMessage: formatToApi(model),
      text: 'no text',
      ...((model.file?.blob || model.file?.id) && {
        file: {
          id: model.file?.id,
          blob: model.file?.blob || (model.file?.id && { fileId: model.file?.id }),
          mimetype: model.file?.mimetype,
          name: model.file?.fileName || model.file?.name,
        },
      }),
      files: [],
      uploadingFiles: false,
      replyTo: null,
    })
    return exit()
  }

  const getErrorMessage = () => {
    if (errorCode === 409) {
      return t('MODAL_DUPLICATE_NAME')
    }
    return t('MODAL_MESSAGE_ERROR')
  }

  const handleChangeHeaderType = (type) => {
    if (type === interactive.header.type) return
    setModel({
      ...model,
      file: null,
      interactive: {
        ...interactive,
        header: {
          type,
          [type]:
            type === 'text'
              ? ''
              : {
                  id: '', // setado na api (url ou id)
                },
        },
      },
    })
  }

  const handleChangeHeaderText = (e) => {
    let value = e?.target?.value || ''

    if (openedBy === 'bot' && botVersion === 'v3') {
      value = e
    }

    setProperty('interactive', {
      ...interactive,
      header: {
        type: 'text',
        text: value,
      },
    })
  }

  const handleChangeHeaderFile = async (e) => {
    const file = e.target.files[0]

    if (!file) return

    const url = await readFileAsDataURL(file)

    setProperty('file', {
      blob: file,
      base64Url: url,
      mimetype: file.type,
      fileName: file.name,
    })
  }

  const handleChangeBody = (e) => {
    let value = e?.target?.value || ''

    if (openedBy === 'bot' && botVersion === 'v3') {
      value = e
    }

    setProperty('interactive', {
      ...interactive,
      body: {
        text: value,
      },
    })
  }

  const handleChangeFooter = (e) => {
    let value = e?.target?.value || ''

    if (openedBy === 'bot' && botVersion === 'v3') {
      value = e
    }

    setProperty('interactive', {
      ...interactive,
      footer: {
        ...interactive.footer,
        text: value,
      },
    })
  }

  const handleChangeActionType = (type) => {
    if (type === interactive?.type) return

    if (!type) {
      return setProperty('interactive', {
        ...interactive,
        header: {
          type: 'text',
        },
      })
    }
    setProperty('interactive', {
      ...interactive,
      type: type === 'simpleList' ? 'list' : 'button',
      header: {
        type: 'text',
      },
      action:
        type === 'simpleList'
          ? {
              button: '',
              sections: [
                {
                  title: '',
                  rows: [
                    {
                      id: uuid(),
                      title: '',
                    },
                  ],
                },
              ],
            }
          : {
              buttons: [
                {
                  type: 'reply',
                  reply: {
                    id: uuid(),
                    title: '',
                  },
                },
              ],
            },
    })
  }

  const handleAddButton = () => {
    const newInteractive = interactive

    if (model.actionType.value === 'simpleList') {
      newInteractive.action.sections[0].rows.push({
        id: uuid(),
        title: '',
      })
    }

    if (model.actionType.value === 'button') {
      newInteractive.action.buttons = buttons || []
      newInteractive.action.buttons.push({
        type: 'reply',
        reply: {
          id: uuid(),
          title: '',
        },
      })
    }

    setProperty('interactive', interactive)
  }

  const handleRemoveButton = (index) => {
    const newInteractive = interactive
    if (model.actionType.value === 'simpleList') {
      newInteractive.action.sections[0].rows = simpleListRows.filter((_, idx) => idx !== index)
    }

    if (model.actionType.value === 'button') {
      newInteractive.action.buttons = buttons.filter((_, idx) => idx !== index)
    }

    setProperty('interactive', newInteractive)
  }

  const handleChangeListSectionItemTitle = (e) => {
    const newInteractive = interactive
    newInteractive.action.sections[0].title = e.target.value
    return setProperty('interactive', newInteractive)
  }

  const handleChangeListItemTitle = (e, index) => {
    const newInteractive = interactive
    newInteractive.action.sections[0].rows[index].title = e.target.value
    setProperty('interactive', newInteractive)
  }

  const handleChangeButton = (e, index) => {
    const newInteractive = interactive
    newInteractive.action.buttons[index].reply.title = e.target.value
    setProperty('interactive', newInteractive)
  }

  const handleChangeListSectionName = (e) => {
    setProperty('interactive', {
      ...interactive,
      action: {
        ...action,
        button: e.target.value || '',
      },
    })
  }

  const handleSelectInteractiveMessage = async (value) => {
    setModel(formatFromApi(value || initialModel, t))
    if (!value) {
      setProperty('actionType', null)
    }
  }

  const [isHeaderPopoverShowing, setIsHeaderPopoverShowing] = useState(false)
  const [isBodyPopoverShowing, setIsBodyPopoverShowing] = useState(false)
  const [isFooterPopoverShowing, setIsFooterPopoverShowing] = useState(false)
  const toggleHeaderPopover = () => setIsHeaderPopoverShowing(!isHeaderPopoverShowing)
  const toggleBodyPopover = () => setIsBodyPopoverShowing(!isBodyPopoverShowing)
  const toggleFooterPopover = () => setIsFooterPopoverShowing(!isFooterPopoverShowing)

  const componentProps = {
    handleSelectInteractiveMessage,
    handleChangeListSectionName,
    handleChangeButton,
    handleChangeListItemTitle,
    handleChangeListSectionItemTitle,
    handleRemoveButton,
    handleAddButton,
    handleChangeActionType,
    handleChangeFooter,
    handleChangeBody,
    handleChangeHeaderFile,
    handleChangeHeaderText,
    handleChangeHeaderType,
    getErrorMessage,
    t,
    isEditable,
    interactive,
    header,
    body,
    footer,
    action,
    sections,
    buttons,
    simpleListSection,
    simpleListRows,
    title,

    submit,
    exit,
    isModalOpen,
    isEditing,
    model,
    setProperty,
    setModel,
    updateModel,
    isAlertShowing,
    closeAlert,
    isLoading,
    error,
    bindInput,
    validation,
    user,
    openedBy,
    botVersion,
    setShowSidebarHelper,
    isHeaderPopoverShowing,
    toggleHeaderPopover,
    isBodyPopoverShowing,
    toggleBodyPopover,
    isFooterPopoverShowing,
    toggleFooterPopover,
    allDisabled,
  }

  useImperativeHandle(
    ref,
    () => {
      return {
        ...ref.current,
        [uuid()]: {
          validation,
          setModel,
          model,
        },
      }
    },
    [validation, setModel, model],
  )

  if (!withModal) {
    return <Component {...{ ...componentProps }} />
  }

  useEffect(() => {
    if (isAlertShowing) {
      dialogContentRef.current.scrollTop = 0
    }
  }, [isAlertShowing])

  return (
    <>
      <Helmet title={title} />

      <Dialog open={isModalOpen} onOpenChange={exit}>
        <DialogContent
          ref={dialogContentRef}
          style={{
            maxWidth: '680px',
            overflow: isAlertShowing ? 'hidden' : 'auto',
          }}
          data-testid="interactive-message-modal"
        >
          <DialogHeader
            style={{
              marginBottom: '40px',
            }}
            data-testid="interactive-message-modal-header"
          >
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>
          <Form ref={ref} onSubmit={(e) => e.preventDefault()} data-testid="interactive-message-form">
            <main data-testid="interactive-message-modal-body">
              <Component {...{ ...componentProps }} />
            </main>
            <DialogFooter
              style={{
                marginTop: '40px',
              }}
              data-testid="interactive-message-modal-footer"
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  gap: '24px',
                }}
              >
                <Button
                  style={{
                    width: '180px',
                  }}
                  variant="outline"
                  type="button"
                  onClick={exit}
                  data-testid="interactive-message-modal-cancel"
                >
                  {t('common:FORM_ACTION_CANCEL')}
                </Button>

                {openedBy === 'chat' ? (
                  <>
                    <IfUserCan
                      permissions={['interactive-messages.create', 'interactive-messages.send']}
                      render={() => (
                        <Button
                          style={{
                            width: '180px',
                          }}
                          type="submit"
                          onClick={() => handleSubmit('saveAndSend')}
                          disabled={isLoading}
                          data-testid="interactive-message-modal-save-and-submit"
                        >
                          {t('SAVE_AND_SEND')}
                        </Button>
                      )}
                    />
                    <Button
                      style={{
                        width: '180px',
                      }}
                      data-testid="interactive-message-modal-submit"
                      type="submit"
                      onClick={() => handleSubmit('send')}
                      disabled={isLoading}
                    >
                      {t('SEND')}
                    </Button>
                  </>
                ) : (
                  <Button
                    style={{
                      width: '180px',
                    }}
                    data-testid="interactive-message-modal-submit"
                    type="submit"
                    onClick={submit}
                    disabled={isLoading}
                  >
                    {t('common:FORM_ACTION_SAVE')}
                  </Button>
                )}
              </div>
            </DialogFooter>
          </Form>
          {isAlertShowing && (
            <div
              style={{
                position: 'absolute',
                inset: 0,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                zIndex: 999999999999,
              }}
            >
              <SweetAlert
                type={errorCode === 500 ? 'error' : 'warning'}
                error
                title={t('common:MESSAGE_ATTENTION')}
                onConfirm={closeAlert}
              >
                {getErrorMessage()}
              </SweetAlert>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
})

export default withRouter(InteractiveMessagesForm)
