import React from 'react'
import { Button } from '../../../../common/unconnected/ui/button'
import templateImage from '../../../../../assets/logos/template-image.svg'
import { CheckCheck } from 'lucide-react'

interface Model {
  name: string
  file: {
    id: string
    name: string
    checksum: string
    publicFilename: string
    extension: string
    mimetype: string
    createdAt: string
    updatedAt: string
    accountId: string
    url: string
    base64Url: string
    fileName: string
  }
  actionType: {
    value: 'simpleList' | 'button'
    label: string
  }
  departments: [
    {
      id: string
      name: string
      archivedAt: string | null
      createdAt: string
      updatedAt: string
      accountId: string
      distributionId: string | null
      interactive_message_departments: {
        createdAt: string
        updatedAt: string
        interactiveMessageI: string
        departmentId: string
      }
    },
  ]
  interactive: {
    header: {
      type: 'text' | 'image' | 'document' | 'video'
      image?: {
        id: string
      }
      text?: string
    }
    body: {
      text: string
    }
    type: string
    action: {
      buttons: [
        {
          type: string
          reply: {
            id: string
            title: string
          }
        },
        {
          type: string
          reply: {
            id: string
            title: string
          }
        },
        {
          type: string
          reply: {
            id: string
            title: string
          }
        },
      ]
      sections: [
        {
          rows: [
            {
              id: string
              title: string
            },
          ]
        },
      ]
    }
    footer: {
      text: string
    }
  }
  id: string
  accountId: string
  archivedAt: string | null
  createdAt: string
  updatedAt: string
  deletedAt: string | null
}

interface PreviewMessageProps {
  model: Model
}

export function PreviewMessage({ model }: PreviewMessageProps) {
  return (
    <div
      style={{
        backgroundColor: '#CDFFC7',
        padding: '16px',
        borderRadius: '16px',
      }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: '8px',
        }}
      >
        <span
          style={{
            color: '#24272D',
            fontSize: '14px',
            fontStyle: 'italic',
          }}
        >
          Nome do contato
        </span>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
          }}
        >
          <span>11:48</span>
          <CheckCheck size={16} color="#5A92D7" />
        </div>
      </div>
      <header>
        {model?.interactive?.header?.type === 'text' && (
          <p
            style={{
              margin: 0,
              fontSize: '14px',
              fontWeight: 'bold',
            }}
          >
            {model?.interactive?.header?.text}
          </p>
        )}
        {model?.interactive?.header?.type === 'image' && (
          <img
            src={model?.file?.url || model?.file?.base64Url || templateImage}
            alt={model?.file?.name}
            style={{
              width: '100%',
              maxHeight: '300px',
              objectFit: 'contain',
              borderRadius: '10px',
            }}
          />
        )}
      </header>
      <p
        style={{
          margin: 0,
          fontSize: '14px',
          color: '#373C43',
        }}
      >
        {model?.interactive?.body?.text}
      </p>
      <p
        style={{
          margin: 0,
          fontSize: '14px',
          color: '#586171',
        }}
      >
        {model?.interactive?.footer?.text}
      </p>
      <div
        style={{
          display: 'flex',
          gap: '8px',
          marginTop: '8px',
        }}
      >
        {model?.actionType?.value === 'button' &&
          model?.interactive?.action?.buttons?.map((button) => (
            <Button
              style={{
                backgroundColor: '#F2F7FC',
                color: '#24272D',
                fontSize: '14px',
                fontWeight: 'normal',
              }}
              key={button.reply.id}
            >
              {button.reply.title}
            </Button>
          ))}
      </div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
          marginTop: '8px',
        }}
      >
        {model?.actionType?.value === 'simpleList' &&
          model?.interactive?.action?.sections?.map((section) =>
            section.rows.map((row) => (
              <Button
                style={{
                  width: '100%',
                  backgroundColor: '#F2F7FC',
                  color: '#24272D',
                  fontSize: '14px',
                  fontWeight: 'normal',
                }}
                key={row.id}
              >
                {row.title}
              </Button>
            )),
          )}
      </div>
    </div>
  )
}
