import styled from 'styled-components'
import { BorderColor } from '../../../styles/colors'

export const AlertInteractiveMessages = styled.div`
  background: white;
  border-radius: 5px;
  border: 1px solid ${BorderColor};
  padding: 1.5rem;
  margin-top: 2rem;
  h5 {
    color: #ff4651;
    font-size: 16px;
    margin-bottom: 0px;
  }
`
export const Status = styled.div`
  display: flex;
  align-items: center;
  padding-left: 0 !important;
`
export const StatusCircle = styled.div`
  margin: 5px;
  border-radius: 50%;
  background-color: ${(props) => props.color};
  width: 15px;
  height: 15px;
`
