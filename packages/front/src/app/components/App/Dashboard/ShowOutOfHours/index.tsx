import React, { memo } from 'react'
import { Form } from 'reactstrap'
import Helmet from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { connect } from 'react-redux'
import config from '../../../../../../config'
import Button from '../../../common/unconnected/Button'
import { PrimaryColor } from '../../styles/colors'
import { logout } from '../../../../modules/auth/actions'
import logoUrl from '../../../../assets/logos/logoUrl'

function ShowOutOfHours({ logout }) {
  const { t } = useTranslation(['showOutOfHours'])

  return (
    <div
      className="full-height d-flex flex-column justify-content-center align-items-center"
      style={{ backgroundColor: '#EEE' }}
    >
      <div className="mb-4">
        <img src={logoUrl(true)} alt={config('whitelabel.appName')} style={{ width: 150 }} />
      </div>
      <Helmet title={t('TITLE_SHOW_OUT_OF_HOURS')} />

      <div className="mb-4">{t('IS_SHOW_OUT_OF_HOURS')}</div>
      <Form onSubmit={logout}>
        <Button type="submit" background={PrimaryColor}>
          {t('LOGIN_ANOTHER_ACCOUNT')}
        </Button>
      </Form>
    </div>
  )
}

export default connect(() => ({}), {
  logout,
})(memo(ShowOutOfHours))
