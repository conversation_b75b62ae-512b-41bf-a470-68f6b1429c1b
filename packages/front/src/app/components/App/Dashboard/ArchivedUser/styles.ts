import styled from 'styled-components'

export const Container = styled.div`
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: #f3f5fc;
  min-height: 100vh;
`

export const Content = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 900px;
  background-color: #fff;
  border-radius: 32px;
  padding: 32px;
`

export const Title = styled.h1`
  font-size: 24px;
  font-weight: 600;
  color: #324b7d;
  margin: unset;
`

export const SVG = styled.div`
  margin-top: 32px;
  margin-bottom: 24px;
`

export const WarningTitle = styled.p`
  font-size: 20px;
  font-weight: 600;
  color: #040405;
  text-align: left;
  align-self: start;
  margin: unset;
`

export const WarningMessage = styled.p`
  font-size: 16px;
  font-weight: 400;
  color: #586171;
  margin: unset;
  margin-top: 14px;
`

export const Button = styled.button`
  width: 200px;
  height: 40px;
  margin-top: 32px;
  background-color: #324b7d;
  border: none;
  border-radius: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
`
