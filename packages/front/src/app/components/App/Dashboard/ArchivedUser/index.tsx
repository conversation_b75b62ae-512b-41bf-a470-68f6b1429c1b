import React from 'react'
import { useTranslation } from 'react-i18next'
import { connect } from 'react-redux'
import { logout } from '../../../../modules/auth/actions'
import ArchivedUserSvg from '../../../common/unconnected/IconDigisac/ArchivedUserSvg'
import * as S from './styles'

const ArchivedUser = ({ logout: handleLogout }) => {
  const { t } = useTranslation('archivedUser')

  return (
    <S.Container>
      <S.Content>
        <S.Title>{t('TITLE')}</S.Title>

        <S.SVG>
          <ArchivedUserSvg width="832" />
        </S.SVG>

        <S.WarningTitle>{t('WARNING_TITLE')}</S.WarningTitle>
        <S.WarningMessage>{t('WARNING_MESSAGE')}</S.WarningMessage>

        <S.Button onClick={handleLogout}>{t('BUTTON_TEXT')}</S.Button>
      </S.Content>
    </S.Container>
  )
}

const mapStateToProps = () => ({
  logout,
})

const actionCreators = {
  logout,
}

export default connect(mapStateToProps, actionCreators)(ArchivedUser)
