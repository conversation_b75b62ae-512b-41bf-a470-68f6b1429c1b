import React from 'react'
import { useTranslation } from 'react-i18next'
import { format } from 'date-fns'

function AuthHistoryBody({ authHistory }) {
  const { t } = useTranslation(['authHistoryPage', 'common'])

  return (
    <div>
      <b>{t('ID')}: </b>
      {authHistory.id}
      <br />
      <b>{t('EVENT')}: </b>
      {authHistory.event === 'auth' ? t('AUTH') : t('PASSWORD_CHANGE')}
      <br />
      <b>{t('ORIGIN_IP')}: </b>
      {authHistory.originIP}
      <br />
      <b>{t('ORIGIN_UA')}: </b>
      {authHistory.originUA}
      <br />
      <b>{t('NAME')}: </b>
      {authHistory?.user.name}
      <br />
      <b>{t('DATE')}: </b>
      {format(new Date(authHistory.createdAt), 'dd/MM/yyyy HH:mm')}
      <br />
      <br />
    </div>
  )
}
export default AuthHistoryBody
