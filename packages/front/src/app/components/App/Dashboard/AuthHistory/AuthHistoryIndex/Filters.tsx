import React, { useState, useEffect, memo } from 'react'
import { useTranslation } from 'react-i18next'
import { Col, FormGroup, Input, Label, Row } from 'reactstrap'
import isAfter from 'date-fns/isAfter'
import isBefore from 'date-fns/isBefore'
import { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import DataSelect from '../../../../common/unconnected/Datetime'
import { GroupInput } from '../../../styles/common'
import Select from '../../../../common/unconnected/Select'
import UsersSelect from '../../../../common/connected/UsersSelect'
import { IconSupport } from '../../../../common/unconnected/IconDigisac'
import { TextColor } from '../../../styles/colors'

function Filters({ filters, handleFilterChange }) {
  const [dateIntervalError, setDateIntervalError] = useState(false)

  useEffect(() => {
    const error =
      (filters.createdAtInit && isAfter(filters.createdAtInit, filters.createdAtFinal)) ||
      (filters.createdAtFinal && isBefore(filters.createdAtFinal, filters.createdAtInit))
    setDateIntervalError(error)
  }, [filters.createdAtInit, filters.createdAtFinal])

  const { t } = useTranslation(['authHistoryPage', 'common'])

  return (
    <Row>
      <Col md="3">
        <InputGroupWrapper
          id="event"
          label={t('EVENT')}
          render={({ id }) => (
            <GroupInput>
              <Select
                id={id}
                value={
                  !filters.event?.label
                    ? {
                        value: false,
                        label: t('ALL'),
                      }
                    : filters.event
                }
                onChange={(value) => {
                  return handleFilterChange('event', value)
                }}
                options={[
                  {
                    value: 'auth',
                    label: t('AUTH'),
                  },
                  {
                    value: 'password_change',
                    label: t('PASSWORD_CHANGE'),
                  },
                ]}
                instanceId={id}
                getOptionValue={(o) => o.value}
                getOptionLabel={(o) => o.label}
                searchPromptText={t('common:SELECT_SEARCH_PROMPT_TEXT')}
                loadingPlaceholder={t('common:LOADING')}
                placeholder={t('common:SELECT_LOADING_PLACEHOLDER')}
                noResultsText={t('common:NO_RESULTS_FOUND')}
                clearValueText={t('common:MESSAGE_CLEAR')}
                clearAllText={t('common:MESSAGE_CLEAR_ALL')}
              />
            </GroupInput>
          )}
        />
      </Col>

      <Col md="3">
        <FormGroup>
          <InputGroupWrapper
            id="from"
            label={t('common:LABEL_FROM')}
            type="textarea"
            {...{
              filters,
            }}
            render={(input) => (
              <DataSelect
                id="datepicker"
                value={filters.createdAtInit}
                onChange={(value) => handleFilterChange('createdAtInit', value)}
              />
            )}
          />
        </FormGroup>
      </Col>

      <Col md="3">
        <FormGroup>
          <InputGroupWrapper
            id="to"
            label={t('common:LABEL_TO')}
            type="textarea"
            {...{
              filters,
            }}
            render={(input) => (
              <DataSelect
                id="datepicker"
                value={filters.createdAtFinal}
                onChange={(value) => handleFilterChange('createdAtFinal', value)}
              />
            )}
          />
          {dateIntervalError && <div className="invalid-feedback mt-0">{t('FILTER_ERROR')}</div>}
        </FormGroup>
      </Col>

      {/* <Col md="3">
        <FormGroup className="form-custom">
          <Label htmlFor="contact">{t('USERNAME')}</Label>
          <Input
            data-tesid="auth-inputFilter-user"
            id="user"
            type="text"
            value={filters.userName}
            onChange={(e) => handleFilterChange('userName', e.target.value)}
            placeholder={t('USERNAME')}
          />
        </FormGroup>
      </Col> */}
      <Col md="3">
        <FormGroup>
          <InputGroupWrapper
            id="user"
            label={t('USERNAME')}
            render={(input) => (
              <GroupInput withIcon>
                <UsersSelect
                  stateId="auth-inputFilter-user"
                  id={input.id}
                  value={filters.user}
                  icon={<IconSupport fill={TextColor} width="30" height="30" />}
                  isPaged
                  onChange={(value) => handleFilterChange('user', value)}
                />
              </GroupInput>
            )}
          />
        </FormGroup>
      </Col>
    </Row>
  )
}
export default memo(Filters)
