import React, { memo } from 'react'
import { Link, Switch, useRouteMatch } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { pickBy, identity } from 'lodash'
import Helmet from 'react-helmet'
import { format } from 'date-fns'
import Icon from '../../../../common/unconnected/Icon'
import Toggler from '../../../../common/unconnected/Toggler'
import Filters from './Filters'
import IfUserCanRoute from '../../../../common/connected/IfUserCanRoute'
import IfUserCan from '../../../../common/connected/IfUserCan'
import TablePagination from '../../../../common/unconnected/TablePagination'
import useIndexController from '../../../../../hooks/crud/useIndexController'
import AuthHistoryShowRoute from '../AuthHistoryShow'
import { useFetchManyAuthHistory } from '../../../../../resources/authHistory/requests'
import {
  Table,
  TableCell,
  TableColumn,
  TableBody,
  TableHead,
  TableOptions,
  TableRow,
  DropdownMenuActions,
  ButtonDropdownActions,
  CardFilters,
  ButtonRefresh,
} from '../../../styles/table'
import Container from '../../../styles/container'
import { IconOptions, IconShowPassword as IconEyes } from '../../../../common/unconnected/IconDigisac'
import { PrimaryColor, TextColor } from '../../../styles/colors'
import Button from '../../../../common/unconnected/Button'

const buildQuery = ({ filters, localPagination }) =>
  pickBy(
    {
      order: [['createdAt', 'DESC']],
      where: pickBy(
        {
          event: filters?.event?.value && { $eq: `${filters.event.value}` },
          createdAt: filters.createdAtInit &&
            filters.createdAtInit && {
              $between: [filters.createdAtInit, filters.createdAtFinal],
            },
        },
        identity,
      ),
      include: [
        {
          model: 'user',
          where: {
            ...(filters?.user && {
              id: { $eq: filters.user.id },
            }),
          },
        },
      ],
      page: localPagination.page,
      perPage: localPagination.perPage,
    },
    identity,
  )

const initialFilters = {
  event: null,
  from: null,
  to: null,
  user: null,
}

function AuthHistory() {
  const {
    models: authHistory,
    pagination,
    isLoading,
    localPagination,
    handleLocalPaginationChange,
    handleFilterChange,
    filters,
    isFiltersShowing,
    toggleFilters,
    fetch,
  } = useIndexController({
    buildQuery,
    initialFilters,
    useFetchMany: useFetchManyAuthHistory,
  })

  const { t } = useTranslation(['authHistoryPage', 'common'])
  const match = useRouteMatch()

  return (
    <div data-testid={t('AUTHENTICATION_HISTORY')}>
      <Helmet title={t('AUTHENTICATION_HISTORY')} />

      <Container>
        <div className="d-flex align-itens-center justify-content-between">
          <div className="title-page" data-testid="authHistory-title-text">
            <h2>{t('AUTHENTICATION_HISTORY')}</h2>
          </div>

          <div className="d-flex">
            <Button
              data-testid="authHistory-button-filters"
              background={PrimaryColor}
              onClick={toggleFilters}
              size="xl"
              className="mr-2"
            >
              <Icon name="search" fixedWidth className="mr-1" />
              {isFiltersShowing ? `${t('common:BUTTON_TEXT_HIDDEN')} ` : `${t('common:BUTTON_TEXT_SHOW')} `}
              {t('common:BUTTON_TEXT_FILTERS')}
            </Button>

            <ButtonRefresh
              loadIcon="sync-alt"
              color="default"
              isLoading={isLoading}
              onClick={fetch}
              data-testid="tag-button-refresh"
            >
              <Icon name="sync-alt" fixedWidth />
            </ButtonRefresh>
          </div>
        </div>

        {isFiltersShowing && (
          <CardFilters>
            <Filters filters={filters} handleFilterChange={handleFilterChange} />
          </CardFilters>
        )}

        <Table>
          <TableHead columns={6}>
            <TableColumn data-testid="authHistory-label-name">{t('EVENT')}</TableColumn>
            <TableColumn data-testid="authHistory-label-name">{t('USER')}</TableColumn>
            <TableColumn data-testid="authHistory-yyy">{t('ORIGIN_IP')}</TableColumn>
            <TableColumn data-testid="authHistory-yyy">{t('ORIGIN_UA')}</TableColumn>
            <TableColumn data-testid="authHistory-yyy">{t('DATE')}</TableColumn>
            <TableColumn data-testid="authHistory-titleColumn-actions" className="text-right justify-content-end pr-4">
              {t('common:ACTIONS_SUBMENU_TITLE')}
            </TableColumn>
          </TableHead>

          <TableBody data-testid="authHistory-table-body">
            {authHistory &&
              authHistory.map((auth, key) => (
                <TableRow cells={6} key={auth.id}>
                  <TableCell>
                    <Link to={`${match.url}/${auth.id}`}>
                      {auth.event === 'auth' ? t('AUTH') : t('PASSWORD_CHANGE')}
                    </Link>
                  </TableCell>

                  <TableCell>
                    <Link to={`users/${auth?.user?.id}`}>{auth?.user?.name}</Link>
                  </TableCell>

                  <TableCell title={auth.originIP}>{auth.originIP}</TableCell>

                  <TableCell title={auth.originUA}>{auth.originUA}</TableCell>

                  <TableCell>{format(new Date(auth.createdAt), 'dd/MM/yyyy HH:mm')}</TableCell>

                  <TableCell actions>
                    <Toggler
                      render={({ active, toggle }) => (
                        <ButtonDropdownActions group={false} isOpen={active} toggle={toggle}>
                          <TableOptions size="sm" color="primary" data-testid="authHistory-button-actions">
                            <IconOptions className="icon-options" fill={PrimaryColor} />
                          </TableOptions>

                          <DropdownMenuActions>
                            <IfUserCan permission="holidays.view">
                              <Link
                                data-testid="authHistory-button-view"
                                to={`${match.url}/${auth.id}`}
                                className="dropdown-item"
                              >
                                <IconEyes fill={TextColor} width="29" height="29" />
                                {t('common:ACTIONS_SUBMENU_VIEW')}
                              </Link>
                            </IfUserCan>
                          </DropdownMenuActions>
                        </ButtonDropdownActions>
                      )}
                    />
                  </TableCell>
                </TableRow>
              ))}
            {authHistory.length < 1 && (
              <tr>
                <td colSpan={6} className="text-center">
                  {t('common:NO_RESULTS_FOUND')}
                </td>
              </tr>
            )}
          </TableBody>
        </Table>

        <TablePagination
          pagination={pagination}
          localPagination={localPagination}
          handlePaginationChange={handleLocalPaginationChange}
        />
      </Container>
      <Switch>
        <IfUserCanRoute permission="holidays.view" exact path={`${match.url}/:id`} component={AuthHistoryShowRoute} />
      </Switch>
    </div>
  )
}

export default memo(AuthHistory)
