import React from 'react'
import { useTranslation } from 'react-i18next'
import { with<PERSON><PERSON><PERSON> } from 'react-router-dom'
import Helmet from 'react-helmet'
import { ModalBody, ModalHeader } from 'reactstrap'
import AuthHistoryBody from './AuthHistoryBody'
import useShowController from '../../../../../hooks/crud/useShowController'
import { useFetchOneAuthHistory } from '../../../../../resources/authHistory/requests'
import { ModalDigisac } from '../../../styles/common'
import ButtonClose from '../../../../common/unconnected/ButtonClose'

const AuthHistoryShow = () => {
  const { t } = useTranslation(['authHistoryPage'])

  const {
    model: authHistory,
    isOpen,
    exit,
  } = useShowController({
    exitToPath: '/auth-history',
    useFetchOne: useFetchOneAuthHistory,
  })

  if (!authHistory) return null

  return (
    <>
      <Helmet title={authHistory?.event === 'auth' ? t('AUTH') : t('PASSWORD_CHANGE')} />

      <ModalDigisac isOpen={isOpen} toggle={exit} autoFocus={false}>
        <ModalHeader data-testid="auth-history-viewModal">
          {authHistory?.event === 'auth' ? t('AUTH') : t('PASSWORD_CHANGE')}
          <ButtonClose onClick={exit} />
        </ModalHeader>
        <ModalBody>
          <AuthHistoryBody authHistory={authHistory} />
        </ModalBody>
      </ModalDigisac>
    </>
  )
}

export default withRouter(AuthHistoryShow)
