import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, ModalBody, ModalHeader } from 'reactstrap'
import InputGroup, { InputGroupWrapper } from '../../../common/unconnected/InputGroup'
import Select from '../../../common/unconnected/Select'
import { IconClock, IconLock } from '../../../common/unconnected/IconDigisac'
import style from './Styles'
import { ModalDigisac, ModalFooter } from '../../styles/common'
import { DisabledColor } from '../../styles/colors'
import { useRequest } from '../../../../hooks/useRequest'
import absenceApi from '../../../../resources/absence/api'
import toast from '../../../../utils/toast'

const AbsenceModal = (props) => {
  const { displayAbsenceModal, setDisplayAbsenceModal, absenceSettings } = props
  const { t } = useTranslation(['companyPage', 'common'])

  const [{ response, isLoading }, getAbsence] = useRequest(absenceApi.findOpenAbsence)

  const [{ response: responseUnlock, isLoading: isLoadingUnlock }, unlock] = useRequest(absenceApi.unlock)

  const [absence, setAbsence] = useState({})
  const reasons = (absenceSettings?.reasons || []).map((reason) => ({
    id: reason,
    name: reason,
  }))

  useEffect(() => {
    if (!absence.id) getAbsence({})
  }, [])

  useEffect(() => {
    if (response && response.id) {
      setAbsence(response)
      setDisplayAbsenceModal({ show: true })
      response.show = displayAbsenceModal
      return
    }
    setDisplayAbsenceModal({ show: false })
  }, [response])

  useEffect(() => {
    if (!isLoadingUnlock && responseUnlock) {
      if (!responseUnlock.success) {
        toast.warn(t(responseUnlock.message))
        return
      }
      setDisplayAbsenceModal({ show: false })
      setAbsence({})
    }
  }, [isLoadingUnlock, responseUnlock])

  return (
    <>
      {displayAbsenceModal && (
        <>
          <div style={style.blockDiv} />
          <ModalDigisac isOpen data-testid="absence-modal">
            <ModalHeader data-testid="absence-label-title">{t('TITLE_ABSENCE')}</ModalHeader>
            <ModalBody>
              {absenceSettings.reason_required && (
                <InputGroupWrapper
                  id="reason"
                  label={t('SELECT_REASON_MODAL')}
                  render={({ id }) => (
                    <Select
                      id={id}
                      options={reasons}
                      data-testid="absence-modal-reason"
                      icon={<IconClock fill={DisabledColor} width="25" height="25" />}
                      onChange={(e) => {
                        if (!e) return setAbsence({ ...absence, reason: '' })
                        setAbsence({ ...absence, reason: e.name })
                      }}
                    />
                  )}
                />
              )}

              {absenceSettings.password_required && (
                <InputGroup
                  type="password"
                  label={t('INSERT_PASSWORD_MODAL')}
                  data-testid="absence-modal-password"
                  onChange={(e) => {
                    setAbsence({ ...absence, password: e.target.value })
                  }}
                  icon={<IconLock fill={DisabledColor} width="25" height="25" />}
                />
              )}
            </ModalBody>

            <ModalFooter>
              <Button
                data-testid="save-absence-button"
                className="confirm"
                type="button"
                onClick={() => unlock(absence)}
              >
                {t('UNLOCK_BUTTON_MODAL')}
              </Button>
            </ModalFooter>
          </ModalDigisac>
        </>
      )}
    </>
  )
}

export default AbsenceModal
