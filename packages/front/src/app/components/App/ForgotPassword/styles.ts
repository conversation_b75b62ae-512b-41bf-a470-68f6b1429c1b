import styled from 'styled-components'
import { shade } from 'polished'
import { LightColor, SecondaryColor, PrimaryColor, DisabledColor, TextColor, PoloBlue } from '../styles/colors'
import { flex, fontFamily } from '../styles/common'
import Link from '../../common/unconnected/Link'

export const ForgotPasswordRequest = styled.section`
  ${flex};
  ${fontFamily};
  background: ${LightColor};
  height: 100%;
  width: 100%;

  button {
    margin-top: 40px;
  }

  input {
    padding: 20px;
    border-radius: 32px;
    color: ${TextColor};
    font-size: 12px;
    font-weight: 400;
    width: 100%;
    outline: none;
    ${fontFamily};
    transition: all 0.2s ease-in-out;
    border: 1px solid ${PoloBlue}!important;
    &::placeholder {
      color: ${TextColor};
      font-weight: 400;
    }

    &:focus {
      border: 1px solid ${PrimaryColor};
    }
  }
`

export const ForgotPasswordUpdate = styled.section`
  ${flex};
  ${fontFamily};
  background: ${LightColor};
  height: 100%;
  width: 100%;

  button {
    margin-top: 40px;
  }

  input {
    padding: 20px;
    border-radius: 32px;
    color: ${TextColor};
    font-size: 12px;
    font-weight: 400;
    width: 100%;
    outline: none;
    ${fontFamily};
    transition: all 0.2s ease-in-out;
    border: 1px solid ${PoloBlue}!important;
    &::placeholder {
      color: ${TextColor};
      font-weight: 400;
    }

    &:focus {
      border: 1px solid ${PrimaryColor};
    }
  }
`

export const ForgotContainer = styled.div`
  ${flex};
  width: 100%;
  max-width: 600px;
  height: 100%;
  h2 {
    margin-bottom: 0px;
  }
  h2,
  p {
    color: ${SecondaryColor};
  }

  input {
    padding-left: 1rem !important;
    border: 1px solid #9aa8c4 !important;
  }
`

export const GroupTitle = styled.div`
  display: grid;
  grid-template-columns: 70% 30%;
  margin-bottom: 3rem;
  div:nth-child(1) {
    display: flex;
    align-items: center;
  }
  a {
    color: ${PrimaryColor}!important;
  }
  li {
    list-style: none;
  }
  button {
    margin: initial;
  }
`

export const Redirect = styled(Link)`
  background: ${DisabledColor};
  width: 26px;
  height: 26px;
  border-radius: 50%;
  margin-top: 4px;
  margin-right: 20px;
  ${flex};

  svg {
    transform: rotate(90deg);
    width: 18px;
  }

  &:hover {
    background: ${(props) => shade(0.2, `${DisabledColor}`)};
  }
`
