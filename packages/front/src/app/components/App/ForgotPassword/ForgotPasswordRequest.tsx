import React, { FormEvent, useCallback, useEffect, useState } from 'react'
import Helmet from 'react-helmet'
import reformed from 'react-reformed'
import { useTranslation } from 'react-i18next'
import * as S from './styles'
import withValidation from '../../common/unconnected/withValidation'
import { isEmail, required } from '../../../utils/validator/validators'
import forgotPasswordApi from '../../../resources/forgotPassword/api'
import SweetModal from '../../common/unconnected/SweetModal'
import Button from '../../common/unconnected/Button'
import { DisabledColor, PrimaryColor } from '../styles/colors'
import { Form } from '../styles/common'
import InputGroup from '../../common/unconnected/InputGroup'
import SwitchLanguage from '../../common/unconnected/SwitchLanguage'
import { IconArrowDown } from '../../common/unconnected/IconDigisac'
import Qs from 'qs'
import config from '../../../../../config'
import useEventCallback from '../../../hooks/useEventCallback'
import { useToast } from '../../../hooks/useToast'
import AccountSelect from '../Login/AccountSelect'

const ForgotPasswordRequest: React.FC = ({ validation, setModel, setProperty, history, model, bindInput }) => {
  const [showModal, setShowModal] = useState(false)
  const [loading, setLoading] = useState(false)
  const { t } = useTranslation(['resetPasswordPage', 'common', 'login'])
  const isOnClusterMode = config('isOnClusterMode')
  const { toast } = useToast()

  useEffect(() => {
    const query = history.location.search && Qs.parse(history.location.search.substring(1))
    const email = query?.email

    setModel({
      ...(isOnClusterMode && {
        accountAlias: '',
      }),
      email: email || '',
    })

    validation.setRules({
      ...(isOnClusterMode && {
        accountAlias: [[required, () => t('common:REQUIRED_FIELD')]],
      }),
      email: [[isEmail, () => t('common:REQUIRED_FIELD')]],
    })
  }, [])

  const handleSubmit = useEventCallback(async (ev: FormEvent) => {
    ev.preventDefault()

    const valid = await validation.validateAll()
    if (!valid) return

    setLoading(true)

    const data = {
      ...(isOnClusterMode && {
        account: model.accountAlias,
      }),
      email: model.email,
    }

    try {
      await forgotPasswordApi.request(data)
      setShowModal(true)
    } catch (e) {
      toast({
        title: t('TEXT_EMAIL_SEND_ERROR'),
        variant: 'destructive',
        autoDismiss: true,
      })
    }

    setLoading(false)
  })

  const toLogin = useCallback(() => {
    history.push('/login')
  }, [history])

  return (
    <S.ForgotPasswordRequest>
      <Helmet title={t('TITLE_RESET_PASSWORD')} />

      <S.ForgotContainer>
        <Form onSubmit={(e) => handleSubmit(e)}>
          <S.GroupTitle>
            <div>
              <S.Redirect to="/">
                <IconArrowDown width="15" height="15" fill="#fff" />
              </S.Redirect>
              <h2>{t('TITLE_RESET_PASSWORD')}</h2>
            </div>

            <SwitchLanguage />
          </S.GroupTitle>
          <p>{t('SUBTITLE_RESET_PASSWORD')}</p>
          {isOnClusterMode && (
            <AccountSelect
              validation={validation}
              value={model.accountAlias}
              onChange={(v) => setProperty('accountAlias', v)}
            />
          )}

          <InputGroup
            data-testid="login-input-email_reset_password"
            id="email"
            label={t('LABEL_INPUT_EMAIL')}
            type="email"
            {...{ bindInput, validation }}
          />

          <Button
            data-testid="login-button-submit_reset_password"
            height="auto"
            background={!bindInput('email').value || loading ? DisabledColor : PrimaryColor}
            disabled={loading}
            type="submit"
          >
            {t('common:LABEL_SUBMIT')}
          </Button>
        </Form>
      </S.ForgotContainer>
      <SweetModal title={t('TITLE_MODAL_SUCCESS')} onConfirm={toLogin} showCancel={false} show={showModal}>
        {t('TEXT_EMAIL_SEND')}
      </SweetModal>
    </S.ForgotPasswordRequest>
  )
}

export default reformed()(withValidation()(ForgotPasswordRequest))
