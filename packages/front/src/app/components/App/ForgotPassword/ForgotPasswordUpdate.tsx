import React, { useState, useEffect, useCallback, FormEvent } from 'react'
import Helmet from 'react-helmet'
import reformed from 'react-reformed'
import { useTranslation } from 'react-i18next'
import * as S from './styles'
import InputGroup from '../../common/unconnected/InputGroup'
import Button from '../../common/unconnected/Button'
import withValidation from '../../common/unconnected/withValidation'
import forgotPasswordA<PERSON> from '../../../resources/forgotPassword/api'
import { DisabledColor, PrimaryColor } from '../styles/colors'
import { Form } from '../styles/common'
import SwitchLanguage from '../../common/unconnected/SwitchLanguage'
import PasswordStrengthWrapper from '../../common/unconnected/PasswordStrengthWrapper/PasswordStrengthWrapper'
import SweetAlert from 'react-bootstrap-sweetalert'
import Qs from 'qs'
import { IconArrowDown } from '../../common/unconnected/IconDigisac'

const ForgotPasswordUpdate: React.FC = ({ bindInput, validation, setModel, match, model, history }) => {
  const [isSuccess, setSuccess] = useState(false)
  const [error, setError] = useState(null)
  const { t } = useTranslation(['resetPasswordTokenPage', 'usersPage', 'common'])

  useEffect(() => {
    const tokenConfirmation = decodeURIComponent(match.params.token)
    const query = history.location.search && Qs.parse(history.location.search.substring(1))
    const creating = !!query.creating

    setModel({
      creating,
      password: '',
      passwordConfirmation: '',
      tokenConfirmation,
    })
  }, [])

  const handleSubmit = useCallback(
    async (ev: FormEvent) => {
      ev.preventDefault()

      // Validate data before submit
      const valid = await validation.validateAll()
      if (!valid) return

      const data = {
        password: model.password,
        token: model.tokenConfirmation,
        accountId: model.accountId,
      }

      try {
        await forgotPasswordApi.save(data)
        setSuccess(true)
      } catch (err) {
        if (err.response?.data?.message === 'Recently used password') {
          setError('usersPage:RECENTLY_USED_PASSWORD')
        } else if (
          err.response?.data?.message === 'Expired token.' ||
          err.response?.data?.message === 'Invalid token.'
        ) {
          setError(!model.creating ? 'RESET_LINK_EXPIRED' : 'CREATE_LINK_EXPIRED')
        } else {
          setError('common:MESSAGE_ERROR_SERVER_PROBLEM')
        }
      }
    },
    [model, setSuccess, setError],
  )

  const toLogin = useCallback(() => {
    history.push('/login')
  }, [history])

  return (
    <S.ForgotPasswordUpdate>
      <Helmet title={t(!model.creating ? 'TITLE_RESET_TOKEN_PASSWORD' : 'TITLE_CREATE_PASSWORD')} />
      <S.ForgotContainer>
        <Form onSubmit={(e) => handleSubmit(e)}>
          <S.GroupTitle>
            <div>
              <S.Redirect to="/" title={t('GO_TO_LOGIN')}>
                <IconArrowDown width="15" height="15" fill="#fff" />
              </S.Redirect>
              <h2>{t(!model.creating ? 'TITLE_RESET_TOKEN_PASSWORD' : 'TITLE_CREATE_PASSWORD')}</h2>
            </div>
            <SwitchLanguage />
          </S.GroupTitle>

          <PasswordStrengthWrapper
            id="password"
            label={t('LABEL_INPUT_NEW_PASSWORD')}
            type="password"
            isPassword
            {...{ bindInput, validation, model }}
          />

          <InputGroup
            id="passwordConfirmation"
            label={t('LABEL_INPUT_CONFIRMATION_PASSWORD')}
            type="password"
            isPassword
            {...{ bindInput, validation, model }}
          />

          <Button
            background={!model.password || !model.passwordConfirmation ? DisabledColor : PrimaryColor}
            height="auto"
            type="submit"
          >
            {!model.creating ? t('CHANGE_PASSWORD') : t('CREATE_PASSWORD')}
          </Button>
        </Form>
      </S.ForgotContainer>

      <SweetAlert
        success
        title={t('TITLE_MODAL_PASSWORD_CHANGED')}
        confirmBtnText={t('GO_TO_LOGIN')}
        show={isSuccess}
        onConfirm={toLogin}
      >
        {t('TEXT_MODAL_PASSWORD_CHANGED')}
      </SweetAlert>

      <SweetAlert
        error
        title={t('common:THIS_ACTION_COULD_NOT_BE_PERFORMED')}
        show={!!error}
        onConfirm={() => setError(null)}
      >
        {t(error)}
      </SweetAlert>
    </S.ForgotPasswordUpdate>
  )
}

export default reformed()(withValidation()(ForgotPasswordUpdate))
