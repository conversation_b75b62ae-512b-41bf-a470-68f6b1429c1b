import React, { useState, useEffect, useMemo } from 'react'
import ProTypes from 'prop-types'
import { useLocation } from 'react-router-dom'
import { connect } from 'react-redux'
import { selectors } from '../../modules/auth'
import interpolate from '../../utils/interpolate'

const Integration = ({ user }) => {
  const location = useLocation()

  const [currentIframeId, setCurrentIframeId] = useState(null)
  const [integration, setIntegration] = useState({})

  const variables = { userId: user?.id }

  const id = location.pathname.split('/')[1] === 'menu-integration' && location.pathname.split('/')[2]

  // Se tiver id e algum iframe aberto que não seja o que o usuario clicar pra abrir então
  if (id && id !== currentIframeId) {
    // Seta id no 'integration' para atualizar o src do iframe
    setIntegration(
      location.pathname.split('/')[1] === 'menu-integration' &&
        user &&
        user.account &&
        user.account.integrations &&
        user.account.integrations.find((i) => i.id === id),
    )
    setCurrentIframeId(id)
  }

  const interpolatedUrl = useMemo(() => interpolate(integration?.url, variables), [integration?.url, variables])

  useEffect(() => {
    if (user) {
      const integrationUrl = (user.account || {}).integrations?.find((i) => i.id === id)
      setIntegration(integrationUrl)
    }
  }, [user])

  return (
    <iframe
      style={{
        display: location.pathname.split('/')[1] === 'menu-integration' ? '' : 'none',
        position: 'absolute',
        top: '54px',
      }}
      title="integration"
      width="100%"
      height="100%"
      src={interpolatedUrl}
      frameBorder="0"
      allow="microphone; camera; accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture; web-share"
      allowFullScreen
    />
  )
}
const mapStateToProps = (state) => ({
  user: selectors.getUser(state),
})

Integration.propTypes = {
  user: ProTypes.any,
}
export default connect(mapStateToProps)(Integration)
