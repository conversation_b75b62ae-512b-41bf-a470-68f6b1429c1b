import React, { memo, useCallback, useEffect, useState } from 'react'
import { useHistory } from 'react-router'
import { useDispatch, useSelector } from 'react-redux'
import Helmet from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { logout } from '../../../modules/auth/actions'
import getQRCodeFromURI from '../../../utils/otp/getQRCodeFromURI'
import { useRequest } from '../../../hooks/useRequest'
import authApi from '../../../modules/auth/services/authApi'
import { getUser, getIsAuthenticated } from '../../../modules/auth/selectors'
import toast from '../../../utils/toast'
import CheckToken from '../TwoFactorAuthentication/CheckToken'
import * as S from './styles'

function SetupSecret({ location, expireSession = false, notGoBack = false }) {
  const user = useSelector(getUser)
  const isAuthenticated = useSelector(getIsAuthenticated)
  const dispatch = useDispatch()
  const [secret] = useState(user?.otpSecretKey)
  const [qrCodeImage, setQrCodeImage] = useState(null)
  const [toggleModal, setToggleModal] = useState(isAuthenticated)
  const [{ isLoading }, updateOTPSecretKey] = useRequest(authApi.updateOTPSecretKey)
  const { t } = useTranslation(['usersPage'])

  const history = useHistory()
  const goBack = () => {
    history.push(location?.state?.previousMatch || '/', { refresh: false })
  }

  const goOut = () => {
    dispatch(logout())
  }

  useEffect(() => {
    if (!secret) goBack()
    if (expireSession) {
      setTimeout(
        () => {
          goOut()
        },
        10 * 60 * 1000,
      )
    }
  }, [])

  useEffect(() => {
    getQRCodeFromURI(user?.otpSecretURI)
      .then(setQrCodeImage)
      .catch(() => setQrCodeImage(''))
  }, [secret])

  const handleConfirm = useCallback(async () => {
    if (secret) {
      await updateOTPSecretKey({ secretKey: secret })
        .then(() => {
          setToggleModal(false)
        })
        .catch(() => {
          toast.error(t('SECRET_KEY_UPDATE_FAILED'))
          return
        })
    }
  }, [secret])

  return (
    <>
      <S.Container isOpen={toggleModal} isLoading={isLoading}>
        <Helmet title={t('TITLE_TWO_FACTOR_AUTH')} />

        <S.ContentWrapper>
          <S.Header data-testid="setupsecret-label-header">
            <S.BackLink
              data-testid={notGoBack ? 'setupsecret-button-go-out' : 'setupsecret-button-go-back'}
              onClick={notGoBack ? goOut : goBack}
              disabled={isLoading}
            >
              <span>←</span>
            </S.BackLink>
            {t('HEADER_TWO_FACTOR_ACTIVATED')}
          </S.Header>
          <div>
            <S.Subtitle>{t('SUBTITLE_SETUP_INSTRUCTIONS')}</S.Subtitle>
            <S.Card>
              <S.Body>
                <h3>{t('INSTRUCTIONS_SETUP_AUTHENTICATOR')}</h3>
                <p>{t('INSTRUCTIONS_BODY_SETUP')}</p>
                <h3>{t('TITLE_SCAN_QR_CODE')}</h3>
                <p>{t('INSTRUCTIONS_SCAN_QR_CODE')}</p>
                <S.QRCodeContainer>
                  <S.QRCodeImage data-testid="setupsecret-img-qrcode" alt={t('TITLE_SCAN_QR_CODE')} src={qrCodeImage} />
                </S.QRCodeContainer>
              </S.Body>
            </S.Card>
            <p>{t('FINAL_STEP_INSTRUCTIONS')}</p>
            <S.Footer>
              <S.ConfirmButton data-testid="setupsecret-button-next" onClick={handleConfirm} disabled={isLoading}>
                {t('BUTTON_CONTINUE')} →
              </S.ConfirmButton>
            </S.Footer>
          </div>
        </S.ContentWrapper>
      </S.Container>
      <CheckToken {...{ setupMode: true, goBack: setToggleModal, isOpen: !toggleModal, model: user }} />
    </>
  )
}

export default memo(SetupSecret)
