import styled from 'styled-components'
import { PrimaryColor } from '../styles/colors'
import { shade } from 'polished'

export const Container = styled.div<{ isOpen: boolean; isLoading: boolean }>`
  display: ${(props) => (props.isOpen ? 'flex' : 'none')} !important;
  width: 100%;
  min-height: 100vh;
  position: absolute;
  top: 0;
  left: 0;

  display: flex;
  flex-direction: column;
  align-items: center;

  background: #f3f5fc;

  filter: ${(props) => (props.isLoading ? 'brightness(0.75)' : 'brightness(1)')};
`

export const ContentWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  max-width: 920px;
  padding: 16px;
  margin: 60px 0;
  color: #24272d;

  @media (min-width: 920px) {
    padding: 0;
  }

  z-index: 2;
`

export const BackLink = styled.button`
  background: none;
  border: none;
  color: ${PrimaryColor};
  display: inline-flex;
  font-size: 2rem;

  &:disabled {
    color: background-color: ${shade(0.1, PrimaryColor)};;
  }
`

export const Header = styled.h1`
  font-size: 2rem;
  font-weight: 700;
  color: ${PrimaryColor};
  margin-bottom: 8px;
`

export const Subtitle = styled.p`
  font-size: 1rem;
`

export const Card = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 32px;
  background: white;
  border-radius: 24px;
  padding: 24px;
  margin-top: 32px;
  margin-bottom: 16px;
`

export const Body = styled.div`
  font-size: 1rem;
  line-height: 1.5;

  h3 {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 8px;
  }

  p {
    margin-bottom: 32px;
  }

  input {
    width: 3rem;
    height: 4rem;
    text-align: center;
    border: none;
    border-bottom: 1px solid #29292b;
    color: ${PrimaryColor};
    font-size: 4rem;
  }
`

export const InputWrapper = styled.div`
  width: 100%;
  display: flex;
  gap: 24px;
  margin: 32px 0;
`

export const QRCodeContainer = styled.div`
  display: inline-block;
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid #b4bbc5;
  margin-top: -24px;
`

export const QRCodeImage = styled.img`
  max-width: 200px;
  height: auto;
`

export const Footer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 24px;
`

export const ConfirmButton = styled.button`
  background-color: ${PrimaryColor};
  color: white;
  height: 40px;
  border-radius: 40px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  text-align: center;
  border: none;
  padding: 0 62px;

  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: ${shade(0.1, PrimaryColor)};
  }

  &:disabled {
    background-color: ${shade(0.1, PrimaryColor)};
    cursor: not-allowed;
  }
`
