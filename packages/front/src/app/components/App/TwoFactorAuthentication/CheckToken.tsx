import React, { memo, useState, useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import Helmet from 'react-helmet'
import { useHistory } from 'react-router'
import { useDispatch } from 'react-redux'
import { attemptLogin, setTwoFactorAuthToken, fetchUser } from '../../../../app/modules/auth/actions'
import { useRequest } from '../../../hooks/useRequest'
import authApi from '../../../modules/auth/services/authApi'
import toast from '../../../utils/toast'
import * as S from './styles'

function CheckToken({ setupMode = false, isOpen = true, goBack, model }) {
  const dispatch = useDispatch()
  const [arrayValue, setArrayValue] = useState<(string | number)[]>(['', '', '', '', '', ''])
  const inputRefs = useRef<(HTMLInputElement | null)[]>([])
  const [{ isLoading }, confirmOTPToken] = useRequest(authApi.confirmOTPToken)
  const history = useHistory()
  const { t } = useTranslation(['usersPage'])

  const resetInputs = () => {
    setArrayValue(['', '', '', '', '', ''])
    inputRefs.current[0]?.focus()
  }

  const handleSubmit = async (submitToken?: string) => {
    const token = submitToken ?? arrayValue.join('')
    if (!token) {
      toast.warn(t('REQUIRED_TOKEN'))
      return
    }

    if (setupMode) {
      await confirmOTPToken(token)
        .then(() => {
          toast.success(t('SETUP_SUCCESSFULLY_COMPLETED'))
          dispatch(fetchUser())

          history.push('/settings', { refresh: false })
        })
        .catch((err) => {
          resetInputs()
          if (err.response?.data?.message.match(/^otp token.*invalid/i)) {
            toast.warn(t('INVALID_TOKEN'))
            return
          }
          if (err.response?.data?.message.match(/^otp token.*required/i)) {
            toast.warn(t('REQUIRED_TOKEN'))
            return
          }
          toast.error(t('TOKEN_CONFIRMATION_FAILED'))
        })

      return
    }

    dispatch(setTwoFactorAuthToken({ token }))

    const dataLogin = {
      ...model,
    }

    dispatch(attemptLogin(dataLogin))
    dispatch(setTwoFactorAuthToken({ token: '' }))
  }

  const onKeyDown = (e) => {
    const keyCode = parseInt(e.key)
    if (
      e.key !== 'Backspace' &&
      e.key !== 'Delete' &&
      e.key !== 'Tab' &&
      !(e.metaKey && e.key === 'v') &&
      !(keyCode >= 0 && keyCode <= 9)
    ) {
      e.preventDefault()
    }
  }

  const onKeyUp = (e, index: number) => {
    if (e.key === 'Backspace' || e.key === 'Delete') {
      setArrayValue((prevValue: (string | number)[]) => {
        const newArray = [...prevValue]
        newArray[index - 1] = ''
        return newArray
      })

      if (index > 0) {
        inputRefs.current[index - 1]?.focus()
      }
    }
  }

  const onChange = (e, index: number) => {
    const input = e.target.value

    if (!isNaN(input)) {
      setArrayValue((preValue: number[]) => {
        const newArray = [...preValue]
        newArray[index] = input
        return newArray
      })

      if (input !== '' && index < arrayValue.length - 1) {
        inputRefs.current[index + 1]?.focus()
      }

      if (input !== '' && index === arrayValue.length - 1) {
        handleSubmit(arrayValue.join('') + input)
      }
    }
  }

  const onPaste = (e: React.ClipboardEvent, index: number) => {
    e.preventDefault()
    const paste = e.clipboardData.getData('text').replace(/\D/g, '')
    const pasteArray = paste.split('').slice(0, 6)

    if (pasteArray.length === 6) {
      setArrayValue(pasteArray)
      inputRefs.current[5]?.focus()
      handleSubmit(pasteArray.join(''))
    } else {
      setArrayValue((prevValue: (string | number)[]) => {
        const newArray = [...prevValue]
        pasteArray.forEach((char, i) => {
          newArray[index + i] = char
          inputRefs.current[index + i + 1]?.focus()
          if (index + i === 5) handleSubmit(newArray.join(''))
        })
        return newArray
      })
    }
  }

  useEffect(() => {
    inputRefs.current[0]?.focus()
    if (!isOpen) resetInputs()
    return () => resetInputs()
  }, [isOpen])

  return (
    <S.Container isOpen={isOpen} isLoading={isLoading}>
      <Helmet title={`${t('TITLE_TWO_FACTOR_AUTH')}`} />

      <S.ContentWrapper>
        <S.Header data-testid="checktoken-label-header">
          <S.BackLink onClick={goBack}>
            <span>←</span>
          </S.BackLink>
          {t('HEADER_LOGIN_REQUEST')}
        </S.Header>

        <S.Card>
          <S.Body>
            <h3>{t('HEADER_ENTER_CODE')}</h3>
            <S.InputWrapper>
              {arrayValue.map((value: string | number, index: number) => (
                // eslint-disable-next-line jsx-a11y/no-access-key
                <input
                  data-testid={`checktoken-input-token-${index}`}
                  key={`index-${index}`}
                  ref={(el) => el && (inputRefs.current[index] = el)}
                  inputMode="numeric"
                  maxLength={1}
                  name="passcode"
                  type="text"
                  value={String(value)}
                  onChange={(e) => onChange(e, index)}
                  onKeyUp={(e) => onKeyUp(e, index)}
                  onKeyDown={(e) => onKeyDown(e)}
                  onPaste={(e) => onPaste(e, index)}
                  autoComplete="off"
                  accessKey={String(index)}
                />
              ))}
            </S.InputWrapper>
          </S.Body>
        </S.Card>

        {/* <p>
          {t('NEED_HELP')} <a href="/two-factor-auth/help">{t('CLICK_HERE')}</a>
        </p> */}
      </S.ContentWrapper>
    </S.Container>
  )
}

export default memo(CheckToken)
