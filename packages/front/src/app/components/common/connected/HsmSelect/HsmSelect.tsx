import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import hsmApi from '../../../../resources/whatsappBusinessTemplate/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'

import { MessageSquarePlus } from 'lucide-react'
import GenericResourceSelect2 from '../../unconnected/GenericResourceSelect'

const useFetchManyHsm = (query = {}) => useFetchManyRequestLocal(hsmApi.fetchMany, query)

interface HsmSelectProps {
  showIcon?: boolean
  [key: string]: any
}

function HsmSelect({ showIcon = false, ...props }: HsmSelectProps) {
  const [response, exec] = useFetchManyHsm()
  const { data: hsm, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ stateId, search, extraQuery }) => {
    const query = assignDeep(
      {
        where: {
          ...(search && {
            $or: {
              internalName: { $iLike: `%${search}%` },
              name: { $iLike: `%${search}%` },
            },
          }),
          archivedAt: { $eq: null },
        },
      },
      extraQuery,
    )
    return exec({ query: JSON.stringify(query) })
  })

  return (
    <GenericResourceSelect2
      {...props}
      options={hsm}
      getOptionLabel={(o) => (o.internalName || o.name) + (o.archivedAt ? ' - Arquivado' : '')}
      isLoading={isLoading}
      error={error}
      fetch={fetch}
      pagination={pagination}
      icon={
        showIcon ? <MessageSquarePlus style={{ height: '20px', marginLeft: '-10px', color: '#515151' }} /> : undefined
      }
    />
  )
}

export default memo(HsmSelect)
