import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import { debounce } from 'lodash'
import ticketTopicsApi from '../../../../resources/ticketTopic/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'
import GenericResourceSelect from '../../unconnected/GenericResourceSelect'

const useFetchManyTicketTopics = (query = {}) => useFetchManyRequestLocal(ticketTopicsApi.fetchMany, query)

function TicketTopicsSelect(props) {
  const [response, exec, cancel] = useFetchManyTicketTopics()

  const { data: ticketTopics, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ search, extraQuery }) => {
    const query = assignDeep(
      {
        where: {
          ...(search && {
            name: { $iLike: `%${search}%` },
          }),
          ...(props.hideArchived && {
            archivedAt: { $eq: null },
          }),
        },
      },
      extraQuery,
    )

    return exec({ query: JSON.stringify(query) })
  })

  return (
    <GenericResourceSelect
      {...{
        ...props,
        options: [...(props.extraOptions || []), ...ticketTopics],
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(TicketTopicsSelect)
