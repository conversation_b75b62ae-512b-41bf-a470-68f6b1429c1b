import React, { useCallback } from 'react'
import find from 'lodash/find'
import GenericResourceSelect from '../../unconnected/GenericResourceSelect'

export default ({ services, extraOptions, fullObject, ...rest }) => {
  const { onChange } = rest
  const handleChange = useCallback(
    (value) => {
      if (!fullObject) {
        onChange(value)
        return
      }
      if (Array.isArray(value)) {
        onChange(value.map((v) => find(services, (item) => v.id === item.id)))
        return
      }
      const service = find(services, (item) => item.id === value.id)
      onChange(service)
    },
    [onChange],
  )
  return (
    <>
      <GenericResourceSelect
        {...rest}
        options={[...(extraOptions || []), ...(services || [])]}
        onChange={handleChange}
      />
    </>
  )
}
