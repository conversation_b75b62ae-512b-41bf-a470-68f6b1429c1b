import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import ServicesSelect from './ServicesSelect'
import servicesApi from '../../../../resources/service/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'

const useFetchManyServices = (query = {}) => useFetchManyRequestLocal(servicesApi.fetchMany, query)

function ServiceSelectContainer(props) {
  const [response, exec, cancel] = useFetchManyServices()

  const { data: services, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ stateId, search, extraQuery }) => {
    const query = assignDeep(
      {
        where: {
          ...(search && {
            name: { $iLike: `%${search}%` },
          }),
          ...(props.hideArchived && {
            archivedAt: { $eq: null },
          }),
        },
      },
      extraQuery,
    )

    return exec({ query: JSON.stringify(query) })
  })

  return (
    <ServicesSelect
      {...{
        ...props,
        services,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(ServiceSelectContainer)
