import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { with<PERSON>out<PERSON> } from 'react-router'
import orderBy from 'lodash/orderBy'
import Helmet from 'react-helmet'
import { Badge, ModalBody, ModalHeader } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { useRequest } from '../../../../hooks/useRequest'
import messageApi from '../../../../resources/message/api'
import ticketsApi from '../../../../resources/ticket/api'
import ticketExportApi from '../../../../resources/ticketHistory/api'
import Message from '../../../App/Dashboard/Chat/ChatBox/InnerChatBox/Message/Message'
import LoadingButton from '../../unconnected/LoadingButton'
import Icon from '../../unconnected/Icon'
import HistoryItem from '../../../App/Dashboard/overview/TicketHistory/HistoryItem'
import useRequestWithErrorHandler from '../../../../hooks/useRequestWithErrorHandler'
import { ModalDigisac, ModalFooter } from '../../../App/styles/common'
import ButtonClose from '../../unconnected/ButtonClose'
import { selectors } from '../../../../modules/auth'
import { chain } from 'lodash'
import format from '../../../../utils/date/format'

const TicketsShow = (props) => {
  const { t } = useTranslation(['ticketHistory', 'common'])
  const { isOpen, onClose, ticketId } = props

  const user = useSelector(selectors.getUser)

  const initialPagination = {
    limit: 30,
    offset: 0,
    paginate: false,
    withTotal: true,
  }
  const [pagination, setPagination] = useState({ ...initialPagination })
  const [messages, setMessages] = useState([])
  const [{ response: messagesResponse, isLoading: isMessagesLoading }, fetchManyMessages] = useRequest(
    messageApi.fetchMany,
  )
  const [{ response: ticketsResponse, isLoading: isTicketsLoading }, fetchTicketById] = useRequest(ticketsApi.fetchById)
  const [exportTxtRes, exportTxt] = useRequest(ticketExportApi.exportTicket)
  const [exportPdfRes, exportPdf] = useRequestWithErrorHandler(ticketExportApi.exportTicketPdf)

  const loadMore = (initial = null) => {
    fetchManyMessages({
      includeTicketTransfer: true,
      where: {
        ticketId,
        type: {
          $ne: 'reaction',
        },
      },
      include: [
        'file',
        'files',
        'hsmFile',
        'preview',
        'thumbnail',
        'hsm',
        {
          model: 'ticket',
          include: ['department', 'ticketTransfers'],
        },
        {
          model: 'user',
          attributes: ['id', 'name'],
        },
        {
          model: 'ticketTransfer',
          include: ['fromUser', 'toUser', 'fromDepartment', 'toDepartment', 'byUser'],
        },
        {
          model: 'from',
          attributes: ['id', 'name', 'alternativeName', 'internalName'],
        },
        {
          model: 'contact',
          attributes: ['id'],
        },
        {
          model: 'quotedMessage',
          include: ['file', 'preview', 'thumbnail', 'from', 'hsmFile', 'hsm'],
        },
        {
          model: 'reactions',
          include: ['from', 'user'],
        },
      ],
      order: [
        ['timestamp', 'ASC'],
        ['id', 'ASC'],
      ],
      ...(initial || pagination),
    })

    const offset = initial ? initial.offset : pagination.offset
    setPagination({
      ...(initial || pagination),
      offset: offset + 30,
    })
  }

  useEffect(() => {
    if (ticketId) {
      setMessages([])
      loadMore(initialPagination)
      fetchTicketById(ticketId, {
        include: [
          {
            model: 'contact',
            include: ['avatar', 'thumbAvatar'],
          },
          'department',
          'user',
          'ticketTopics',
        ],
      })
    }
  }, [ticketId])

  useEffect(() => {
    if (!messagesResponse) return

    setMessages((messages) => [
      ...messages,
      ...messagesResponse.map((msg) => {
        const currentTicketTransfer =
          msg.ticket?.ticketTransfers?.length &&
          orderBy(
            msg.ticket.ticketTransfers.filter((tt) => tt.createdAt < msg.createdAt),
            (tt) => tt.createdAt,
            'desc',
          )[0]
        return { ...msg, currentTicketTransfer }
      }),
    ])
  }, [messagesResponse])

  const defaultTimestamp = format(new Date(1), 'dd/MM/yyyy')
  const messagesByDate = chain(messages)
    .groupBy((data) => format(new Date(data.timestamp), 'dd/MM/yyyy'))
    .map((messages, date) => ({ date, messages }))
    .filter(({ date }) => date !== defaultTimestamp)
    .value()

  useEffect(() => {
    if (ticketsResponse && !exportTxtRes.isLoading && exportTxtRes.response) {
      const link = document.createElement('a')
      link.download = `${ticketsResponse.contact.name}_${ticketsResponse.protocol}.txt`
      const blob = new Blob([exportTxtRes.response], {
        type: 'text/plain;charset=utf-8',
      })
      link.href = window.URL.createObjectURL(blob)
      link.click()
    }
  }, [exportTxtRes.response, exportTxtRes.isLoading])

  useEffect(() => {
    if (ticketsResponse && !exportPdfRes.isLoading && exportPdfRes.response) {
      const link = document.createElement('a')
      link.download = `${ticketsResponse.contact.name}_${ticketsResponse.protocol}.pdf`
      const blob = new Blob([exportPdfRes.response], {
        type: 'application/pdf',
      })
      link.href = window.URL.createObjectURL(blob)
      link.click()
    }
  }, [exportPdfRes.response, exportPdfRes.isLoading])

  const isLoadMorePossible =
    messagesResponse && messagesResponse.total ? messagesResponse.total > messages.length : true

  return (
    <div>
      <Helmet title={t('CREATE_STATS_TICKET_HISTORY_LABEL_TICKET')} />

      <ModalDigisac isOpen={isOpen} toggle={onClose} autoFocus={false} size="lg" scrollable="true">
        <ModalHeader className="w-100">
          {t('CREATE_STATS_TICKET_HISTORY_CALLED_MESSAGES')}

          <ButtonClose onClick={onClose} />
        </ModalHeader>

        <ModalBody className="bg-light">
          {!isTicketsLoading && <HistoryItem ticket={ticketsResponse} />}

          <div className="p-3 my-3 shadow-sm bg-white rounded">
            {messagesByDate.map(({ date, messages }) => (
              <div key={date}>
                <div className="d-flex justify-content-center align-items-center my-3">
                  <h5>
                    <Badge
                      className="p-3 text-white fw-semibold"
                      style={{
                        minHeight: 0,
                        backgroundColor: '#95A7CC',
                        borderRadius: '5px',
                        fontFamily: 'Montserrat',
                      }}
                    >
                      {date}
                    </Badge>
                  </h5>
                </div>
                {messages.map((message) => (
                  <Message key={message.id} message={message} viewMode />
                ))}
              </div>
            ))}
          </div>

          {isLoadMorePossible && (
            <div className="d-flex justify-content-center mb-3">
              <LoadingButton
                color="default"
                className="cancel"
                onClick={() => loadMore()}
                isLoading={isMessagesLoading}
              >
                {t('common:LABEL_LOAD_MORE')}
              </LoadingButton>
            </div>
          )}
        </ModalBody>

        <ModalFooter>
          {!isTicketsLoading && (
            <div className="my-2 d-flex align-items-center justify-content-end w-100">
              <LoadingButton data-testid="button-Cancel" type="button" onClick={() => onClose()} className="mr-2">
                {t('common:FORM_ACTION_CLOSE')}
              </LoadingButton>

              <LoadingButton
                data-testid="button-Export_PDF"
                color="primary"
                type="button"
                onClick={() => exportPdf(ticketsResponse.id, user.language)}
                isLoading={exportPdfRes.isLoading}
                className="mr-2"
              >
                <Icon name="download" title="Exportar atendimento" fixedWidth className="mr-2" />
                {t('CREATE_STATS_TICKET_HISTORY_LABEL_EXPORT', {
                  extension: 'PDF',
                })}
              </LoadingButton>

              <LoadingButton
                data-testid="button-Export_TXT"
                color="primary"
                className="confirm"
                type="button"
                onClick={() => exportTxt({ protocol: ticketsResponse.protocol })}
                isLoading={exportTxtRes.isLoading}
              >
                <Icon name="download" title="Exportar atendimento" fixedWidth className="mr-2" />
                {t('CREATE_STATS_TICKET_HISTORY_LABEL_EXPORT', {
                  extension: 'TXT',
                })}
              </LoadingButton>
            </div>
          )}
        </ModalFooter>
      </ModalDigisac>
    </div>
  )
}

export default withRouter(TicketsShow)
