import React, { memo } from 'react'
import { useTranslation } from 'react-i18next'
import ModulesSelect from './ModulesSelect'
import { modules } from './index'

function ModulesSelectContainer(props) {
  const { t } = useTranslation(['navbar', 'common'])
  return (
    <ModulesSelect
      {...{
        ...props,
        modules: modules.map((module) => ({
          ...module,
          label: t(module.label),
        })),
        placeholder: t('common:SELECT_LOADING_PLACEHOLDER'),
      }}
    />
  )
}

export default memo(ModulesSelectContainer)
