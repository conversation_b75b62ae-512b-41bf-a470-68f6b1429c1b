import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import AccountsSelect from './AccountsSelect'
import accountsApi from '../../../../resources/admin/account/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'

const useFetchManyAccounts = (query = {}) => useFetchManyRequestLocal(accountsApi.fetchMany, query)

function AccountSelectContainer(props) {
  const [response, exec, cancel] = useFetchManyAccounts()

  const { data: accounts, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ stateId, search, extraQuery }) => {
    const query = assignDeep(
      {
        ...(search && {
          where: {
            name: { $iLike: `%${search}%` },
          },
        }),
      },
      extraQuery,
    )

    return exec(query)
  })

  return (
    <AccountsSelect
      {...{
        ...props,
        accounts,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(AccountSelectContainer)
