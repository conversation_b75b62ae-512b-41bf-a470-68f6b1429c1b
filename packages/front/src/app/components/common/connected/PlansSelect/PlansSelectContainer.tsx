import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import PlansSelect from './PlansSelect'
import plansApi from '../../../../resources/plan/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'

const useFetchManyPlans = (query = {}) => useFetchManyRequestLocal(plansApi.fetchMany, query)

function PlanSelectContainer(props) {
  const [response, exec, cancel] = useFetchManyPlans()

  const { data: plans, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ stateId, search, extraQuery }) => {
    const query = assignDeep(
      {
        ...(search && {
          where: {
            name: { $iLike: `%${search}%` },
          },
        }),
      },
      extraQuery,
    )

    return exec(query)
  })

  return (
    <PlansSelect
      {...{
        ...props,
        plans,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(PlanSelectContainer)
