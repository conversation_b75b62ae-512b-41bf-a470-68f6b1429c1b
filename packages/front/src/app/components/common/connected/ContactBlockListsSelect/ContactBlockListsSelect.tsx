import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import api from '../../../../resources/contactsBlockLists/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'
import GenericResourceSelect from '../../unconnected/GenericResourceSelect'

const useFetchMany = (query = {}) => useFetchManyRequestLocal(api.fetchMany, query)

const ContactBlockListsSelect = (props) => {
  const [response, exec, cancel] = useFetchMany()

  const { data: interactiveMessages, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ search, extraQuery }) => {
    const query = assignDeep(
      {
        where: {
          ...(search && {
            name: {
              $iLike: `%${search}%`,
            },
          }),
          status: 'done',
          // archivedAt: { $eq: null },
        },
        order: [['updatedAt', 'desc']],
      },
      extraQuery,
    )

    return exec({ query: JSON.stringify(query) })
  })

  return (
    <GenericResourceSelect
      {...{
        ...props,
        options: interactiveMessages,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(ContactBlockListsSelect)
