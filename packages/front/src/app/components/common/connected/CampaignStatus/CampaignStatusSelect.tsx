import React, { useCallback } from 'react'
import find from 'lodash/find'
import GenericResourceSelect from '../../unconnected/GenericResourceSelect'

export default ({ status, ...rest }) => {
  const { onChange } = rest
  const handleChange = useCallback(
    (value) => {
      const selected = find(status, (item) => item?.id === value?.id)
      onChange(selected)
    },
    [onChange],
  )

  return <GenericResourceSelect options={status} {...rest} onChange={handleChange} />
}
