import React, { memo } from 'react'
import { useTranslation } from 'react-i18next'
import CampaignStatusSelect from './CampaignStatusSelect'
import { status } from './index'

function CampaignStatusSelectContainer(props) {
  const { t } = useTranslation(['campaignPage', 'common'])

  const defaultPagination = {
    currentPage: 1,
    total: null,
    lastPage: null,
    from: null,
    to: null,
  }

  return (
    <CampaignStatusSelect
      {...{
        ...props,
        status: status.map((stat) => ({ ...stat, name: t(stat.name) })),
        placeholder: t('common:SELECT_LOADING_PLACEHOLDER'),
        fetch: () => {},
        pagination: defaultPagination,
      }}
    />
  )
}

export default memo(CampaignStatusSelectContainer)
