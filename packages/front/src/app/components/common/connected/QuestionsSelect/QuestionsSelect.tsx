import React from 'react'
import GenericResourceSelect from '../../unconnected/GenericResourceSelect'
import { useFetchManyQuestions } from '../../../../resources/questions/requests'
import useEventCallback from '../../../../hooks/useEventCallback'

function QuestionSelect(props) {
  const [{ data: models = [], isLoading, pagination }, exec] = useFetchManyQuestions()

  const fetch = useEventCallback(({ search, extraQuery }) => {
    exec({
      where: {
        ...(search && {
          name: { $iLike: `%${search}%` },
        }),
      },
      order: [['name', 'ASC']],
      ...(extraQuery && {
        page: extraQuery.page,
      }),
    })
  })

  return (
    <GenericResourceSelect {...props} options={models} fetch={fetch} isLoading={isLoading} pagination={pagination} />
  )
}

export default QuestionSelect
