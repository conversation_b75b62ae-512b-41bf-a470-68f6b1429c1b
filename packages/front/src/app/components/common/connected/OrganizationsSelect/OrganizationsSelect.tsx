import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import organizationsApi from '../../../../resources/organization/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'
import GenericResourceSelect from '../../unconnected/GenericResourceSelect'

const useFetchManyOrganizations = (query = {}) => useFetchManyRequestLocal(organizationsApi.fetchMany, query)

function OrganizationsSelect(props) {
  const [response, exec] = useFetchManyOrganizations()

  const { data: organizations, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ search, extraQuery }) => {
    const query = assignDeep(
      {
        attributes: ['id', 'name'],
        ...(search && {
          where: {
            name: { $iLike: `%${search}%` },
          },
        }),
        order: [['name', 'ASC']],
      },
      extraQuery,
    )

    return exec(query)
  })

  return (
    <GenericResourceSelect
      {...{
        ...props,
        options: organizations,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(OrganizationsSelect)
