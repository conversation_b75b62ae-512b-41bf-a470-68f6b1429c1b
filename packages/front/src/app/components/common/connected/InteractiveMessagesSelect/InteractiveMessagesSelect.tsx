import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import api from '../../../../resources/interactiveMessages/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'
import GenericResourceSelect from '../../unconnected/GenericResourceSelect'

const useFetchMany = (query = {}) => useFetchManyRequestLocal(api.fetchMany, query)

const InteractiveMessagesSelect = (props) => {
  const [response, exec, cancel] = useFetchMany()

  const { data: interactiveMessages, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ stateId, search, extraQuery }) => {
    const query = assignDeep(
      {
        where: {
          ...(search && {
            name: {
              $iLike: `%${search}%`,
            },
          }),
          archivedAt: { $eq: null },
        },
      },
      extraQuery,
    )

    return exec({ query: JSON.stringify(query) })
  })

  return (
    <GenericResourceSelect
      {...{
        ...props,
        options: interactiveMessages,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(InteractiveMessagesSelect)
