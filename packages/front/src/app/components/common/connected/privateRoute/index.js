import { connect } from 'react-redux'
import redirectIf from '../../unconnected/redirectIf/index'
import { getIsAuthenticated } from '../../../../modules/auth/selectors'

const mapStateToProps = (state) => ({
  isAuthenticated: getIsAuthenticated(state),
})

export default connect(mapStateToProps)(
  redirectIf(
    (props) => !props.isAuthenticated,
    (props) => ({
      pathname: '/login',
      state: { from: props.location },
    }),
  ),
)
