import React from 'react'
import { Route, Redirect } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { urlRedirect } from '../../../../modules/auth/actions'
import { getIsAuthenticated } from '../../../../modules/auth/selectors'

export default ({ component: Component, ...rest }) => {
  const isAuthenticated = useSelector(getIsAuthenticated)

  const dispatch = useDispatch()

  dispatch(
    urlRedirect({
      urlRedirect: `${window.location.pathname}${window.location.search}`,
    }),
  )

  return (
    <Route
      {...rest}
      render={(props) => {
        if (!isAuthenticated) {
          return <Redirect to="/login" />
        }

        return <Component {...props} />
      }}
    />
  )
}
