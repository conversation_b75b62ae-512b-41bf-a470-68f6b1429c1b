import React, { memo, useEffect, useState } from 'react'
import assignDeep from 'lodash/merge'
import useEventCallback from '../../../../hooks/useEventCallback'
import GenericResourceSelect from '../../unconnected/GenericResourceSelect'
import { useFetchManyContactsLocal } from '../../../../resources/contact/requests'
import { isEqual } from 'lodash'

function ContactsSelect(props) {
  const { onlyGroups, filterBy = {} } = props
  const [response, exec, cancel] = useFetchManyContactsLocal()
  const [previousFilter, setPreviousFilter] = useState(filterBy)

  useEffect(() => {
    if (!isEqual(filterBy, previousFilter) || onlyGroups) {
      setPreviousFilter(filterBy)
      exec({
        where: {
          visible: true,
          ...(onlyGroups && {
            isGroup: true,
          }),
          ...filterBy,
        },
      })
    }
  }, [filterBy, onlyGroups])

  const { data: contacts, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ search, extraQuery }) => {
    const query = assignDeep(
      {
        where: {
          visible: true,
          ...(search && {
            $or: {
              internalName: { $iLike: `%${search}%` },
              name: { $iLike: `%${search}%` },
              alternativeName: { $iLike: `%${search}%` },
              'data.number': { $iLike: `%${search}%` },
            },
          }),
          ...(onlyGroups && {
            isGroup: true,
          }),
          ...filterBy,
        },
        ...(props.showServiceIcon && {
          include: [
            {
              model: 'service',
              where: { archivedAt: null },
              required: true,
            },
          ],
        }),
      },
      extraQuery,
    )

    return exec({ query: JSON.stringify(query) })
  })

  useEffect(() => () => cancel(), [])

  const options = contacts.map((item) => ({
    id: item.id,
    name: item.internalName || item.name,
  }))

  return (
    <GenericResourceSelect
      {...{
        ...props,
        options,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(ContactsSelect)
