import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import peopleApi from '../../../../resources/person/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'
import GenericResourceSelect from '../../unconnected/GenericResourceSelect'

const useFetchManyPeople = (query = {}) => useFetchManyRequestLocal(peopleApi.fetchMany, query)

function PeopleSelect(props) {
  const [response, exec, cancel] = useFetchManyPeople()

  const { data: people, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ stateId, search, extraQuery }) => {
    const query = assignDeep(
      {
        ...(search && {
          where: {
            name: { $iLike: `%${search}%` },
          },
        }),
      },
      extraQuery,
    )

    return exec(query)
  })

  return (
    <GenericResourceSelect
      {...{
        ...props,
        options: people,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(PeopleSelect)
