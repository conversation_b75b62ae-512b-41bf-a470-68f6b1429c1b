import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import BotsSelect from './BotsSelect'
import botsApi from '../../../../resources/bot/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'
import { Bot } from '../../../../types/Bot'
import { format } from 'date-fns'

const useFetchManyBots = (query = {}) => useFetchManyRequestLocal(botsApi.fetchMany, query)

function BotSelectContainer(props) {
  const [response, exec, cancel] = useFetchManyBots()

  const { data: bots, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ stateId, search, extraQuery }) => {
    const query = assignDeep(
      {
        where: {
          ...(search && {
            name: { $iLike: `%${search}%` },
          }),
          currentBotVersionId: { $ne: null },
        },
        include: ['currentBotVersion'],
      },
      extraQuery,
    )
    return exec({ query: JSON.stringify(query) })
  })

  const formatBot = (bot: Bot) => {
    if (!bot?.name) return bot

    return {
      ...bot,
      name: bot.currentBotVersion
        ? `${bot.name} - Publicado em ${format(new Date(bot.currentBotVersion?.updatedAt ? bot.currentBotVersion?.updatedAt : null), 'dd/MM/yyyy HH:mm')}`
        : bot.name,
    }
  }

  return (
    <BotsSelect
      isPaged
      {...{
        ...props,
        bots: bots?.map(formatBot),
        ...(props.value && { value: formatBot(props.value) }),
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(BotSelectContainer)
