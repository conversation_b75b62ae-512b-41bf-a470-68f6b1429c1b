import React, { memo, useCallback } from 'react'
import cns from 'classnames'
import Avatar from '../Avatar/Avatar'
import ContactName from '../../unconnected/ContactName'
import EllipsisSpinner from '../../unconnected/EllipsisSpinner'
import FakeBox from '../../unconnected/FakeBox'
import ServiceBadge from '../../unconnected/ServiceBadge'

const styles = {
  innerBox: { width: '100%' },
  avatar: { height: 50, width: 50, marginRight: 16 },
  nameBox: { width: 'calc(100% - 66px)' },
  innerNameBox: {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
}

function ContactItem(props) {
  const { contact, style, isLoading, onClick, isSelected } = props

  const handleClick = useCallback(() => {
    if (onClick) onClick(contact)
  }, [contact])

  if (!contact && isLoading) {
    return (
      <div className="text-center pt-2" style={style}>
        <EllipsisSpinner color="#b3b3b3" />
      </div>
    )
  }

  if (!contact) return null

  return (
    <div className={cns('contact-item', isSelected && 'active')} style={style} onClick={handleClick}>
      <div className="d-flex justify-content-start" style={styles.innerBox}>
        <FakeBox value={isSelected} className="align-self-center mr-3" />
        <Avatar url={(contact.thumbAvatar || {}).url || (contact.avatar || {}).url} style={styles.avatar} />
        <div style={styles.nameBox}>
          <div className="d-flex justify-content-between align-items-center">
            <div style={styles.innerNameBox}>
              <ContactName contact={contact} />
            </div>
          </div>
          <div>
            <ServiceBadge service={contact.service} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default memo(ContactItem)
