import React, { useState, useCallback, useEffect, memo } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>dal<PERSON><PERSON>, ModalFooter, Input } from 'reactstrap'
import { AutoSizer, InfiniteLoader, List } from 'react-virtualized'
import debounce from 'lodash/debounce'
import { uniqBy } from 'lodash'
import { useTranslation } from 'react-i18next'
import { useRequest } from '../../../../hooks/useRequest'
import contactApi from '../../../../resources/contact/api'
import LoadingButton from '../../unconnected/LoadingButton'
import ContactItem from './ContactItem'
import isPhoneNumber from '../../../../utils/isPhoneNumber'
import useEventCallback from '../../../../hooks/useEventCallback'
import getBooleanMapKeys from '../../../../utils/getBooleanMapKeys'

const makeLoadMoreQuery = ({ filters, offset = 0, limit = 30, departmentsIds, ticketsEnabled } = {}) => ({
  where: {
    visible: true,
    ...(filters.search && {
      $or: {
        name: { $iLike: `%${filters.search}%` },
        internalName: { $iLike: `%${filters.search}%` },
        alternativeName: { $iLike: `%${filters.search}%` },
        ...(isPhoneNumber(filters.search) && {
          'data.number': { $iLike: `%${filters.search}%` },
        }),
      },
    }),
  },
  departmentsIds,
  ticketsEnabled,
  order: [['name', 'ASC']],
  paginate: false,
  withTotal: true,
  offset,
  limit,
})

const useController = ({ onSelect, user }) => {
  const [state, setState] = useState({
    contacts: [],
    selectedContactsMap: {},
    total: 0,
    isLoading: false,
    error: null,
    filters: {
      search: '',
    },
  })
  const { contacts, total, isLoading, selectedContactsMap, filters } = state

  const [, fetch] = useRequest(contactApi.forward)
  const hasNextPage = total > contacts.length
  const rowCount = hasNextPage ? contacts.length + 1 : contacts.length

  const fetchContacts = useEventCallback(async ({ startIndex = 0, loadMore = false } = {}) => {
    const perPage = 30
    const page = Math.ceil(startIndex / perPage)

    const offset = perPage * page
    const limit = perPage

    if (loadMore && ((contacts.length && contacts.length > offset) || (total && contacts.length >= total))) return

    try {
      setState((prevState) => ({
        ...prevState,
        isLoading: true,
        error: null,
      }))
      const departmentsIds = (user?.departments && user.departments.map((dep) => dep.id)) || []

      const query = makeLoadMoreQuery({
        filters,
        offset,
        limit,
        departmentsIds,
        ticketsEnabled: user.account.settings.ticketsEnabled,
      })

      const result = await fetch({ query: JSON.stringify(query) })

      setState((prevState) => ({
        ...prevState,
        contacts: loadMore ? uniqBy([...prevState.contacts, ...result.data], 'id') : uniqBy(result.data, 'id'),
        total: result.total,
        isLoading: false,
        error: null,
      }))
    } catch (error) {
      if (error.isCancel) return

      setState((prevState) => ({
        ...prevState,
        isLoading: false,
        error,
      }))
      throw error
    }
  })

  const debouncedFetchContacts = useCallback(debounce(fetchContacts, 500), [fetchContacts])

  const loadMore = useCallback((params) => fetchContacts({ ...params, loadMore: true }), [fetchContacts])

  const handleLoadMoreRows = useCallback(
    (params) => {
      loadMore({ ...params }).catch(console.error)
    },
    [loadMore],
  )

  const handleSuccessClick = useCallback(() => {
    onSelect(getBooleanMapKeys(selectedContactsMap))
  }, [onSelect, selectedContactsMap])

  const handleSearchChanged = useCallback((e) => {
    const search = e.target.value

    setState((prevState) => ({
      ...prevState,
      filters: {
        ...prevState.filters,
        search,
      },
    }))
  }, [])

  const handleContactClick = useCallback((contact) => {
    setState((prevState) => ({
      ...prevState,
      selectedContactsMap: {
        ...prevState.selectedContactsMap,
        [contact.id]: !prevState.selectedContactsMap[contact.id],
      },
    }))
  }, [])

  const isRowLoaded = useCallback(
    ({ index }) => !hasNextPage && index < contacts.length,
    [hasNextPage, contacts.length],
  )

  useEffect(() => {
    debouncedFetchContacts({ startIndex: 0 })
  }, [filters])

  return {
    contacts,
    selectedContactsMap,
    isLoading,
    rowCount,
    filters,
    handleLoadMoreRows,
    handleSuccessClick,
    handleSearchChanged,
    handleContactClick,
    isRowLoaded,
  }
}

const styles = {
  modalBody: { height: '400px' },
  nothingFound: { marginTop: '60%' },
}

function ContactPicker({ onSelect, isOpen, toggle, submitButtonText = 'Encaminhar', user }) {
  const {
    contacts,
    selectedContactsMap,
    isLoading,
    rowCount,
    filters,
    handleLoadMoreRows,
    handleSuccessClick,
    handleSearchChanged,
    handleContactClick,
    isRowLoaded,
  } = useController({ onSelect, user })

  const { t } = useTranslation(['chatPage'])

  const rowRenderer = useCallback(
    ({ key, index, style }) => {
      const contact = contacts[index]

      if (!contact) return null

      return (
        <ContactItem
          {...{
            key,
            id: index,
            position: null,
            contact,
            onClick: handleContactClick,
            style,
            isLoading,
            isSelected: selectedContactsMap[contact.id],
          }}
        />
      )
    },
    [isLoading, uniqBy(contacts, 'id'), handleContactClick, selectedContactsMap],
  )

  return (
    <Modal isOpen={isOpen} toggle={toggle}>
      <ModalHeader toggle={toggle}>{t('SELECT_CONTACTS')}</ModalHeader>
      <div className="px-2 pt-2 bg-light">
        <div className="d-flex">
          <div className="flex-fill">
            <Input
              id="search-text"
              placeholder={t('SEARCH_PHONE_NAME')}
              value={filters.search}
              onChange={handleSearchChanged}
            />
          </div>
        </div>
      </div>
      <ModalBody style={styles.modalBody}>
        {!contacts.length && (
          <p className="text-center text-secondary" style={styles.nothingFound}>
            {t('NO_RESULTS')}
          </p>
        )}

        {!!contacts.length && (
          <AutoSizer>
            {({ height, width }) => (
              <InfiniteLoader isRowLoaded={isRowLoaded} loadMoreRows={handleLoadMoreRows} rowCount={rowCount}>
                {({ onRowsRendered, registerChild }) => (
                  <List
                    width={width}
                    height={height}
                    ref={registerChild}
                    onRowsRendered={onRowsRendered}
                    rowCount={rowCount}
                    rowHeight={80}
                    rowRenderer={rowRenderer}
                  />
                )}
              </InfiniteLoader>
            )}
          </AutoSizer>
        )}
      </ModalBody>

      <ModalFooter>
        <div className="d-flex align-items-end flex-column">
          <LoadingButton color="primary" type="submit" onClick={handleSuccessClick} isLoading={isLoading}>
            {submitButtonText}
          </LoadingButton>
        </div>
      </ModalFooter>
    </Modal>
  )
}

export default memo(ContactPicker)
