import React, { memo, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import assignDeep from 'lodash/merge'
import useEventCallback from '../../../../hooks/useEventCallback'
import GenericResourceSelect from '../../unconnected/GenericResourceSelect'
import pipelinesApi from '../../../../resources/pipelines/api'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'

const useFetchManyPipelines = (query = {}) => useFetchManyRequestLocal(pipelinesApi.fetchMany, query)

function PipelinesSelect(props) {
  const { filterBy = {} } = props
  const [response, exec, cancel] = useFetchManyPipelines()
  const { t } = useTranslation(['common'])

  useEffect(() => {
    if (Object.keys(filterBy).length) {
      const query = {
        where: {
          ...filterBy,
          ...(props.hideArchived && {
            archivedAt: { $eq: null },
          }),
        },
        include: [
          {
            model: 'stages',
            include: ['statuses'],
          },
        ],
      }

      exec({ query: JSON.stringify(query) })
    }
  }, [JSON.stringify(filterBy)])

  const { data: pipelines, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ search, extraQuery }) => {
    const query = assignDeep(
      {
        where: {
          ...(search && { name: { $iLike: `%${search}%` } }),
          ...filterBy,
          ...(props.hideArchived && {
            archivedAt: { $eq: null },
          }),
        },
        include: [
          {
            model: 'stages',
            include: ['statuses'],
          },
        ],
        paginate: true,
      },
      extraQuery,
    )

    return exec({ query: JSON.stringify(query) })
  })

  useEffect(() => () => cancel(), [])

  const options = pipelines.map((item) => ({
    id: item.id,
    name: `${item.name}${item?.archivedAt ? ` - ${t('LABEL_ARCHIVED')}` : ''}`,
    stages: item.stages || [],
  }))

  return (
    <GenericResourceSelect
      {...{
        ...props,
        options,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(PipelinesSelect)
