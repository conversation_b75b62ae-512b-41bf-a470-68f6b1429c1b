import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import TagsSelect from './TagsSelect'
import tagsApi from '../../../../resources/tag/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'
import { StylesConfig } from 'react-select'
import { Tag } from '../../../../types/Tag'

const useFetchManyTags = (query = {}) => useFetchManyRequestLocal(tagsApi.fetchMany, query)

function TagSelectContainer(props) {
  const [response, exec, cancel] = useFetchManyTags()

  const { data: tags, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ stateId, search, extraQuery }) => {
    const query = assignDeep(
      {
        ...(search && {
          where: {
            label: { $iLike: `%${search}%` },
          },
        }),
      },
      extraQuery,
    )

    return exec(query)
  })

  const dot = (color?: string) => {
    if (!color) return {}

    return {
      ':before': {
        backgroundColor: color,
        borderRadius: 2,
        content: '" "',
        display: 'block',
        minHeight: 8,
        minWidth: 8,
      },
    }
  }

  const customStyle: StylesConfig<Tag, true> = {
    control: (providedStyles, stateEvent) => ({
      ...providedStyles,
      margin: 0,
      padding: props.icon ? '0 8px 0 16px !important' : '0 8px !important',
      borderRadius: 20,
      minHeight: 40,
      border: '1px solid #b4bbc5 !important',
      boxShadow: 'none',
      ':hover': {
        borderColor: '#b4bbc5 !important',
      },
      backgroundColor: stateEvent.isDisabled ? '#EDEEF1' : 'white',
    }),
    option: (styles, { data }) => ({
      ...styles,
      ...dot(data.backgroundColor || 'transparent'),
      display: 'flex !important',
      justifyContent: 'flex-start',
      alignItems: 'center',
      gap: 8,
    }),
    multiValueLabel: (styles) => ({
      ...styles,
      padding: 0,
      paddingRight: 4,
      paddingLeft: 4,
      color: '#4c5461 !important',
    }),
    multiValueRemove: () => ({
      color: '#586171 !important',
      borderRadius: '50%',
      padding: 0,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      '&>svg>circle': {
        color: '#586171 !important',
      },
      ':hover': {
        color: 'white !important',
        backgroundColor: '#586171 !important',
      },
    }),
    multiValue: (styles, { data }) => ({
      ...styles,
      ...dot(data.backgroundColor),
      alignItems: 'center',
      display: 'flex',
      height: 24,
      borderRadius: 12,
      backgroundColor: '#f7f8f8 !important',
      color: '#4c5461 !important',
      padding: '0 8px !important',
    }),
    valueContainer: (providedStyles) => ({
      ...providedStyles,
      color: '#24272D',
      fontSize: 14,
      gap: 4,
      padding: '4px 0',
    }),
  }

  return (
    <TagsSelect
      {...{
        ...props,
        tags,
        isLoading,
        error,
        fetch,
        pagination,
        isPaged: true,
        styles: customStyle,
      }}
    />
  )
}

export default memo(TagSelectContainer)
