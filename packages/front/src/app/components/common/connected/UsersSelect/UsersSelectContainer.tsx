import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import UsersSelect from './UsersSelect'
import usersApi from '../../../../resources/user/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'
import User from '../../../../types/User'

interface UserWhereConditions {
  name?: { $iLike: string }
  archivedAt?: { $eq: null } | { $ne: null }
}

const useFetchManyUsers = (query = {}) => useFetchManyRequestLocal(usersApi.fetchMany, query)

function UserSelectContainer(props) {
  const [response, exec, cancel] = useFetchManyUsers()

  const { data: users, isLoading, error, pagination } = response

  const getArchivedAtCondition = () => {
    if (!props.userStatus || props.userStatus === 'all') {
      if (props.hideArchived) {
        return { $eq: null }
      }
      return undefined
    }

    if (props.userStatus === 'active') {
      return { $eq: null }
    }

    if (props.userStatus === 'archived') {
      return { $ne: null }
    }

    return undefined
  }

  const fetch = useEventCallback(({ stateId, search, extraQuery }) => {
    const whereConditions: UserWhereConditions = {}

    if (search) {
      whereConditions.name = { $iLike: `%${search}%` }
    }

    const archivedAtCondition = getArchivedAtCondition()
    if (archivedAtCondition) {
      whereConditions.archivedAt = archivedAtCondition
    }

    const query = assignDeep(
      {
        where: whereConditions,
      },
      extraQuery,
    )

    return exec({ query: JSON.stringify(query) })
  })

  return (
    <UsersSelect
      {...{
        ...props,
        users,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(UserSelectContainer)
