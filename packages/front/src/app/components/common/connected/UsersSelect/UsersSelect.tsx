import React from 'react'
import GenericResourceSelect from '../../unconnected/GenericResourceSelect'
import { selectStyles } from '../../unconnected/Select'

const statusMap = {
  online: 'green',
  offline: 'grey',
  away: 'yellow',
}
export default ({ users, extraOptions, ...rest }) => (
  <GenericResourceSelect
    options={[...(extraOptions || []), ...(users || [])]}
    styles={{
      option: (styles, { data }) => ({
        ...styles,
        ...(data.status && {
          ':before': {
            backgroundColor: statusMap[data.status],
            borderRadius: 10,
            content: '" "',
            display: 'block',
            marginRight: 8,
            height: 10,
            width: 10,
          },
          alignItems: 'center',
          display: 'flex',
          minHeight: 36,
        }),
      }),
    }}
    {...rest}
  />
)
