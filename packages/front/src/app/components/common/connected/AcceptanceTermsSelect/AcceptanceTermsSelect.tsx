import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import acceptanceTermsApi from '../../../../resources/acceptanceTerms/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'
import GenericResourceSelect from '../../unconnected/GenericResourceSelect'

const useFetchManyAcceptanceTerms = (query = {}) => useFetchManyRequestLocal(acceptanceTermsApi.fetchMany, query)

function AcceptanceTermsSelect(props) {
  const [response, exec, cancel] = useFetchManyAcceptanceTerms()

  const { data: acceptanceTerms, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ stateId, search, extraQuery }) => {
    const query = assignDeep(
      {
        ...(search && {
          where: {
            name: { $iLike: `%${search}%` },
          },
        }),
      },
      extraQuery,
    )

    return exec(query)
  })

  return (
    <GenericResourceSelect
      {...{
        ...props,
        options: acceptanceTerms,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(AcceptanceTermsSelect)
