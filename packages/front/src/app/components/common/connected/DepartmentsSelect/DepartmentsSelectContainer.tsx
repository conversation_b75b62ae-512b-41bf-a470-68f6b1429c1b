import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import DepartmentsSelect from './DepartmentsSelect'
import departmentsApi from '../../../../resources/departments/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'

const useFetchManyDepartments = (query = {}) => useFetchManyRequestLocal(departmentsApi.fetchMany, query)

function DepartmentSelectContainer(props) {
  const [response, exec, cancel] = useFetchManyDepartments()

  const { data: departments, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ search, extraQuery }) => {
    const query = assignDeep(
      {
        where: {
          ...(search && {
            name: { $iLike: `%${search}%` },
          }),
          ...(props.hideArchived && {
            archivedAt: { $eq: null },
          }),
        },
      },
      extraQuery,
    )

    return exec({ query: JSON.stringify(query) })
  })

  return (
    <DepartmentsSelect
      {...{
        ...props,
        departments,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(DepartmentSelectContainer)
