import React, { memo, useCallback, useEffect, useState } from 'react'
import debounce from 'lodash/debounce'
import Select from '../../unconnected/Select'
import { useCreateCategory, useFetchManyCategories } from '../../../../resources/categories/requests'
import useEventCallback from '../../../../hooks/useEventCallback'

const getOptionLabel = (o) => o.label

function CreatableCategorySelect({ onChange, value = [], isCreatable = true, ...rest }) {
  const [, create] = useCreateCategory()
  const [{ isLoading: isFetchLoading, data: categories }, fetch] = useFetchManyCategories()

  const fetchCategory = useCallback(debounce(fetch, 300), [fetch])
  const [tempCategories, setTempCategories] = useState([])

  const handleInputChange = useEventCallback((search = '') => {
    fetchCategory({ where: { title: { $iLike: `%${search}%` } } })
  })

  const handleChange = useEventCallback((selectedCategories) => {
    const parsedCategories = (selectedCategories || []).map((t) => ({
      ...t,
      id: t.id || t.value,
    }))

    const newOptions = parsedCategories.filter((category) => category.__isNew__)
    const oldOptions = parsedCategories.filter((category) => !category.__isNew__)

    if (newOptions.length) {
      Promise.all(newOptions.map((category) => create({ title: category.label }))).then((newCategories) => {
        setTempCategories((prevTempCategories) =>
          prevTempCategories.filter((op) => newCategories.includes((t) => t.label !== op.label)),
        )

        const newValue = [...value, ...newCategories]

        onChange(newValue)
      })
    }

    setTempCategories(newOptions)
    onChange(oldOptions)
  })

  useEffect(() => {
    fetch({})
  }, [])

  const optionsWithTemp = [...categories, ...tempCategories].map((category) => ({
    ...category,
    label: category?.title ?? category?.label,
  }))
  const valueWithTemp = [...value, ...tempCategories].map((category) => ({
    ...category,
    label: category?.title ?? category?.label,
  }))

  const styles = {
    option: (providedStyles, props) => ({
      ...providedStyles,
      color: props.data.__isNew__ && '#324B7D',
      background: props.data.__isNew__ && 'transparent',
      textAlign: props.data.__isNew__ && 'center',
      cursor: props.data.__isNew__ && 'pointer',
      fontWeight: props.data.__isNew__ && '600',
      fontSize: props.data.__isNew__ && '16px',
    }),
    menu: (providedStyles) => ({
      ...providedStyles,
      borderRadius: '20px',
    }),
    menuList: (providedStyles) => ({
      ...providedStyles,
      borderRadius: '20px',
      padding: '0px',
    }),
  }

  return (
    <Select
      isCreatable={isCreatable}
      isClearable
      isMulti
      options={optionsWithTemp}
      value={valueWithTemp}
      onChange={handleChange}
      onInputChange={handleInputChange}
      isLoading={isFetchLoading}
      getOptionLabel={getOptionLabel}
      styles={styles}
      {...rest}
    />
  )
}

export default memo(CreatableCategorySelect)
