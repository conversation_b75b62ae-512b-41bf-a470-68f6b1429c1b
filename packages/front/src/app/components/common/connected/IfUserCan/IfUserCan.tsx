import React from 'react'
import memoize from 'moize'
import intersection from 'lodash/intersection'
import parsePermissions from '../../../../utils/parsePermissions'

/**
 *
 * @param existingPermissions
 * @param permissionsToCheck
 * @param any
 *
 * @param isPermissionsLoading
 * @param existingPermissions
 * @param permission
 * @param permissions
 * @param any
 * @param Loading
 * @param Unauthorized
 * @param children
 * @param props
 * @returns {null}
 * @returns {boolean}
 * */

export const hasPermission = memoize((existingPermissions = [], permissionsToCheck = [], any = false) => {
  existingPermissions = parsePermissions(existingPermissions)
  permissionsToCheck = parsePermissions(permissionsToCheck)

  const has = intersection(existingPermissions, permissionsToCheck)

  return any ? has.length > 0 : has.length === permissionsToCheck.length
})

export default ({
  isPermissionsLoading,
  existingPermissions,
  permission,
  permissions,
  any,
  Loading,
  renderLoading,
  Unauthorized,
  renderUnauthorized,
  children,
  render,
  ...props
}) => {
  const per = permission || permissions
  const permissionsToCheck = Array.isArray(per) ? per : [per]

  if (isPermissionsLoading) {
    if (renderLoading) return renderLoading(props)
    if (Loading) return <Loading {...props} />
    return null
  }

  if (!hasPermission(existingPermissions, permissionsToCheck, any)) {
    if (renderUnauthorized) return renderUnauthorized(props)
    if (Unauthorized) return <Unauthorized {...props} />
    return null
  }

  return render ? render(props) : children
}
