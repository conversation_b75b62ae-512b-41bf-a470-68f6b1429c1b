import React, { memo } from 'react'
import { useTranslation } from 'react-i18next'
import EventsSelect from './EventsSelect'
import { events } from './index'

function EventsSelectContainer(props) {
  const { t } = useTranslation(['common'])
  return (
    <EventsSelect
      {...{
        ...props,
        events: events.map((event) => ({ ...event, label: t(event.label) })),
        placeholder: t('common:SELECT_LOADING_PLACEHOLDER'),
      }}
    />
  )
}

export default memo(EventsSelectContainer)
