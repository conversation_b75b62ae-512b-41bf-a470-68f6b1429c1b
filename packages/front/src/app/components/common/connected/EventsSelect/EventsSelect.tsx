import React from 'react'
import Select from 'react-select'
import { selectStyles } from '../../unconnected/Select'

export default ({ events, ...rest }) => (
  <Select
    options={events}
    styles={{
      ...selectStyles,
      option: (styles, { data }) => ({
        ...styles,
        ...(data.status && {
          ':before': {
            borderRadius: 10,
            content: '" "',
            display: 'block',
            marginRight: 8,
            height: 10,
            width: 10,
          },
          alignItems: 'center',
          display: 'flex',
        }),
      }),
    }}
    {...rest}
    isMulti
  />
)
