import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import RolesSelect from './RolesSelect'
import rolesApi from '../../../../resources/role/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'

const useFetchManyRoles = (query = {}) => useFetchManyRequestLocal(rolesApi.fetchMany, query)

function RoleSelectContainer(props) {
  const [response, exec, cancel] = useFetchManyRoles()

  const { data: roles, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ stateId, search, extraQuery }) => {
    const query = assignDeep(
      {
        ...(search && {
          where: {
            displayName: { $iLike: `%${search}%` },
          },
        }),
      },
      extraQuery,
    )

    return exec(query)
  })

  return (
    <RolesSelect
      {...{
        ...props,
        roles,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(RoleSelectContainer)
