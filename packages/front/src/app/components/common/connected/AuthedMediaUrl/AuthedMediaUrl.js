import React, { memo } from 'react'
import PropTypes from 'prop-types'
import axios from 'axios'
import memoize from 'moize'
import config from '../../../../../../config/index'
import { addTokenToAxiosConfig } from '../../../../utils/apiResourceAuthWrapper'
import { isEqual } from '../../../../utils/logic'

const requiresFetch = memoize((url) => url && url.includes(config('apiUrl')))

const fetchMedia = memoize.promise((token, url) =>
  axios
    .get(url, addTokenToAxiosConfig({ responseType: 'blob' }, token))
    .then((res) => window.URL.createObjectURL(res.data)),
)

class AuthedMediaUrl extends React.PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      publicUrl: props.downloadLink ? `${props.url}?access_token=${props.accessToken}` : props.defaultPublicUrl || null,
    }
  }

  componentDidMount() {
    this.componentIsMounted = true
    if (!this.props.downloadLink) this.fetchMedia(this.props.url)
  }

  componentWillUnmount() {
    this.componentIsMounted = false
  }

  UNSAFE_componentWillReceiveProps(newProps) {
    if (!newProps.downloadLink && newProps.url !== this.props.url) {
      this.fetchMedia(newProps.url)
    }
  }

  fetchMedia(url) {
    const { accessToken, defaultPublicUrl = null } = this.props

    if (!url) {
      this.setState({ publicUrl: defaultPublicUrl })
      return
    }

    if (!requiresFetch(url)) {
      this.setState({ publicUrl: url })
      return
    }

    fetchMedia(accessToken, url).then((publicUrl) => {
      if (!this.componentIsMounted) return
      this.setState({ publicUrl })
    })
  }

  render() {
    const { publicUrl } = this.state
    const { render, renderLoading = () => null } = this.props

    if (!publicUrl) return renderLoading({ ...this.props })

    return render({ ...this.props, url: publicUrl })
  }
}

AuthedMediaUrl.propTypes = {
  url: PropTypes.string,
  render: PropTypes.func,
  renderLoading: PropTypes.func,
}

export default memo(AuthedMediaUrl, (prevProps, nextProps) => isEqual(prevProps, nextProps, ['url']))
