import React, { memo } from 'react'
import assignDeep from 'lodash/merge'
import TimetablesSelect from './TimetablesSelect'
import TimetablesApi from '../../../../resources/timetable/api'
import useEventCallback from '../../../../hooks/useEventCallback'
import { useFetchManyRequestLocal } from '../../../../hooks/useFetchManyRequest'

const useFetchManyTimetables = (query = {}) => useFetchManyRequestLocal(TimetablesApi.fetchMany, query)

function TimetablesSelectContainer(props) {
  const [response, exec, cancel] = useFetchManyTimetables()

  const { data: timetables, isLoading, error, pagination } = response

  const fetch = useEventCallback(({ stateId, search, extraQuery }) => {
    const query = assignDeep(
      {
        ...(search && {
          where: {
            name: { $iLike: `%${search}%` },
          },
        }),
      },
      extraQuery,
    )

    return exec(query)
  })

  return (
    <TimetablesSelect
      {...{
        ...props,
        timetables,
        isLoading,
        error,
        fetch,
        pagination,
      }}
    />
  )
}

export default memo(TimetablesSelectContainer)
