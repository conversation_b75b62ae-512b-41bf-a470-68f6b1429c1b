import React, { memo, useCallback } from 'react'
import cns from 'classnames'
import defaultAvatarUrl from './default_avatar.png'
import defaultGroupUrl from './group_avatar.png'
import Lightbox from '../../../App/Dashboard/Chat/ChatBox/InnerChatBox/Message/Lightbox'
import useToggle from '../../../../hooks/useToggle'
import isEqual from '../../../../utils/logic/isEqual'

function Avatar(props) {
  const { contact, url, thumbAvatar, style, className, isSideBar, onImageClick, lightbox, exportPdf, ...rest } = props
  const avatarUrl = contact?.isGroup ? defaultGroupUrl : defaultAvatarUrl
  const { isOpen, close, open } = useToggle()

  const handleClick = useCallback(() => {
    if (lightbox) open()
  }, [lightbox, open])

  return (
    <>
      {exportPdf && (
        <div
          style={{
            ...style,
            objectFit: 'cover',
          }}
        >
          &nbsp;
        </div>
      )}
      {!exportPdf && (
        <img
          src={contact?.avatar?.url || avatarUrl}
          alt="Avatar"
          rel="preload"
          className={cns(['img-fluid', 'rounded-circle'], className)}
          style={{
            ...style,
            objectFit: 'cover',
          }}
          onClick={handleClick}
          {...rest}
        />
      )}
      <Lightbox isOpen={isOpen} mainSrc={contact?.avatar?.url || avatarUrl} onCloseRequest={close} />
    </>
  )
}

const propsAreEqual = (props, next) =>
  isEqual(props.contact, next.contact, ['avatarUrl']) &&
  isEqual((props.contact || {}).thumbAvatar, (next.contact || {}).thumbAvatar, ['url']) &&
  isEqual(props.url, next.url) &&
  isEqual(props.style, next.style) &&
  isEqual(props.lightbox, next.lightbox) &&
  isEqual(props.onImageClick, next.onImageClick) &&
  isEqual(props.className, next.className)

export default memo(Avatar, propsAreEqual)
