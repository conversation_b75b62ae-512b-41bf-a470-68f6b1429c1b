import React, { memo, useCallback, useEffect, useMemo, useState } from 'react'
import debounce from 'lodash/debounce'
import Select from '../unconnected/Select'
import { useCreateTag, useFetchManyTags } from '../../../resources/tag/requests'
import useEventCallback from '../../../hooks/useEventCallback'

const getOptionLabel = (o) => o.label

function CreatableTagSelect({ onChange, value, ...rest }) {
  const [, create] = useCreateTag()
  const [{ isLoading: isFetchLoading, data: tags }, fetch] = useFetchManyTags()

  const fetchTag = useCallback(debounce(fetch, 300), [fetch])
  const [tempTags, setTempTags] = useState([])

  const handleInputChange = useEventCallback((search = '') => {
    fetchTag({ where: { label: { $iLike: `%${search}%` } } })
  })

  const handleChange = useEventCallback((selectedTags) => {
    const parsedTags = (selectedTags || []).map((t) => ({
      ...t,
      id: t.id || t.value,
    }))

    const newOptions = parsedTags.filter((tag) => tag.__isNew__)
    const oldOptions = parsedTags.filter((tag) => !tag.__isNew__)

    if (newOptions.length) {
      Promise.all(newOptions.map((tag) => create({ label: tag.label }))).then((newTags) => {
        setTempTags((prevTempTags) => prevTempTags.filter((op) => newTags.includes((t) => t.label !== op.label)))

        const newValue = [...value, ...newTags]

        onChange(newValue)
      })
    }

    setTempTags(newOptions)
    onChange(oldOptions)
  })

  useEffect(() => {
    fetch({})
  }, [])

  const optionsWithTemp = useMemo(() => [...tags, ...tempTags], [tags, tempTags])
  const valueWithTemp = useMemo(() => [...value, ...tempTags], [value, tempTags])

  return (
    <Select
      isCreatable
      isClearable
      isMulti
      options={optionsWithTemp}
      value={valueWithTemp}
      onChange={handleChange}
      onInputChange={handleInputChange}
      isLoading={isFetchLoading}
      getOptionLabel={getOptionLabel}
      {...rest}
    />
  )
}

export default memo(CreatableTagSelect)
