import React from 'react'
import GenericResourceSelect from '../../unconnected/GenericResourceSelect'
import { useFetchManyOrganizations } from '../../../../resources/organization/requests'

function OrganizationsSelect(props) {
  const [{ data: models = [], pagination, isLoading }, fetch] = useFetchManyOrganizations()

  return <GenericResourceSelect {...props} options={models} fetch={fetch} isLoading={isLoading} />
}

export default OrganizationsSelect
