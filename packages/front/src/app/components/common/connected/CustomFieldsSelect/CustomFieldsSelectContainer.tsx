// CustomFieldsSelectContainer.tsx
import React, { memo, useCallback } from 'react'
import assignDeep from 'lodash/merge'
import { useFetchManyCustomFields } from '../../../../resources/customFields/requests'
import CustomFieldsSelect from './CustomFieldsSelect'
import { CustomFieldType } from '../../../../constants/customFields'

type Props = {
  types?: CustomFieldType[] | 'all'
} & Omit<React.ComponentProps<typeof CustomFieldsSelect>, 'fetch'>

function CustomFieldsSelectContainer({ types = [CustomFieldType.text, CustomFieldType.type], ...props }: Props) {
  const [{ data: customFields = [], isLoading, pagination, error }, exec] = useFetchManyCustomFields()

  const fetch = useCallback(
    ({ search, extraQuery }) => {
      const where: Record<string, any> = {}
      if (search) where.name = { $iLike: `%${search}%` }
      if (types !== 'all') where.type = types

      const query = assignDeep({ where, order: [['name', 'ASC']] }, extraQuery ? { page: extraQuery.page } : {})

      exec(query)
    },
    [exec, types],
  )

  return (
    <CustomFieldsSelect
      {...props}
      customFields={customFields}
      isLoading={isLoading}
      error={error}
      fetch={fetch}
      pagination={pagination}
    />
  )
}

export default memo(CustomFieldsSelectContainer)
