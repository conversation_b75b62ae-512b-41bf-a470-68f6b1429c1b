import React, { memo, useCallback, useRef } from 'react'
import { Popover, PopoverBody, PopoverHeader } from 'reactstrap'
import Icon from '../Icon'
import useToggle from '../../../../hooks/useToggle'
import { IconHelp } from '../IconDigisac'
import { TextColor } from '../../../App/styles/colors'

function HelpPopover({ title, body, iconName = 'question-circle' }) {
  const buttonRef = useRef()
  const { toggle, isOpen } = useToggle(false)

  return (
    <>
      <span className="text-muted hover-pointer" ref={buttonRef} onClick={toggle}>
        <IconHelp fill={TextColor} width="20" height="20" />
      </span>
      <Popover placement="top" isOpen={isOpen} target={buttonRef} toggle={toggle}>
        <PopoverHeader>{title}</PopoverHeader>
        <PopoverBody>{body}</PopoverBody>
      </Popover>
    </>
  )
}

export default memo(HelpPopover)
