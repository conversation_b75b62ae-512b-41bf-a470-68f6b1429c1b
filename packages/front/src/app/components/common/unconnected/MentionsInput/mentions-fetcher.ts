import { debounce, identity, pickBy } from 'lodash'
import { useCallback, useMemo, useRef } from 'react'
import contactApi from '../../../../resources/contact/api'
const clean = (obj) => pickBy(obj, identity)

export const useMentionsFetcher = (contactId: string, serviceId: string, accountId: string) => {
  const cacheRef = useRef([])
  const fetchingRef = useRef(false)

  const buildQuery = useCallback(
    (name: string) => {
      const trimmedName = name.trim()
      const nameFilter = trimmedName
        ? {
            $or: [
              { name: { $iLike: `%${trimmedName}%` } },
              { alternativeName: { $iLike: `%${trimmedName}%` } },
              { internalName: { $iLike: `%${trimmedName}%` } },
              { data: { number: { $iLike: `%${trimmedName}%` } } },
            ],
          }
        : {}

      return {
        query: JSON.stringify({
          attributes: ['alternativeName', 'data', 'internalName', 'name', 'isMe', 'idFromService'],
          where: clean({ serviceId, accountId, ...nameFilter }),
          include: [
            'avatar',
            {
              model: 'groups',
              attributes: ['data'],
              where: { id: contactId },
              required: true,
              through: { attributes: ['isAdmin', 'isSuperAdmin'] },
            },
          ],
          subQuery: false,
          order: [['name', 'ASC']],
          orderByGroupParticipantsIsAdmin: true,
          perPage: trimmedName ? 100 : 20,
        }),
      }
    },
    [serviceId, accountId, contactId],
  )

  const normalizeContacts = useCallback((list: any[]) => {
    const [withName, withoutName] = list.reduce<[any[], any[]]>(
      (acc, curr) => {
        if (curr?.isMe) return acc
        const id = curr.idFromService
        const display = curr.name || curr.internalName || curr.data?.number

        const base = { ...curr, id, display }
        if (curr.name || curr.internalName) {
          acc[0].push(base)
        } else {
          acc[1].push({ ...base, name: `~${curr.alternativeName || id}`, display })
        }
        return acc
      },
      [[], []],
    )

    return [...withName.sort(), ...withoutName.sort()]
  }, [])

  const fetchContacts = useMemo(
    () =>
      debounce(async (name, callback) => {
        fetchingRef.current = true
        const { data } = await contactApi.fetchMany(buildQuery(name))
        const results = normalizeContacts(data)
        cacheRef.current = results
        fetchingRef.current = false
        callback(results)
      }, 400),
    [buildQuery, normalizeContacts],
  )

  const handleFetch = useCallback(
    (query: string, callback: (items: any[]) => void) => {
      const raw = query.trim()
      if (!raw) return fetchContacts('', callback)
      if (raw.startsWith(' ') || raw.includes('  ')) return callback([])

      const [term] = raw.split(' ')
      const prev = cacheRef.current
      if (prev.length && !fetchingRef.current) {
        const filtered = prev.filter((item) => item.display.toLowerCase().includes(term.toLowerCase()))
        callback(filtered)
      }

      fetchContacts(term, callback)
    },
    [fetchContacts],
  )

  return handleFetch
}
