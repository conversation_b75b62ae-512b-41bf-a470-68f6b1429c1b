.editor-wrapper {
  position: relative;
  width: 100%;
  border-radius: 20px;
  padding: 9.5px 16px;
  background: transparent;
  min-height: 40px;
  display: flex;
  align-items: flex-start;
  box-sizing: border-box;
}

.editor-input {
  max-height: 110px;
  overflow: auto;
  flex: 1;
  width: 100%;
  min-height: 1em;
  outline: none;
  border: none;
  background: transparent;
  font-size: 14px;
  user-select: text;
  white-space: pre-wrap;
  word-break: break-word;
}

.editor-input p {
  margin: 0;
}

.editor-placeholder {
  position: absolute;
  top: 50%;
  left: 16px;
  transform: translateY(-50%);
  color: #6b7280;
  font-size: 14px;
  pointer-events: none;
  user-select: none;
}

.mentions-typeahead {
  position: absolute;
  bottom: 50px;
  width: 282px;
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid #cad0d5;
  padding-block: 8px;
  backdrop-filter: blur(100px);
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.14);
}

.mentions-typeahead ul {
  max-height: 320px;
  overflow-y: auto;
  margin: 0;
  padding: 0;
  list-style: none;
}

.mentions-typeahead .item {
  display: flex;
  align-items: center;
  padding-inline: 12px;
  height: 56px;
  cursor: pointer;
  font-size: 14px;
}

.mentions-typeahead .item.selected,
.mentions-typeahead .item[aria-selected='true'] {
  background-color: #eaf4fe;
}

.mentions-typeahead .item .avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 0.5rem;
}
