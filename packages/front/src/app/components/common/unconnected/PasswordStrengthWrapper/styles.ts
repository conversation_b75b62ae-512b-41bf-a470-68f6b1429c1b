import styled from 'styled-components'

export const ListItem = styled.div<{ isValid: boolean }>`
  .list-group-item {
    background-color: #fff;
    color: ${(props) => (props.isValid ? '#009542' : '#dc3545')};
    border-width: 0px;
    padding: 0px;
    font-size: 12px;
  }
`

export const Progress = styled.div<{ score }>`
  .progress {
    background-color: #d6d7d7;
    margin-top: 10px;
    height: 1.2rem;
    border-radius: 32px;
  }
  .progress-bar {
    color: white;
    background-color: ${(props) => props.score.bgColor};
    text-align: left;
    font-family: Inter, sans-serif;
    font-weight: 600;
    padding: 10px;
    min-width: 80px;
  }
`
export const Frame = styled.div`
  font-family: Inter, sans-serif;
  font-size: 12px;
  font-weight: 400;
  border-radius: 0 0 25px 25px;
  padding: 5px 10px;
  //border: 1px solid #d6d7d7;
  //margin: 0 10px 0 10px;
`
