import React, { useState, useEffect, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { ListGroup, ListGroupItem, Progress } from 'reactstrap'
import InputGroup from '../InputGroup'
import {
  has<PERSON>igitCharacter,
  hasUpperCaseCharacter,
  hasLowerCaseCharacter,
  hasSpecialCharacter,
  acceptBlank,
  hasLengthGreaterThanOrEqual,
  sameAs,
  containKnownPatternsInZxcvbnResult,
} from '../../../../utils/validator/validators'
import Icon from '../Icon'
import * as S from './styles'
import zxcvbnHelper from '../../../../utils/zxcvbnHelper'

const PasswordStrengthWrapper = (props) => {
  const [zxcvbnResult, setZxcvbnResult] = useState({ score: 0, pattern: '' })
  const [showFrame, setShowFrame] = useState(false)
  const { model, validation, allowNull = false } = props
  const { t } = useTranslation(['passwordStrength'])
  const scores = [
    { bgColor: '#FF1D33', name: t('PASSWORD_SCORE_0'), score: 0 },
    { bgColor: '#FF6A00', name: t('PASSWORD_SCORE_1'), score: 1 },
    { bgColor: '#F4C50A', name: t('PASSWORD_SCORE_2'), score: 2 },
    { bgColor: '#009542', name: t('PASSWORD_SCORE_3'), score: 3 },
    { bgColor: '#00137F', name: t('PASSWORD_SCORE_4'), score: 4 },
  ]

  const validatePasswordStrength = () => {
    const { password = '' } = model
    return [
      [hasLengthGreaterThanOrEqual(8)({ value: password }), t('AT_LEAST_LENGTH', { minLength: 8 })],
      [hasUpperCaseCharacter()({ value: password }), t('AT_LEAST_UPPERCASE', { minLength: 1 })],
      [hasLowerCaseCharacter()({ value: password }), t('AT_LEAST_LOWERCASE', { minLength: 1 })],
      [hasDigitCharacter()({ value: password }), t('AT_LEAST_DIGITS', { minLength: 1 })],
      [hasSpecialCharacter()({ value: password }), t('AT_LEAST_SPECIAL', { minLength: 1 })],
      [
        !containKnownPatternsInZxcvbnResult(zxcvbnHelper.getPasswordPatterns(), zxcvbnResult)({ value: password }),
        t('NOT_CONTAIN_KNOWN_PATTERNS'),
      ],
    ]
  }

  const getScorePasswordDebounce = useCallback(zxcvbnHelper.getPasswordStrengthDebounced, [])

  const rules = {
    password: [
      [acceptBlank(hasLengthGreaterThanOrEqual(8)), t('INSUFFICIENT_PASSWORD_COMPLEXITY')],
      [hasUpperCaseCharacter(), t('INSUFFICIENT_PASSWORD_COMPLEXITY')],
      [hasLowerCaseCharacter(), t('INSUFFICIENT_PASSWORD_COMPLEXITY')],
      [hasDigitCharacter(), t('INSUFFICIENT_PASSWORD_COMPLEXITY')],
      [hasSpecialCharacter(), t('INSUFFICIENT_PASSWORD_COMPLEXITY')],
      [
        ({ value }) => !containKnownPatternsInZxcvbnResult(zxcvbnHelper.getPasswordPatterns())({ value }),
        t('INSUFFICIENT_PASSWORD_COMPLEXITY'),
      ],
    ],
    passwordConfirmation: [[sameAs('password'), t('common:PASSWORDS_ARE_NOT_EQUAL')]],
  }

  useEffect(() => {
    if (model.password || !allowNull) {
      validation.addRules(rules)
    }

    return () => {
      validation.removeRules(rules)
    }
  }, [model.password, model.passwordConfirmation])

  useEffect(() => {
    const { password = '' } = model
    getScorePasswordDebounce(password, null, setZxcvbnResult)
  }, [model?.password])

  const StrengthScore = () => {
    if (!showFrame) return <></>
    return [scores.find((sc) => sc.score === (zxcvbnResult || {}).score)].map((m, i) => (
      <S.Progress key={i} score={m} data-testid={'progress-strength-score'}>
        <Progress max={5} value={m.score + 1}>
          {m.name}
        </Progress>
      </S.Progress>
    ))
  }

  const RequirementList = () => {
    if (!showFrame) return <></>

    return (
      <S.Frame>
        <ListGroup data-testid={'list-group-requirement'}>
          {t('THE_NEW_PASSWORD_MUST')}
          {validatePasswordStrength().map(([isValid, message], idx) => {
            return (
              <div key={idx} style={{ display: 'flex' }}>
                {isValid ? (
                  <Icon name="check" color="#009542" fixedWidth className="mr-2" />
                ) : (
                  <Icon name="times" color="#dc3545" fixedWidth className="mr-2" />
                )}
                <S.ListItem isValid={isValid}>
                  <ListGroupItem key={`rlgri-${idx}`}>{message}</ListGroupItem>
                </S.ListItem>
              </div>
            )
          })}
        </ListGroup>
      </S.Frame>
    )
  }

  const handleBlur = useCallback((e) => {
    if (!e.target.value) setShowFrame(false)
  }, [])

  return (
    <InputGroup
      {...props}
      onFocus={() => setShowFrame(true)}
      onBlur={handleBlur}
      after={
        <>
          <StrengthScore />
          <RequirementList />
        </>
      }
    />
  )
}

export default PasswordStrengthWrapper
