import React from 'react'
import { UncontrolledTooltip } from 'reactstrap'
import Icon from './Icon'

class BroadcastBadge extends React.Component {
  render() {
    return (
      <>
        <span
          ref={(ref) => {
            this.iconRef = ref
          }}
        >
          <Icon name="bullhorn" fixedWidth className="mr-2 text-secondary" />
        </span>

        {this.iconRef && (
          <UncontrolledTooltip placement="right" target={this.iconRef}>
            Lista de Transmissão
          </UncontrolledTooltip>
        )}
      </>
    )
  }
}

export default BroadcastBadge
