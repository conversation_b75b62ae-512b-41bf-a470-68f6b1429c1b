import React, { useState, useCallback, useRef, useLayoutEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useDispatch, useSelector } from 'react-redux'
import { IconArrowDown } from '../IconDigisac/index'
import { actions as actionsUser, selectors as selectorsUser } from '../../../../modules/auth'
import * as S from './styles'
import flagbr from '../../../../assets/logos/flags/br.svg'
import flagen from '../../../../assets/logos/flags/en.svg'
import flages from '../../../../assets/logos/flags/es.svg'

const SwitchLanguage = ({ renderName }) => {
  const { i18n, t } = useTranslation('navbar')
  const [dropdownShow, setDropdownShow] = useState(false)
  const menu = useRef()

  const dispatch = useDispatch()
  const user = useSelector(selectorsUser.getUser)

  const languages = [
    {
      name: t('LABEL_NAVBAR_LANGUAGE_PORTUGUESE'),
      flag: flagbr,
      language: 'pt-BR',
      testId: 'menu-button-language_BR',
    },
    {
      name: t('LABEL_NAVBAR_LANGUAGE_ENGLISH'),
      flag: flagen,
      language: 'en-US',
      testId: 'menu-button-language_US',
    },
    {
      name: t('LABEL_NAVBAR_LANGUAGE_SPANISH'),
      flag: flages,
      language: 'es',
      testId: 'menu-button-language_ES',
    },
  ]

  const handleRenderLanguageSelected = useCallback(
    (type) => {
      const languageSelected = languages.find((lng) => lng.language === i18n.language)

      switch (type) {
        case 'flag':
          return <S.IconLanguage iconItem flag={languageSelected?.flag} />
        case 'name':
          return languageSelected?.name
        default:
          return null
      }
    },
    [i18n],
  )

  const filteredLanguage = languages.filter((lng) => lng.language !== i18n.language)

  const handleLanguageChange = useCallback(
    (lng) => {
      /* Change Language */
      i18n.changeLanguage(lng.language)
      if (user) {
        dispatch(actionsUser.updateUser({ language: lng.language }))
      }

      setDropdownShow(false)
    },
    [i18n, dropdownShow],
  )

  useLayoutEffect(() => {
    if (typeof window !== `undefined`) {
      window.addEventListener('mousedown', handleClickOutside)

      return () => {
        window.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [])

  function handleClickOutside(event) {
    if (menu.current && !menu.current.contains(event.target)) {
      setDropdownShow(false)
    }
  }

  return (
    <S.Dropdown>
      <S.DropdownItem onClick={() => setDropdownShow(!dropdownShow)}>
        {handleRenderLanguageSelected('flag')}
        {renderName && (
          <>
            <span> {handleRenderLanguageSelected('name')}</span>
            <IconArrowDown className="arrow-down" width="15" height="15" fill="#333" />
          </>
        )}
      </S.DropdownItem>

      <S.DropdownList ref={menu} show={dropdownShow}>
        {filteredLanguage.map((item) => (
          <S.DropdownListLanguage
            key={item?.language}
            onClick={() => handleLanguageChange(item)}
            data-testid={item.testId}
          >
            <S.IconLanguage flag={item?.flag} />
            <span>{item?.name}</span>
          </S.DropdownListLanguage>
        ))}
      </S.DropdownList>
    </S.Dropdown>
  )
}

export default SwitchLanguage
