import styled, { css } from 'styled-components'

const menuItem = css`
  display: flex;
  align-items: center;
  overflow: hidden;
  /* padding: 20px 20px 20px 0px; */
  cursor: pointer;
  text-decoration: none !important;
`

const alignCenter = css`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
`

export const Dropdown = styled.div`
  ${alignCenter};
  position: relative;
`

export const DropdownItem = styled.div`
  ${menuItem};
  ${alignCenter};
  span {
    margin-left: 7px;
    &:hover {
      text-decoration: underline;
      cursor: pointer;
    }
  }
  svg {
    margin-left: 5px;
  }
`

export const IconLanguage = styled.div`
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #fff;
  position: relative;
  background: url(${(props) => props.flag && props.flag});
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;

  svg {
    position: absolute;
    object-fit: cover;
    left: ${(props) => props.iconItem && 0};
    top: ${(props) => props.iconItem && 0};
    height: 100%;
    width: 100%;
    border-radius: 50%;
  }
`
export const DropdownList = styled.div`
  display: ${(props) => (props.show ? 'block' : 'none')};
  transition: 0.2s ease-in-out;
  cursor: pointer;
  position: absolute;
  background: white;
  border-radius: 16px;
  border: 1px solid #52658c4d;
  left: 50%;
  transform: translateX(-50%);
  bottom: -80px;
  padding: 5px 0px;

  z-index: 999;

  &::before {
    content: '';
    width: 13px;
    height: 13px;
    transform: rotate(45deg) translateX(-50%);
    background: black;
    position: absolute;
    top: -2px;
    left: 50%;
    border-top-left-radius: 3px;
    background: white;
    border-left: 1px solid #52658c4d;
    border-top: 1px solid #52658c4d;
  }

  div:nth-child(1) {
    margin-top: 0px;
  }
`

export const DropdownListLanguage = styled.div`
  display: flex;
  padding: 6px 15px;
  cursor: pointer;
  transition: 0.2s ease-in-out;
  &:hover {
    opacity: 0.7;
  }
  span {
    margin-left: 10px;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    display: flex;
    align-items: center;
    color: #333;
  }
`
