import React, { useCallback, memo, useState } from 'react'
import { FormGroup, FormFeedback, Popover, PopoverBody } from 'reactstrap'
import { Input, InputProps } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import * as S from './styles'
import * as SL from '../../../App/Login/styles'
import { TextColor } from '../../../App/styles/colors'
import { IconHidePassword, IconShowPassword, IconInfo } from '../IconDigisac/index'

import Icon from '../Icon'

const styles = {
  formFeedback: { display: 'block !important' },
}

export const InputGroupWrapper = memo((props) => {
  const {
    validation,
    label,
    textColor,
    id,
    children,
    render,
    description,
    required = false,
    textPopover,
    isPassword = false,
    newPopover = false,
    classNameInputGroupWrapper = '',
    ...rest
  } = props
  const errors = validation && validation.errors[id]
  const childProps = {
    ...rest,
    id,
    label,
    validation,
  }

  const [openPopover, setOpenPopover] = useState(false)

  const togglePopover = () => {
    setOpenPopover(!openPopover)
  }

  return (
    <FormGroup className={classNameInputGroupWrapper} color={errors ? 'danger' : null}>
      {(!!label || Boolean(description)) && (
        <S.LabelWrapper>
          {!!label && (
            <S.Label htmlFor={id} onClick={togglePopover} style={{ color: textColor }}>
              {label}{' '}
              {required && (
                <strong className="text-danger" title="Campo obrigatório">
                  *
                </strong>
              )}
              {textPopover && !newPopover && (
                <>
                  <Icon
                    id={id}
                    size="lg"
                    name="info-circle"
                    title="informação"
                    color="#324b7d"
                    style={{
                      cursor: 'pointer',
                      marginLeft: 10,
                    }}
                  />

                  <Popover target={id} placement="top" isOpen={openPopover}>
                    <PopoverBody>{textPopover}</PopoverBody>
                  </Popover>
                </>
              )}
              {textPopover && newPopover && (
                <div style={{ float: 'right', marginLeft: 10, cursor: 'pointer' }}>
                  <IconInfo fill={TextColor} width="15" height="15" />
                  <Popover target={id} placement="top" isOpen={openPopover}>
                    <PopoverBody>{textPopover}</PopoverBody>
                  </Popover>
                </div>
              )}
            </S.Label>
          )}
          {Boolean(description) && <S.Description>{description}</S.Description>}
        </S.LabelWrapper>
      )}
      {children || render(childProps)}
      {errors && (
        <FormFeedback data-testid="Required-field" style={styles.formFeedback}>
          {errors.messages[0]}
        </FormFeedback>
      )}
    </FormGroup>
  )
})

interface InputGroupProps extends InputProps {
  disabled?: boolean
  id: string
  label: string | React.ReactNode
  placeholder?: string
  required?: boolean
  rows?: number
  type?: string
  after?: string | React.ReactNode
}

const InputGroup = memo((props: InputGroupProps) => {
  const {
    validation,
    bindInput,
    label,
    id,
    title,
    type = 'text',
    noPlaceholder,
    before = null,
    after = null,
    icon = null,
    isPassword = false,
    classNameInputGroup = '',
    ...rest
  } = props
  const errors = validation && validation.errors[id]
  const [showPassword, setShowPassword] = useState(false)

  const Component = type === 'textarea' ? Textarea : Input

  if ('value' in (rest || {})) {
    rest.value = rest.value || ''
  }

  const handleBlur = useCallback(() => {
    if (validation) {
      validation.setTouched(id)
    }
  }, [validation && validation.setTouched, id])

  const Password = () => {
    if (!isPassword) return <></>
    return (
      <>
        <FontAwesomeIcon icon={showPassword ? 'eye-slash' : 'eye'} onClick={() => setShowPassword(!showPassword)} />{' '}
        {props.label}
      </>
    )
  }

  const typePassword = () => (showPassword ? 'text' : 'password')

  return (
    <S.InputGroup className={classNameInputGroup}>
      <InputGroupWrapper {...props}>
        {before}
        {!!icon && (
          <div>
            <S.IconWrapper>{icon}</S.IconWrapper>
          </div>
        )}
        {isPassword ? (
          <>
            <SL.InputGroup>
              <input
                id={id}
                data-testid={`InputGroupWrapper_${props.label}_${props.id}`}
                title={title}
                style={{ paddingLeft: icon && '40px' }}
                type={!isPassword ? type : typePassword()}
                placeholder={!noPlaceholder && label}
                className="form-control"
                onBlur={handleBlur}
                {...(bindInput ? bindInput(id) : {})}
                {...rest}
              />
              <SL.Icon onClick={() => setShowPassword(!showPassword)}>
                <>{showPassword ? <IconHidePassword fill="#52658C" /> : <IconShowPassword fill="#52658C" />}</>
              </SL.Icon>
            </SL.InputGroup>
            {after}
          </>
        ) : (
          <>
            <Component
              type={type}
              id={id}
              title={title}
              data-testid={`InputGroupWrapper_${props.label}_${props.id}`}
              style={{ paddingLeft: icon && '40px' }}
              placeholder={!noPlaceholder && label}
              onBlur={handleBlur}
              valid={errors ? false : undefined}
              {...(bindInput ? bindInput(id) : {})}
              {...rest}
            />
            {after}
          </>
        )}
      </InputGroupWrapper>
    </S.InputGroup>
  )
})

export default InputGroup
