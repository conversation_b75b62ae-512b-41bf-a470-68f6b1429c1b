import React, { useState } from 'react'
import CreatableSelect from 'react-select/creatable'
import { useTranslation } from 'react-i18next'
import uniq from 'lodash/uniq'
import { customStyles } from './GenericResourceSelect'

export default function MultipleInput({ value = [], onChange, ...rest }) {
  const [currentInput, setCurrentInput] = useState('')
  const { t } = useTranslation(['multipleInput'])

  const handleSetValueOnTabAndOnBlurEvents = (e) => {
    e.preventDefault()

    const internalValue = uniq([...(value || []), currentInput])
    setCurrentInput('')

    !e.target.value && !value.length ? onChange([]) : onChange(internalValue)

    if (!e.target.value && !value.length) {
      return onChange([])
    }

    if (!e.target.value && value.length > 0) {
      return onChange(value)
    }

    onChange(internalValue)
  }

  const handleKeyDown = (e) => {
    if (!currentInput) return

    switch (e.key) {
      case 'Enter':
      case ' ':
      case 'Tab':
        handleSetValueOnTabAndOnBlurEvents(e)
    }
  }

  const handleChange = (newValue) => (!newValue ? onChange([]) : onChange(newValue.map((val) => val.value)))

  const handleBlur = (e) => handleSetValueOnTabAndOnBlurEvents(e)

  return (
    <CreatableSelect
      components={{
        DropdownIndicator: null,
      }}
      inputValue={currentInput}
      isClearable
      isMulti
      menuIsOpen={false}
      onBlur={handleBlur}
      onChange={handleChange}
      onInputChange={(v) => {
        setCurrentInput(v)
      }}
      onKeyDown={handleKeyDown}
      value={(value || []).map((val) => ({
        label: val,
        value: val,
      }))}
      placeholder={t('PLACEHOLDER')}
      styles={{
        ...customStyles,
        valueContainer: (providedStyles) => ({
          ...providedStyles,
          color: '#24272D',
          fontSize: 14,
          gap: 4,
          padding: '4px 0',
        }),
      }}
      {...rest}
    />
  )
}
