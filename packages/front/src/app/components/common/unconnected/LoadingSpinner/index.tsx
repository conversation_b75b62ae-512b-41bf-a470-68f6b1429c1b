import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import Icon from '../Icon'

interface LoadingSpinnerProps {
  isLoading: boolean
  latencyThreshold?: number
  position?: 'absolute' | 'fixed'
}

const LoadingSpinner = ({ isLoading, latencyThreshold = 300, position = 'absolute' }: LoadingSpinnerProps) => {
  const [isShowing, setIsShowing] = useState(false)
  const { t } = useTranslation('common')
  let timeout = null

  useEffect(() => {
    if (!isLoading) {
      if (timeout) clearTimeout(timeout)
      setIsShowing(false)
      return
    }

    if (!timeout) {
      timeout = setTimeout(() => setIsShowing(true), latencyThreshold)
    }

    return () => {
      if (timeout) clearTimeout(timeout)
    }
  }, [isLoading, latencyThreshold])

  if (!isShowing) return null

  return (
    <div
      style={{
        background: '#FFF',
        opacity: 0.9,
        zIndex: 999995,
        position,
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        width: '100%',
        height: '100%',
      }}
      className="text-center"
    >
      <div
        data-testid="loading-spinner"
        style={{
          position: 'absolute',
          margin: 'auto',
          height: '2em',
          opacity: 1,
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
        }}
      >
        <Icon name="circle-notch" spin fixedWidth size="2x" className="text-primary mb-1" />
        <br />
        <h5>{t('TABLE_LOADING')}</h5>
      </div>
    </div>
  )
}

export default React.memo(LoadingSpinner)
