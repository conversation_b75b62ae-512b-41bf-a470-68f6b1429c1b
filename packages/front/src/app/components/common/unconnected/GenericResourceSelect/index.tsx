import React, { useCallback, useEffect, useState } from 'react'
import debounce from 'lodash/debounce'
import deepEquals from 'fast-deep-equal'
import Select from '../Select'
import usePrevious from '../../../../hooks/usePrevious'

export const customStyles = {
  control: (providedStyles, stateEvent) => ({
    ...providedStyles,
    margin: 0,
    padding: '0 8px 0 16px !important',
    borderRadius: 20,
    minHeight: 40,
    border: '1px solid #b4bbc5 !important',
    boxShadow: 'none',
    ':hover': {
      borderColor: '#b4bbc5 !important',
    },
    backgroundColor: stateEvent.isDisabled ? '#EDEEF1' : 'white',
  }),
  placeholder: (providedStyles) => ({
    ...providedStyles,
    color: '#6e7a89',
    fontSize: 14,
  }),
  valueContainer: (providedStyles, stateEvent) => ({
    ...providedStyles,
    color: '#24272D',
    fontSize: 14,
    padding: stateEvent.isMulti ? '4px 0px' : '0px',
    gap: 4,
  }),
  singleValue: (providedStyles) => ({
    ...providedStyles,
    color: '#24272D',
    fontSize: 14,
  }),
  input: (providedStyles) => ({
    ...providedStyles,
    color: '#24272D',
    fontSize: 14,
  }),
  option: (providedStyles) => ({
    ...providedStyles,
    display: 'flex !important',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 8,
    minHeight: 36,
  }),
  menu: (providedStyles) => ({
    ...providedStyles,
    borderRadius: 16,
    overflow: 'hidden',
  }),
  menuList: (providedStyles) => ({
    ...providedStyles,
    borderRadius: 16,
    padding: '0px',
  }),
  multiValueLabel: (providedStyles) => ({
    ...providedStyles,
    padding: 0,
    paddingRight: 4,
    paddingLeft: 4,
    color: '#4c5461 !important',
  }),
  multiValueRemove: () => ({
    color: '#586171 !important',
    borderRadius: '50%',
    padding: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    '&>svg>circle': {
      color: '#586171 !important',
    },
    ':hover': {
      color: 'white !important',
      backgroundColor: '#586171 !important',
    },
  }),
  multiValue: (providedStyles) => ({
    ...providedStyles,
    alignItems: 'center',
    display: 'flex',
    height: 24,
    borderRadius: 12,
    backgroundColor: '#f7f8f8 !important',
    color: '#4c5461 !important',
    padding: '0 8px !important',
    margin: 0,
  }),
}

const defaultGetOptionLabel = (o) => o.name + (o.archivedAt ? ' - Arquivado' : '')
const defaultGetOptionValue = (o) => o.id

function GenericResourceSelect2(props) {
  const {
    id,
    options,
    isLoading,
    getOptionLabel = defaultGetOptionLabel,
    getOptionValue = defaultGetOptionValue,
    extraQuery,
    stateId,
    onChange,
    isMulti,
    icon,
    alt,
    pagination,
    styles,
    ...rest
  } = props

  const prevExtraQuery = usePrevious(extraQuery)
  const [state, setState] = useState({ search: '' })
  const { search } = state
  const fetch = useCallback(debounce(props.fetch, 500), [])
  const [numPage, setNumPage] = useState(1)
  const [accumulatorOptions, setAccumulatorOptions] = useState([])

  const lastPage = pagination?.lastPage || 1

  const fetchWithSearch = useCallback(
    (value = '', page = 1) => {
      if (props?.isPaged) setNumPage(page + 1)
      setState({ search: value })
      fetch({
        stateId,
        search: value,
        extraQuery: {
          ...extraQuery,
          page,
        },
      })
    },
    [fetch, stateId, search, extraQuery],
  )

  const handleInputChange = (value) => {
    fetchWithSearch(value)
  }

  const maxValuesAcc = (page) => (page - 2) * 15

  useEffect(() => {
    if (options.length) {
      if (pagination?.currentPage === 1) {
        setAccumulatorOptions(options)
        return
      }
      if (maxValuesAcc(numPage) !== accumulatorOptions.length) return
      setAccumulatorOptions([...accumulatorOptions, ...options])
    }
  }, [options])

  const handlePageChange = (page) => {
    if (page <= lastPage) {
      fetchWithSearch(search, page)
    }
  }

  const handleChange = (value) => {
    value = isMulti && !value ? [] : value
    if (onChange) onChange(value)
    setState({ search: '' })
  }

  useEffect(() => {
    if (extraQuery && !deepEquals(prevExtraQuery, extraQuery)) {
      fetchWithSearch()
    }
  }, [prevExtraQuery, extraQuery, fetchWithSearch])

  useEffect(() => {
    fetchWithSearch()
  }, [])

  const handleClose = () => {
    setNumPage(1)
    handlePageChange(1)
    setAccumulatorOptions([])
  }

  const handleSmallOptions = () => {
    if (isMulti && props.value?.length >= 8) {
      handlePageChange(numPage)
    }
  }

  return (
    <Select
      styles={{
        ...customStyles,
        ...styles,
      }}
      id={id}
      instanceId={id}
      getOptionLabel={getOptionLabel}
      getOptionValue={getOptionValue}
      inputValue={search}
      onInputChange={handleInputChange}
      options={options && accumulatorOptions}
      isLoading={isLoading}
      onChange={handleChange}
      isMulti={isMulti}
      icon={icon}
      {...rest}
      {...(props?.isPaged && {
        onMenuClose: () => handleClose(),
        onMenuScrollToBottom: () => handlePageChange(numPage),
        onMenuOpen: () => handleSmallOptions(),
      })}
      onMenuOp
    />
  )
}

export default GenericResourceSelect2
