import React, { useState, useEffect, DragEvent } from 'react'
import { useTranslation } from 'react-i18next'
import { Plus, Trash2, Menu } from 'lucide-react'
import { Button } from '../ui/button'
import InputGroup from '../InputGroup'
import { Label } from '../ui/label'
import { IconColorGray } from '../../../App/styles/colors'

interface Option {
  id: string
  label: string
}

interface CustomOptionsSectionProps {
  options: Option[]
  onChange: (options: Option[]) => void
  addLabel?: string
  placeholder?: string
  title?: React.ReactNode
}

const CustomOptionsSection: React.FC<CustomOptionsSectionProps> = ({
  options = [],
  onChange,
  addLabel,
  placeholder,
  title,
}) => {
  const { t } = useTranslation(['customFields', 'common'])

  const [localOptions, setLocalOptions] = useState<Option[]>(options)
  const [draggedOptionId, setDraggedOptionId] = useState<string | null>(null)
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)
  const finalTitle = title || <strong>{t('OPTIONS_SECTION_TITLE')}</strong>
  const finalAddLabel = addLabel || t('OPTIONS_SECTION_ADD')
  const finalPlaceholder = placeholder || t('OPTIONS_SECTION_PLACEHOLDER')

  useEffect(() => {
    setLocalOptions(options)
  }, [options])

  const handleAddOption = () => {
    const newOption = { id: `opt_${Date.now()}`, label: '' }
    const updatedOptions = [...localOptions, newOption]
    setLocalOptions(updatedOptions)
    onChange(updatedOptions)
  }

  const handleRemoveOption = (id: string) => {
    const updatedOptions = localOptions.filter((opt) => opt.id !== id)
    setLocalOptions(updatedOptions)
    onChange(updatedOptions)
  }

  const handleLabelChange = (id: string, value: string) => {
    const updatedOptions = localOptions.map((opt) => (opt.id === id ? { ...opt, label: value } : opt))
    setLocalOptions(updatedOptions)
    onChange(updatedOptions)
  }

  const handleDragStart = (e: DragEvent<HTMLDivElement>, id: string) => {
    setDraggedOptionId(id)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: DragEvent<HTMLDivElement>, overIndex: number) => {
    e.preventDefault()
    const target = e.currentTarget as HTMLDivElement
    const rect = target.getBoundingClientRect()
    const mouseY = e.clientY
    const isAfterHalf = mouseY > rect.top + rect.height / 2
    const newIndex = isAfterHalf ? overIndex + 1 : overIndex
    setHoveredIndex(newIndex)
  }

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    if (hoveredIndex === null || draggedOptionId === null) {
      setDraggedOptionId(null)
      setHoveredIndex(null)
      return
    }

    const draggedIndex = localOptions.findIndex((opt) => opt.id === draggedOptionId)
    if (draggedIndex === -1) {
      setDraggedOptionId(null)
      setHoveredIndex(null)
      return
    }

    const updatedOptions = [...localOptions]
    const [moved] = updatedOptions.splice(draggedIndex, 1)

    let dropIndex = hoveredIndex
    // Ajuste se o item removido estava antes da posição de inserção
    // pois a remoção muda os índices subsequentes
    if (draggedIndex < dropIndex) {
      dropIndex -= 1
    }

    if (dropIndex > updatedOptions.length) {
      dropIndex = updatedOptions.length
    }

    updatedOptions.splice(dropIndex, 0, moved)
    setLocalOptions(updatedOptions)
    onChange(updatedOptions)

    setDraggedOptionId(null)
    setHoveredIndex(null)
  }

  return (
    <div className="custom-options-section" style={{ width: '100%' }}>
      <div
        className="options-header"
        style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
      >
        <div style={{ display: 'inline-flex', gap: '2px' }}>
          <Label>{finalTitle}</Label>
          <strong className="text-danger" title={t('REQUIRED_FIELD')}>
            *
          </strong>
        </div>
        <Button variant="ghost" onClick={handleAddOption} size={'sm'} className="flex items-center space-x-2">
          <Plus size={14} style={{ marginRight: '5px', marginBottom: '2px' }} />
          {finalAddLabel}
        </Button>
      </div>

      <div
        className="options-container"
        style={{ marginTop: '8px', width: '100%' }}
        onDragOver={(e) => e.preventDefault()}
        onDrop={handleDrop}
        onDragLeave={() => {
          setHoveredIndex(null)
        }}
      >
        {localOptions.map((option, index) => {
          const showLineAbove = hoveredIndex === index
          const showLineBelow = hoveredIndex === index + 1

          return (
            <div key={option.id} style={{ width: '100%', position: 'relative' }}>
              {showLineAbove && (
                <div
                  style={{
                    height: '1px',
                    background: '#7ab0e0',
                    position: 'relative',
                    width: '200px',
                    top: '-1px',
                    left: 0,
                    right: 0,
                    margin: 'auto',
                  }}
                />
              )}
              <div
                className="option-item"
                draggable
                onDragStart={(e) => handleDragStart(e, option.id)}
                onDragOver={(e) => handleDragOver(e, index)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '0px',
                  padding: '4px',
                  width: '100%',
                  boxSizing: 'border-box',
                  background: '#fff',
                }}
              >
                <div
                  style={{ height: '30px', cursor: 'grab', marginRight: '8px', display: 'flex', alignItems: 'center' }}
                >
                  <Menu size={20} />
                </div>
                <div style={{ height: '40px', flex: 1, display: 'flex', alignItems: 'center' }}>
                  <InputGroup
                    id={`option-${option.id}-label`}
                    value={option.label}
                    onChange={(e) => handleLabelChange(option.id, e.target.value)}
                    placeholder={finalPlaceholder}
                    noPlaceholder={false}
                    label=""
                    classNameInputGroup="w-100"
                    style={{ width: '100%', marginBottom: '0px', marginTop: '14px' }}
                  />
                </div>
                <button
                  type="button"
                  className="remove-option-btn"
                  onClick={() => handleRemoveOption(option.id)}
                  style={{
                    cursor: 'pointer',
                    background: 'none',
                    border: 'none',
                    display: 'flex',
                    alignItems: 'center',
                    marginLeft: '8px',
                  }}
                >
                  <Trash2 color={IconColorGray} size={20} />
                </button>
              </div>
              {showLineBelow && (
                <div
                  style={{
                    height: '1px',
                    background: '#7ab0e0',
                    position: 'relative',
                    width: '200px',
                    top: '-1px',
                    left: 0,
                    right: 0,
                    margin: 'auto',
                  }}
                />
              )}
            </div>
          )
        })}
        {hoveredIndex === localOptions.length && (
          <div
            style={{
              height: '1px',
              background: '#7ab0e0',
              position: 'relative',
              width: '200px',
              top: '-1px',
              left: 0,
              right: 0,
              margin: 'auto',
            }}
          />
        )}
      </div>
    </div>
  )
}

export default CustomOptionsSection
