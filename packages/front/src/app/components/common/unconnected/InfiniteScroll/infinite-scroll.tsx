import React, { useCallback, useEffect, useRef } from 'react'
import Ellip<PERSON><PERSON>pinner from '../EllipsisSpinner'
import { PrimaryColor } from '../../../App/styles/colors'

type InfiniteScrollProps = {
  onEnd?: () => void
  children: React.ReactNode
  hasNextPage?: boolean
  isFetching?: boolean
}

export function InfiniteScroll({ children, onEnd, hasNextPage, isFetching }: InfiniteScrollProps): JSX.Element {
  const lastElementRef = useRef<HTMLElement | null>(null)

  const handleOnEnd = useCallback(() => {
    if (!hasNextPage) return

    onEnd?.()
  }, [hasNextPage])

  useEffect(() => {
    const ref = lastElementRef.current

    const observer = new IntersectionObserver((entries) => {
      const [element] = entries

      if (element?.isIntersecting) handleOnEnd()
    })

    observer.observe(ref)

    return () => {
      observer.unobserve(ref)
      observer.disconnect()
    }
  }, [handleOnEnd])

  return (
    <div data-testid={'infinite-scrolling-wrapper'} style={{ paddingBlockEnd: 60 }}>
      {children}
      <span ref={lastElementRef}>
        <center>{isFetching && <EllipsisSpinner color={PrimaryColor} />}</center>
      </span>
    </div>
  )
}
