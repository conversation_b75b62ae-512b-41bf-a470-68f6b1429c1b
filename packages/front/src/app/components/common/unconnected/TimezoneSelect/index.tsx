import React, { useMemo } from 'react'
import Select from 'react-select'
import timezones from '../../../../data/timezones'
import useEventCallback from '../../../../hooks/useEventCallback'
import * as S from './styles'

const options = timezones.map((t) => ({
  label: t.text,
  value: t.abbr,
}))

const timezonesMap = options.reduce((obj, item) => {
  obj[item.value] = item
  return obj
}, {})

const selectStyles = {
  control:
    (styles) =>
    (...styles) => ({
      ...styles,
      backgroundColor: 'transparent',
      border: 'none',
      padding: 0,
      borderColor: 'transparent',
      display: 'flex',
      paddingLeft: '40px',
      minHeight: '40px',
      ':hover': {
        borderColor: 'transparent',
      },
    }),
  valueContainer: (styles) => ({ ...styles, padding: 0 }),
  indicatorSeparator: (styles) => ({ ...styles, display: 'none' }),
  indicatorContainer: (styles) => ({ ...styles, color: '#868FA1' }),
}

function TimezoneSelect({ onChange, value, icon }) {
  const handleChange = useEventCallback((value) => {
    if (onChange) onChange(value.value)
  })

  const parsedValue = useMemo(() => timezonesMap[value] || null, [value])

  return (
    <S.GroupTimezone>
      {!!icon && icon}
      <Select styles={selectStyles} options={options} value={parsedValue} onChange={handleChange} />
    </S.GroupTimezone>
  )
}

export default TimezoneSelect
