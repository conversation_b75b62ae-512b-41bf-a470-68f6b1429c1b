import { Component } from 'react'
import get from 'lodash/get'
import { clone, setWith, curry } from 'lodash/fp'

export const setIn = curry((path, value, obj) => setWith(clone, path, value, clone(obj)))

const move = (arr, from, to) => {
  const arrClone = [...arr]
  Array.prototype.splice.call(arrClone, to, 0, Array.prototype.splice.call(arrClone, from, 1)[0])
  return arrClone
}

export default class FieldArray extends Component {
  constructor(props) {
    super(props)

    this.push = this.push.bind(this)
    this.remove = this.remove.bind(this)
    this.set = this.set.bind(this)
    this.clear = this.clear.bind(this)
    this.moveUp = this.moveUp.bind(this)
    this.moveDown = this.moveDown.bind(this)
  }

  push(item) {
    const { model, setModel, path } = this.props

    const attr = get(model, path)

    setModel(setIn(path, [...attr, item], model))
  }

  remove(index) {
    const { model, setModel, path } = this.props

    const attr = get(model, path)

    setModel(setIn(path, [...attr.slice(0, index), ...attr.slice(index + 1)], model))
  }

  moveUp(index) {
    const { model, setModel, path } = this.props

    const attr = get(model, path)

    setModel(setIn(path, move(attr, index, index - 1), model))
  }

  moveDown(index) {
    const { model, setModel, path } = this.props

    const attr = get(model, path)

    setModel(setIn(path, move(attr, index, index + 1), model))
  }

  clear() {
    const { model, setModel, path } = this.props

    const attr = get(model, path)

    setModel(setIn(path, [], model))
  }

  set(index, value) {
    const { model, setModel, path } = this.props

    const attr = get(model, path)

    setModel(setIn(path, [...attr.slice(0, index), value, ...attr.slice(index + 1)], model))
  }

  render() {
    const { model, path } = this.props

    const arrayHelpers = {
      slice: get(model, path),
      push: this.push,
      remove: this.remove,
      clear: this.clear,
      set: this.set,
      moveUp: this.moveUp,
      moveDown: this.moveDown,
      path,
    }

    return (this.props.render || this.props.children)(arrayHelpers)
  }
}
