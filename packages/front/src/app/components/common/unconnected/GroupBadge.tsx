import React from 'react'
import { UncontrolledTooltip } from 'reactstrap'
import Icon from './Icon'

class GroupBadge extends React.Component {
  render() {
    return (
      <>
        <span
          ref={(ref) => {
            this.iconRef = ref
          }}
        >
          <Icon name="users" fixedWidth className="mr-2 text-secondary" />
        </span>

        {this.iconRef && (
          <UncontrolledTooltip placement="right" target={this.iconRef}>
            Grupo
          </UncontrolledTooltip>
        )}
      </>
    )
  }
}

export default GroupBadge
