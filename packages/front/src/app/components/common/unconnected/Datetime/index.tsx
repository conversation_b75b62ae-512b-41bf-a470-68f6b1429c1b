import React, { useCallback, useState, useEffect } from 'react'
import moment from 'moment'
import 'moment/locale/es'
import 'moment/locale/pt-br'
import Datetime from 'react-datetime'
import InputMask from 'react-input-mask'
import { useTranslation } from 'react-i18next'
import { IconClock } from '../IconDigisac'
import { TextColor } from '../../../App/styles/colors'
import * as S from './styles'
import 'react-datetime/css/react-datetime.css'

type Props = {
  onBlur?: () => void
  onChange?: (value: string | Date) => void
  isValidDate?: (currentDate: moment.Moment) => boolean
  value?: string | Date
  mode?: 'datetime' | 'time'
  viewMode?: string
  dateFormat?: string
  timeFormat?: string
  maskFormat?: string
  disabled?: boolean
}

export default (props: Props) => {
  const {
    onChange,
    isValidDate,
    value,
    mode = 'datetime',
    viewMode = mode === 'time' ? 'time' : 'days',
    dateFormat = mode === 'time' ? false : 'DD/MM/YYYY',
    timeFormat = 'HH:mm',
    maskFormat = mode === 'datetime' ? '39/19/9999 29:59' : '29:59',
    disabled = false,
  } = props
  const [innerValue, setInnerValue] = useState()

  const handleChange = useCallback(
    (val) => {
      setInnerValue(val)

      if (onChange) {
        onChange(moment.isMoment(val) ? val.toDate() : null)
      }
    },
    [onChange],
  )

  useEffect(() => {
    if (value === null && moment.isMoment(innerValue)) {
      setInnerValue(value)
    }
  }, [value, innerValue])

  const { i18n } = useTranslation()

  const valueDate = value instanceof Date || !value ? value : new Date(value)

  const finalValue = valueDate || innerValue

  return (
    <Datetime
      isValidDate={isValidDate}
      dateFormat={dateFormat}
      timeFormat={timeFormat}
      locale={i18n.language.toLowerCase()}
      viewMode={viewMode}
      {...props}
      value={finalValue}
      onChange={handleChange}
      renderInput={
        maskFormat
          ? (renderProps) => (
              <S.GroupDateTime>
                <IconClock fill={TextColor} width="25" height="25" />
                <InputMask
                  data-testid="DataTime-input"
                  onBlur={props.onBlur}
                  mask={maskFormat}
                  formatChars={{
                    1: '[0-1]',
                    2: '[0-2]',
                    3: '[0-3]',
                    4: '[0-4]',
                    5: '[0-5]',
                    6: '[0-6]',
                    7: '[0-7]',
                    8: '[0-8]',
                    9: '[0-9]',
                  }}
                  {...renderProps}
                  disabled={disabled}
                >
                  {(inputProps) => <input {...inputProps} disabled={disabled} />}
                </InputMask>
              </S.GroupDateTime>
            )
          : undefined
      }
    />
  )
}
