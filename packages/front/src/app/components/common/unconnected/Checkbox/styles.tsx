import React from 'react'
import styled from 'styled-components'
import { PrimaryColor } from '../../../App/styles/colors'

interface StyledCheckboxProps {
  checked: boolean
}

const CheckboxContainer = styled.div`
  display: flex;
  vertical-align: middle;
`

const Icon = styled.svg`
  fill: none;
  stroke: white;
  stroke-width: 3px;
`

const HiddenCheckbox = styled.input.attrs({ type: 'checkbox' })`
  border: 0;
  clip: rect(0 0 0 0);
  clippath: inset(50%);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  white-space: nowrap;
  width: 1px;
`

const StyledCheckbox = styled.div<StyledCheckboxProps>`
  display: inline-block;
  width: 15px;
  height: 15px;
  background: ${(props) => (props.checked ? PrimaryColor : 'white')};
  border: 2px solid ${(props) => (props.checked ? 'transparent' : PrimaryColor)};
  border-radius: 3px;
  transition: all 150ms;
  cursor: pointer;
  margin-right: 10px;

  /* ${HiddenCheckbox}:focus + & {
    box-shadow: 0 0 0 3px pink;
  } */

  ${Icon} {
    visibility: ${(props) => (props.checked ? 'visible' : 'hidden')};
  }
`

export const Checkbox = ({ className, checked, onChange, ...props }: any) => (
  <CheckboxContainer className={className}>
    <HiddenCheckbox checked={checked} onChange={onChange} {...props} />
    <StyledCheckbox checked={checked}>
      <Icon viewBox="2 5 19 22">
        <polyline points="20 6 9 17 4 12" />
      </Icon>
    </StyledCheckbox>
  </CheckboxContainer>
)
