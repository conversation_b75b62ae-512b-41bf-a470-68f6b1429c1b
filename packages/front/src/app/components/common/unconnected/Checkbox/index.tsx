import React, { ChangeEvent, useEffect } from 'react'
import * as S from './styles'

interface CheckboxProps {
  onChange: () => void
  checked: boolean
  type: 'checkbox'
}

const Checkbox = ({ onChange, checked, type = 'checkbox' }: CheckboxProps) => {
  useEffect(() => {
    console.log(checked)
    console.log(onChange)
  }, [checked, onChange])

  return <S.Checkbox onChange={() => onChange()} checked={checked} type={type} />
}

export default Checkbox
