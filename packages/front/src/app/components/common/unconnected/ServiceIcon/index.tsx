import React, { memo } from 'react'
import Icon from '../Icon'
import {
  WhatsAppColor,
  WhatsAppBusinessColor,
  InstagramColor,
  MessengerColor,
  TelegramColor,
  SmsColor,
  WebChatColor,
  EmailColor,
  WhatsAppRemoteColor,
  GoogleBusinessMessageColor,
  ReclameAquiColor,
} from '../../../App/styles/colors'
import { IconGoogleBusinessMessage } from '../IconDigisac'

type Props = {
  type: string
  whiteIcons?: boolean
}

export const getIconTypeAndColorByType = (type: string): string[] | (string | string[])[] =>
  ({
    whatsapp: [['fab', 'whatsapp'], WhatsAppColor, '17px'],
    'whatsapp-business': [['fab', 'whatsapp'], WhatsAppBusinessColor, '17px'],
    email: ['envelope', EmailColor, '13px'],
    'whatsapp-remote': [['fab', 'whatsapp'], WhatsAppRemoteColor, '17px'],
    telegram: [['fab', 'telegram-plane'], TelegramColor, '15px'],
    'sms-wavy': ['sms', SmsColor, '15px'],
    webchat: ['comments', WebChatColor, '13px'],
    'facebook-messenger': [['fab', 'facebook-messenger'], MessengerColor],
    instagram: [['fab', 'instagram'], InstagramColor],
    'google-business-message': [null, GoogleBusinessMessageColor],
    'reclame-aqui': [['fa', 'bullhorn'], ReclameAquiColor, '13px'],
  })[type]

function ServiceIcon({ type, whiteIcons, ...props }: Props) {
  const [icon, color, font] = getIconTypeAndColorByType(type)

  return (
    <>
      {type === 'google-business-message' ? (
        <IconGoogleBusinessMessage {...props} width="13px" height="13px" fill="#FFFFFF" />
      ) : (
        <Icon {...props} icon={icon} style={{ color: whiteIcons ? '#fff' : color, fontSize: font }} fixedWidth />
      )}
    </>
  )
}

export default memo(ServiceIcon)
