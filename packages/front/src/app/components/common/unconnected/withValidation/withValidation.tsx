/* eslint-disable no-shadow */
import React, { Component } from 'react'
import isEmpty from 'lodash/isEmpty'
import getErrors from '../../../../utils/validator/getErrors'
import getErrorsWithState from '../../../../utils/validator/getErrorsWithState'

const withValidation =
  ({ rules = {}, validateOn = 'touched' } = {}) =>
  (WrappedComponent) =>
    class extends Component {
      constructor(props) {
        super(props)

        this.state = {
          rules,
          errors: {},
          touched: {},
          dirty: {},
          triedSubmitting: false,
          validateOn,
        }

        this.isValid = this.isValid.bind(this)
        this.setDirty = this.setDirty.bind(this)
        this.setTouched = this.setTouched.bind(this)
        this.setRules = this.setRules.bind(this)
        this.getRules = this.getRules.bind(this)
        this.addRules = this.addRules.bind(this)
        this.removeRules = this.removeRules.bind(this)
        this.validateAll = this.validateAll.bind(this)
      }

      UNSAFE_componentWillReceiveProps(newProps) {
        const { model: oldModel } = this.props
        const { model } = newProps

        Object.keys(model).forEach((key) => {
          if (!oldModel[key] && model[key]) {
            this.setDirty(key)
          }
        })
      }

      /**
       * @param {object} rules
       */
      setRules(entries) {
        this.setState(() => ({
          rules: entries,
        }))
      }

      getRules() {
        return this.state.rules
      }

      addRules(entries) {
        this.setState({
          rules: {
            ...this.state.rules,
            ...entries,
          },
        })
      }

      removeRules(entries) {
        const entriesKey = Object.keys(entries)
        const newRules = {}

        for (const [key, value] of Object.entries(this.state.rules)) {
          if (!entriesKey.includes(key)) {
            newRules[key] = value
          }
        }

        this.setState({
          rules: newRules,
        })
      }

      /**
       * @param {string} key
       * @param {string} statusType
       * @param {Function} callback
       */
      setStatus(key, statusType, callback) {
        this.setState(
          (prevState) => ({
            [statusType]: {
              ...prevState[statusType],
              [key]: {
                ...prevState[statusType][key],
                [statusType]: true,
              },
            },
          }),
          () => this.updateErrors(callback),
        )
      }

      /**
       * @param {string} key
       * @param {Function} callback
       */
      setDirty(key, callback) {
        this.setStatus(key, 'dirty', callback)
      }

      /**
       * @param {string} key
       * @param {Function} callback
       */
      setTouched(key, callback) {
        this.setStatus(key, 'touched', callback)
      }

      /**
       * @returns {Promise}
       */
      updateErrors() {
        const { model } = this.props
        const { triedSubmitting, rules, dirty, touched, validateOn } = this.state

        const getErrorFun = triedSubmitting
          ? // display all errors after user tried submitting
            getErrors
          : // display errors only on touched/changed field
            getErrorsWithState(validateOn === 'touched' ? touched : dirty)

        return new Promise((resolve) => {
          getErrorFun(model, rules).then((errors) => {
            this.setState(
              () => ({ errors }),
              () => resolve(errors),
            )
          })
        })
      }

      /**
       * @returns {boolean}
       */
      isValid() {
        const { errors } = this.state

        return isEmpty(errors)
      }

      /**
       * @returns {Promise}
       */
      validateAll() {
        return new Promise((resolve) => {
          this.setState(
            () => ({ triedSubmitting: true }),
            () => this.updateErrors().then(() => resolve(this.isValid())),
          )
        })
      }

      render() {
        const { errors, dirty, touched } = this.state

        const validation = {
          errors,
          dirty,
          touched,
          isValid: this.isValid,
          setDirty: this.setDirty,
          setTouched: this.setTouched,
          setRules: this.setRules,
          getRules: this.getRules,
          addRules: this.addRules,
          removeRules: this.removeRules,
          validateAll: this.validateAll,
        }

        return <WrappedComponent validation={validation} {...this.props} />
      }
    }

export default withValidation
