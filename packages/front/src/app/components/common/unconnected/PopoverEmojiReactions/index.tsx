import React from 'react'
import data, { EmojiMartData, Emoji as EmojiMartType } from '@emoji-mart/data'
import emojiMartI18n from '@emoji-mart/data/i18n/pt.json'
// import 'emoji-mart/css/emoji-mart.css'
import { Picker } from 'emoji-mart'
import * as S from './styles'
import Emoji from '../Emoji'
import { telegramEmojis } from '../../../../../app/components/App/Dashboard/telegramEmojis'

const { emojis } = data as EmojiMartData

type Config = {
  emojisReactions: string[]
  reactionCodeDictionary: { [key: string]: string }
  moreOptionsVisible: boolean
}

type ServicesConfig = {
  whatsapp: Config
  instagram: Config
  'whatsapp-business': Config
  telegram: Config
}

const servicesConfig: ServicesConfig = {
  whatsapp: {
    emojisReactions: ['+1', 'heart', 'joy', 'open_mouth', 'cry', 'pray'],
    reactionCodeDictionary: {},
    moreOptionsVisible: true,
  },
  instagram: {
    emojisReactions: ['heart'],
    reactionCodeDictionary: {
      heart: 'love',
    },
    moreOptionsVisible: false,
  },
  'whatsapp-business': {
    emojisReactions: ['+1', 'heart', 'joy', 'open_mouth', 'cry', 'pray'],
    reactionCodeDictionary: {},
    moreOptionsVisible: true,
  },
  telegram: {
    emojisReactions: ['+1', '-1', 'heart', 'fire', 'clap'],
    reactionCodeDictionary: {},
    moreOptionsVisible: true,
  },
}

interface PopoverEmojiReactionsProps {
  onEmojiClick: (emoji: string, reactionCode?: string) => void
  onRevokeClick?: (revokeReactionMessage: string) => void
  currentReaction?: {
    text: string
    id: string
  }
  isFromMe: boolean
  serviceType: string
}

export function PopoverEmojiReactions({
  onEmojiClick,
  onRevokeClick,
  currentReaction,
  isFromMe,
  serviceType,
}: PopoverEmojiReactionsProps) {
  const config: Config = servicesConfig[serviceType]
  if (!config) {
    return null
  }

  const [isEmojiPickerVisible, setIsEmojiPickerVisible] = React.useState(false)

  const handleEmojiClickDefault = (emoji: EmojiMartType) => {
    if (currentReaction && currentReaction.text === emoji.skins[0].native) {
      onRevokeClick(currentReaction.id)
      return
    }

    onEmojiClick(emoji.skins[0].native, config.reactionCodeDictionary[emoji.id])
  }

  const handleEmojiClickPicker = (emoji: any) => {
    onEmojiClick(emoji.native)
  }

  return (
    <S.Container $isFromMe={isFromMe}>
      {!isEmojiPickerVisible ? (
        <S.EmojiReactionWrapper data-testid="chat-popover-message_reaction">
          {config.emojisReactions.map((emoji) => (
            <S.EmojiReaction
              key={emoji}
              onClick={() => handleEmojiClickDefault(emojis[emoji])}
              data-testid={
                currentReaction && currentReaction.text === emojis[emoji].skins[0].native
                  ? 'chat-button-revoke_default_reaction'
                  : 'chat-button-send_default_reaction'
              }
            >
              {currentReaction && currentReaction.text === emojis[emoji].skins[0].native ? (
                <S.EmojiReactionSelected>{emojis[emoji].skins[0].native}</S.EmojiReactionSelected>
              ) : (
                <div>{emojis[emoji].skins[0].native}</div>
              )}
            </S.EmojiReaction>
          ))}
          {currentReaction &&
          !config.emojisReactions.find((emoji) => currentReaction.text === emojis[emoji].skins[0].native) ? (
            <S.EmojiReaction
              onClick={() => onRevokeClick(currentReaction.id)}
              data-testid="chat-button-revoke_more_reaction"
            >
              <S.EmojiReactionSelected>
                <Emoji>{currentReaction.text}</Emoji>
              </S.EmojiReactionSelected>
            </S.EmojiReaction>
          ) : (
            config.moreOptionsVisible && (
              <S.ButtonOpenEmojiPicker
                onClick={() => setIsEmojiPickerVisible(true)}
                data-testid="chat-button-open_picker_more_reaction"
              >
                +
              </S.ButtonOpenEmojiPicker>
            )
          )}
        </S.EmojiReactionWrapper>
      ) : (
        <S.WrapperPicker $isFromMe={isFromMe} data-testid="chat-popover-picker_more_reaction">
          {serviceType === 'telegram' ? (
            <S.EmojiReactionTelegramWrapper data-testid="chat-popover-message_reaction">
              {telegramEmojis.map((emoji) => (
                <S.EmojiReaction
                  key={emoji}
                  onClick={() => handleEmojiClickDefault(emojis[emoji])}
                  data-testid={
                    currentReaction && currentReaction.text === emojis[emoji].skins[0].native
                      ? 'chat-button-revoke_default_reaction'
                      : 'chat-button-send_default_reaction'
                  }
                >
                  {currentReaction && currentReaction.text === emojis[emoji].skins[0].native ? (
                    <S.EmojiReactionSelected>{emojis[emoji].skins[0].native}</S.EmojiReactionSelected>
                  ) : (
                    <div>{emojis[emoji].skins[0].native}</div>
                  )}
                </S.EmojiReaction>
              ))}
            </S.EmojiReactionTelegramWrapper>
          ) : (
            <Picker
              set="twitter"
              i18n={emojiMartI18n}
              title="Emojis"
              onClick={handleEmojiClickPicker}
              showPreview={false}
            />
          )}
        </S.WrapperPicker>
      )}
    </S.Container>
  )
}
