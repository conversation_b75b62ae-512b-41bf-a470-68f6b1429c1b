import styled from 'styled-components'

interface ContainerProps {
  $isFromMe: boolean
}

export const Container = styled.div<ContainerProps>`
  position: absolute;
  z-index: 999;
  transform: ${(props) => (props.$isFromMe ? 'translate(40%, -100%)' : 'translate(-40%, -100%)')};
`

export const EmojiReactionWrapper = styled.div`
  display: flex;
  justify-content: center;
  border-radius: 48px;
  background-color: white;
  border: 1px solid #e5e7eb;
  padding: 4px 16px;
`

export const EmojiReactionTelegramWrapper = styled.div`
  display: grid;
  justify-content: center;
  border-radius: 48px;
  background-color: white;
  border: 1px solid #e5e7eb;
  padding: 8px 16px;
  grid-template-columns: repeat(5, auto);
  grid-auto-rows: auto;
  gap: 8px;
  max-height: 384px;
  overflow-y: auto;
`

export const EmojiReaction = styled.button`
  font-size: 24px;
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  border: none;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    transform: scale(1.1);
  }
`

export const EmojiReactionSelected = styled.div`
  background-color: #eee;
  border-radius: 999px;
  min-height: 2.5rem /* 40px */;
  min-width: 2.5rem /* 40px */;
  display: flex;
  align-items: center;
  justify-content: center;
`

export const ButtonOpenEmojiPicker = styled.button`
  display: flex;
  min-height: 2.5rem /* 40px */;
  min-width: 2.5rem /* 40px */;
  align-items: center;
  justify-content: center;
  border-radius: 999px;
  transition-property: background-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  border: none;
  background-color: transparent;

  &:hover {
    background-color: #f3f4f6;
  }
`

interface WrapperPickerProps {
  $isFromMe: boolean
}

export const WrapperPicker = styled.div<WrapperPickerProps>`
  position: absolute;
  z-index: 999;
  bottom: -40px;
  transform: translateX(-50%);
`
