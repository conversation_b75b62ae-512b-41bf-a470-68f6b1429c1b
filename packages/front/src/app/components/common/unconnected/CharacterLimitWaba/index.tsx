import React, { memo, useEffect, useState } from 'react'

interface Props {
  message: string
  limit: number
  t: any
}
const CharacterLimit: React.FC<Props> = ({ limit, message, t }) => {
  const getQuantity = (message: number) => {
    const len = message - 1
    return len > 0 ? parseInt(len / limit) + 1 : 1
  }

  const getLeftChar = (message: string) => limit * getQuantity(message.length) - message.length

  useEffect(() => {
    getLeftChar(message)
  }, [getLeftChar])
  return (
    <div
      data-testid="chat-label-limit_message"
      style={{ color: 'gray', float: 'right', margin: '10px 15px 0  5px' }}
      title={`${t('common:LABEL_CARACTERS_WABA')} ${limit}`}
    >
      <label>{`${getLeftChar(message)}`}</label>
    </div>
  )
}

export default memo(CharacterLimit)
