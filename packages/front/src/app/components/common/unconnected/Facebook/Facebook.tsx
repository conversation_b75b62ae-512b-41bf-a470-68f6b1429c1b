import React, { useEffect, useState } from 'react'
import Axios from 'axios'
import LoadingSpinner from '../LoadingSpinner'
import Icon from '../Icon'
import * as S from './styles'

interface User {
  id: string
  name: string
  picture: {
    data: {
      url: string
    }
  }
}

interface Page {
  id: string
  name: string
  category: string
  tasks: string[]
  imageUrl: string
}

interface PropsAuthenticate {
  t(name: string): React.ReactNode
  loadingIframe: boolean
  setLoadingIframe(data): void
}

interface PropsPages {
  t(name: string): React.ReactNode
  previousStep(): void
  handleSelectPage(pageId): void
  handleLogout(): void
  pages: Page[]
  user: User
}

export const Authenticate: React.FC<PropsAuthenticate> = ({ t, loadingIframe, setLoadingIframe }) => (
  <S.ContainerAuthenticate>
    {loadingIframe && (
      <div className="m-auto">
        <Icon name="circle-notch" spin fixedWidth size="2x" className="text-primary mb-1" />
      </div>
    )}

    <iframe
      hidden={loadingIframe}
      onLoad={() => setLoadingIframe(false)}
      id="facebook-sdk-iframe"
      title="facebook-sdk"
      width="100%"
      height="100px"
      src="https://facebook-sdk.ikatec.cloud/"
      frameBorder="0"
      allow="microphone; camera; accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture; web-share"
    />

    <p>{t('FACEBOOK_MODAL_CONTENT_AUTHENTICATE')}</p>

    <p>
      <a href="https://digisac.com.br/politicasdeprivacidade.html" target="blank">
        {t('FACEBOOK_MODAL_PRIVACY_POLICY')}
      </a>{' '}
      e{' '}
      <a href="https://digisac.com.br/termos-de-uso" target="blank">
        {t('FACEBOOK_MODAL_TERMS_DIGISAC')}
      </a>
    </p>
  </S.ContainerAuthenticate>
)

export const Pages: React.FC<PropsPages> = ({ t, handleSelectPage, handleLogout, pages, user }) => (
  <S.ContainerPages>
    <S.UserAutheticated>
      <p> {user?.name} </p>
      <img className="avatar" src={user?.picture.data.url} />
    </S.UserAutheticated>

    <p className="other-account">
      {t('FACEBOOK_MODAL_IS_NOT_USER')} {user?.name}?{' '}
      <span onClick={handleLogout} data-testid="facebook-modal-span-other_account">
        {t('FACEBOOK_MODAL_ENTER_OTHER_ACCOUNT')}
      </span>
    </p>

    <S.PagesContainer>
      <p>{t('FACEBOOK_MODAL_SELECT_PAGE')}</p>

      {!pages && <p> {t('FACEBOOK_MODAL_PAGE_NOT_FOUND')} </p>}

      <S.WrapperPages>
        {pages?.map((item, index) => (
          <S.Page key={index} onClick={() => handleSelectPage(item.id)} data-testid="facebook-modal-page-item_name">
            <p>{item.name}</p>
            <img className="avatar" src={item.imageUrl} />
          </S.Page>
        ))}
      </S.WrapperPages>
    </S.PagesContainer>
  </S.ContainerPages>
)

const formatPhoneNumber = (number = '') => number.replace(/\D/g, '')

const getQualityColor = (status) =>
  ({
    GREEN: '#0f0',
    UNKNOWN: '#6a6a6a',
  })[status] || '#f00'

const getQualityName = (name, t) =>
  ({
    GREEN: t('FACEBOOK_MODAL_QUALITY_NAME_GREEN'),
    UNKNOWN: t('FACEBOOK_MODAL_QUALITY_NAME_UNKNOWN'),
  })[name] ||
  name ||
  '-'

const getStatusColor = (name) =>
  ({
    VERIFIED: '#0f0',
    NOT_VERIFIED: '#ff0',
    UNKNOWN: '#6a6a6a',
  })[name] || '#f00'

const getStatusName = (name, t) =>
  ({
    VERIFIED: t('FACEBOOK_MODAL_STATUS_NAME_VERIFIED'),
    NOT_VERIFIED: t('FACEBOOK_MODAL_STATUS_NAME_NOT_VERIFIED'),
    UNKNOWN: t('FACEBOOK_MODAL_STATUS_NAME_UNKNOWN'),
  })[name] ||
  name ||
  '-'

export const WabaNumbers = (props) => {
  const { t, handleSelectNumber, handleLogout, user, sdkData } = props

  const auth = sdkData?.auth
  const accessToken = auth?.authResponse?.accessToken

  const disconnected = !auth?.authResponse?.accessToken || auth.status !== 'connected'

  const userId = user?.id

  const [numbers, setNumbers] = useState([])
  const [isLoading, setIsLoading] = useState(false)

  const hasWabaAccount = (i) => 'owned_whatsapp_business_accounts' in i

  const getWabaAccounts = async (businessId) =>
    Axios.get(
      `https://graph.facebook.com/${businessId}?fields=owned_whatsapp_business_accounts{name,id,phone_numbers}&access_token=${accessToken}`,
    )

  const getNumbers = async (business) => {
    try {
      const { data: accounts } = await getWabaAccounts(business.id)

      if (!accounts || !hasWabaAccount(accounts)) return

      const ownedWabas = accounts.owned_whatsapp_business_accounts.data

      return ownedWabas.flatMap((ow) =>
        ow.phone_numbers?.data
          ?.flatMap((pn) => ({
            ...pn,
            businessId: ow.id,
            businessName: business?.name,
          }))
          .filter(Boolean),
      )
    } catch (e) {
      // facebook não permite parâmetros nessa rota, nem outras abordagens
      // https://developers.facebook.com/docs/marketing-api/reference/business-user
      return false
    }
  }

  const fetchNumbers = async () => {
    if (disconnected) return
    setIsLoading(true)

    // 1 - Pegar empresas do usuário
    const { data: businessesResponse } = await Axios.get(
      `https://graph.facebook.com/${userId}/businesses?access_token=${accessToken}`,
    )

    // 2 - Pegar contas de whatsapp de cada empresa
    const numbers = (await Promise.all(businessesResponse.data.map(getNumbers))).flat().filter(Boolean)
    setIsLoading(false)
    setNumbers(numbers)
  }

  useEffect(() => {
    if (user) {
      fetchNumbers()
    }
  }, [user])

  return (
    <S.ContainerPages>
      <S.UserAutheticated>
        <p> {user?.name} </p>
        <img className="avatar" src={user?.picture.data.url} />
      </S.UserAutheticated>

      <p className="other-account">
        {t('FACEBOOK_MODAL_IS_NOT_USER')} {user?.name}?{' '}
        <span onClick={handleLogout} data-testid="facebook-modal-span-enter_outher_account">
          {t('FACEBOOK_MODAL_ENTER_OTHER_ACCOUNT')}
        </span>
      </p>

      <S.PagesContainer
        style={{
          width: '90%',
        }}
      >
        <p>{t('FACEBOOK_MODAL_SELECT_NUMBER')}</p>

        <S.WrapperPages>
          <S.Page header>
            <p style={{ color: '#000' }}>{t('FACEBOOK_MODAL_SELECT_NUMBER_HEADER_COMPANY')}</p>
            <p style={{ color: '#000' }}>{t('FACEBOOK_MODAL_SELECT_NUMBER_HEADER_NUMBER')}</p>
            <p style={{ color: '#000' }}>{t('FACEBOOK_MODAL_SELECT_NUMBER_HEADER_STATUS')}</p>
            <p style={{ color: '#000' }}>{t('FACEBOOK_MODAL_SELECT_NUMBER_HEADER_QUALITY')}</p>
          </S.Page>

          <LoadingSpinner isLoading={isLoading} position="fixed" />

          {!numbers.length && !isLoading && <p> {t('FACEBOOK_MODAL_PAGE_NOT_FOUND')} </p>}

          {!!numbers?.length &&
            numbers?.map((number, index) => {
              const numberStatus = number.code_verification_status
              const numberQuality = number.quality_rating
              return (
                <S.Page
                  key={number.id}
                  onClick={() => handleSelectNumber(number.id, number.businessId, number.display_phone_number)}
                  data-testid="facebook-modal-page-select_number"
                >
                  <p>
                    <S.BusinessName>{number.businessName || '-'}</S.BusinessName>
                  </p>
                  <p>{formatPhoneNumber(number.display_phone_number)}</p>
                  <p>
                    <S.StatusName statusColor={getStatusColor(numberStatus)}>
                      {getStatusName(numberStatus, t)}
                    </S.StatusName>
                  </p>
                  <p>
                    <S.QualityName qualityColor={getQualityColor(numberQuality)}>
                      {getQualityName(numberQuality, t)}
                    </S.QualityName>
                  </p>
                </S.Page>
              )
            })}
        </S.WrapperPages>
      </S.PagesContainer>
    </S.ContainerPages>
  )
}
