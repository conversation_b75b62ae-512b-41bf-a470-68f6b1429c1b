import styled from 'styled-components'

export const Container = styled.div`
  padding: 24px 7px;
`

export const Title = styled.h1`
  color: #353535;
  font-weight: 500;
  font-size: 18px;
  line-height: 22px;

  text-align: center;
`
export const SubTitle = styled.h2`
  color: #52658c;
  font-weight: 500;
  font-size: 20px;
  line-height: 27px;

  text-align: center;
`
export const Content = styled.div``
export const CountSteps = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;

  div {
    width: 12px;
    height: 12px;

    margin-left: 5px;

    border-radius: 50%;

    background: #c4c4c4;

    &.active {
      background: #0083ff;
    }
  }
`

export const ContainerAuthenticate = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;

  p {
    font-size: 12px;
    line-height: 18px;

    width: 420px;

    color: #353535;

    text-align: center;

    a {
      color: #0083ff;
      cursor: pointer;
    }
  }
`

export const ContainerPages = styled.div`
  display: flex;
  align-items: center;

  flex-direction: column;

  padding: 15px 0;

  > p.other-account {
    font-size: 12px;
    color: #757575;

    margin: 15px 0;

    span {
      color: #0083ff;
      cursor: pointer;

      :hover {
        text-decoration: underline;
      }
    }
  }
`
export const UserAutheticated = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 15px;

  border: 1px solid #ececec;
  box-sizing: border-box;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.05);
  border-radius: 16px;

  > p {
    font-size: 18px;
    color: #353535;

    margin: 0;
  }

  > img.avatar {
    width: 53px;
    height: 53px;

    border-radius: 7px;

    margin-left: 7px;

    background-color: #c4c4c4;
  }
`
export const PagesContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  flex-direction: column;

  padding: 15px;

  border: 1px solid #ececec;
  box-sizing: border-box;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.05);
  border-radius: 16px;

  > p {
    font-size: 12px;
    color: #353535;
  }
`
export const WrapperPages = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;

  width: 100%;
  min-height: 200px;
  overflow-y: auto;
`
export const Page = styled.div`
  width: 100%;

  display: flex;
  align-items: center;

  padding: 15px 7px;

  border-bottom: 1px solid #e9e9e9;

  transition: 0.3s;

  cursor: ${(props) => (props.header ? 'default' : 'pointer')};

  :hover {
    box-shadow: ${(props) => (props.header ? 'none' : 'rgba(100, 100, 111, 0.2) 0px 7px 29px 0px')};
    border-radius: ${(props) => (props.header ? '0px' : '7px')};
  }

  p {
    width: 100%;
    margin: 5px;
    color: #0083ff;
    font-size: 12px;
  }

  > img.avatar {
    width: 53px;
    height: 53px;

    border-radius: 7px;

    margin-left: 7px;

    background-color: #c4c4c4;
  }
`
export const QualityName = styled.div`
  font-weight: bolder;
  color: #000;
  border-radius: 15px;
  background-color: ${(props) => props.qualityColor};
  padding: 5px;
  margin: 1px;
  text-align: center;
`

export const StatusName = styled.div`
  font-weight: bolder;
  color: #000;
  border-radius: 15px;
  background-color: ${(props) => props.statusColor};
  padding: 5px;
  margin: 1px;
  text-align: center;
`

export const BusinessName = styled.div`
  color: #000;
`
