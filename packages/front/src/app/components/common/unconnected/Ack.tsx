import React, { memo, useRef } from 'react'
import { UncontrolledTooltip } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { IconCheck, IconDoubleCheck, IconClock, IconInactivate, IconClose, IconPlay } from './IconDigisac'

export const iconMap = (ack, color) => {
  const iconOptions = {
    error: <IconClose fill={color} width="20" height="20" />,
    revoked: <IconInactivate fill={color} width="18" height="18" />,
    0: <IconClock fill={color} width="18" height="18" />,
    1: <IconCheck fill={color} width="15" height="15" />,
    2: <IconDoubleCheck fill={color} width="15" height="15" />,
    3: <IconDoubleCheck fill={color} width="15" height="15" />,
    4: <IconPlay fill={color} width="12" height="12" />,
    // Opções de icones usados somente em Campanha
    notReceived: <IconCheck fill={color} width="15" height="15" />,
    sent: <IconClock fill={color} width="18" height="18" />,
  }
  return iconOptions[ack]
}

const RED = 'red'
const GREY = '#adadad'
const BLUE = '#4fc3f7'

export const colorMap = {
  error: RED,
  revoked: GREY,
  '-1': GREY,
  0: GREY,
  1: GREY,
  2: GREY,
  3: BLUE,
  4: BLUE,
  notReceived: GREY,
  sent: GREY,
}

export const getTooolTipMap = (t) => ({
  error: t('ACK_TOOL_TIP_FAILURE'),
  revoked: t('ACK_TOOL_TIP_MESSAGE_DELETED'),
  '-1': t('ACK_TOOL_TIP_NOT_SENT'),
  0: t('ACK_TOOL_TIP_SENDING'),
  1: t('ACK_TOOL_TIP_SENT_IN'),
  2: t('ACK_TOOL_TIP_RECEIVED'),
  3: t('ACK_TOOL_TIP_READ'),
  4: t('ACK_TOOL_TIP_PLAYED'),
  notReceived: t('ACK_TOOL_TIP_SENT_AND_NOT_RECEIVED'),
  sent: t('ACK_TOOL_TIP_SEND'),
})

function Ack(props) {
  const { ack, style, className, iconClassName = [], type = 'whatsapp' } = props
  const IconSelected = iconMap(ack, colorMap[ack])
  const { t } = useTranslation('common')

  const tooltipMap = getTooolTipMap(t)

  const iconRef = useRef(null)

  if (!IconSelected) return null

  return (
    <>
      <span ref={iconRef} className={className} style={style}>
        {iconMap(ack, colorMap[ack])}
      </span>

      <UncontrolledTooltip placement="right" target={iconRef}>
        {type === 'reclame-aqui' && ack === 'error' ? t('MESSAGE_RECLAME_AQUI_FAILURE') : tooltipMap[ack]}
      </UncontrolledTooltip>
    </>
  )
}

export default memo(Ack)
