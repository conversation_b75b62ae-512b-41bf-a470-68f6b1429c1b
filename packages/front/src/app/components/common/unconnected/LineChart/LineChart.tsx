import React from 'react'
import {
  LineChart as LC,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Brush,
  ResponsiveContainer,
} from 'recharts'
import secondsToHms from '../../../../utils/date/secondsToHms'

type DataKey = {
  name: string
  color?: string
}

interface LineChartType {
  data: Array<{}>
  dataKeys: Array<DataKey>
  subtitles: { [key: string]: string }
  tooltip?: boolean
  title: string
  type?: 'number' | 'date'
}

const styles = {
  tooltip: {
    opacity: '0.9',
    backgroundColor: '#fff',
    borderRadius: 5,
    padding: 10,
    boxShadow: '2px 2px 5px #000',
  },
}

const defaultColors = ['#00f', '#f00', '#0f0']

const LineChart = ({ data, dataKeys, subtitles, tooltip = true, title, type = 'number' }: LineChartType) => {
  const format = (value) => {
    if (type === 'date') {
      const date = secondsToHms(value, ':', true).split(' ')
      const days = Number(date[0])
      const hours = date[1]
      if (!days) {
        return hours
      }
      return `${days}${days === 1 ? ' dia' : ' dias'} ${hours}`
    }
    return value
  }

  const TooltipCustom = ({ payload, active, label }: any) => {
    if (!active || !payload) return <></>
    return (
      <div style={styles.tooltip}>
        <p>{`${label}`}</p>
        {Array.isArray(payload) ? (
          payload.map(({ name, value }, index) => <p key={index}>{`${subtitles[name]}: ${format(value)} `}</p>)
        ) : (
          <p>{`${subtitles[payload.name]}: ${format(payload.value)} `}</p>
        )}
      </div>
    )
  }

  return (
    <div className="mt-5 mb-5">
      <h3 className="text-center">{title}</h3>
      <ResponsiveContainer width="100%" height={400} debounce={50}>
        <LC
          data={data}
          margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 0,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          {tooltip && <Tooltip isAnimationActive content={<TooltipCustom />} />}
          <Legend formatter={(name) => subtitles[name]} />
          {dataKeys.map(({ name, color }, i) => (
            <Line
              key={i}
              type="monotone"
              dataKey={name}
              stroke={color || defaultColors[i]}
              fill={color || defaultColors[i]}
            />
          ))}
          <Brush />
        </LC>
      </ResponsiveContainer>
    </div>
  )
}

export default LineChart
