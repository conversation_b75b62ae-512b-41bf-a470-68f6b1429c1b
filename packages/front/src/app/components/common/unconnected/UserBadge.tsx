import React, { memo } from 'react'
import { Badge } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { fontFamily } from '../../App/styles/common'
import { isEqual } from '../../../utils/logic'

const style = {
  userBadge: {
    padding: '5px 10px',
    color: '#FFFFFF',
    fontFamily: `${fontFamily}`,
    borderRadius: '16px',
    fontSize: '12px',
    fontWeight: '400',
  },
}

const UserBadge = ({ user, ...rest } = {}) => {
  if (!user) return null
  const { t } = useTranslation('chatPage')

  const { name } = user

  return (
    <Badge style={style.userBadge} color="secondary" className="ml-2" title={t('LABEL_CALL_ATTENDANT')} {...rest}>
      {name}
    </Badge>
  )
}

const propsAreEqual = (props, nextProps) => isEqual(props.user, nextProps.user, ['name'])

export default memo(UserBadge, propsAreEqual)
