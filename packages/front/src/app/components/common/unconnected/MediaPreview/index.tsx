import React, { memo, useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { FileText, Image, Lock, Video } from 'lucide-react'
import { MediaIconColor } from '../../../App/styles/colors'
import * as S from './styles'
import { useToast } from '../../../../hooks/useToast'
import { Skeleton } from '../ui/skeleton'
import { useSelector } from 'react-redux'
import { getUser } from '../../../../modules/auth/selectors'
import { checkUserCanDownload, getFileTypeByMimetype } from '../../../../utils/downloadPermissions'

interface MediaPreviewProps {
  url: string
  mimetype: string
  isAuthorized: boolean
  width: string
  height: string
  onPress: () => void
  preview?: string
  onMediaBeenOnScreen?: (id: string) => void
  id?: string
  hasBeenOnScreen?: boolean
}

function mimetypeCheck(mimetype = '') {
  if (mimetype.includes('image')) return 'image'
  if (mimetype.includes('video')) return 'video'
  if (mimetype.includes('application/pdf')) return 'document'
  return 'document'
}

export const MediaPreview: React.FC<MediaPreviewProps> = memo(
  ({ url, width, height, onPress, preview, mimetype, isAuthorized, onMediaBeenOnScreen, id, hasBeenOnScreen }) => {
    const [isImageValid, setIsImageValid] = useState(true)
    const { toast } = useToast()
    const { t } = useTranslation(['chatPage'])
    const [isOnScreen, setIsOnScreen] = useState(hasBeenOnScreen)
    const imagePreviewRef = useRef<HTMLDivElement | null>(null)
    const mediaType = mimetypeCheck(mimetype)

    const user = useSelector(getUser)
    const userCanDownload = checkUserCanDownload(user, getFileTypeByMimetype(mimetype))

    const getToastTitle = (isAuthorized, mimetype, t) => {
      if (isAuthorized) return ''
      return mimetypeCheck(mimetype) === 'document' ? t('NO_DOCUMENT_PERMISSION') : t('NO_PERMISSION_PREVIEW')
    }

    const toastTitle = getToastTitle(isAuthorized, mimetype, t)

    const handlePress = () => {
      if (!isAuthorized) {
        return toast({
          title: toastTitle,
          variant: 'destructive',
        })
      } else {
        onPress()
      }
    }

    const RenderContent = () => {
      if (!isAuthorized) {
        return (
          <S.IconBox data-testid="lock-icon" width={width} height={height}>
            <Lock size={'16px'} color={MediaIconColor} />
          </S.IconBox>
        )
      }
      if (mediaType === 'image') {
        if (isImageValid) {
          return (
            <S.ImageBox
              data-testid="image"
              src={url}
              width={width}
              height={height}
              onError={() => setIsImageValid(false)}
              role="img"
              onContextMenu={(e) => !userCanDownload && e.preventDefault()}
            />
          )
        } else {
          return (
            <S.IconBox data-testid="fallback-icon" width={width} height={height} onClick={handlePress}>
              <Image size={'16px'} color={MediaIconColor} />
            </S.IconBox>
          )
        }
      }

      if (mediaType === 'video') {
        return (
          <S.VideoWrapper>
            <S.VideoBox
              data-testid="video"
              src={url}
              poster={preview}
              width={width}
              height={height}
              role="video"
              onContextMenu={(e) => !userCanDownload && e.preventDefault()}
            />
            <div className="play-icon">
              <Video />
            </div>
          </S.VideoWrapper>
        )
      }
      if (mediaType === 'document') {
        return (
          <S.IconBox data-testid="file-icon" width={width} height={height}>
            <FileText size={'16px'} color={MediaIconColor} />
          </S.IconBox>
        )
      }
      return (
        <S.IconBox data-testid="fallback-icon" width={width} height={height} onClick={handlePress}>
          <Image size={'16px'} color={MediaIconColor} />
        </S.IconBox>
      )
    }

    const chargeUrl = useCallback(() => {
      setIsOnScreen(true)
    }, [isOnScreen])

    useEffect(() => {
      const ref = imagePreviewRef.current
      if (!ref) return
      const observer = new IntersectionObserver(
        (entries) => {
          const [element] = entries
          element.isIntersecting && chargeUrl()
        },
        {
          threshold: 0.4,
        },
      )
      observer.observe(ref)
      return () => {
        observer.disconnect()
        observer.unobserve(ref)
      }
    }, [isOnScreen, chargeUrl])

    useEffect(() => {
      if (isOnScreen) onMediaBeenOnScreen?.(id)
    }, [isOnScreen])

    return (
      <S.Container
        data-testid="container"
        onClick={handlePress}
        width={width}
        height={height}
        ref={imagePreviewRef}
        key={url}
      >
        {isOnScreen || hasBeenOnScreen ? <RenderContent /> : <Skeleton height={height + 'px'} width={width + 'px'} />}
      </S.Container>
    )
  },
  () => true,
)
