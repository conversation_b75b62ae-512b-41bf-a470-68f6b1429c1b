import styled from 'styled-components'

type Props = {
  width: string
  height: string
}

export const Container = styled.div<Props>`
  height: ${(props) => props.height}px;
  width: ${(props) => props.width}px;
  border: 1px solid #e6e7e8;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  overflow: hidden;
`

export const ImageBox = styled.img`
  min-width: 110%;
  min-height: 110%;
  object-fit: cover;
  max-width: 100%;
  max-height: 100%;
`

export const VideoWrapper = styled.div`
  position: relative;
  .play-icon {
    bottom: 0;
    left: 0;
    right: 0;
    padding: 8px;
    position: absolute;
    background: linear-gradient(0deg, #000000 0%, rgba(0, 0, 0, 0) 100%);

    svg {
      fill: #ffffff;
      color: #ffffff;
      width: 16px;
    }
  }
`

export const VideoBox = styled.video`
  min-width: 110%;
  min-height: 110%;
  object-fit: cover;
  max-width: 100%;
  max-height: 100%;
`

export const IconBox = styled.div<Props>`
  width: ${(props) => props.width};
  height: ${(props) => props.height};
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background-color: #f7f9fa;
  cursor: pointer;
`
