import { Component } from 'react'

export default class FieldArray extends Component {
  constructor(props) {
    super(props)

    this.push = this.push.bind(this)
    this.remove = this.remove.bind(this)
    this.set = this.set.bind(this)
  }

  push(item) {
    const { model, setProperty, name } = this.props

    setProperty(name, [...model[name], item])
  }

  remove(index) {
    const { model, setProperty, name } = this.props

    const attr = model[name]

    setProperty(name, [...attr.slice(0, index), ...attr.slice(index + 1)])
  }

  set(index, value) {
    const { model, setProperty, name } = this.props

    const attr = model[name]

    setProperty(name, [...attr.slice(0, index), value, ...attr.slice(index + 1)])
  }

  render() {
    const arrayHelpers = {
      push: this.push,
      remove: this.remove,
      set: this.set,
    }

    return (this.props.render || this.props.children)(arrayHelpers)
  }
}
