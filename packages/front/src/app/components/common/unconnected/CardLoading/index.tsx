import React from 'react'

import PropTypes from 'prop-types'
import { CardImgOverlay } from 'reactstrap'
import Icon from '../Icon/index'

class CardLoading extends React.Component {
  constructor() {
    super()

    this.state = {
      isShowing: false,
    }
  }

  UNSAFE_componentWillReceiveProps(newProps) {
    const { timeout } = this
    const { latencyThreshold = 300 } = newProps

    // Not loading
    if (!newProps.isLoading) {
      if (timeout) clearTimeout(timeout)
      this.timeout = null
      this.setState({ isShowing: false })
      return
    }

    // Is loading but was already loading
    if (timeout) return

    // Is loading and wasn't
    this.timeout = setTimeout(() => this.setState({ isShowing: true }), latencyThreshold)
  }

  componentWillUnmount() {
    const { timeout } = this
    if (timeout) clearTimeout(timeout)
    this.timeout = null
  }

  render() {
    const { isShowing } = this.state
    const { isTransparent } = this.props

    if (!isShowing) return null

    return (
      <CardImgOverlay
        style={{
          height: '100%',
          width: '100%',
          background: isTransparent ? 'transparent' : '#FFF',
          opacity: 0.8,
          zIndex: 900,
          position: 'fixed',
        }}
        className="text-center"
      >
        <div
          data-testid="card-loading"
          style={{
            position: 'absolute',
            margin: 'auto',
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
            height: '2em',
            opacity: 1,
          }}
        >
          <Icon name="circle-notch" spin fixedWidth size="2x" className="text-primary mb-1" />
          <br />
          <h5>Carregando...</h5>
        </div>
      </CardImgOverlay>
    )
  }
}

CardLoading.propTypes = {
  isTransparent: PropTypes.bool,
}

export default CardLoading
