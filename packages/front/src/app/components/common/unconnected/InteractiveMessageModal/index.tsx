import React, { forwardRef } from 'react'
import InteractiveMessagesForm from '../../../App/Dashboard/interactiveMessages/InteractiveMessagesForm'

const InteractiveMessagesModal = forwardRef((props, wrappedComponentRef) => {
  const {
    allDisabled = false,
    toggle,
    isOpen,
    sendMessage,
    contact,
    openedBy = 'chat',
    withModal = true,
    onChange,
    externalModel,
  } = props

  const handleSendMessage = async (data) => {
    await sendMessage({ ...data, contactId: contact.id })
  }

  return (
    <InteractiveMessagesForm
      {...{
        allDisabled,
        wrappedComponentRef,
        isModalOpen: isOpen,
        submit: handleSendMessage,
        exit: toggle,
        exitToPath: '/',
        openedBy,
        withModal,
        onChange,
        externalModel,
      }}
    />
  )
})

export default InteractiveMessagesModal
