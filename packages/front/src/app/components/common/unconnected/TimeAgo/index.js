import React, { Component, memo } from 'react'
import differenceInSeconds from 'date-fns/differenceInSeconds'
import humanTime from '../../../../utils/date/humanTime'

class TimeAgo extends Component {
  componentDidMount() {
    this.isComponentMounted = true
    if (this.props.isLive) {
      this.updateTime()
    }
  }

  componentWillUnmount() {
    this.isComponentMounted = false
    if (this.timeout) clearTimeout(this.timeout)
  }

  updateTime = () => {
    const interval = this.getInterval()
    if (interval > 0) {
      this.timeout = setTimeout(this.updateTime, interval)
      if (this.isComponentMounted) this.forceUpdate()
    }
  }

  getDifference() {
    return differenceInSeconds(new Date(), this.props.date)
  }

  getInterval() {
    const diff = this.getDifference()
    if (diff < 3600) {
      return 60000
    }
    if (diff >= 3600 && diff <= 86400) {
      return 3600000
    }
    return 0
  }

  getParsedDate() {
    return humanTime(this.props.date)
  }

  render() {
    return React.createElement(
      this.props.element,
      { className: this.props.className ? this.props.className : '' },
      this.getParsedDate(),
    )
  }
}

TimeAgo.defaultProps = {
  element: 'span',
  date: new Date(),
  className: undefined,
  isLive: true,
  addSuffix: true,
  includeSeconds: true,
}

export default memo(TimeAgo)
