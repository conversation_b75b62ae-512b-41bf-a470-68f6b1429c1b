import React, { useState } from 'react'
import { Popover, PopoverHeader, PopoverBody } from 'reactstrap'
import Icon from '../Icon'

const ChartInfoCard = ({
  activeInfo = false,
  popoverHeader,
  popoverBody,
  className,
  title,
  icon,
  iconColor = null,
  id,
  children,
  ...rest
}) => {
  const [popoverOpen, setPopoverOpen] = useState(false)

  const togglePopover = () => setPopoverOpen(!popoverOpen)
  return (
    <div className={`d-flex my-3 p-3 px-4 rounded shadow-sm bg-white ${className}`} {...rest}>
      {icon && (
        <div className="px-1 align-self-center">
          <div>
            <Icon name={icon} size="lg" fixedWidth color={iconColor} />
          </div>
        </div>
      )}

      {title && (
        <div className="flex-grow-1 p-2 bd-highlight align-self-center">
          <div
            style={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            <b className="mb-1 text-dark" title={title}>
              {title}
            </b>
          </div>
          {children}
        </div>
      )}
      {activeInfo && (
        <Icon
          id={id}
          onClick={togglePopover}
          size="lg"
          name="info-circle"
          title="informação"
          color="#DDDDDD"
          style={{
            cursor: 'pointer',
          }}
        />
      )}

      {activeInfo && (
        <Popover placement="top" isOpen={popoverOpen} target={id} toggle={togglePopover}>
          <PopoverHeader>{popoverHeader}</PopoverHeader>
          <PopoverBody>{popoverBody}</PopoverBody>
        </Popover>
      )}
    </div>
  )
}

export default ChartInfoCard
