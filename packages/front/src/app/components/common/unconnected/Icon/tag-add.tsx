import * as React from 'react'
import { Icon } from '../../../../types/Icon'

export const TagAdd = (props: Icon) => (
  <svg
    width={props.size || props.width}
    height={props.size || props.height}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_6933_2404)">
      <path
        d="M1.33341 0.666748C0.965225 0.666748 0.666748 0.965225 0.666748 1.33341V8.00008C0.666748 8.17703 0.737093 8.34671 0.862285 8.47176L6.58856 14.1914C6.77427 14.3772 6.9948 14.5247 7.23753 14.6253C7.48033 14.726 7.74059 14.7778 8.00341 14.7778C8.26624 14.7778 8.5265 14.726 8.76929 14.6253C9.01198 14.5247 9.23272 14.3771 9.41841 14.1912L11.3097 12.2999L10.3669 11.3571L8.47508 13.2489C8.41316 13.3109 8.33964 13.3601 8.25871 13.3936C8.17778 13.4272 8.09103 13.4445 8.00341 13.4445C7.9158 13.4445 7.82905 13.4272 7.74812 13.3936C7.66719 13.3601 7.59366 13.3109 7.53175 13.2489L7.53121 13.2484L2.00008 7.72371V2.00008H7.72394L10.3669 4.64305L11.3097 3.70025L8.47149 0.86201C8.34646 0.736986 8.17689 0.666748 8.00008 0.666748H1.33341Z"
        fill={props.fill || props.color}
      />
      <path
        d="M4.66675 4.00008C4.29856 4.00008 4.00008 4.29856 4.00008 4.66675C4.00008 5.03494 4.29856 5.33341 4.66675 5.33341H4.67341C5.0416 5.33341 5.34008 5.03494 5.34008 4.66675C5.34008 4.29856 5.0416 4.00008 4.67341 4.00008H4.66675Z"
        fill={props.fill || props.color}
      />
      <path
        d="M12.6667 5.33341C13.0349 5.33341 13.3334 5.63189 13.3334 6.00008V7.33341H14.6667C15.0349 7.33341 15.3334 7.63189 15.3334 8.00008C15.3334 8.36827 15.0349 8.66675 14.6667 8.66675H13.3334V10.0001C13.3334 10.3683 13.0349 10.6667 12.6667 10.6667C12.2986 10.6667 12.0001 10.3683 12.0001 10.0001V8.66675H10.6667C10.2986 8.66675 10.0001 8.36827 10.0001 8.00008C10.0001 7.63189 10.2986 7.33341 10.6667 7.33341H12.0001V6.00008C12.0001 5.63189 12.2986 5.33341 12.6667 5.33341Z"
        fill={props.fill || props.color}
      />
    </g>
    <defs>
      <clipPath id="clip0_6933_2404">
        <rect width={props.size || props.width} height={props.size || props.height} fill="white" />
      </clipPath>
    </defs>
  </svg>
)
