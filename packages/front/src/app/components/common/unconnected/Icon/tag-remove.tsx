import * as React from 'react'
import { Icon } from '../../../../types/Icon'

export const TagRemove = (props: Icon) => (
  <svg
    width={props.size || props.width}
    height={props.size || props.height}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M1.33342 0.666748C0.965225 0.666748 0.666748 0.965225 0.666748 1.33342V8.00008C0.666748 8.17703 0.737093 8.34672 0.862285 8.47176L6.58856 14.1914C6.77427 14.3773 6.9948 14.5247 7.23754 14.6253C7.48034 14.726 7.74059 14.7778 8.00342 14.7778C8.26625 14.7778 8.5265 14.726 8.7693 14.6253C9.01198 14.5247 9.23273 14.3771 9.41842 14.1912L11.3097 12.2999L10.3669 11.3571L8.47508 13.2489C8.41317 13.3109 8.33964 13.3601 8.25871 13.3937C8.17778 13.4272 8.09103 13.4445 8.00342 13.4445C7.91581 13.4445 7.82905 13.4272 7.74812 13.3937C7.66719 13.3601 7.59367 13.3109 7.53175 13.2489L7.53121 13.2484L2.00008 7.72372V2.00008H7.72394L10.3669 4.64306L11.3097 3.70025L8.47149 0.86201C8.34647 0.736986 8.1769 0.666748 8.00008 0.666748H1.33342Z"
      fill={props.fill || props.color}
    />
    <path
      d="M4.66675 4.00008C4.29856 4.00008 4.00008 4.29856 4.00008 4.66675C4.00008 5.03494 4.29856 5.33342 4.66675 5.33342H4.67342C5.04161 5.33342 5.34008 5.03494 5.34008 4.66675C5.34008 4.29856 5.04161 4.00008 4.67342 4.00008H4.66675Z"
      fill={props.fill || props.color}
    />
    <path
      d="M10.0001 8.00008C10.0001 7.63189 10.2986 7.33342 10.6668 7.33342H14.6668C15.0349 7.33342 15.3334 7.63189 15.3334 8.00008C15.3334 8.36828 15.0349 8.66675 14.6668 8.66675H10.6668C10.2986 8.66675 10.0001 8.36828 10.0001 8.00008Z"
      fill={props.fill || props.color}
    />
  </svg>
)
