import React, { ComponentProps } from 'react'
import { FontAwesomeIcon, FontAwesomeIconProps } from '@fortawesome/react-fontawesome'
import { library, config } from '@fortawesome/fontawesome-svg-core'
import { fas, faBullhorn, IconName as FasIconName } from '@fortawesome/free-solid-svg-icons'
import { far, faCheckCircle, faFile, IconName as FarIconName } from '@fortawesome/free-regular-svg-icons'
import {
  faWhatsapp,
  faTelegramPlane,
  faTelegram,
  faYoutube,
  faFacebookMessenger,
  faInstagram,
  faWindows,
  faLinux,
} from '@fortawesome/free-brands-svg-icons'
import isEqual from '../../../../utils/logic/isEqual'

config.autoAddCss = false
config.autoReplaceSvg = false
config.searchPseudoElements = false
config.observeMutations = false
library.add(
  fas,
  far,
  faCheckCircle,
  faWhatsapp,
  faTelegram,
  faYoutube,
  faTelegramPlane,
  faFacebookMessenger,
  faInstagram,
  faWindows,
  faLinux,
  faBullhorn,
  faFile,
)

export type IconName = FasIconName | FarIconName

const IconComponent = ({ name, ...props }: Partial<FontAwesomeIconProps> & { name: IconName }) => {
  return <FontAwesomeIcon icon={name} {...props} />
}

const areEqual = (prevProps: ComponentProps<typeof IconComponent>, nextProps: ComponentProps<typeof IconComponent>) => {
  return isEqual(prevProps, nextProps, ['name', 'icon']) && isEqual(prevProps.style, nextProps.style, ['color'])
}

export default React.memo(IconComponent, areEqual)
