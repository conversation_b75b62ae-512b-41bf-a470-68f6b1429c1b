import React, { useRef, useState, useEffect } from 'react'
import { But<PERSON> } from '../ui/button'
import { Pause, Play, X } from 'lucide-react'
import { useDispatch, useSelector } from 'react-redux'
import { getUser } from '../../../../modules/auth/selectors'
import { actions as actionsUser } from '../../../../modules/auth'
import { PeaksProgress } from './peaks-progress'
import { AudioSlider } from './audio-slider'

interface AudioPlayerProps {
  url: string
  audioMetadata?: {
    duration?: number
    peaks?: number[]
  }
}

const formatTime = (seconds = 0) => [seconds / 60, seconds % 60].map((v) => `0${Math.floor(v)}`.slice(-2)).join(':')

export const AudioPlayer: React.FC<AudioPlayerProps> = ({ url, audioMetadata }) => {
  const dispatch = useDispatch()
  const user = useSelector(getUser)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [playbackRate, setPlaybackRate] = useState(user?.preferences?.audioSpeed || 1)

  const togglePlay = () => {
    const audio = audioRef.current
    if (audio) {
      if (isPlaying) {
        audio.pause()
      } else {
        audio.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  const togglePlaybackRate = () => {
    const audio = audioRef.current
    if (audio) {
      let newRate = playbackRate + 0.5
      if (newRate > 2) newRate = 1
      setPlaybackRate(newRate)
      audio.playbackRate = newRate
      dispatch(actionsUser.updateUser({ preferences: { ...user.preferences, audioSpeed: newRate } }))
    }
  }

  const handleProgressChange = (value: number[]) => {
    const audio = audioRef.current
    const newTime = value[0]
    if (audio) {
      audio.currentTime = newTime
      setCurrentTime(newTime)
    }
  }

  useEffect(() => {
    const audio = audioRef.current
    if (audio) {
      const handleLoadedMetadata = () => setDuration(audio.duration)
      const handleTimeUpdate = () => setCurrentTime(audio.currentTime)
      const handleEnded = () => {
        setIsPlaying(false)
        setCurrentTime(0)
      }

      audio.addEventListener('loadedmetadata', handleLoadedMetadata)
      audio.addEventListener('timeupdate', handleTimeUpdate)
      audio.addEventListener('ended', handleEnded)

      return () => {
        audio.removeEventListener('loadedmetadata', handleLoadedMetadata)
        audio.removeEventListener('timeupdate', handleTimeUpdate)
        audio.removeEventListener('ended', handleEnded)
      }
    }
  }, [])

  useEffect(() => {
    const audio = audioRef.current
    if (audio) {
      if (user?.preferences?.audioSpeed === audio.playbackRate) return
      audio.playbackRate = user?.preferences?.audioSpeed || 1
      setPlaybackRate(user?.preferences?.audioSpeed || 1)
    }
  }, [user?.preferences?.audioSpeed])

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
        }}
      >
        <Button variant="ghost" size="icon" onClick={togglePlay} data-testid="chat_audio-button-play_pause">
          {isPlaying ? (
            <Pause fill="#586171" color="#586171" style={{ width: '24px', height: '24px' }} />
          ) : (
            <Play fill="#586171" color="#586171" style={{ width: '24px', height: '24px' }} />
          )}
        </Button>

        <div
          style={{
            minWidth: '200px',
          }}
        >
          {audioMetadata?.peaks ? (
            <PeaksProgress
              currentTime={currentTime}
              duration={duration}
              peaks={audioMetadata.peaks}
              onValueChange={handleProgressChange}
            />
          ) : (
            <AudioSlider min={0} max={duration} value={[currentTime]} onValueChange={handleProgressChange} />
          )}
        </div>

        <Button
          size="sm"
          style={{ minWidth: '56px', background: '#586171' }}
          onClick={togglePlaybackRate}
          data-testid="chat_audio-button-speed"
        >
          {playbackRate}
          <X style={{ width: '10px', height: '10px' }} />
        </Button>
      </div>
      <span
        style={{
          color: '#586171',
          fontSize: '12px',
          marginLeft: '52px',
        }}
      >
        {formatTime(currentTime || duration)}
      </span>

      {/* Hidden Audio Element */}
      <audio ref={audioRef} src={url} preload="metadata">
        <track kind="captions" />
      </audio>
    </div>
  )
}
