import React, { useEffect, useState } from 'react'
import styled from 'styled-components'

const Peak = styled.span<{ height: number; index: number; activeIndex: number }>`
  min-width: 3px;
  min-height: 1px;
  border-radius: 2px;
  height: ${(props) => props.height * 50}px;
  background-color: ${(props) => (props.activeIndex === 0 || props.index <= props.activeIndex ? '#6E7A89' : '#B4BBC5')};

  transition: background-color 200ms linear;
`

const PeakWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-width: 5px;
  height: 50px;

  &:hover ${Peak} {
    transition: background-color 50ms linear;
    background-color: #4e5762;
  }
`

interface PeaksProgressProps {
  currentTime: number
  duration: number
  peaks: number[]
  onValueChange: (value: number[]) => void
}

export function PeaksProgress({ currentTime, duration, peaks, onValueChange }: PeaksProgressProps) {
  const [normalizedPeaks, setNormalizedPeaks] = useState<number[]>([])

  const normalizePeaks = (audioPeaks: number[]) => {
    const min = Math.min(...audioPeaks)
    const max = Math.max(...audioPeaks)

    return audioPeaks.map((value) => (value - min) / (max - min))
  }

  const activeIndex = Math.floor((currentTime / duration) * normalizedPeaks.length)

  useEffect(() => {
    setNormalizedPeaks(normalizePeaks(peaks.map((peak) => Math.abs(peak))))
  }, [peaks])

  return (
    <div style={{ display: 'flex', alignItems: 'center', height: '50px' }}>
      {normalizedPeaks.map((height, index) => (
        <PeakWrapper key={index} onClick={() => onValueChange([(index / normalizedPeaks.length) * duration])}>
          <Peak height={height} index={index} activeIndex={activeIndex} />
        </PeakWrapper>
      ))}
    </div>
  )
}
