import React, { useState, useCallback, ReactNode } from 'react'

interface TogglerProps {
  render: (props: RenderProps) => ReactNode
}

interface RenderProps {
  active: boolean
  toggle: () => void
  [key: string]: any
}

const Toggler: React.FC<TogglerProps> = ({ render, ...props }) => {
  const [active, setActive] = useState(false)

  const toggle = useCallback(() => {
    setActive((prevActive) => !prevActive)
  }, [])

  return render({ ...props, active, toggle })
}

export default React.memo(Toggler)
