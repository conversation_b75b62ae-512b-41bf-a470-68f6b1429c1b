import styled from 'styled-components'

export const Pagination = styled.div<{ centered?: boolean; background?: string; justify?: string; newStyle?: boolean }>`
  display: flex;
  align-items: center;
  border: 1px solid rgba(82, 101, 140, 0.15);
  padding: 1rem;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  font-family: 'Inter', sans-serif;
  border-top: 0;
  background: ${(props) => (props.background ? props.background : '')};
  justify-content: ${(props) => (props.justify ? props.justify : '')};

  .pagination {
    justify-content: ${(props) => (props.centered ? 'center' : 'flex-start')};
    margin-bottom: ${(props) => (props.centered ? '10px' : '0px')};
  }

  .page-item {
    margin-right: 0.5rem;

    .page-link {
      padding: 0;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: ${(props) => props.newStyle && '0px'};
      background: ${(props) => props.newStyle && 'transparent'};
      font-weight: ${(props) => props.newStyle && '600'};
      color: ${(props) => props.newStyle && '#36425c'};
    }
  }

  .active {
    font-weight: 700;
    border-radius: ${(props) => props.newStyle && '50%'};
    background: ${(props) => props.newStyle && '#A5CBEB'};

    .page-link {
      background: ${(props) => props.newStyle && '#A5CBEB'};
    }
  }
`

export const GroupSelect = styled.div`
  position: relative;
  min-width: 5rem;
  margin: 0px 5px;

  select {
    width: 100% !important;
    border-radius: 12px;
    background: #fff;
    -webkit-appearance: none;
    cursor: pointer;
  }

  svg {
    position: absolute;
    right: 10px;
    top: 7px;
  }
`
