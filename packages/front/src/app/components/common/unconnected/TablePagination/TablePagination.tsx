import React, { useEffect } from 'react'
import { Col, Input } from 'reactstrap'
import PaginationComponent from 'react-ultimate-pagination-bootstrap-4'
import { useTranslation } from 'react-i18next'
import * as S from './styles'
import { IconArrowDown } from '../IconDigisac'
import { TextColor } from '../../../App/styles/colors'

interface Pagination {
  currentPage: number
  from: number
  lastPage: number
  to: number
  total: number
}

interface LocalPagination {
  page: number
  perPage: number
}

interface TablePaginationProps {
  newStyle?: boolean
  pagination: Pagination
  localPagination: LocalPagination
  perPageOptions?: number[]
  handlePaginationChange: (page: string, numberPage: number) => void
  style?: any
  centered?: boolean
  simplePagination?: boolean
  removeQuantityPerPageSimplePagination?: boolean
}

function TablePagination({
  newStyle = false,
  pagination,
  localPagination,
  handlePaginationChange,
  perPageOptions = [15, 30, 50, 100, 200],
  style = {},
  centered = false,
  simplePagination = false,
  removeQuantityPerPageSimplePagination = false,
}: TablePaginationProps) {
  const { t } = useTranslation(['componentPagination'])
  const totalPages = pagination.lastPage || 1
  const currentPage =
    (localPagination.page || 1) <= pagination.lastPage ? localPagination.page || 1 : pagination.lastPage

  useEffect(() => {
    if (localPagination.page > pagination.lastPage) {
      handlePaginationChange('page', pagination.lastPage)
    }
  }, [pagination])

  const getBody = () => {
    if (!simplePagination) {
      return (
        <S.Pagination
          className="pagination-content"
          data-testid="pagination-footer-all"
          style={style}
          centered={centered}
        >
          <Col data-testid="pagination-footer">
            <PaginationComponent
              currentPage={currentPage || 1}
              totalPages={totalPages}
              onChange={(page: number) => handlePaginationChange('page', page)}
            />
          </Col>
          <Col className="d-flex flex-row-reverse">
            <div className="d-flex align-items-center">
              {t('PAGINATION_SHOWING', {
                first: pagination.total > 0 ? pagination.from + 1 : pagination.from || 0,
                last: pagination.to > pagination.total ? pagination.total : pagination.to || 0,
                results: pagination.total || 0,
              })}
              <S.GroupSelect>
                <Input
                  type="select"
                  bsSize="sm"
                  data-testid="per-page-select"
                  style={{ width: 'auto', display: 'inline' }}
                  value={localPagination.perPage}
                  onChange={(e) => handlePaginationChange('perPage', Number(e.target.value))}
                >
                  {perPageOptions.map((perPage) => (
                    <option key={perPage}>{perPage}</option>
                  ))}
                </Input>
                <IconArrowDown fill={TextColor} width="15" height="15" />
              </S.GroupSelect>
              {t('PAGINATION_PER_PAGE')}
            </div>
          </Col>
        </S.Pagination>
      )
    }

    return (
      <S.Pagination
        className="pagination-content"
        data-testid="pagination-footer-all"
        background="#f2f7fc"
        justify={removeQuantityPerPageSimplePagination ? 'center' : 'space-between'}
        style={style}
        newStyle={newStyle}
      >
        {!removeQuantityPerPageSimplePagination && (
          <div className="d-flex align-items-center">
            {newStyle
              ? t('PAGINATION_SHOWING_SIMPLIFIED', {
                  resultsInPage:
                    localPagination.perPage > pagination.total - pagination.from
                      ? pagination.total - pagination.from
                      : localPagination?.perPage || 0,
                  results: pagination.total || 0,
                })
              : t('PAGINATION_SHOWING', {
                  first: pagination.total > 0 ? pagination.from + 1 : pagination.from || 0,
                  last: pagination.to > pagination.total ? pagination.total : pagination.to || 0,
                  results: pagination.total || 0,
                })}
          </div>
        )}
        <div className="d-flex align-items-center justify-content-center">
          <PaginationComponent
            currentPage={currentPage || 1}
            totalPages={totalPages}
            onChange={(page: number) => handlePaginationChange('page', page)}
          />
        </div>
      </S.Pagination>
    )
  }

  return getBody()
}

export default TablePagination
