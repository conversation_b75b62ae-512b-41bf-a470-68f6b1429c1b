import React, { useEffect } from 'react'
import { Download, FileText, Link, Lock } from 'lucide-react'
import { useDispatch, useSelector } from 'react-redux'
import * as S from './styles'
import { useTranslation } from 'react-i18next'
import { useToast } from '../../../../hooks/useToast'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip'
import { LockIconColor, MediaIconColor } from '../../../App/styles/colors'
import messageApi from '../../../../resources/message/api'
import { useRequest } from '../../../../hooks/useRequest'
import { actions } from '../../../../modules/chat'
import { getUser } from '../../../../modules/auth/selectors'
import { checkUserCanDownload, getFileTypeByMimetype } from '../../../../utils/downloadPermissions'

interface DocumentLinkProps {
  name: string
  url: string
  mimetype: string
  messageId: string
  asLink?: boolean
  isAuthorized?: boolean
  onPress?: () => void
}

export const DocumentLink: React.FC<DocumentLinkProps> = ({
  name,
  url,
  mimetype,
  messageId,
  asLink = false,
  isAuthorized = true,
  onPress,
}) => {
  const { t } = useTranslation(['chatPage'])
  const { toast } = useToast()
  const LIMIT_MESSAGES_ON_CHAT = 500
  const title = isAuthorized ? name : asLink ? t('NO_LINK_PERMISSION') : t('NO_DOCUMENT_PERMISSION')

  const user = useSelector(getUser)
  const userCanDownload = checkUserCanDownload(user, getFileTypeByMimetype(mimetype))

  const handleOpenUrl = () => {
    window.open(url, '_blank')
  }

  const [, requestMessageInChat] = useRequest(messageApi.outOfRange)

  const getMessageElement = () => document.getElementById(messageId)

  const scrollToMessage = () => {
    const element = getMessageElement()
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    })
    element.children?.[0]?.children?.[0]?.animate(
      [{ boxShadow: '0 0 0 0 rgba(0, 0, 0, 0.2)' }, { boxShadow: '0 0 0 20px rgba(0, 0, 0, 0)' }],
      {
        duration: 2000,
        iterations: 1,
      },
    )
  }
  const dispatch = useDispatch()
  const chatState = useSelector<Record<string, any>>((state) => state.chat) as Record<string, any>

  const handleGoToMessage = async () => {
    const element = getMessageElement()
    if (element) return scrollToMessage()
    const result = await requestMessageInChat({ id: messageId, limit: LIMIT_MESSAGES_ON_CHAT })
    if (result.outOfRange)
      return toast({
        description: t('NOT_FOUND_MESSAGE_ON_CHAT'),
        variant: 'warn',
      })

    dispatch(
      actions.loadMoreMessages({
        id: chatState?.currentContactId,
        limit: LIMIT_MESSAGES_ON_CHAT,
        callback: () => setTimeout(() => scrollToMessage(), 1200),
      }),
    )
  }

  const handlePress = () => {
    if (!isAuthorized) {
      return toast({
        title: title,
        variant: 'destructive',
      })
    }

    if (asLink) {
      handleOpenUrl()
    } else {
      onPress?.()
    }
  }

  const formatURL = () => {
    if (url?.startsWith('http')) return url

    return 'https://' + url
  }

  return (
    <S.Container onContextMenu={(e) => !userCanDownload && e.preventDefault()}>
      <S.DocumentContent>
        <S.Flex>
          <S.Icon>
            {asLink ? <Link size={22} color={MediaIconColor} /> : <FileText size={22} color={MediaIconColor} />}
          </S.Icon>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <S.Title
                  onClick={!asLink ? handlePress : undefined}
                  role="button"
                  aria-label={title}
                  as={asLink ? 'a' : 'div'}
                  {...(asLink ? { href: formatURL(), target: '_blank' } : {})}
                >
                  {title}
                </S.Title>
              </TooltipTrigger>
              <TooltipContent>{title}</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </S.Flex>
        {!asLink && (
          <S.Icon>
            {isAuthorized && userCanDownload ? (
              <Download
                data-testid="document-link-download-icon"
                cursor={'pointer'}
                onClick={handleOpenUrl}
                size={22}
                color={MediaIconColor}
              />
            ) : (
              <Lock size={16} color={LockIconColor} data-testid="document-link-lock-icon" />
            )}
          </S.Icon>
        )}
      </S.DocumentContent>
      <S.SolidDivider />
      <S.SeeDocumentMessageArea>
        <S.SeeDocumentMessageLink onClick={handleGoToMessage}>{t('VIEW_MESSAGE')}</S.SeeDocumentMessageLink>
      </S.SeeDocumentMessageArea>
    </S.Container>
  )
}
