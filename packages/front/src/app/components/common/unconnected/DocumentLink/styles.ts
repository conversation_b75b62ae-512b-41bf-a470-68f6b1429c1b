import styled from 'styled-components'

const colors = {
  primary: '#373c43',
  background: '#f7f9fa',
  border: '#14141414',
}

export const Container = styled.div`
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px;
  justify-content: space-between;
  width: 100%;
  max-width: 100%;
  background: ${colors.background};
  border: 1px solid ${colors.border};
  border-radius: 8px;
  overflow: hidden;
`

export const Icon = styled.div`
  padding: 8px;
`

export const Title = styled.div`
  color: ${colors.primary};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 600;
  font-size: 14px;
  max-width: 270px;
`

export const Flex = styled.div`
  display: flex;
  min-width: 0;
  width: 100%;
  align-items: center;
  gap: 4px;
`

export const DocumentContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 100%;
`

export const SolidDivider = styled.hr`
  border: 0;
  width: 100%;
  background-color: #d7dbe0;
  height: 1px;
  margin-top: 4px;
  margin-bottom: 4px;
`

export const SeeDocumentMessageArea = styled.div`
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 100%;
  padding: 4px;
`

export const SeeDocumentMessageLink = styled.span`
  color: #324b7d;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
`
