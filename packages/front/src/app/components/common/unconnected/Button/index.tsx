import React, { ButtonHTMLAttributes } from 'react'
import * as S from './styles'

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  background: string
  disabled?: boolean
  size?: 'sm' | 'lg' | 'md' | 'xl' | 'xxl'
  height?: string
}

const Button: React.FC<ButtonProps> = ({ children, background, disabled, ...props }) => (
  <S.Button type="button" background={background} disabled={disabled} {...props}>
    {children}
  </S.Button>
)

export default Button
