import styled from 'styled-components'
import { shade } from 'polished'
import { Button as ReactstrapButton } from 'reactstrap'
import { flex } from '../../../App/styles/common'

interface ButtonProps {
  background: string
  color?: string
  borderColor?: string
  height?: string
  size?: 'sm' | 'lg' | 'md' | 'xl' | 'xxl'
}

const button_size_xll = '18rem'
const button_size_xl = '14rem'
const button_size_lg = '12rem'
const button_size_md = '10rem'
const button_size_sm = '8rem'

export const Button = styled(ReactstrapButton)<ButtonProps>`
  /* padding: 10px 0; */
  height: ${(props) => (props.height ? props.height : '100%')};
  min-height: 2.4rem;
  position: relative;
  border-radius: 32px;
  background: ${(props) => props.background};
  border-color: ${(props) => (props.borderColor ? props.borderColor : 'transparent')};
  font-size: 14px;
  color: ${(props) => (props.color ? props.color : '#FFFFFF')};
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: 0.2s ease-in-out;
  outline: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  /* height: 100%; */
  width: ${(props) =>
    props.size === 'sm'
      ? button_size_sm
      : props.size === 'md'
        ? button_size_md
        : props.size === 'lg'
          ? button_size_lg
          : props.size === 'xl'
            ? button_size_xl
            : button_size_xll};

  svg {
    margin-right: 0.5rem;
  }

  a {
    color: ${(props) => (props.color ? props.color : '#FFFFFF')};
    text-decoration: none;
    display: block;

    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &:hover {
    background: ${(props) => shade(0.2, `${props.background}`)};
  }
`
