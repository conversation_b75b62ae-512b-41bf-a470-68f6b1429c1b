import React, { useLayoutEffect, useRef, ReactNode } from 'react'
import * as S from './styles'

interface BoxDropdownProps {
  onClickOutside: () => void
  children: ReactNode
  data: boolean
  className?: string
}

export function BoxDropdown({ onClickOutside, children, data, className }: BoxDropdownProps) {
  const ref = useRef(null)

  const handleClickOutside = (event) => {
    if (ref.current && !ref.current.contains(event.target)) {
      if (data && onClickOutside) onClickOutside()
    }
  }

  useLayoutEffect(() => {
    if (typeof window !== 'undefined') {
      window.addEventListener('mousedown', handleClickOutside)

      return () => {
        window.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [data])

  return (
    <S.BoxDropdown className={className} ref={ref}>
      {children}
    </S.BoxDropdown>
  )
}
