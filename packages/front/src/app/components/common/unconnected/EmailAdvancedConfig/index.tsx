import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalBody, FormGroup, Button, Label } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import InputGroup from '../InputGroup'
import MultipleInput from '../MultipleInput'
import { <PERSON>dalDigisac, ModalFooter } from '../../../App/styles/common'

export default ({ isOpen, toggle, subject, ccs, handleSubjectChange, handleCcsChange }) => {
  const clearFilters = () => {
    handleSubjectChange({ target: { value: '' } })
    handleCcsChange([])
  }
  const { t } = useTranslation('chatPage')
  return (
    <ModalDigisac isOpen={isOpen} toggle={toggle} autoFocus={false} data-testid="advanced-filters-modal">
      <ModalHeader toggle={toggle}>{t('TITLE_DIALOG_OPTIONS_EMAIL')}</ModalHeader>
      <ModalBody>
        <FormGroup className="mt-2">
          <InputGroup
            id="subject"
            label={t('SUBJECT')}
            onChange={handleSubjectChange}
            data-testid="subject"
            value={subject}
          />

          <Label>CC</Label>
          <MultipleInput placeholder={t('ENTER_THE_EMAIL_AND_PRESS_SPACE')} value={ccs} onChange={handleCcsChange} />
        </FormGroup>
      </ModalBody>
      <ModalFooter>
        <Button className="cancel" color="light" onClick={clearFilters} data-testid="chat-button-clean_options">
          {t('LABEL_CLEAR')}
        </Button>

        <Button className="close-filters" color="secondary" onClick={toggle} data-testid="chat-button-subject_email">
          {t('LABEL_OK')}
        </Button>
      </ModalFooter>
    </ModalDigisac>
  )
}
