import React, { memo } from 'react'

import { Button, Row, Col, Input, FormGroup, Label } from 'reactstrap'

import Icon from '../Icon'
import FieldArray2, { ArrayHelpers } from '../FieldArray2'

const createRule = () => ({
  title: null,
  value: null,
})

function ButtonAction(props) {
  const { value = [], onChange, disabled } = props

  return (
    <FieldArray2
      value={value}
      onChange={onChange}
      render={(arr: ArrayHelpers) => (
        <>
          {value.map((rule, i) => (
            <Row key={i}>
              <Col md={5}>
                <FormGroup>
                  <Label htmlFor="title">Título</Label>
                  <Input
                    id={`title_${i}`}
                    value={rule.title}
                    onChange={(event) =>
                      arr.set(i, {
                        ...rule,
                        title: event.target.value,
                      })
                    }
                    placeholder={`Título do ${i + 1}º botão`}
                  />
                </FormGroup>
              </Col>
              <Col md={5}>
                <FormGroup>
                  <Label htmlFor="value">Valor</Label>
                  <Input
                    id={`value_${i}`}
                    value={rule.value}
                    onChange={(event) =>
                      arr.set(i, {
                        ...rule,
                        value: event.target.value,
                      })
                    }
                    placeholder={`Valor do ${i + 1}º botão`}
                  />
                </FormGroup>
              </Col>
              <Col
                md={2}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'flex-end',
                }}
              >
                <Button color="danger" outline onClick={() => arr.remove(i)}>
                  <Icon name="times" />
                </Button>
              </Col>
            </Row>
          ))}
          <p className="m-0"> A ação de exibição de botões é aplicada para conexões do tipo Webchat </p>
          <Button disabled={disabled} onClick={() => arr.push(createRule())} className="mt-2" onBlur={props.onBlur}>
            <Icon name="plus" />
            &nbsp;Adicionar botões
          </Button>
        </>
      )}
    />
  )
}

export default memo(ButtonAction)
