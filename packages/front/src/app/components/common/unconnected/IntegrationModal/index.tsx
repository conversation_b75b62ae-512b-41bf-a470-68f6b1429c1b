/* eslint-disable jsx-a11y/media-has-caption,no-underscore-dangle */

import React, { useMemo } from 'react'
import { Modal, ModalBody, ModalHeader } from 'reactstrap'
import reformed from 'react-reformed'
import interpolate from '../../../../utils/interpolate'
import withValidation from '../withValidation/withValidation'

const IntegrationModal = (props) => {
  const { isOpen, onToggle, integration, variables } = props

  if (!integration) return null

  const interpolatedUrl = useMemo(() => interpolate(integration.url, variables), [integration.url, variables])

  const sizes = {
    sm: { height: '200px' },
    md: { height: '400px' },
    lg: { height: '600px' },
  }

  const integrationModalSize = integration.size || 'md'

  return (
    <Modal
      isOpen={isOpen}
      toggle={() => onToggle(false)}
      autoFocus={false}
      data-testid="integration-modal"
      size={integrationModalSize}
    >
      <ModalHeader toggle={() => onToggle(false)}>{integration.text}</ModalHeader>
      <ModalBody>
        <div>
          <iframe
            title="integration"
            width="100%"
            height={sizes[integrationModalSize].height}
            src={interpolatedUrl}
            frameBorder="0"
            allow="microphone; camera; accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowFullScreen
          />
        </div>
      </ModalBody>
    </Modal>
  )
}

export default reformed()(withValidation()(IntegrationModal))
