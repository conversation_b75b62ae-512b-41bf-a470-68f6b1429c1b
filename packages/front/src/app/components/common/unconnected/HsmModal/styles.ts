import styled from 'styled-components'
import backgroundImage from './background.png'

export const Background = styled.div`
  background: url(${backgroundImage});
  width: 100%;
  height: 100%;
  padding-top: 20px;
  min-height: 150px;
  overflow-y: auto;
  padding: 20px 0;
  overflow-wrap: anywhere;
`

export const Message = styled.div`
  border-radius: 0 15px 15px 15px;
  background-color: #fff;
  margin: 0 20px;
  padding: 20px;
  min-height: 100px;
`

export const Header = styled.div`
  font-weight: bold;
  margin-bottom: 10px;
`

export const Body = styled.div`
  margin-bottom: 10px;
`

export const Footer = styled.div`
  color: rgba(0, 0, 0, 0.45);
  line-height: 17px;
  font-size: 13px;
`

export const Button = styled.div`
  border-radius: 15px;
  background-color: #fff;
  margin: 5px 20px 0 20px;
  padding: 10px;
  color: #00a5f4;
  display: flex;
  justify-content: center;
`

export const Media = styled.div`
  height: ${(props) => (props.format === 'VIDEO' ? '200px' : '100px')};
  width: 100%;
  margin: auto;
  display: flex;
  align-content: center;
  justify-content: center;
  align-items: center;
  flex-direction: row;
`
export const ButtonFile = styled.div`
  width: 14rem;
  height: 40px;
  color: #fff;
  background-color: #324b7d;
  border-radius: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 10px;
  float: right;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
`
