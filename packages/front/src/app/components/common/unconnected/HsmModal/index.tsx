import React, { useEffect, useState, memo } from 'react'
import { ModalHeader, ModalBody, FormGroup, Button, Card, CardBody, CardFooter, Label, Row, Col } from 'reactstrap'
import omit from 'lodash/omit'
import escapeRegExp from 'lodash/escapeRegExp'
import uniq from 'lodash/uniq'
import { useTranslation } from 'react-i18next'
import { isEmpty } from 'lodash'
import { ModalFooter, ModalDigisac, GroupInput } from '../../../App/styles/common'
import ButtonClose from '../ButtonClose'
import HsmSelect from '../../connected/HsmSelect'
import InputGroup, { InputGroupWrapper } from '../InputGroup'
import * as S from './styles'
import templateImage from '../../../../assets/logos/template-image.svg'

import Icon from '../Icon'

// utils
import { getAutheticationText, interpolateFooterAuthentication } from '../../../../utils/whatsappTemplateLanguage'
import readFileAsDataURL from '../../../../utils/readFileAsDataURL'

/**
 * Retorna a imagem do botão no preview
 *
 * @param {string} buttonType
 * @returns string
 */
const getImageIcon = (buttonType) =>
  ({
    URL: 'link',
    OTP: 'copy',
    PHONE_NUMBER: 'phone',
  })[buttonType] || ''

const Component = memo(
  ({
    template,
    handleChangeBodyParams,
    handleChangeTemplate,
    contact,
    serviceId,
    headerParams,
    bodyParams,
    buttonParams,
    handleChangeHeaderParams,
    header,
    handleChangeButtonParams,
    button,
    body,
    fileTemplate,
    headerTemplate,
    bodyTemplate,
    footerTemplate,
    buttonTemplate,
    showInputs,
    validationError,
    hsmSelectDisabled,
    t,
    setDefaultFileTemplate,
  }) => (
    <FormGroup className="mt-2" data-testid="HSM-modal_Send_HSM-Select">
      <InputGroupWrapper
        id="template"
        label="Template"
        required
        render={(input) => (
          <GroupInput data-testid="hsm-modal-input-name_template">
            <HsmSelect
              className="template-select"
              id="template"
              placeholder={t('HSM_MODAL_PLACEHOLDER_SELECT_NAME_TEMPLATE')}
              value={template}
              onChange={handleChangeTemplate}
              disabled={hsmSelectDisabled}
              extraQuery={{
                where: {
                  serviceId: contact?.serviceId || serviceId, // campanha não tem contact
                  status: 'APPROVED',
                },
              }}
            />
          </GroupInput>
        )}
      />

      <Card>
        <CardBody>
          <Row>
            {(!!headerParams?.length ||
              (headerTemplate && headerTemplate?.format !== 'TEXT') ||
              !!bodyParams?.length ||
              !!buttonParams?.length) &&
              showInputs && (
                <Col>
                  <Card>
                    <CardBody>
                      {!!headerParams?.length && (
                        <div className="d-flex flex-column">
                          <Label
                            style={{
                              fontWeight: 'bold',
                            }}
                          >
                            {t('HSM_MODAL_LABEL_MESSAGE_HEADER')}
                          </Label>
                          {headerParams.map((param, index) => (
                            <InputGroup
                              key={`headerTemplate-param-${index}`}
                              label={param}
                              onChange={handleChangeHeaderParams}
                              value={header}
                              data-testid="hsm-modal-input-message_header"
                            />
                          ))}
                        </div>
                      )}
                      {headerTemplate?.format && headerTemplate.format !== 'TEXT' && (
                        <div className="d-flex flex-column">
                          <Label
                            style={{
                              fontWeight: 'bold',
                            }}
                          >
                            {t('HSM_MODAL_LABEL_MESSAGE_HEADER')}
                          </Label>
                          <label htmlFor="inputfile" className="float-left">
                            <InputGroup
                              type="file"
                              data-testid="hsm-modal-input-file"
                              key="headerTemplate-param"
                              label={`${t('HSM_MODAL_INPUT_FILE')} ${getTypeByFormat(headerTemplate.format, t)}`}
                              accept={
                                (headerTemplate.format === 'IMAGE' && 'image/*') ||
                                (headerTemplate.format === 'VIDEO' && 'video/*') ||
                                (!!setDefaultFileTemplate &&
                                  headerTemplate.format === 'DOCUMENT' &&
                                  'application/pdf') ||
                                '*'
                              }
                              onChange={handleChangeHeaderParams}
                              id="inputfile"
                              name="inputfile"
                              style={{ display: 'none' }}
                            />
                            <S.ButtonFile data-testid="hsm-modal-button-file" className="file" type="file">
                              <Icon name="file" fixedWidth className="mr-1" />
                              {t('HSM_MODAL_BUTTON_FILE_CHOSSE')}
                            </S.ButtonFile>
                          </label>
                          <div className="float-left">
                            <label style={{ padding: '10px 5px 10px 0' }}>{fileTemplate?.name}</label>
                          </div>
                        </div>
                      )}
                      {!!bodyParams?.length && (
                        <>
                          <Label
                            style={{
                              fontWeight: 'bold',
                            }}
                          >
                            {t('HSM_MODAL_LABEL_MESSAGE_BODY')}
                          </Label>

                          {bodyParams.map((param, index) => (
                            <InputGroup
                              type="text"
                              key={`bodyTemplate-param-${index}`}
                              label={param}
                              onChange={(e) => handleChangeBodyParams(e, index)}
                              value={body[index] || ''}
                              data-testid="hsm-modal-input-body_params"
                              maxlength={template.category == 'AUTHENTICATION' && '8'}
                            />
                          ))}
                        </>
                      )}
                      {!!buttonParams?.length && (
                        <>
                          <Label
                            style={{
                              fontWeight: 'bold',
                            }}
                          >
                            {t('HSM_MODAL_LABEL_MESSAGE_BUTTONS')}
                          </Label>

                          {buttonParams.map((param, index) => (
                            <InputGroup
                              key={`buttonTemplate-param-${index}`}
                              label={param}
                              onChange={handleChangeButtonParams}
                              value={button}
                              data-testid="hsm-modal-input-button_params"
                            />
                          ))}
                        </>
                      )}
                    </CardBody>
                    {validationError && (
                      <CardFooter style={{ color: '#f00' }}>
                        {!template
                          ? t('HSM_MODAL_CARD_FOOTER_CHOOSE_TEMPLATE')
                          : t('HSM_MODAL_CARD_FOOTER_UNFILLED_FIELDS')}
                        <br />
                        {t('HSM_MODAL_CARD_FOOTER_INFO')}
                      </CardFooter>
                    )}
                  </Card>
                </Col>
              )}

            <Col>
              {!showInputs && <>PREVIEW</>}
              <S.Background>
                <S.Message>
                  {headerTemplate && (
                    <S.Header>
                      {headerTemplate?.format === 'TEXT' ? (
                        interpolate(headerTemplate?.text, header)
                      ) : ['VIDEO', 'DOCUMENT'].includes(headerTemplate?.format) ? (
                        fileTemplate?.base64Url || template?.fileExample?.url ? (
                          headerTemplate.format === 'VIDEO' ? (
                            (fileTemplate?.base64Url || template?.fileExample?.url) && (
                              <video
                                width="100%"
                                height="300"
                                controls
                                src={fileTemplate?.base64Url || template?.fileExample?.url}
                                type="video/mp4"
                              />
                            )
                          ) : (
                            <a
                              href={fileTemplate?.base64Url || template?.fileExample?.url}
                              target="_blank"
                              rel="noreferrer"
                            >
                              {(fileTemplate || template?.fileExample)?.name || t('HSM_MODAL_DOCUMENT')}
                            </a>
                          )
                        ) : (
                          <S.Media format={headerTemplate?.format}>
                            <Icon
                              fixedWidth
                              name={headerTemplate?.format === 'VIDEO' ? 'play' : 'file'}
                              size="2x"
                              className="text-primary"
                            />
                          </S.Media>
                        )
                      ) : (
                        <img
                          src={fileTemplate?.base64Url || template?.fileExample?.url || templateImage}
                          style={{
                            width: '100%',
                            maxHeight: '200px',
                          }}
                        />
                      )}
                    </S.Header>
                  )}
                  <S.Body>{interpolate(bodyTemplate?.text, body)}</S.Body>
                  <S.Footer>
                    {template?.category === 'AUTHENTICATION'
                      ? interpolateFooterAuthentication(footerTemplate?.text, footerTemplate?.code_expiration_minutes)
                      : footerTemplate?.text}
                  </S.Footer>
                </S.Message>

                {buttonTemplate?.buttons?.map((btn, index) => (
                  <S.Button key={index} data-testid="hsm-modal-button_type">
                    {btn.type !== 'QUICK_REPLY' && <Icon name={getImageIcon(btn.type)} />}
                    &nbsp;
                    {btn.type === 'URL' ? interpolate(btn.text, button) : btn.text}
                  </S.Button>
                ))}
              </S.Background>
            </Col>
          </Row>
        </CardBody>
      </Card>
    </FormGroup>
  ),
)

const getTypeByFormat = (format, t) =>
  ({
    IMAGE: t('HSM_MODAL_BY_FORMAT_IMAGE'),
    DOCUMENT: t('HSM_MODAL_BY_FORMAT_DOCUMENT'),
    VIDEO: t('HSM_MODAL_BY_FORMAT_VIDEO'),
  })[format]

const interpolate = (text = '', params = []) => {
  const matches = uniq(text.match(/(\{{\d{0,}\}\})/g) || [])
    .join('')
    .replaceAll('{{', '')
    .replaceAll('}}', '')
    .split('')
    .sort((a, b) => a - b)
    .map((i) => `{{${i}}}`)

  if (!matches?.length) return text
  ;(Array.isArray(params) ? params : [params]).forEach((param, index) => {
    const regex = new RegExp(`(${escapeRegExp(matches[index])})`, 'g')
    text = text.replace(regex, param === '' ? `{{${index + 1}}}` : param)
  })
  return text
}

/**
 * Formatação do template
 *
 * @param {object} template
 * @returns object
 */
const getBodyTemplate = (template) => {
  const body = template?.components.find((c) => c.type === 'BODY')

  if (template?.category === 'AUTHENTICATION') {
    body.text = getAutheticationText(template?.language)
  }

  return body
}

export const getHsmModalStates = (template, fileTemplate, header, body, button) => {
  const headerTemplate = template?.components.find((c) => c.type === 'HEADER')
  const bodyTemplate = getBodyTemplate(template)
  const footerTemplate = template?.components.find((c) => c.type === 'FOOTER')
  const buttonTemplate = template?.components.find((c) => c.type === 'BUTTONS')

  const headerParams =
    headerTemplate?.format === 'TEXT' &&
    ((headerTemplate?.params?.length ? headerTemplate?.params : headerTemplate?.text?.match(/(\{{\d{0,}\}\})/g)) || [])

  const bodyParams = uniq(
    (bodyTemplate?.params?.length ? bodyTemplate?.params : bodyTemplate?.text.match(/(\{{\d{0,}\}\})/g)) || [],
  )

  const buttonParams =
    buttonTemplate &&
    (buttonTemplate?.buttons?.find((b) => b.type === 'URL')?.params?.length
      ? buttonTemplate?.buttons.find((b) => b.type === 'URL')?.params
      : buttonTemplate?.buttons?.find((b) => b.type === 'URL')?.url?.match(/(\{{\d{0,}\}\})/g) || [])

  const headerFormat = headerTemplate?.format?.toLowerCase()

  const parameters = [
    ...(headerParams?.length
      ? [
          {
            type: 'header',
            parameters: [
              {
                type: 'text',
                text: header,
              },
            ],
          },
        ]
      : []),
    ...(!!headerTemplate && headerTemplate.format !== 'TEXT'
      ? [
          {
            type: 'header',
            parameters: [
              {
                type: headerFormat,
                [headerFormat]: {
                  link: !fileTemplate ? template?.fileExample?.url || '' : '',
                },
              },
            ],
          },
        ]
      : []),
    ...(bodyParams?.length
      ? [
          {
            type: 'body',
            parameters: bodyParams.map((bp, index) => ({
              type: 'text',
              text: body[index] || '',
            })),
          },
        ]
      : []),
    ...(buttonParams?.length
      ? [
          {
            type: 'button',
            sub_type: 'url',
            index: 0,
            parameters: [
              {
                type: 'text',
                text: button,
              },
            ],
          },
        ]
      : []),
  ]

  return {
    headerTemplate,
    bodyTemplate,
    footerTemplate,
    buttonTemplate,
    headerParams,
    bodyParams,
    buttonParams,
    headerFormat,
    parameters,
  }
}

const HsmModal = ({
  isOpen,
  toggle,
  sendMessage,
  contact = null,
  serviceId,
  withModal = true,
  model = null,
  showInputs = true,
  hsmSelectDisabled = false,
  defaultTemplate = null,
  defaultFileTemplate = null,
  setDefaultFileTemplate = null,
  title = null,
}) => {
  const { t } = useTranslation(['hsmPage', 'common'])

  const [
    template,
    setTemplate,
    header,
    setHeader,
    body,
    setBody,
    button,
    setButton,
    fileTemplate,
    setFileTemplate,
    validationError,
    setValidationError,
  ] = !withModal
    ? model
    : (() => {
        const [template, setTemplate] = useState(defaultTemplate)
        const [header, setHeader] = useState([])
        const [body, setBody] = useState([])
        const [button, setButton] = useState([])
        const [validationError, setValidationError] = useState(false)
        const [fileTemplate, setFileTemplate] = defaultTemplate
          ? [defaultFileTemplate, setDefaultFileTemplate]
          : useState()

        return [
          template,
          setTemplate,
          header,
          setHeader,
          body,
          setBody,
          button,
          setButton,
          fileTemplate,
          setFileTemplate,
          validationError,
          setValidationError,
        ]
      })()

  const {
    headerTemplate,
    bodyTemplate,
    footerTemplate,
    buttonTemplate,
    headerParams,
    bodyParams,
    buttonParams,
    headerFormat,
    parameters,
  } = getHsmModalStates(template, fileTemplate, header, body, button)

  const clear = () => {
    if (!defaultTemplate) {
      setTemplate(null)
    }
    setHeader([])
    setBody([])
    setButton([])
    setFileTemplate(null)
  }

  const handleChangeTemplate = (value) => {
    clear()
    setTemplate(value)
  }

  const handleChangeHeaderParams = async (e) => {
    if (headerTemplate.format === 'TEXT') {
      setHeader(e.target.value)
      return
    }
    const file = e.target.files[0]

    if (!file) return

    const url = await readFileAsDataURL(file)

    setFileTemplate({
      blob: file,
      base64Url: url,
      mimetype: file.type,
      name: file.name,
    })
  }

  const handleChangeBodyParams = (e, index) => {
    e.persist()

    setBody((prev) => {
      prev[index] = e.target.value

      return prev.map(String)
    })
  }

  const handleChangeButtonParams = (e) => {
    setButton(e.target.value)
  }

  const validate = () => {
    if (!template) return false

    if (headerFormat && headerFormat !== 'text') {
      if (!fileTemplate && !template.fileExample) return false
    }

    if (!!headerParams?.length && headerFormat === 'text') {
      if (!parameters?.find((p) => p.type === 'header')?.parameters.every((p) => !!p.text)) {
        return false
      }
    }
    if (bodyParams?.length) {
      if (
        !parameters
          ?.find((p) => p.type === 'body')
          ?.parameters.every((p) => !!p.text.trim().length && !p.text.includes('  '))
      ) {
        return false
      }
    }
    if (buttonParams?.length) {
      if (!parameters?.find((p) => p.type === 'button')?.parameters.every((p) => !!p.text)) {
        return false
      }
    }

    return true
  }

  useEffect(() => {
    setValidationError(!validate())
  }, [parameters])

  const submit = async () => {
    if (validate()) {
      setValidationError(false)
      await sendMessage({
        type: 'chat',
        contactId: contact?.id,
        hsmId: template.id,
        hsmFileId: isEmpty(fileTemplate) && template.fileExample?.id,
        files: [],
        uploadingFiles: false,
        replyTo: null,
        parameters,
        file: omit(fileTemplate, ['base64Url']),
      })
      return toggle()
    }
    setValidationError(true)
  }

  useEffect(() => {
    if (!isOpen || serviceId) {
      clear()
    }
  }, [isOpen, serviceId])

  if (withModal) {
    return (
      <ModalDigisac isOpen={isOpen} toggle={toggle} autoFocus={false} data-testid="advanced-filters-modal" size="lg">
        <ModalHeader>
          {title || t('TITLE_SEND_HSM')}
          <ButtonClose onClick={toggle} />
        </ModalHeader>
        <ModalBody>
          <Component
            {...{
              template,
              handleChangeBodyParams,
              handleChangeTemplate,
              contact,
              serviceId,
              headerParams,
              bodyParams,
              buttonParams,
              handleChangeHeaderParams,
              header,
              body,
              fileTemplate,
              handleChangeButtonParams,
              button,
              headerTemplate,
              bodyTemplate,
              footerTemplate,
              buttonTemplate,
              showInputs,
              validationError,
              hsmSelectDisabled,
              t,
              setDefaultFileTemplate,
            }}
          />
        </ModalBody>
        <ModalFooter>
          <Button
            data-testid="HSM-buttonTemplate-clear_filters"
            className="cancel"
            type="buttonTemplate"
            color="light"
            onClick={clear}
          >
            {t('common:MESSAGE_CLEAR')}
          </Button>

          <Button data-testid="HSM-buttonTemplate-confirm_HSM" className="confirm" type="submit" onClick={submit}>
            {t('common:BUTTON_TEXT_CONFIRM')}
          </Button>
        </ModalFooter>
      </ModalDigisac>
    )
  }

  return (
    <Component
      {...{
        template,
        handleChangeBodyParams,
        handleChangeTemplate,
        contact,
        serviceId,
        headerParams,
        bodyParams,
        buttonParams,
        handleChangeHeaderParams,
        header,
        body,
        fileTemplate,
        handleChangeButtonParams,
        button,
        headerTemplate,
        bodyTemplate,
        footerTemplate,
        buttonTemplate,
        showInputs,
        validationError,
        hsmSelectDisabled,
        t,
        setDefaultFileTemplate,
      }}
    />
  )
}

export default HsmModal
