import React, { memo } from 'react'
import { isEqual } from '../../../../utils/logic'
import './index.css'

export default memo(
  ({ color = '#fff' }: { color?: string }) => {
    const style = {
      backgroundColor: color,
    }

    return (
      <div className="lds-ellipsis">
        <div style={style} />
        <div style={style} />
        <div style={style} />
        <div style={style} />
      </div>
    )
  },
  (prevProps, nextProps) => isEqual(prevProps, nextProps, ['color']),
)
