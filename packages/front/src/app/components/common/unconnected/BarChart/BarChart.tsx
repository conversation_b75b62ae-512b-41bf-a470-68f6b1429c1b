import React from 'react'
import {
  <PERSON><PERSON><PERSON> as BC,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LabelList,
  Label,
} from 'recharts'
import secondsToHms from '../../../../utils/date/secondsToHms'

type DataKey = {
  name: string
  color?: string
}

interface BarChartType {
  data: Array<{}>
  dataKeys: Array<DataKey>
  subtitles: { [key: string]: string }
  labelX: string
  labelY: string
  tooltip?: boolean
  title: string
  minHeight?: number
  type?: 'number' | 'date'
}

const styles = {
  tooltip: {
    opacity: '0.9',
    backgroundColor: '#fff',
    borderRadius: 5,
    padding: 10,
    boxShadow: '2px 2px 5px #000',
  },
}

const defaultColors = ['#00f', '#f00', '#0f0']

const BarChart = ({
  data,
  dataKeys,
  subtitles,
  labelX,
  labelY,
  tooltip = true,
  title,
  minHeight = 180,
  type = 'number',
}: BarChartType) => {
  const format = (value) => {
    if (type === 'date') {
      const date = secondsToHms(value, ':', true).split(' ')
      const days = Number(date[0])
      const hours = date[1]
      if (!days) {
        return hours
      }
      return `${days}${days === 1 ? '_dia' : '_dias'}_${hours}`
    }
    return value
  }

  const TooltipCustom = ({ payload, active, label }: any) => {
    if (!active || !payload) return <></>
    return (
      <div style={styles.tooltip}>
        <p>{`${label}`}</p>
        {Array.isArray(payload) ? (
          payload.map(({ name, value }, index) => <p key={index}>{`${subtitles[name]}: ${format(value)} `}</p>)
        ) : (
          <p>{`${subtitles[payload.name]}: ${format(payload.value)} `}</p>
        )}
      </div>
    )
  }

  return (
    <div className="mb-5 mt-5">
      <h3 className="text-center">{title}</h3>
      <ResponsiveContainer width="100%" height={data.length > 4 ? 40 * data.length : minHeight} debounce={50}>
        <BC data={data} layout="vertical" margin={{ left: 130, right: 140, bottom: 10 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis axisLine hide={type === 'date'} type="number">
            <Label value={labelX} offset={0} position="insideBottom" />
          </XAxis>
          <YAxis
            minTickGap={0}
            tickFormatter={(text) => {
              let newText = text
                ? String(text)
                    .trim()
                    .replaceAll(' ', '.')
                    .replaceAll('-', '.')
                    .replaceAll('...', '.')
                    .replaceAll('..', '.')
                    .replaceAll('.', '_')
                : ''

              if (newText.length >= 20 + 3) {
                newText = newText.substring(0, 20).concat('...')
              }

              return newText
            }}
            dataKey="name"
            type="category"
            padding={{ top: 5, bottom: 5 }}
            allowDecimals
            label={{ value: labelY, position: 'insideBottomLeft', offset: -30 }}
          />

          {tooltip && <Tooltip isAnimationActive content={<TooltipCustom />} />}
          <Legend formatter={(name) => subtitles[name]} />
          {dataKeys.map(({ name, color }, i) => (
            <Bar
              key={i}
              dataKey={name}
              minPointSize={3}
              barSize={16}
              fill={color || defaultColors[i]}
              {...(dataKeys.length >= 2 && { stackId: 'a' })}
            >
              {dataKeys.length < 2 && (
                <LabelList dataKey={name} formatter={(value) => format(value)} position="right" />
              )}
            </Bar>
          ))}
        </BC>
      </ResponsiveContainer>
    </div>
  )
}

export default BarChart
