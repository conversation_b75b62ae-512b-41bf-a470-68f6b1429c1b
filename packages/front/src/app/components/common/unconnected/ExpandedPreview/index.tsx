import React, { useState, useEffect, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { ChevronLeft, ChevronRight, Download, File, Image, Lock } from 'lucide-react'
import { useSelector } from 'react-redux'
import * as S from './styles'
import { useToast } from '../../../../hooks/useToast'
import { getUser } from '../../../../modules/auth/selectors'
import { checkUserCanDownload, getFileTypeByMimetype } from '../../../../utils/downloadPermissions'

interface ExpandedPreviewProps {
  data: {
    id: string
    attachedId: string
    name: string
    mimetype: string
    createdAt: string
    updatedAt: string
    accountId: string
    url: string
    isAuthorized: boolean
    preview?: string
  }[]
  isOpen: boolean
  setOpen: (value: boolean) => void
  index: number
}

export default function ExpandedPreview({ data, isOpen, setOpen, index }: ExpandedPreviewProps) {
  const { t } = useTranslation(['chatPage', 'quickRepliesPage'])
  const [selectedIndex, setSelectedIndex] = useState(index)
  const [fileError, setFileError] = useState(false)
  const { toast } = useToast()
  const selectedFile = data[selectedIndex]

  const user = useSelector(getUser)
  const userCanDownload = checkUserCanDownload(user, getFileTypeByMimetype(selectedFile?.mimetype))

  const isPdf = selectedFile?.mimetype === 'application/pdf'

  const showNavigators = data.length > 1

  useEffect(() => {
    setSelectedIndex(index)
    setFileError(false)
  }, [index])

  const handleChangeIndex = useCallback(
    (direction: number) => {
      const newIndex = (selectedIndex + direction + data.length) % data.length
      setSelectedIndex(newIndex)
      setFileError(false)
    },
    [selectedIndex, data],
  )

  const handleKeydown = useCallback(
    (event: KeyboardEvent) => {
      const keyFunctions = new Map<string, Function>([
        [
          'ArrowLeft',
          () => {
            handleChangeIndex(-1)
          },
        ],
        [
          'ArrowRight',
          () => {
            handleChangeIndex(1)
          },
        ],
      ])
      keyFunctions.get(event.key)?.()
    },
    [handleChangeIndex],
  )

  useEffect(() => {
    if (!isOpen || data?.length === 1) return

    window.addEventListener('keydown', handleKeydown, true)

    return () => {
      window.removeEventListener('keydown', handleKeydown, true)
    }
  }, [isOpen, data, handleKeydown])

  const handleDownload = () => {
    if (!selectedFile.isAuthorized) {
      return toast({
        title: t('ERROR_DOWNLOAD'),
        variant: 'destructive',
      })
    }

    const link = document.createElement('a')
    link.href = selectedFile.url
    if (isPdf) {
      link.target = '_blank'
    }
    link.download = selectedFile.name || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const MediaNotFound = () => (
    <S.MediaMessageContainer
      onContextMenu={(e) => !userCanDownload && e.preventDefault()}
      height={isPdf ? '70vh' : '500px'}
      role="img"
      aria-label="Media not found"
      data-test-id="media-not-found"
    >
      <Image />
      <h6>{selectedFile?.name}</h6>
      <p>{t('quickRepliesPage:FILE_SHOW_ERROR')}</p>
    </S.MediaMessageContainer>
  )
  const DocumentNotFound = () => (
    <S.FileMessageContainer
      onContextMenu={(e) => !userCanDownload && e.preventDefault()}
      role="alert"
      data-test-id="document-not-found"
    >
      <File />
      <p>{t('quickRepliesPage:FILE_SHOW_ERROR')}</p>
    </S.FileMessageContainer>
  )

  const MediaNotAuthorized = () => (
    <S.MediaMessageContainer
      height={isPdf ? '70vh' : '500px'}
      width={isPdf ? '700px' : '500px'}
      role="img"
      aria-label="Media not authorized"
      data-test-id="media-not-authorized"
    >
      <Lock aria-label="lock" role="img" />
      <h6>{t('chatPage:PREVIEW_UNAVAILABLE')}</h6>
      <p>{t('chatPage:NO_FILE_PERMISSION')}</p>
    </S.MediaMessageContainer>
  )

  const RenderFileElement = () => {
    if (!selectedFile?.isAuthorized) return <MediaNotAuthorized />
    if (fileError) return <MediaNotFound />

    if (selectedFile?.mimetype.includes('image')) {
      return (
        <S.ImageWrapper>
          <img
            src={selectedFile.url}
            alt={selectedFile.name}
            onError={() => setFileError(true)}
            data-test-id="file-image"
            onContextMenu={(e) => !userCanDownload && e.preventDefault()}
          />
        </S.ImageWrapper>
      )
    }

    if (isPdf)
      return (
        <S.DocumentContainer height={isPdf ? '80vh' : '500px'} data-test-id="pdf-preview">
          <h6>{selectedFile.name}</h6>
          <iframe
            src={selectedFile.url + '#toolbar=0&view=FitH'}
            role="document"
            style={{ height: 'calc(80vh - 40px)', borderBottomLeftRadius: 8, borderBottomRightRadius: 8 }}
            width={'100%'}
          >
            <S.VStackContainer>
              <DocumentNotFound />
            </S.VStackContainer>
          </iframe>
        </S.DocumentContainer>
      )

    if (selectedFile?.mimetype === 'video/mp4') {
      return (
        <S.VideoWrapper>
          <video
            src={selectedFile.url}
            controls
            onError={() => setFileError(true)}
            data-test-id="video-preview"
            {...(!userCanDownload && { controlsList: 'nodownload' })}
            role="video"
            onContextMenu={(e) => !userCanDownload && e.preventDefault()}
          />
        </S.VideoWrapper>
      )
    }
    return <MediaNotFound />
  }

  return (
    <S.ModalDigisacSlide
      size="full"
      isOpen={isOpen}
      toggle={() => setOpen(!isOpen)}
      autoFocus
      role="dialog"
      aria-labelledby="expanded-preview-title"
      data-test-id="expanded-preview-modal"
    >
      <S.Container>
        <S.FileContainer>
          {showNavigators ? (
            <S.IconButton aria-label="Previous" onClick={() => handleChangeIndex(-1)} data-test-id="previous-file">
              <ChevronLeft />
            </S.IconButton>
          ) : (
            <div />
          )}
          <RenderFileElement />
          {showNavigators ? (
            <S.IconButton aria-label="Next" onClick={() => handleChangeIndex(1)} data-test-id="next-file">
              <ChevronRight />
            </S.IconButton>
          ) : (
            <div />
          )}
        </S.FileContainer>
        <S.Footer>
          {selectedFile && selectedFile.isAuthorized && !fileError && userCanDownload && (
            <S.IconButton onClick={handleDownload} aria-label="Download file" data-test-id="download-file">
              <Download />
            </S.IconButton>
          )}
          <S.ButtonWhite size="lg" onClick={() => setOpen(false)} data-test-id="close-button">
            {t('chatPage:LABEL_CLOSE')}
          </S.ButtonWhite>
        </S.Footer>
      </S.Container>
    </S.ModalDigisacSlide>
  )
}
