import styled from 'styled-components'
import { ModalDigisac } from '../../../App/styles/common'
import { Button } from '../ui/button'

type ContainerProps = {
  height: string
  width?: string
}

export const ModalDigisacSlide = styled(ModalDigisac)`
  .modal-content {
    background: transparent !important;
    height: calc(100vh - 56px);
    display: flex;
    justify-content: center;
  }
  video {
    max-height: 70vh;
    width: 80%;
    background: black;
  }
  img {
    max-height: 84vh;
  }
`

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  align-items: center;
  width: 100%;
  padding: 0px 10px;
  justify-content: space-between;
`

export const FileContainer = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 30px;
  margin-top: auto;
  flex: 1;
`

export const IconButton = styled(Button)`
  background-color: #ffffff;
  width: 48px;
  height: 48px;
  :hover {
    svg {
      stroke: #ffffff;
    }
  }
  svg {
    transform: scale(1.5);
    stroke: #324b7d;
  }
`

export const Footer = styled.footer`
  display: flex;
  justify-content: center;
  gap: 10px;
  padding: 10px 0;
  width: 100%;
`

export const PdfWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
`

export const PdfName = styled.span`
  font-size: 16px;
  font-weight: bold;
`

export const PdfError = styled.p`
  color: red;
  font-size: 14px;
  margin-top: 8px;
`

export const ButtonWhite = styled(Button)`
  background-color: #ffffff;
  color: #212e4a;
  font-weight: 600;
  :hover {
    color: #ffffff;
    svg {
      stroke: #ffffff !important;
    }
  }
`
export const FileMessageContainer = styled.div`
  width: 500px;
  border-radius: 8px;
  background-color: #ffffff;
  padding: 0px 70px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 500px;
  gap: 10px;
  text-align: center;
  p {
    color: #586171;
  }
  svg {
    stroke: #586171;
    width: 30px;
    height: 30px;
    margin-bottom: 10px;
  }
`

export const DocumentContainer = styled.div<ContainerProps>`
  height: ${(props) => props.height};
  width: 700px;
  border-radius: 8px;
  background-color: #ffffff;
  h6 {
    padding: 12px;
    white-space: nowrap;
    overflow: hidden;
    margin: 0px;
    text-overflow: ellipsis;
    border-bottom: 1px solid #d7dbe0;
    height: 40px;
  }
  object {
    width: 700px;
    height: calc(80vh - 50px);
  }
`
export const MediaMessageContainer = styled.div<ContainerProps>`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 2px;
  padding: 70px;
  height: ${(props) => props.height};
  width: ${(props) => props.width};
  border-radius: 8px;
  text-align: center;
  background-color: #ffffff;
  p {
    color: #586171;
  }
  h6 {
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 20rem;
  }
  svg {
    width: 30px;
    height: 30px;
    margin-bottom: 10px;
  }
`

export const VStackContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: calc(80vh - 50px);
`
export const ImageWrapper = styled.div`
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;

  flex: 1;
  img {
    max-height: calc(100vh - 150px);
    width: auto;
    height: auto;
    object-fit: contain;
  }
`

export const VideoWrapper = styled.div`
  height: 100%;
  max-width: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  video {
    max-height: calc(100vh - 150px);
    width: auto;
    height: auto;
    object-fit: contain;
  }
`
