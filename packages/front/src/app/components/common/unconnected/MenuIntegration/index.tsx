import React, { useEffect } from 'react'
import { useParams } from 'react-router'
import { useFetchOneIntegration } from '../../../../resources/integration/requests'

export default function MenuIntegration() {
  // const { id } = useParams()
  // const [{ data: integration }, fetch] = useFetchOneIntegration()

  // useEffect(() => {
  //   fetch(id, {})
  // }, [])

  // if (!integration) return null

  return <div />
}
