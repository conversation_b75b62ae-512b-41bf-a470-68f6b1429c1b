import React, { useMemo, useRef } from 'react'
import { format, useMask } from '@react-input/mask'
import { useTranslation } from 'react-i18next'
import { FormFeedback } from 'reactstrap'

import { Input, InputProps } from '../../ui/input'
import { Cnpj } from '../../../../../utils/Cnpj'

function MaskedCnpjInput({ showInnerErrorMessage = true, ...props }: InputProps & { showInnerErrorMessage?: boolean }) {
  const { t } = useTranslation('customFields')
  const errorRef = useRef<HTMLElement>(null)

  function clearError() {
    if (!errorRef.current) {
      return
    }
    errorRef.current.innerHTML = ''
  }

  const maskOptions = {
    mask: '__.___.___/____-__',
    replacement: { _: /\d/ },
  }

  const inputRef = useMask({
    ...maskOptions,
    onMask({ detail }) {
      if (!errorRef.current || !showInnerErrorMessage) {
        return
      }

      const cnpj = new Cnpj(detail.value)

      if (cnpj.isWrongFormat(false)) return (errorRef.current.innerHTML = t('KEY_CNPJ_WRONG_FORMAT'))

      if (detail.isValid && !cnpj.isValid(false)) {
        return (errorRef.current.innerHTML = t('KEY_CNPJ_INVALID_CNPJ'))
      }

      clearError()
    },
  })

  const formatedValue = useMemo(
    () => format(props.defaultValue ?? props.value ?? '', maskOptions),
    [props.defaultValue, props.value],
  )

  return (
    <>
      <Input
        {...props}
        value={formatedValue}
        placeholder="00.000.000/0000-00"
        ref={inputRef}
        onChange={(event) => {
          if (!event.target.value?.length) clearError()
          props.onChange?.(event)
        }}
      />
      {showInnerErrorMessage && (
        <FormFeedback>
          <span ref={errorRef} />
        </FormFeedback>
      )}
    </>
  )
}

export default MaskedCnpjInput
