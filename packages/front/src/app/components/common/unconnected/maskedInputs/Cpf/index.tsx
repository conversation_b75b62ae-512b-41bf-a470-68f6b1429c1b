import React, { useMemo, useRef } from 'react'
import { useMask, format } from '@react-input/mask'
import { useTranslation } from 'react-i18next'
import { FormFeedback } from 'reactstrap'

import { Input, InputProps } from '../../ui/input'
import { Cpf } from '../../../../../utils/Cpf'

function MaskedCpfInput({ showInnerErrorMessage = true, ...props }: InputProps & { showInnerErrorMessage?: boolean }) {
  const { t } = useTranslation('customFields')
  const errorRef = useRef<HTMLElement>(null)

  function clearError() {
    if (!errorRef.current) {
      return
    }
    errorRef.current.innerHTML = ''
  }

  const maskOptions = {
    mask: '___.___.___-__',
    replacement: { _: /\d/ },
  }

  const inputRef = useMask({
    ...maskOptions,
    onMask({ detail }) {
      if (!errorRef.current || !showInnerErrorMessage) {
        return
      }

      const cpf = new Cpf(detail.value)

      if (cpf.isWrongFormat(false)) return (errorRef.current.innerHTML = t('KEY_CPF_WRONG_FORMAT'))

      if (detail.isValid && !cpf.isValid(false)) {
        return (errorRef.current.innerHTML = t('KEY_CPF_INVALID_CPF'))
      }

      clearError()
    },
  })

  const formatedValue = useMemo(
    () => format(props.defaultValue ?? props.value ?? '', maskOptions),
    [props.defaultValue, props.value],
  )

  return (
    <>
      <Input
        {...props}
        ref={inputRef}
        value={formatedValue}
        placeholder="000.000.000-00"
        onChange={(event) => {
          if (!event.target.value?.length) clearError()

          props.onChange?.(event)
        }}
      />
      {showInnerErrorMessage && (
        <FormFeedback>
          <span ref={errorRef} />
        </FormFeedback>
      )}
    </>
  )
}

export default MaskedCpfInput
