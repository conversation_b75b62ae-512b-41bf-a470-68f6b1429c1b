import React, { useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { FormFeedback } from 'reactstrap'
import { Input, InputProps } from '../../ui/input'

function MaskedEmailInput({
  showInnerErrorMessage = true,
  ...props
}: InputProps & { showInnerErrorMessage?: boolean }) {
  const { t } = useTranslation('customFields')

  const errorRef = useRef<HTMLElement>(null)

  function clearError() {
    if (!errorRef.current) {
      return
    }
    errorRef.current.innerHTML = ''
  }

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target

    if (value.length && !validateEmail(value) && errorRef.current) {
      return (errorRef.current.innerHTML = t('KEY_EMAIL_WRONG_FORMAT'))
    }

    clearError()
  }

  return (
    <>
      <Input
        {...props}
        placeholder="E-mail"
        onChange={(event) => {
          handleChange(event)
          props.onChange?.(event)
        }}
      />
      {showInnerErrorMessage && (
        <FormFeedback>
          <span ref={errorRef} />
        </FormFeedback>
      )}
    </>
  )
}

export default MaskedEmailInput
