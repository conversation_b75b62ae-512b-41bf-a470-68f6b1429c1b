import React, { useMemo, useRef } from 'react'
import { useMask, format } from '@react-input/mask'
import { useTranslation } from 'react-i18next'
import { FormFeedback } from 'reactstrap'

import { Input, InputProps } from '../../ui/input'
import { HourRange } from '../../../../../utils/hourRange'
import { Clock } from 'lucide-react'

function MaskedHourRangeInput({
  showInnerErrorMessage = true,
  ...props
}: InputProps & { showInnerErrorMessage?: boolean }) {
  const { t } = useTranslation('customFields')
  const errorRef = useRef<HTMLElement>(null)

  function clearError() {
    if (!errorRef.current) {
      return
    }
    errorRef.current.innerHTML = ''
  }

  const maskOptions = {
    mask: '__:__ - __:__',
    replacement: { _: /\d/ },
  }

  const inputRef = useMask({
    ...maskOptions,
    onMask({ detail }) {
      if (!errorRef.current || !showInnerErrorMessage) {
        return
      }

      const hourRange = new HourRange(detail.value)

      if (hourRange.isWrongFormat(false)) return (errorRef.current.innerHTML = t('KEY_HOUR_RANGE_WRONG_FORMAT'))

      if (detail.isValid && !hourRange.isValid())
        return (errorRef.current.innerHTML = t('KEY_HOUR_RANGE_INVALID_HOUR_RANGE'))

      clearError()
    },
  })

  const formatedValue = useMemo(
    () => format(props.defaultValue ?? props.value ?? '', maskOptions),
    [props.defaultValue, props.value],
  )

  return (
    <>
      <Input
        {...props}
        icon={<Clock color="#324b7d" style={{ width: '16px', height: '16px', margin: '2px 0px' }} />}
        ref={inputRef}
        value={formatedValue}
        placeholder="00:00 - 00:00"
        onChange={(event) => {
          if (!event.target.value?.length) clearError()

          props.onChange?.(event)
        }}
      />
      {showInnerErrorMessage && (
        <FormFeedback>
          <span ref={errorRef} />
        </FormFeedback>
      )}
    </>
  )
}

export default MaskedHourRangeInput
