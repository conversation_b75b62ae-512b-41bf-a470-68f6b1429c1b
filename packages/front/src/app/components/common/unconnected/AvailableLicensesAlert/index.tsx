import React from 'react'
import { useTranslation } from 'react-i18next'
import IconInfo from '../IconDigisac/Info'
import * as S from './styles'

type AvailableLicensesAlertProps = {
  quantity: number
  type: 'service' | 'user'
}

export const AvailableLicensesAlert = ({ quantity, type }: AvailableLicensesAlertProps) => {
  const { t } = useTranslation('creditsControlPage')
  const createMessage = type === 'service' ? t('NEW_SERVICE') : t('NEW_USER')

  return (
    <S.Container>
      <IconInfo name="info-circle" height="24" width="24" fill="#3C66B9" />
      <S.Message>
        {t('AVAILABLE_LICENSES_ALERT_START')}
        <S.BoldText>{t('AVAILABLE_LICENSES', { count: quantity })}</S.BoldText>
        {createMessage}
      </S.Message>
    </S.Container>
  )
}
