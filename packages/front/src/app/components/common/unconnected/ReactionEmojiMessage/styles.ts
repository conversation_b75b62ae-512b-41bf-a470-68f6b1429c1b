import styled from 'styled-components'

interface ReactionEmojiMessageContainerProps {
  $isFromMe: boolean
}

export const ReactionEmojiMessageContainer = styled.div<ReactionEmojiMessageContainerProps>`
  position: absolute;
  bottom: ${({ $isFromMe }) => ($isFromMe ? 'auto' : '-16px')};
  left: ${({ $isFromMe }) => ($isFromMe ? 'auto' : '8px')};
  right: ${({ $isFromMe }) => ($isFromMe ? '8px' : 'auto')};
  background-color: ${({ $isFromMe }) => ($isFromMe ? '#E9FFE5' : '#fff')};
  padding: 0 8px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  height: 24px;
  border: 1px solid #edeef1;
  cursor: pointer;
`
