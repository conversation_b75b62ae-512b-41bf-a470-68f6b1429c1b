import React from 'react'
import { renderToString } from 'react-dom/server'
import * as S from './styles'
import Emoji from '../Emoji'

interface ReactionEmojiMessageProps {
  reactions: {
    text: string
    isRevokedReaction: boolean
  }[]
  isFromMe: boolean
  onClick?: () => void
  exportPdf: boolean
  message: Record<string, any>
}

export function RenderEmojiFormPdf({ reaction, useEmojiWrapper = true }) {
  //somente funciona para emoji da exportação de pdf
  const srcString = renderToString(useEmojiWrapper ? <Emoji>{reaction}</Emoji> : reaction).replace(/<!-- -->/g, '')
  const fragments = srcString.split(/<img|\/>/) // divido mensagem que possua texto e emojis
  const renderedMessage = fragments.map((fragment) => {
    const src = fragment.match(/src="(.*?)"/)
    if (!src) {
      // retono o fragmento da mensagem que não tiver emoji
      return fragment
    }

    return (
      <img
        data-testid="emiji-image"
        src={`https:${src[1]}`} //quando é feita a exportaçao de pdf, o "https:" não é incluso na string
        alt={src[1]}
        style={{
          height: '1em',
          width: '1em',
          margin: '0 .05em 0 .1em',
          verticalAlign: '-0.1em',
        }}
      />
    )
  })

  return renderedMessage
}

export function ReactionEmojiMessage({ reactions, isFromMe, onClick, exportPdf }: ReactionEmojiMessageProps) {
  const filterReactions = reactions.filter((reaction) => reaction.text && !reaction.isRevokedReaction)

  const groupReactions = filterReactions.reduce((group, reaction) => {
    group[reaction.text] = (group[reaction.text] || 0) + 1

    return group
  }, {})

  const listReactions = Object.keys(groupReactions)
    .map((key) => ({ text: key, amount: groupReactions[key] }))
    .sort((a, b) => b.amount - a.amount) // Ordenar a quantidade de reações de forma decrescente
    .map((reaction) => reaction.text)

  return (
    <S.ReactionEmojiMessageContainer
      $isFromMe={isFromMe}
      onClick={onClick}
      data-testid="chat-button-received_message_reactions"
    >
      {listReactions.slice(0, 4).map((reaction) => {
        return exportPdf ? <RenderEmojiFormPdf reaction={reaction} useEmojiWrapper /> : <Emoji>{reaction}</Emoji>
      })}
      {filterReactions?.length > 1 && filterReactions.length}
    </S.ReactionEmojiMessageContainer>
  )
}
