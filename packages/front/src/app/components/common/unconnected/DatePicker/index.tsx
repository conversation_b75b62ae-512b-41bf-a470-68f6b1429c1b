import React from 'react'
import DatePicker from 'react-datepicker'
import moment from 'moment'
import 'react-datepicker/dist/react-datepicker.css'
import './index.css'

const DataSelect = ({ onChange, selected, dateFormat = 'dd/MM/yyyy', timeFormat }) => (
  <DatePicker
    onChange={onChange}
    selected={selected}
    dateFormat={dateFormat}
    timeFormat={timeFormat}
    minDate={moment()}
    showDisabledMonthNavigation
    isClearable
    placeholderText="Selecione a data para envio"
    className="form-control"
  />
)

export default DataSelect
