import React from 'react'
import { NavItem } from 'reactstrap'
import { NavLink } from 'react-router-dom'

const NavItemLink = ({ children, to, disabled = false, ...rest }) => (
  <NavItem>
    <NavLink
      to={!disabled ? to : '/#'}
      className={`nav-link${disabled ? ' disabled' : ''}`}
      activeClassName="active"
      {...rest}
    >
      {children}
    </NavLink>
  </NavItem>
)

export default NavItemLink
