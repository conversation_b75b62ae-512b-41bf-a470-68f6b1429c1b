import { CustomField } from '../../../../types/CustomFields'
import { CustomFieldType } from '../../../../constants/customFields'
import InputGroup, { InputGroupWrapper } from '../InputGroup'
import React from 'react'
import { Input } from '../ui/input'
import MaskedEmailInput from '../maskedInputs/Email'
import MaskedCnpjInput from '../maskedInputs/Cnpj'
import MaskedCpfInput from '../maskedInputs/Cpf'
import MaskedHourRangeInput from '../maskedInputs/HourRange'
import MaskedHourInput from '../maskedInputs/Hour'
import MaskedNumericInput from '../maskedInputs/Numeric'
import MaskedMonetaryInput from '../maskedInputs/Monetary'
import MaskedPhoneInput from '../maskedInputs/Phone'
import MaskedPostalCodeInput from '../maskedInputs/PostalCode'
import { DateRangePicker } from '../ui/date-range-picker'
import { DatePicker } from '../ui/date-picker'
import { EMPTY_CUSTOM_FIELD_VALUE } from '../../../../utils/customFields'
import { Textarea } from '../ui/textarea'

export default function CustomFieldInputHandler({
  inputProps,
  customField,
}: {
  inputProps: any
  customField: CustomField
}) {
  const inputsMap = new Map<CustomField['type'], JSX.Element>([
    [
      CustomFieldType.type,
      <InputGroup
        {...inputProps}
        {...(customField?.settings?.maxLength && {
          maxLength: customField?.settings?.maxLength,
        })}
      />,
    ],
    [CustomFieldType.text, <InputGroup {...inputProps} />],
    [
      CustomFieldType.email,
      <InputGroupWrapper
        {...inputProps}
        render={(renderProps) => (
          <MaskedEmailInput
            {...renderProps}
            onFocus={() => renderProps.validation.setTouched(renderProps.id)}
            showInnerErrorMessage={false}
          />
        )}
      />,
    ],
    [
      CustomFieldType['long-text'],
      <InputGroupWrapper
        {...inputProps}
        render={(renderProps) => (
          <Textarea
            {...renderProps}
            onFocus={() => renderProps.validation.setTouched(renderProps.id)}
            showInnerErrorMessage={false}
          />
        )}
      />,
    ],
    [
      CustomFieldType.numeric,
      <InputGroupWrapper
        {...inputProps}
        render={(renderProps) => (
          <MaskedNumericInput
            {...renderProps}
            onFocus={() => renderProps.validation.setTouched(renderProps.id)}
            showInnerWarningMessage={inputProps.min > 0 || inputProps.max > 0 ? true : false}
            showInnerErrorMessage={false}
          />
        )}
      />,
    ],
    [
      CustomFieldType.monetary,
      <InputGroupWrapper
        {...inputProps}
        render={(renderProps) => (
          <MaskedMonetaryInput
            {...renderProps}
            onFocus={() => renderProps.validation.setTouched(renderProps.id)}
            showInnerWarningMessage={inputProps.min > 0 || inputProps.max > 0 ? true : false}
            showInnerErrorMessage={false}
          />
        )}
      />,
    ],
    [
      CustomFieldType.phone,
      <InputGroupWrapper
        {...inputProps}
        render={(renderProps) => (
          <MaskedPhoneInput
            {...renderProps}
            onFocus={() => renderProps.validation.setTouched(renderProps.id)}
            showInnerErrorMessage={false}
          />
        )}
      />,
    ],
    [
      CustomFieldType['postal-code'],
      <InputGroupWrapper
        {...inputProps}
        render={(renderProps) => (
          <MaskedPostalCodeInput
            {...renderProps}
            onFocus={() => renderProps.validation.setTouched(renderProps.id)}
            showInnerErrorMessage={false}
          />
        )}
      />,
    ],
    [
      CustomFieldType.cnpj,
      <InputGroupWrapper
        {...inputProps}
        render={(renderProps) => (
          <MaskedCnpjInput
            {...renderProps}
            onFocus={() => renderProps.validation.setTouched(renderProps.id)}
            showInnerErrorMessage={false}
          />
        )}
      />,
    ],
    [
      CustomFieldType.cpf,
      <InputGroupWrapper
        {...inputProps}
        render={(renderProps) => (
          <MaskedCpfInput
            {...renderProps}
            onFocus={() => renderProps.validation.setTouched(renderProps.id)}
            showInnerErrorMessage={false}
          />
        )}
      />,
    ],
    [
      CustomFieldType['date-range'],
      <InputGroupWrapper
        {...inputProps}
        render={(renderProps) => {
          const { value = {}, onChange, id } = renderProps || {}

          return (
            <DateRangePicker
              dateRange={value}
              setDateRange={(dates) => {
                renderProps.validation.setTouched(renderProps.id)
                onChange?.({ target: { id, value: dates || EMPTY_CUSTOM_FIELD_VALUE } })
                return dates
              }}
            />
          )
        }}
      />,
    ],
    [
      CustomFieldType.date,
      <InputGroupWrapper
        {...inputProps}
        render={(renderProps) => {
          const { value, onChange, id } = renderProps || {}

          return (
            <DatePicker
              date={value}
              setDate={(dates) => {
                renderProps.validation.setTouched(renderProps.id)
                onChange?.({ target: { id, value: dates || EMPTY_CUSTOM_FIELD_VALUE } })
                return dates
              }}
            />
          )
        }}
      />,
    ],
    [
      CustomFieldType.hour,
      <InputGroupWrapper
        {...inputProps}
        render={(renderProps) => (
          <MaskedHourInput
            {...renderProps}
            onFocus={() => renderProps.validation.setTouched(renderProps.id)}
            showInnerErrorMessage={false}
          />
        )}
      />,
    ],
    [
      CustomFieldType['hour-range'],
      <InputGroupWrapper
        {...inputProps}
        render={(renderProps) => (
          <MaskedHourRangeInput
            {...renderProps}
            onFocus={() => renderProps.validation.setTouched(renderProps.id)}
            showInnerErrorMessage={false}
          />
        )}
      />,
    ],
  ])

  return inputsMap.get(customField.type)
}
