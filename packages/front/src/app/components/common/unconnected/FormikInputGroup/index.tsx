import React, { memo } from 'react'
import { FormGroup, Label, Input, FormFeedback } from 'reactstrap'

export const InputGroupWrapper = memo((props) => {
  const { touched = [], errors = [], label, id, children, render, noLabel = false } = props

  return (
    <FormGroup color={errors[id] ? 'danger' : null}>
      {!noLabel && <Label htmlFor={id}>{label}</Label>}
      {children || render(props)}
      {!!touched[id] && !!errors[id] && (
        <FormFeedback style={{ display: 'block !important' }}>{errors[id][0]}</FormFeedback>
      )}
    </FormGroup>
  )
})

const InputGroup = memo((props) => {
  const {
    handleChange,
    handleBlur,
    touched = false,
    errors = {},
    values = '',
    value = {},
    label,
    id,
    type = 'text',
  } = props

  return (
    <InputGroupWrapper {...props}>
      <Input
        type={type}
        id={id}
        name={id}
        placeholder={label}
        onChange={handleChange}
        onBlur={handleBlur}
        valid={touched[id] && !errors[id]}
        value={values[id] || value || ''}
      />
    </InputGroupWrapper>
  )
})

export default InputGroup
