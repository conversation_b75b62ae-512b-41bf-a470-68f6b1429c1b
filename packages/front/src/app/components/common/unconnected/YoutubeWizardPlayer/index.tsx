import React, { Component } from 'react'
import ReactPlayer from 'react-player/youtube'
import { YOUTUBE_LINKS } from './constantsLinks'

class YoutubeWizardPlayer extends Component {
  constructor(props) {
    super(props)
  }

  handleEnded() {
    const { handleVideoEnds } = this.props
    handleVideoEnds()
  }

  render() {
    const { state } = this.props
    const currentUrl = YOUTUBE_LINKS[state]
    const videoConfigs = {
      youtube: {
        playerVars: {
          disablekb: 1,
          iv_load_policy: 3,
          rel: 0,
          showinfo: 0,
        },
      },
    }

    return (
      <>
        <ReactPlayer
          url={currentUrl}
          playing
          config={videoConfigs}
          volume={0.8}
          height="100%"
          width="100%"
          onEnded={() => this.handleEnded()}
        />
      </>
    )
  }
}

export default YoutubeWizardPlayer
