import React, { useCallback } from 'react'
import Icon from './Icon'

export default function FakeBox({ onChange, value = false, className, ...rest }) {
  const clickHandler = useCallback(() => (onChange ? onChange(!value) : null), [onChange, value])

  return (
    <div className={className} {...rest} onClick={clickHandler}>
      <Icon name={['far', value ? 'check-square' : 'square']} size="lg" />
    </div>
  )
}
