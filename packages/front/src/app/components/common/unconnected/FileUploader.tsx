import React, { useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { CardImg, CardImgOverlay } from 'reactstrap'
import isImage from './isImage'
import readFileAsDataURL from '../../../utils/readFileAsDataURL'
import { File, Trash2, Upload } from 'lucide-react'
import { Button } from './ui/button'

export default function FileManager({
  value,
  onChange,
  acceptableFileTypes = '',
  disableButtons = false,
  hasRemoveButton = true,
  isBotAction,
  buttonName = null,
  fileUploadRef = null,
  ...rest
}) {
  const { t } = useTranslation('common')

  buttonName = buttonName || t('ADD_IMAGE_OR_FILE_FILE_UPLOAD_BUTTON_NAME')
  const fileUploadEl = 'current' in (fileUploadRef || {}) ? fileUploadRef : useRef(null)

  return (
    <div className="mb-3">
      {/* {(value.base64Url || value.file) && ( */}
      {value && (
        <div style={{ position: 'relative' }}>
          {value.id &&
            (isImage(value.mimetype) ? (
              <CardImg
                width="270%"
                src={value.url}
                alt={value.name.length > 35 ? `${value.name.replace(value.name.substring(27), '')}...` : value.name}
              />
            ) : (
              <h4 className="pl-4 pt-4">
                <a
                  href={value.url}
                  download={
                    value.publicFilename.length > 35
                      ? `${value.publicFilename.replace(value.publicFilename.substring(27), '')}...`
                      : value.publicFilename
                  }
                >
                  <File size={16} className="mr-1" />
                  {value.publicFilename.length > 35
                    ? `${value.publicFilename.replace(value.publicFilename.substring(27), '')}...`
                    : value.publicFilename}
                </a>
              </h4>
            ))}

          {value.base64Url && (
            <>
              {isImage(value.mimetype) ? (
                <CardImg
                  width="270%"
                  src={value.base64Url}
                  alt={
                    value.fileName.length > 35
                      ? `${value.fileName.replace(value.fileName.substring(27), '')}...`
                      : value.fileName
                  }
                />
              ) : (
                <h5 className="text-break" style={{ paddingTop: '1.25rem', maxWidth: 'calc(100% - 60px)' }}>
                  <a
                    style={{
                      wordBreak: 'break-word',
                      flexDirection: 'column',
                    }}
                    href={value.base64Url}
                  >
                    <File size={16} className="mr-1" />
                    {value.fileName.length > 35
                      ? `${value.fileName.replace(value.fileName.substring(27), '')}...`
                      : value.fileName}
                  </a>
                </h5>
              )}
            </>
          )}

          {(value.base64Url || value.id) && hasRemoveButton && (
            <CardImgOverlay>
              <div style={{ float: 'right' }}>
                <Button
                  variant="outline"
                  color="danger"
                  type="button"
                  size="icon"
                  title="Remover mídia"
                  disabled={disableButtons}
                  onClick={() => {
                    onChange(null)

                    fileUploadEl.current.value = null
                  }}
                  data-testid="file_uploader-button-remove"
                >
                  <Trash2 />
                </Button>
              </div>
            </CardImgOverlay>
          )}
        </div>
      )}

      <div>
        <input
          type="file"
          accept={acceptableFileTypes}
          ref={fileUploadEl}
          disabled={disableButtons}
          onChange={async (e) => {
            const file = e.target.files[0]
            const url = await readFileAsDataURL(file)

            await onChange({
              ...value,
              file,
              base64Url: url,
              mimetype: file.type,
              fileName: file.name,
            })
          }}
          className="d-none"
        />

        {!(value && (value.base64Url || value.id)) && (
          <Button
            variant="ghost"
            name="base64Url"
            color="default"
            className="mb-2"
            disabled={disableButtons}
            onClick={() => fileUploadEl.current.click()}
            data-testid="file_uploader-button-upload"
          >
            <Upload
              size={16}
              style={{
                marginRight: '8px',
              }}
            />
            {buttonName}
          </Button>
        )}

        {/* <div> */}
        {/* <Button */}
        {/* outline */}
        {/* color="danger" */}
        {/* type="button" */}
        {/* size="sm" */}
        {/* disabled={disableButtons} */}
        {/* onClick={this.handleRemove} */}
        {/* > */}
        {/* <Icon name="times" fixedWidth /> */}
        {/* </Button> */}
        {/* </div> */}
      </div>
    </div>
  )
}
