import React from 'react'
import SweetAlert from 'react-bootstrap-sweetalert'
import './css.scss'
import { PrimaryColor, PoloBlue } from '../../../App/styles/colors'

const styleButton = {
  padding: '10px 20px',
  borderRadius: '32px',
  color: 'white',
  fontWeight: 600,
  transition: 'opacity 0.2s ease-in-out',
  border: 'none',
  boxShadow: 'none',
  fontSize: '1rem',
  '&:hover': {
    opacity: 0.8,
  },
}

const SweetModal = ({
  type,
  showCancel,
  confirmBtnText,
  confirmBtnBsStyle,
  cancelBtnBsStyle,
  cancelBtnText,
  showModal,
  title,
  children,
  onConfirm,
  onCancel,
  ...rest
}) => (
  <SweetAlert
    type={type || 'success'}
    show={typeof showModal === 'boolean' ? showModal : true}
    showCancel={typeof showCancel === 'boolean' ? showCancel : true}
    confirmBtnText={confirmBtnText || 'Ok'}
    confirmBtnBsStyle={confirmBtnBsStyle || type}
    cancelBtnText={cancelBtnText || 'Cancelar'}
    cancelBtnBsStyle={cancelBtnBsStyle || 'default'}
    cancelBtnStyle={{ ...styleButton, background: PoloBlue }}
    confirmBtnStyle={{ ...styleButton, background: PrimaryColor }}
    onConfirm={onConfirm || ''}
    onCancel={onCancel || ''}
    title={title || ''}
    html
    {...rest}
  >
    {children}
  </SweetAlert>
)
export default SweetModal
