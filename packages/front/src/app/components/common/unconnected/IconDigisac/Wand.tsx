import React from 'react'
import { IconProps } from './Types'

const Wand = ({ fill = 'white', width = '16', height = '16', viewBox = '0 0 12 12' }: IconProps) => (
  <svg width={width} height={height} viewBox={viewBox} fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_1_580)">
      <path
        d="M10.82 1.82001L10.18 1.18001C10.1238 1.12317 10.0568 1.07804 9.98299 1.04725C9.90918 1.01645 9.83 1.0006 9.75002 1.0006C9.67005 1.0006 9.59087 1.01645 9.51706 1.04725C9.44325 1.07804 9.37628 1.12317 9.32002 1.18001L1.18002 9.32001C1.12318 9.37627 1.07806 9.44324 1.04726 9.51704C1.01647 9.59085 1.00061 9.67003 1.00061 9.75001C1.00061 9.82998 1.01647 9.90916 1.04726 9.98297C1.07806 10.0568 1.12318 10.1237 1.18002 10.18L1.82002 10.82C1.87593 10.8775 1.94279 10.9231 2.01664 10.9543C2.0905 10.9855 2.16985 11.0016 2.25002 11.0016C2.33019 11.0016 2.40955 10.9855 2.4834 10.9543C2.55726 10.9231 2.62411 10.8775 2.68002 10.82L10.82 2.68001C10.8775 2.6241 10.9231 2.55724 10.9543 2.48338C10.9855 2.40953 11.0016 2.33018 11.0016 2.25001C11.0016 2.16984 10.9855 2.09048 10.9543 2.01663C10.9231 1.94277 10.8775 1.87592 10.82 1.82001Z"
        stroke={fill}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path d="M7 3.5L8.5 5" stroke={fill} stroke-linecap="round" stroke-linejoin="round" />
      <path d="M2.5 3V5" stroke={fill} stroke-linecap="round" stroke-linejoin="round" />
      <path d="M9.5 7V9" stroke={fill} stroke-linecap="round" stroke-linejoin="round" />
      <path d="M5 1V2" stroke={fill} stroke-linecap="round" stroke-linejoin="round" />
      <path d="M3.5 4H1.5" stroke={fill} stroke-linecap="round" stroke-linejoin="round" />
      <path d="M10.5 8H8.5" stroke={fill} stroke-linecap="round" stroke-linejoin="round" />
      <path d="M5.5 1.5H4.5" stroke={fill} stroke-linecap="round" stroke-linejoin="round" />
    </g>
    <defs>
      <clipPath id="clip0_1_580">
        <rect width="12" height="12" fill={fill} />
      </clipPath>
    </defs>
  </svg>
)

export default Wand
