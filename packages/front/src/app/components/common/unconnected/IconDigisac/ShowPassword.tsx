import React from 'react'
import { IconProps } from './Types'

const ShowPassword = ({ fill, width, height }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M25.874 15.5043C25.234 14.3943 21.714 8.82427 15.734 9.00427C10.204 9.14427 7.00397 14.0043 6.13397 15.5043C6.04621 15.6563 6 15.8287 6 16.0043C6 16.1798 6.04621 16.3522 6.13397 16.5043C6.76397 17.5943 10.134 23.0043 16.024 23.0043H16.274C21.804 22.8643 25.014 18.0043 25.874 16.5043C25.9617 16.3522 26.0079 16.1798 26.0079 16.0043C26.0079 15.8287 25.9617 15.6563 25.874 15.5043ZM16.224 21.0043C11.914 21.1043 9.10397 17.4143 8.22397 16.0043C9.22397 14.3943 11.834 11.1043 15.834 11.0043C20.124 10.8943 22.944 14.5943 23.834 16.0043C22.804 17.6143 20.224 20.9043 16.224 21.0043Z"
      fill={fill}
    />
    <path
      d="M16.004 12.5042C15.3117 12.5042 14.635 12.7094 14.0595 13.094C13.4839 13.4786 13.0353 14.0252 12.7704 14.6648C12.5055 15.3043 12.4362 16.008 12.5712 16.687C12.7063 17.3659 13.0396 17.9895 13.5291 18.479C14.0186 18.9685 14.6422 19.3019 15.3212 19.4369C16.0001 19.5719 16.7038 19.5026 17.3434 19.2377C17.9829 18.9728 18.5295 18.5242 18.9141 17.9486C19.2987 17.3731 19.504 16.6964 19.504 16.0042C19.504 15.0759 19.1352 14.1857 18.4788 13.5293C17.8225 12.8729 16.9322 12.5042 16.004 12.5042ZM16.004 17.5042C15.7073 17.5042 15.4173 17.4162 15.1706 17.2514C14.9239 17.0865 14.7317 16.8523 14.6181 16.5782C14.5046 16.3041 14.4749 16.0025 14.5328 15.7115C14.5907 15.4205 14.7335 15.1533 14.9433 14.9435C15.1531 14.7337 15.4204 14.5909 15.7113 14.533C16.0023 14.4751 16.3039 14.5048 16.578 14.6183C16.8521 14.7319 17.0864 14.9241 17.2512 15.1708C17.416 15.4175 17.504 15.7075 17.504 16.0042C17.504 16.402 17.3459 16.7835 17.0646 17.0648C16.7833 17.3461 16.4018 17.5042 16.004 17.5042Z"
      fill={fill}
    />
  </svg>
)

export default ShowPassword
