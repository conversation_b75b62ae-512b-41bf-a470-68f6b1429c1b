import React from 'react'
import { IconProps } from './Types'

const Filter = ({ width, height, fill }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.9345 7.59091H25.0909C25.7185 7.59091 26.2273 8.09968 26.2273 8.72727C26.2273 9.35487 25.7185 9.86364 25.0909 9.86364H23.9345C23.5036 10.8025 22.5552 11.4545 21.4545 11.4545C20.3539 11.4545 19.4055 10.8025 18.9746 9.86364H6.90909C6.2815 9.86364 5.77273 9.35487 5.77273 8.72727C5.77273 8.09968 6.2815 7.59091 6.90909 7.59091H18.9746C19.4055 6.65203 20.3539 6 21.4545 6C22.5552 6 23.5036 6.65203 23.9345 7.59091ZM6.90909 14.8636C6.2815 14.8636 5.77273 15.3724 5.77273 16C5.77273 16.6276 6.2815 17.1364 6.90909 17.1364H13.52C13.9509 18.0752 14.8993 18.7273 16 18.7273C17.1007 18.7273 18.0491 18.0752 18.48 17.1364H25.0909C25.7185 17.1364 26.2273 16.6276 26.2273 16C26.2273 15.3724 25.7185 14.8636 25.0909 14.8636H18.48C18.0491 13.9248 17.1007 13.2727 16 13.2727C14.8993 13.2727 13.9509 13.9248 13.52 14.8636H6.90909ZM5.77273 23.2727C5.77273 22.6451 6.2815 22.1364 6.90909 22.1364H8.06547C8.49639 21.1975 9.44478 20.5455 10.5455 20.5455C11.6461 20.5455 12.5945 21.1975 13.0254 22.1364H25.0909C25.7185 22.1364 26.2273 22.6451 26.2273 23.2727C26.2273 23.9003 25.7185 24.4091 25.0909 24.4091H13.0254C12.5945 25.348 11.6461 26 10.5455 26C9.44478 26 8.49639 25.348 8.06547 24.4091H6.90909C6.2815 24.4091 5.77273 23.9003 5.77273 23.2727Z"
      fill={fill}
    />
  </svg>
)

export default Filter
