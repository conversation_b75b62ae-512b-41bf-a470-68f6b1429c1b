import React from 'react'
import { IconProps } from './Types'

const Success = ({ fill, width, height }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.8819 12.3064L18.3333 12.9961L15.1957 16.9938L13.1652 14.9641C13.0524 14.8446 12.9171 14.7487 12.7669 14.6818C12.6145 14.6138 12.4498 14.5773 12.2829 14.5743C12.116 14.5714 11.9502 14.6021 11.7954 14.6646C11.6406 14.7272 11.5 14.8202 11.3819 14.9383C11.2639 15.0563 11.1708 15.1969 11.1083 15.3517C11.0458 15.5065 11.0151 15.6723 11.018 15.8392C11.021 16.0062 11.0575 16.1708 11.1254 16.3233C11.1923 16.4734 11.2883 16.6088 11.4078 16.7215L14.378 19.6917C14.4962 19.8099 14.637 19.903 14.792 19.9656C14.947 20.0281 15.113 20.0587 15.2801 20.0556C15.4472 20.0525 15.612 20.0158 15.7646 19.9475C15.9172 19.8793 16.0544 19.781 16.1681 19.6585L16.1782 19.6477L20.6654 14.0359C20.8769 13.7996 20.9902 13.4913 20.9818 13.1739C20.9733 12.8516 20.8399 12.5453 20.6098 12.3195C20.3797 12.0937 20.0709 11.9661 19.7485 11.9637C19.4261 11.9612 19.1154 12.0841 18.8819 12.3064Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16 6C10.4772 6 6 10.4772 6 16C6 21.5228 10.4772 26 16 26C21.5228 26 26 21.5228 26 16C26 10.4772 21.5228 6 16 6ZM7.81818 16C7.81818 11.4813 11.4813 7.81818 16 7.81818C20.5187 7.81818 24.1818 11.4813 24.1818 16C24.1818 20.5187 20.5187 24.1818 16 24.1818C11.4813 24.1818 7.81818 20.5187 7.81818 16Z"
      fill={fill}
    />
  </svg>
)

export default Success
