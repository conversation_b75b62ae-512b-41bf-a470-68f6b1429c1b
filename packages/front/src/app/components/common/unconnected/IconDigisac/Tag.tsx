import React from 'react'
import { IconProps } from './Types'

const Tag = ({ fill, width, height }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.9696 15.1708V8.36885C23.9696 8.2791 23.9339 8.19302 23.8704 8.12956C23.807 8.06609 23.7209 8.03044 23.6312 8.03044H16.8292C16.7847 8.03036 16.7407 8.03905 16.6996 8.056C16.6584 8.07296 16.6211 8.09785 16.5896 8.12925L8.12943 16.5894C8.09791 16.6209 8.07291 16.6582 8.05585 16.6993C8.03879 16.7404 8.03 16.7845 8.03 16.829C8.03 16.8735 8.03879 16.9176 8.05585 16.9587C8.07291 16.9998 8.09791 17.0372 8.12943 17.0686L14.9314 23.8706C14.9628 23.9021 15.0002 23.9271 15.0413 23.9442C15.0824 23.9612 15.1265 23.97 15.171 23.97C15.2155 23.97 15.2596 23.9612 15.3007 23.9442C15.3418 23.9271 15.3791 23.9021 15.4106 23.8706L23.8707 15.4104C23.9022 15.3789 23.927 15.3416 23.944 15.3004C23.961 15.2593 23.9696 15.2153 23.9696 15.1708ZM26 15.1708V8.36885C26 7.06124 24.9388 6 23.6312 6H16.8292C16.2011 6 15.5974 6.24907 15.1534 6.69441L6.69323 15.1546C6.24934 15.5988 6 16.201 6 16.829C6 17.457 6.24934 18.0593 6.69323 18.5034L13.4966 25.3068C13.9407 25.7507 14.543 26 15.171 26C15.799 26 16.4012 25.7507 16.8454 25.3068L25.3056 16.8466C25.5258 16.6266 25.7004 16.3654 25.8196 16.0778C25.9387 15.7903 26 15.4821 26 15.1708ZM19.2319 11.4145C19.5909 11.4145 19.9352 11.5571 20.189 11.811C20.4429 12.0648 20.5855 12.4091 20.5855 12.7681C20.5855 13.1271 20.4429 13.4714 20.189 13.7253C19.9352 13.9791 19.5909 14.1218 19.2319 14.1218C18.8729 14.1218 18.5286 13.9791 18.2747 13.7253C18.0209 13.4714 17.8782 13.1271 17.8782 12.7681C17.8782 12.4091 18.0209 12.0648 18.2747 11.811C18.5286 11.5571 18.8729 11.4145 19.2319 11.4145Z"
      fill={fill}
    />
  </svg>
)

export default Tag
