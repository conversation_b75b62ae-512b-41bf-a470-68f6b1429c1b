import React from 'react'
import { IconProps } from './Types'

const Attendant = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 17}
    height={height || 24}
    className={className}
    viewBox="5 0 32 32"
    fill={fill}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M26.86 15.2148C26.86 18.8401 23.9212 21.7789 20.2959 21.7789C16.6707 21.7789 13.7319 18.8401 13.7319 15.2148C13.7319 11.5896 16.6707 8.65078 20.2959 8.65078C23.9212 8.65078 26.86 11.5896 26.86 15.2148ZM24.2744 22.3925C26.7952 20.9923 28.501 18.3028 28.501 15.2148C28.501 10.6833 24.8275 7.00977 20.2959 7.00977C15.7644 7.00977 12.0909 10.6833 12.0909 15.2148C12.0909 18.3029 13.7968 20.9925 16.3177 22.3927C11.5151 23.9182 7.91004 28.1318 7.2698 33.2661C7.15754 34.1663 8.00524 34.9071 8.91246 34.9071H31.8708C32.7849 34.9071 33.436 34.1732 33.3229 33.2661C32.6826 28.1316 29.0773 23.9179 24.2744 22.3925ZM20.2963 23.42C26.0833 23.42 30.8708 27.6993 31.6671 33.2661H8.92557C9.72184 27.6993 14.5094 23.42 20.2963 23.42Z"
      fill={fill}
    />
    <path
      d="M26.8167 15.1587C26.8167 15.8049 26.63 16.3587 26.1321 16.7736C25.6058 17.2122 24.605 17.6033 22.7424 17.6033H21.9275V17.381C21.9275 17.0537 21.6621 16.7884 21.3348 16.7884H19.2606C18.9333 16.7884 18.668 17.0537 18.668 17.381V19.4552C18.668 19.7825 18.9333 20.0479 19.2606 20.0479H21.3348C21.6621 20.0479 21.9275 19.7825 21.9275 19.4552V19.233H22.7424C24.7912 19.233 26.235 18.8093 27.1754 18.0256C27.8952 17.4258 27.7973 16.6777 27.6994 15.9295C27.6655 15.6705 27.6316 15.4116 27.6316 15.1587H26.8167Z"
      fill={fill}
    />
    <path
      d="M11.6677 11.913C11.9429 11.9726 12.149 12.2174 12.149 12.5103V17.807C12.149 18.1445 11.8754 18.4182 11.5378 18.4182H10.5193C8.71909 18.4182 7.25977 16.9588 7.25977 15.1587C7.25977 13.5431 8.43517 12.202 9.97747 11.944C11.0648 7.24947 15.2727 3.75049 20.2978 3.75049C25.323 3.75049 29.5309 7.24949 30.6182 11.9441C32.1603 12.2022 33.3356 13.5432 33.3356 15.1587C33.3356 16.9588 31.8762 18.4182 30.0761 18.4182H29.0575C28.7199 18.4182 28.4463 18.1445 28.4463 17.807V12.5103C28.4463 12.2173 28.6526 11.9724 28.9279 11.913C27.8683 8.14365 24.4058 5.38023 20.2978 5.38023C16.1898 5.38023 12.7273 8.14369 11.6677 11.913Z"
      fill={fill}
    />
  </svg>
)

export default Attendant
