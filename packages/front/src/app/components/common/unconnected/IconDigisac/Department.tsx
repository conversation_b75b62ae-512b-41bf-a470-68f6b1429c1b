import React from 'react'
import { IconProps } from './Types'

const Department = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.87868 6.87868C9.44129 6.31607 10.2044 6 11 6H21C21.7956 6 22.5587 6.31607 23.1213 6.87868C23.6839 7.44129 24 8.20435 24 9V24H25C25.5523 24 26 24.4477 26 25C26 25.5523 25.5523 26 25 26H7C6.44772 26 6 25.5523 6 25C6 24.4477 6.44772 24 7 24H8V9C8 8.20435 8.31607 7.44129 8.87868 6.87868ZM10 24H13V20C13 19.4696 13.2107 18.9609 13.5858 18.5858C13.9609 18.2107 14.4696 18 15 18H17C17.5304 18 18.0391 18.2107 18.4142 18.5858C18.7893 18.9609 19 19.4696 19 20V24H22V9C22 8.73478 21.8946 8.48043 21.7071 8.29289C21.5196 8.10536 21.2652 8 21 8H11C10.7348 8 10.4804 8.10536 10.2929 8.29289C10.1054 8.48043 10 8.73478 10 9V24ZM15 24H17V20H15V24ZM12 11C12 10.4477 12.4477 10 13 10H14C14.5523 10 15 10.4477 15 11C15 11.5523 14.5523 12 14 12H13C12.4477 12 12 11.5523 12 11ZM17 11C17 10.4477 17.4477 10 18 10H19C19.5523 10 20 10.4477 20 11C20 11.5523 19.5523 12 19 12H18C17.4477 12 17 11.5523 17 11ZM12 15C12 14.4477 12.4477 14 13 14H14C14.5523 14 15 14.4477 15 15C15 15.5523 14.5523 16 14 16H13C12.4477 16 12 15.5523 12 15ZM17 15C17 14.4477 17.4477 14 18 14H19C19.5523 14 20 14.4477 20 15C20 15.5523 19.5523 16 19 16H18C17.4477 16 17 15.5523 17 15Z"
      fill={fill}
    />
  </svg>
)

export default Department
