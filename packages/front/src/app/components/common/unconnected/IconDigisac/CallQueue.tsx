import React from 'react'
import { IconProps } from './Types'

const CallQueue = ({ width, height, fill }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.20615 6C8.34676 6 7.54755 6.22631 6.9096 6.73963C6.27234 7.25239 5.88615 7.97904 5.71832 8.81576L4.02157 16.6218C4.00723 16.6877 4 16.755 4 16.8225V22.5801L4 22.5829C4.00274 23.4958 4.37858 24.3659 5.03774 25.0043C5.69612 25.6419 6.58354 25.9974 7.50497 26L7.50769 26H24.4923L24.495 26C25.4165 25.9974 26.3039 25.6419 26.9623 25.0043C27.6214 24.3659 27.9973 23.4958 28 22.5829L28 22.5801V16.8225C28 16.755 27.9928 16.6877 27.9784 16.6218L26.2823 8.81877C26.1129 7.9455 25.7069 7.21516 25.0581 6.71052C24.4129 6.20876 23.6178 6 22.7938 6H9.20615ZM7.60036 9.19272C7.69887 8.69658 7.89606 8.3984 8.11905 8.21898C8.3436 8.0383 8.68555 7.90476 9.20615 7.90476H22.7938C23.2968 7.90476 23.6428 8.03015 23.8735 8.20953C24.0973 8.38365 24.301 8.67692 24.3984 9.18614C24.3998 9.19392 24.4014 9.20168 24.4031 9.20942L25.8791 16H19.36C18.8298 16 18.4 16.4298 18.4 16.96C18.4 17.7622 18.12 18.5098 17.6534 19.0431C17.1904 19.5723 16.592 19.84 16 19.84C15.408 19.84 14.8096 19.5723 14.3466 19.0431C13.88 18.5098 13.6 17.7622 13.6 16.96C13.6 16.9579 13.6 16.9559 13.6 16.9538C13.5966 16.4265 13.1681 16 12.64 16C12.6395 16 12.6391 16 12.6386 16L6.12085 16L7.59689 9.20942L7.60036 9.19272ZM11.7671 17.92L5.92 17.92L5.92 22.5785C5.92152 22.9728 6.08374 23.3554 6.379 23.6413C6.67507 23.9281 7.08094 24.0937 7.50926 24.0952H24.4907C24.9191 24.0937 25.3249 23.9281 25.621 23.6413C25.9163 23.3553 26.0785 22.9727 26.08 22.5783V17.92H20.2329C20.0705 18.8062 19.684 19.6382 19.0984 20.3075C18.3011 21.2185 17.1902 21.76 16 21.76C14.8098 21.76 13.6988 21.2185 12.9016 20.3075C12.316 19.6382 11.9295 18.8062 11.7671 17.92Z"
      fill={fill}
    />
  </svg>
)

export default CallQueue
