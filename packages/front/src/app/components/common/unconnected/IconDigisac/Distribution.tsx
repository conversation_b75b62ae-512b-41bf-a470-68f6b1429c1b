import React from 'react'
import { IconProps } from './Types'

const Distribution = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill={fill}
      d="M19.7579 23.0739L16.6643 26.3738C16.2982 26.7643 15.7046 26.7643 15.3385 26.3738L12.2449 23.0739C11.6543 22.444 12.0726 21.3668 12.9078 21.3669H14.9063L14.9063 17.1667H10.9686V19.2984C10.9686 20.1893 9.95882 20.6355 9.3682 20.0055L6.2746 16.7057C5.90847 16.3152 5.90847 15.682 6.2746 15.2915L9.3682 11.9917C9.95878 11.3617 10.9686 11.8079 10.9686 12.6988V14.8333H14.9063V10.6332H12.9051C12.0699 10.6332 11.6516 9.55607 12.2422 8.92607L15.3358 5.62625C15.7019 5.23571 16.2955 5.23571 16.6616 5.62625L19.7552 8.92607C20.3458 9.55603 19.9275 10.6332 19.0923 10.6332H17.0938V14.8333H21.0314V12.7016C21.0314 11.8107 22.0412 11.3645 22.6318 11.9945L25.7254 15.2943C26.0915 15.6848 26.0915 16.318 25.7254 16.7085L22.6318 20.0083C22.0412 20.6383 21.0314 20.1921 21.0314 19.3012V17.1667H17.0938V21.3668H19.095C19.9302 21.3668 20.3485 22.4439 19.7579 23.0739Z"
    />
  </svg>
)

export default Distribution
