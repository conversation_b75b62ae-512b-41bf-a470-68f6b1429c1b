import React from 'react'
import { IconProps } from './Types'

const CircleCheck = ({ fill, width, height }: IconProps) => (
  <svg width={width} height={height} viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M5.66618 7.99998L6.99951 9.33331L9.66618 6.66665M14.3328 7.99998C14.3328 11.6819 11.3481 14.6666 7.66618 14.6666C3.98428 14.6666 0.999512 11.6819 0.999512 7.99998C0.999512 4.31808 3.98428 1.33331 7.66618 1.33331C11.3481 1.33331 14.3328 4.31808 14.3328 7.99998Z"
      stroke="#0B690B"
      stroke-width="1.33333"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
)

export default CircleCheck
