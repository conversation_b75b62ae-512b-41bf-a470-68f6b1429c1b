import React from 'react'
import { IconProps } from './Types'

const Connection = ({ width, height, fill }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M19.3333 7.66667C19.7753 7.66667 20.1993 7.84226 20.5118 8.15482C20.8244 8.46738 21 8.89131 21 9.33333V22.6667C21 23.1087 20.8244 23.5326 20.5118 23.8452C20.1993 24.1577 19.7753 24.3333 19.3333 24.3333H12.6666C12.2246 24.3333 11.8007 24.1577 11.4881 23.8452C11.1756 23.5326 11 23.1087 11 22.6667V9.33333C11 8.89131 11.1756 8.46738 11.4881 8.15482C11.8007 7.84226 12.2246 7.66667 12.6666 7.66667H19.3333ZM12.6666 6C11.7826 6 10.9347 6.35119 10.3096 6.97631C9.6845 7.60143 9.33331 8.44928 9.33331 9.33333V22.6667C9.33331 23.5507 9.6845 24.3986 10.3096 25.0237C10.9347 25.6488 11.7826 26 12.6666 26H19.3333C20.2174 26 21.0652 25.6488 21.6903 25.0237C22.3155 24.3986 22.6666 23.5507 22.6666 22.6667V9.33333C22.6666 8.44928 22.3155 7.60143 21.6903 6.97631C21.0652 6.35119 20.2174 6 19.3333 6H12.6666Z"
      fill={fill}
    />
    <path
      d="M16 22.6666C16.442 22.6666 16.866 22.491 17.1785 22.1785C17.4911 21.8659 17.6667 21.442 17.6667 20.9999C17.6667 20.5579 17.4911 20.134 17.1785 19.8214C16.866 19.5089 16.442 19.3333 16 19.3333C15.558 19.3333 15.1341 19.5089 14.8215 19.8214C14.5089 20.134 14.3333 20.5579 14.3333 20.9999C14.3333 21.442 14.5089 21.8659 14.8215 22.1785C15.1341 22.491 15.558 22.6666 16 22.6666ZM5.33167 9.42994C5.42843 9.48114 5.51415 9.5509 5.58395 9.63523C5.65374 9.71957 5.70623 9.81683 5.73842 9.92146C5.77062 10.0261 5.78188 10.136 5.77157 10.245C5.76126 10.354 5.72958 10.4599 5.67834 10.5566C4.79208 12.2339 4.33028 14.1029 4.33334 15.9999C4.33334 17.9666 4.82 19.8199 5.67834 21.4433C5.78177 21.6387 5.80336 21.8671 5.73834 22.0784C5.67333 22.2897 5.52705 22.4665 5.33167 22.5699C5.13629 22.6734 4.90783 22.695 4.69654 22.63C4.48525 22.5649 4.30844 22.4187 4.205 22.2233C3.19171 20.3056 2.66354 18.1689 2.66667 15.9999C2.66667 13.7549 3.22334 11.6349 4.205 9.77661C4.2562 9.67985 4.32596 9.59413 4.41029 9.52434C4.49463 9.45454 4.59189 9.40205 4.69652 9.36986C4.80114 9.33767 4.91109 9.3264 5.02007 9.33671C5.12905 9.34702 5.23494 9.3787 5.33167 9.42994ZM26.6683 9.42994C26.7651 9.3787 26.871 9.34702 26.9799 9.33671C27.0889 9.3264 27.1989 9.33767 27.3035 9.36986C27.4081 9.40205 27.5054 9.45454 27.5897 9.52434C27.674 9.59413 27.7438 9.67985 27.795 9.77661C28.8084 11.6943 29.3365 13.831 29.3333 15.9999C29.3365 18.1689 28.8084 20.3056 27.795 22.2233C27.6916 22.4187 27.5148 22.5649 27.3035 22.63C27.0922 22.695 26.8637 22.6734 26.6683 22.5699C26.473 22.4665 26.3267 22.2897 26.2617 22.0784C26.1967 21.8671 26.2182 21.6387 26.3217 21.4433C27.2079 19.7659 27.6697 17.897 27.6667 15.9999C27.6667 14.0333 27.18 12.1799 26.3217 10.5566C26.2704 10.4599 26.2387 10.354 26.2284 10.245C26.2181 10.136 26.2294 10.0261 26.2616 9.92146C26.2938 9.81683 26.3463 9.71957 26.4161 9.63523C26.4859 9.5509 26.5716 9.48114 26.6683 9.42994ZM7.76167 11.8899C7.8637 11.9297 7.95689 11.9892 8.03591 12.0651C8.11493 12.1409 8.17823 12.2315 8.22219 12.3318C8.26615 12.4321 8.28991 12.5401 8.29211 12.6496C8.29431 12.7591 8.2749 12.868 8.235 12.9699C7.85823 13.9357 7.66547 14.9633 7.66667 15.9999C7.66667 17.0699 7.86667 18.0916 8.23334 19.0299C8.27984 19.1329 8.30492 19.2443 8.30702 19.3573C8.30913 19.4703 8.28824 19.5826 8.2456 19.6872C8.20297 19.7919 8.13949 19.8868 8.05903 19.9662C7.97857 20.0455 7.88281 20.1077 7.77757 20.1489C7.67232 20.1901 7.5598 20.2094 7.44685 20.2058C7.33389 20.2021 7.22286 20.1755 7.1205 20.1276C7.01815 20.0797 6.92661 20.0114 6.85146 19.927C6.7763 19.8426 6.7191 19.7438 6.68334 19.6366C6.23078 18.4776 5.99902 17.2442 6 15.9999C6 14.7183 6.24167 13.4916 6.68334 12.3633C6.7637 12.1576 6.92239 11.9923 7.12457 11.9035C7.32676 11.8148 7.55589 11.8099 7.76167 11.8899ZM24.2383 11.8899C24.3403 11.85 24.4492 11.8306 24.5587 11.8328C24.6682 11.835 24.7761 11.8588 24.8764 11.9028C24.9767 11.9467 25.0674 12.01 25.1432 12.089C25.219 12.1681 25.2786 12.2612 25.3183 12.3633C25.7583 13.4916 26 14.7183 26 15.9999C26 17.2816 25.7583 18.5083 25.3167 19.6366C25.2287 19.8313 25.0694 19.9848 24.8715 20.0654C24.6737 20.146 24.4524 20.1476 24.2534 20.0697C24.0545 19.9918 23.893 19.8405 23.8025 19.647C23.7119 19.4535 23.6991 19.2326 23.7667 19.0299C24.1333 18.0916 24.3333 17.0699 24.3333 15.9999C24.3333 14.9299 24.1333 13.9083 23.7667 12.9699C23.686 12.7642 23.6904 12.5348 23.7788 12.3323C23.8673 12.1297 24.0326 11.9706 24.2383 11.8899Z"
      fill={fill}
    />
  </svg>
)

export default Connection
