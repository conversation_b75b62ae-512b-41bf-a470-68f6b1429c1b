import React from 'react'
import { IconProps } from './Types'

const Database = ({ width, height, fill }: IconProps) => (
  <svg width={width || 36} height={height || 39} viewBox="0 0 36 39" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M17.9468 16.1632C26.4677 16.1632 33.3753 13.0932 33.3753 9.3061C33.3753 5.519 26.4677 2.44895 17.9468 2.44895C9.4258 2.44895 2.5182 5.519 2.5182 9.3061C2.5182 13.0932 9.4258 16.1632 17.9468 16.1632Z"
      stroke={fill || '#000'}
      strokeWidth={3.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.5182 9.30611V29.8775C2.5182 33.649 9.37534 36.7347 17.9468 36.7347C26.5182 36.7347 33.3753 33.649 33.3753 29.8775V9.30611"
      stroke={fill || '#000'}
      strokeWidth={3.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M33.3753 19.5918C33.3753 23.3632 26.5182 26.4489 17.9468 26.4489C9.37533 26.4489 2.51819 23.3632 2.51819 19.5918"
      stroke={fill || '#000'}
      strokeWidth={3.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export default Database
