import React from 'react'
import { IconProps } from './Types'

const AccessManual = ({ width, height, fill, className }: IconProps) => (
  <svg
    width={width || 18}
    height={height || 22}
    className={className}
    viewBox="0 0 18 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M11.0967 12.5192H5.08" stroke={fill} strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.0967 9.03082H5.08" stroke={fill} strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.37584 5.55005H5.08" stroke={fill} strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.2572 1.2915C11.2572 1.2915 4.85966 1.29484 4.84966 1.29484C2.54966 1.309 1.12549 2.82234 1.12549 5.13067V12.794C1.12549 15.114 2.56049 16.6332 4.88049 16.6332C4.88049 16.6332 11.2772 16.6307 11.288 16.6307C13.588 16.6165 15.013 15.1023 15.013 12.794V5.13067C15.013 2.81067 13.5772 1.2915 11.2572 1.2915Z"
      stroke={fill}
      strokeWidth="1.25"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export default AccessManual
