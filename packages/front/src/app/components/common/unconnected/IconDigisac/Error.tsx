import React from 'react'
import { IconProps } from './Types'

const Close = ({ fill, width, height, onClick }: IconProps) => (
  <svg
    width={width || 24}
    height={height || 24}
    onClick={onClick}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17 2.55002C9.01947 2.55002 2.54999 9.0195 2.54999 17C2.54999 24.9805 9.01947 31.45 17 31.45C24.9805 31.45 31.45 24.9805 31.45 17C31.45 9.0195 24.9805 2.55002 17 2.55002ZM4.53332 17C4.53332 10.1149 10.1148 4.53335 17 4.53335C23.8851 4.53335 29.4667 10.1149 29.4667 17C29.4667 23.8852 23.8851 29.4667 17 29.4667C10.1148 29.4667 4.53332 23.8852 4.53332 17Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M22.0962 11.1551C22.303 10.9483 22.6382 10.9483 22.8449 11.1551C23.0517 11.3618 23.0517 11.697 22.8449 11.9038L17.7487 17L22.8449 22.0962C23.0517 22.303 23.0517 22.6382 22.8449 22.8449C22.6382 23.0517 22.303 23.0517 22.0962 22.8449L17 17.7487L11.9038 22.8449C11.697 23.0517 11.3618 23.0517 11.1551 22.8449C10.9483 22.6382 10.9483 22.303 11.1551 22.0962L16.2513 17L11.1551 11.9038C10.9483 11.697 10.9483 11.3618 11.1551 11.1551C11.3618 10.9483 11.697 10.9483 11.9038 11.1551L17 16.2513L22.0962 11.1551Z"
      fill={fill}
    />
    <path
      d="M22.0962 11.1551L21.7427 10.8015L21.7427 10.8015L22.0962 11.1551ZM22.8449 11.1551L23.1985 10.8015L23.1985 10.8015L22.8449 11.1551ZM22.8449 11.9038L23.1985 12.2573L23.1985 12.2573L22.8449 11.9038ZM17.7487 17L17.3951 16.6464L17.0416 17L17.3951 17.3536L17.7487 17ZM22.8449 22.0962L22.4914 22.4498L22.4914 22.4498L22.8449 22.0962ZM22.0962 22.8449L21.7427 23.1985L21.7427 23.1985L22.0962 22.8449ZM17 17.7487L17.3536 17.3951L17 17.0416L16.6464 17.3951L17 17.7487ZM11.9038 22.8449L11.5502 22.4914L11.5502 22.4914L11.9038 22.8449ZM11.1551 22.8449L10.8015 23.1985L10.8015 23.1985L11.1551 22.8449ZM11.1551 22.0962L10.8015 21.7427L10.8015 21.7427L11.1551 22.0962ZM16.2513 17L16.6049 17.3536L16.9584 17L16.6049 16.6464L16.2513 17ZM11.1551 11.9038L11.5086 11.5502L11.5086 11.5502L11.1551 11.9038ZM11.1551 11.1551L11.5086 11.5086L11.5086 11.5086L11.1551 11.1551ZM11.9038 11.1551L12.2573 10.8015L12.2573 10.8015L11.9038 11.1551ZM17 16.2513L16.6464 16.6049L17 16.9584L17.3536 16.6049L17 16.2513ZM22.4498 11.5086C22.4613 11.4971 22.4799 11.4971 22.4914 11.5086L23.1985 10.8015C22.7965 10.3995 22.1447 10.3995 21.7427 10.8015L22.4498 11.5086ZM22.4914 11.5086C22.5029 11.5201 22.5029 11.5387 22.4914 11.5502L23.1985 12.2573C23.6005 11.8553 23.6005 11.2035 23.1985 10.8015L22.4914 11.5086ZM22.4914 11.5502L17.3951 16.6464L18.1023 17.3536L23.1985 12.2573L22.4914 11.5502ZM17.3951 17.3536L22.4914 22.4498L23.1985 21.7427L18.1023 16.6464L17.3951 17.3536ZM22.4914 22.4498C22.5029 22.4613 22.5029 22.4799 22.4914 22.4914L23.1985 23.1985C23.6005 22.7965 23.6005 22.1447 23.1985 21.7427L22.4914 22.4498ZM22.4914 22.4914C22.4799 22.5029 22.4613 22.5029 22.4498 22.4914L21.7427 23.1985C22.1447 23.6005 22.7965 23.6005 23.1985 23.1985L22.4914 22.4914ZM22.4498 22.4914L17.3536 17.3951L16.6464 18.1023L21.7427 23.1985L22.4498 22.4914ZM16.6464 17.3951L11.5502 22.4914L12.2573 23.1985L17.3536 18.1023L16.6464 17.3951ZM11.5502 22.4914C11.5387 22.5029 11.5201 22.5029 11.5086 22.4914L10.8015 23.1985C11.2035 23.6005 11.8553 23.6005 12.2573 23.1985L11.5502 22.4914ZM11.5086 22.4914C11.4971 22.4799 11.4971 22.4613 11.5086 22.4498L10.8015 21.7427C10.3995 22.1447 10.3995 22.7965 10.8015 23.1985L11.5086 22.4914ZM11.5086 22.4498L16.6049 17.3536L15.8977 16.6464L10.8015 21.7427L11.5086 22.4498ZM16.6049 16.6464L11.5086 11.5502L10.8015 12.2573L15.8977 17.3536L16.6049 16.6464ZM11.5086 11.5502C11.4971 11.5387 11.4971 11.5201 11.5086 11.5086L10.8015 10.8015C10.3995 11.2035 10.3995 11.8553 10.8015 12.2573L11.5086 11.5502ZM11.5086 11.5086C11.5201 11.4971 11.5387 11.4971 11.5502 11.5086L12.2573 10.8015C11.8553 10.3995 11.2035 10.3995 10.8015 10.8015L11.5086 11.5086ZM11.5502 11.5086L16.6464 16.6049L17.3536 15.8977L12.2573 10.8015L11.5502 11.5086ZM17.3536 16.6049L22.4498 11.5086L21.7427 10.8015L16.6464 15.8977L17.3536 16.6049Z"
      fill={fill}
    />
  </svg>
)

export default Close
