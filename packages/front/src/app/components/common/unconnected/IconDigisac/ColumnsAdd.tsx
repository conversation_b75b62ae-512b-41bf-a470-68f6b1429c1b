import React from 'react'
import { IconProps } from './Types'

const ColumnsAdd = ({ fill = '#324B7D', width = '24', height = '24' }: IconProps) => (
  <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2 5C2 3.34315 3.34315 2 5 2H10C11.6569 2 13 3.34315 13 5V19C13 20.6569 11.6569 22 10 22H5C3.34315 22 2 20.6569 2 19V5ZM5 4C4.44772 4 4 4.44772 4 5V19C4 19.5523 4.44772 20 5 20H10C10.5523 20 11 19.5523 11 19V5C11 4.44772 10.5523 4 10 4H5Z"
      fill={fill}
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M14 12C14 11.4477 14.4477 11 15 11H21C21.5523 11 22 11.4477 22 12C22 12.5523 21.5523 13 21 13H15C14.4477 13 14 12.5523 14 12Z"
      fill={fill}
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M18 8C18.5523 8 19 8.44772 19 9V15C19 15.5523 18.5523 16 18 16C17.4477 16 17 15.5523 17 15V9C17 8.44772 17.4477 8 18 8Z"
      fill={fill}
    />
  </svg>
)

export default ColumnsAdd
