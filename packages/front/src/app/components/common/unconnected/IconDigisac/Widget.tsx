import React from 'react'
import { IconProps } from './Types'

const Widget = ({ fill, width, height }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M21.2983 26.0419L20.7204 26.3071C19.2859 26.9653 17.6886 27.3333 16 27.3333C9.74077 27.3333 4.66666 22.2592 4.66666 16C4.66666 9.74078 9.74077 4.66667 16 4.66667C22.2592 4.66667 27.3333 9.74078 27.3333 16C27.3333 18.554 26.4909 20.9056 25.0679 22.7997L24.4955 23.5616L25.3033 26.7929L21.2983 26.0419ZM21.5545 28.1248C19.8636 28.9007 17.9823 29.3333 16 29.3333C8.6362 29.3333 2.66666 23.3638 2.66666 16C2.66666 8.63621 8.6362 2.66667 16 2.66667C23.3638 2.66667 29.3333 8.63621 29.3333 16C29.3333 19.0021 28.3412 21.7724 26.6669 24.001L27.4957 27.3163C27.7314 28.2591 26.9117 29.1293 25.9565 28.9502L21.5545 28.1248Z"
      fill={fill}
    />
    <circle cx="10.1818" cy="15.5151" r="2.18182" fill={fill} />
    <circle cx="16" cy="15.5152" r="2.18182" fill={fill} />
    <circle cx="21.8182" cy="15.5152" r="2.18182" fill={fill} />
  </svg>
)

export default Widget
