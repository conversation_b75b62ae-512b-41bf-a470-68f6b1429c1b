import React from 'react'
import { IconProps } from './Types'

const ArrowDown = ({ fill, width, height, className, style }: IconProps) => (
  <svg
    width={width || '24'}
    height={height || '24'}
    className={className}
    viewBox="0 0 24 24"
    fill={fill || 'none'}
    xmlns="http://www.w3.org/2000/svg"
    style={style}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.07315 8.06338C4.70895 8.41758 4.70895 8.99421 5.07315 9.3484L11.8473 15.9366C12.2084 16.2878 12.7916 16.2878 13.1527 15.9366L19.9269 9.3484C20.291 8.99421 20.291 8.41758 19.9269 8.06338C19.5658 7.71222 18.9826 7.71222 18.6215 8.06338L12.5 14.0169L6.37847 8.06338C6.0174 7.71222 5.43422 7.71222 5.07315 8.06338Z"
      fill={fill}
    />
  </svg>
)

export default ArrowDown
