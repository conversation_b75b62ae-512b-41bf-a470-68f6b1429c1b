import React from 'react'
import { IconProps } from './Types'

const SearchGlass = ({ fill = '#324B7D', width = '11', height = '10', viewBox = '0 0 11 10' }: IconProps) => (
  <svg width={width} height={height} viewBox={viewBox} fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M5 1C3.067 1 1.5 2.567 1.5 4.5C1.5 6.433 3.067 8 5 8C5.94297 8 6.79885 7.62709 7.4282 7.02069C7.44135 7.00358 7.45577 6.98712 7.47145 6.97145C7.48712 6.95577 7.50358 6.94135 7.52069 6.9282C8.12709 6.29885 8.5 5.44297 8.5 4.5C8.5 2.567 6.933 1 5 1ZM8.51595 7.30884C9.13176 6.53901 9.5 5.56251 9.5 4.5C9.5 2.01472 7.48528 0 5 0C2.51472 0 0.5 2.01472 0.5 4.5C0.5 6.98528 2.51472 9 5 9C6.06251 9 7.03901 8.63176 7.80884 8.01595L9.64645 9.85355C9.84171 10.0488 10.1583 10.0488 10.3536 9.85355C10.5488 9.65829 10.5488 9.34171 10.3536 9.14645L8.51595 7.30884Z"
      fill={fill}
    />
  </svg>
)

export default SearchGlass
