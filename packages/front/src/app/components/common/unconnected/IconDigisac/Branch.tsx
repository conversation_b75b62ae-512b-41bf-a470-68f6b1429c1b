import React from 'react'
import { IconProps } from './Types'

const Branch = ({ fill = '#000', width = '28', height = '28', ...rest }: IconProps) => (
  <svg width={width} height={height} viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg" {...rest}>
    <path
      d="M24.4257 20.2866V17.258C24.4257 16.0483 23.9452 14.8881 23.0899 14.0327C22.2346 13.1774 21.0746 12.6968 19.865 12.6968H17.2589C16.7405 12.6968 16.2433 12.4909 15.8767 12.1243C15.5102 11.7577 15.3043 11.2605 15.3043 10.742V7.7134C15.8479 7.46507 16.3089 7.06581 16.6323 6.56312C16.9557 6.06042 17.1279 5.47541 17.1285 4.87766C17.1285 4.04815 16.7991 3.25262 16.2126 2.66607C15.6261 2.07952 14.8306 1.75 14.0012 1.75C13.1718 1.75 12.3763 2.07952 11.7898 2.66607C11.2033 3.25262 10.8738 4.04815 10.8738 4.87766C10.8738 6.13784 11.6244 7.21819 12.6981 7.7134V10.742C12.6981 11.2605 12.4922 11.7577 12.1256 12.1243C11.7591 12.4909 11.2619 12.6968 10.7435 12.6968H8.1374C6.92782 12.6968 5.76778 13.1774 4.91248 14.0327C4.05718 14.8881 3.57667 16.0483 3.57667 17.258V20.2866C2.92744 20.5827 2.39922 21.0923 2.07996 21.7305C1.76069 22.3687 1.66965 23.097 1.82199 23.7942C1.97432 24.4913 2.36085 25.1153 2.91721 25.5621C3.47357 26.009 4.16619 26.2517 4.87974 26.25C5.59256 26.2504 6.28406 26.0069 6.83942 25.56C7.39477 25.113 7.78058 24.4896 7.93276 23.7931C8.08494 23.0967 7.99435 22.3691 7.67602 21.7313C7.3577 21.0934 6.83079 20.5836 6.1828 20.2866V17.258C6.1828 16.7395 6.38873 16.2423 6.75529 15.8757C7.12185 15.5091 7.61901 15.3032 8.1374 15.3032H10.7435C11.4459 15.3032 12.1039 15.1312 12.6981 14.8471V20.2866C12.0489 20.5827 11.5207 21.0923 11.2014 21.7305C10.8821 22.3687 10.7911 23.097 10.9434 23.7942C11.0958 24.4913 11.4823 25.1153 12.0387 25.5621C12.595 26.009 13.2876 26.2517 14.0012 26.25C14.714 26.2504 15.4055 26.0069 15.9609 25.56C16.5162 25.113 16.902 24.4896 17.0542 23.7931C17.2064 23.0967 17.1158 22.3691 16.7975 21.7313C16.4792 21.0934 15.9522 20.5836 15.3043 20.2866V14.8471C15.8985 15.1312 16.5565 15.3032 17.2589 15.3032H19.865C20.3834 15.3032 20.8805 15.5091 21.2471 15.8757C21.6137 16.2423 21.8196 16.7395 21.8196 17.258V20.2866C21.2759 20.5349 20.815 20.9342 20.4916 21.4369C20.1682 21.9396 19.9959 22.5246 19.9953 23.1223C19.9953 23.9518 20.3248 24.7474 20.9113 25.3339C21.4978 25.9205 22.2932 26.25 23.1226 26.25C23.9521 26.25 24.7475 25.9205 25.334 25.3339C25.9205 24.7474 26.25 23.9518 26.25 23.1223C26.25 21.8621 25.5007 20.7818 24.4257 20.2866ZM14.0012 3.07274C14.4795 3.07274 14.9382 3.26276 15.2764 3.60101C15.6146 3.93925 15.8046 4.39801 15.8046 4.87636C15.8046 5.3547 15.6146 5.81346 15.2764 6.15171C14.9382 6.48995 14.4795 6.67997 14.0012 6.67997C13.5229 6.67997 13.0642 6.48995 12.726 6.15171C12.3878 5.81346 12.1977 5.3547 12.1977 4.87636C12.1977 4.39801 12.3878 3.93925 12.726 3.60101C13.0642 3.26276 13.5229 3.07274 14.0012 3.07274V3.07274ZM4.87974 24.9273C4.64273 24.9273 4.40805 24.8806 4.18909 24.7899C3.97013 24.6992 3.77118 24.5662 3.60359 24.3986C3.436 24.231 3.30307 24.032 3.21237 23.813C3.12167 23.5941 3.07499 23.3594 3.07499 23.1223C3.07499 22.8853 3.12167 22.6506 3.21237 22.4316C3.30307 22.2126 3.436 22.0137 3.60359 21.8461C3.77118 21.6785 3.97013 21.5455 4.18909 21.4548C4.40805 21.3641 4.64273 21.3174 4.87974 21.3174C5.35839 21.3174 5.81743 21.5076 6.15588 21.8461C6.49434 22.1845 6.68448 22.6436 6.68448 23.1223C6.68448 23.601 6.49434 24.0601 6.15588 24.3986C5.81743 24.7371 5.35839 24.9273 4.87974 24.9273V24.9273ZM14.0012 24.9273C13.7642 24.9273 13.5295 24.8806 13.3105 24.7899C13.0916 24.6992 12.8926 24.5662 12.725 24.3986C12.5575 24.231 12.4245 24.032 12.3338 23.813C12.2431 23.5941 12.1964 23.3594 12.1964 23.1223C12.1964 22.8853 12.2431 22.6506 12.3338 22.4316C12.4245 22.2126 12.5575 22.0137 12.725 21.8461C12.8926 21.6785 13.0916 21.5455 13.3105 21.4548C13.5295 21.3641 13.7642 21.3174 14.0012 21.3174C14.4798 21.3174 14.9389 21.5076 15.2773 21.8461C15.6158 22.1845 15.8059 22.6436 15.8059 23.1223C15.8059 23.601 15.6158 24.0601 15.2773 24.3986C14.9389 24.7371 14.4798 24.9273 14.0012 24.9273V24.9273ZM23.1226 24.9273C22.8856 24.9273 22.651 24.8806 22.432 24.7899C22.213 24.6992 22.0141 24.5662 21.8465 24.3986C21.6789 24.231 21.546 24.032 21.4553 23.813C21.3646 23.5941 21.3179 23.3594 21.3179 23.1223C21.3179 22.8853 21.3646 22.6506 21.4553 22.4316C21.546 22.2126 21.6789 22.0137 21.8465 21.8461C22.0141 21.6785 22.213 21.5455 22.432 21.4548C22.651 21.3641 22.8856 21.3174 23.1226 21.3174C23.6013 21.3174 24.0603 21.5076 24.3988 21.8461C24.7372 22.1845 24.9274 22.6436 24.9274 23.1223C24.9274 23.601 24.7372 24.0601 24.3988 24.3986C24.0603 24.7371 23.6013 24.9273 23.1226 24.9273V24.9273Z"
      fill={fill}
    />
  </svg>
)

export default Branch
