import React from 'react'
import { IconProps } from './Types'

const Trello = ({ width, height, stroke }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M15.8333 2.5H4.16667C3.24619 2.5 2.5 3.24619 2.5 4.16667V15.8333C2.5 16.7538 3.24619 17.5 4.16667 17.5H15.8333C16.7538 17.5 17.5 16.7538 17.5 15.8333V4.16667C17.5 3.24619 16.7538 2.5 15.8333 2.5Z"
      stroke={stroke}
      stroke-opacity="0.82"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M8.33333 5.83333H5.83333V13.3333H8.33333V5.83333Z"
      stroke={stroke}
      stroke-opacity="0.82"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M14.1667 5.83333H11.6667V10H14.1667V5.83333Z"
      stroke={stroke}
      stroke-opacity="0.82"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
)

export default Trello
