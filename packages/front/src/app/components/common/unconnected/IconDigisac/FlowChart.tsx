import React from 'react'
import { IconProps } from './Types'

const FlowChart = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24.5108 21.1319V18.6596C24.5108 17.6721 24.1185 16.725 23.4203 16.0267C22.7221 15.3285 21.7752 14.9362 20.7877 14.9362H18.6603C18.2371 14.9362 17.8313 14.768 17.532 14.4688C17.2328 14.1695 17.0647 13.7636 17.0647 13.3404V10.8681C17.5085 10.6654 17.8848 10.3394 18.1488 9.92907C18.4128 9.51871 18.5534 9.04115 18.5539 8.55319C18.5539 7.87604 18.2849 7.22663 17.8062 6.74781C17.3274 6.269 16.6781 6 16.001 6C15.3239 6 14.6745 6.269 14.1958 6.74781C13.717 7.22663 13.448 7.87604 13.448 8.55319C13.448 9.58191 14.0607 10.4638 14.9372 10.8681V13.3404C14.9372 13.7636 14.7691 14.1695 14.4699 14.4688C14.1707 14.768 13.7648 14.9362 13.3417 14.9362H11.2142C10.2268 14.9362 9.27982 15.3285 8.58161 16.0267C7.88341 16.725 7.49116 17.6721 7.49116 18.6596V21.1319C6.96118 21.3736 6.52998 21.7896 6.26935 22.3106C6.00873 22.8316 5.93441 23.4261 6.05876 23.9952C6.18312 24.5644 6.49866 25.0737 6.95283 25.4385C7.407 25.8032 7.9724 26.0014 8.55489 26C9.13678 26.0003 9.70127 25.8015 10.1546 25.4367C10.608 25.0719 10.9229 24.5629 11.0471 23.9944C11.1714 23.4259 11.0974 22.8319 10.8376 22.3112C10.5777 21.7905 10.1476 21.3744 9.61861 21.1319V18.6596C9.61861 18.2364 9.78672 17.8305 10.086 17.5312C10.3852 17.2319 10.791 17.0638 11.2142 17.0638H13.3417C13.915 17.0638 14.4522 16.9234 14.9372 16.6915V21.1319C14.4073 21.3736 13.9761 21.7896 13.7154 22.3106C13.4548 22.8316 13.3805 23.4261 13.5048 23.9952C13.6292 24.5644 13.9447 25.0737 14.3989 25.4385C14.8531 25.8032 15.4185 26.0014 16.001 26C16.5829 26.0003 17.1474 25.8015 17.6007 25.4367C18.0541 25.0719 18.369 24.5629 18.4932 23.9944C18.6175 23.4259 18.5435 22.8319 18.2837 22.3112C18.0238 21.7905 17.5937 21.3744 17.0647 21.1319V16.6915C17.5498 16.9234 18.0869 17.0638 18.6603 17.0638H20.7877C21.2109 17.0638 21.6168 17.2319 21.916 17.5312C22.2152 17.8305 22.3833 18.2364 22.3833 18.6596V21.1319C21.9395 21.3346 21.5632 21.6606 21.2992 22.0709C21.0352 22.4813 20.8946 22.9588 20.8941 23.4468C20.8941 24.124 21.1631 24.7734 21.6419 25.2522C22.1206 25.731 22.77 26 23.4471 26C24.1241 26 24.7735 25.731 25.2523 25.2522C25.731 24.7734 26 24.124 26 23.4468C26 22.4181 25.3884 21.5362 24.5108 21.1319ZM16.001 7.07979C16.3914 7.07979 16.7659 7.23491 17.042 7.51103C17.3181 7.78714 17.4732 8.16164 17.4732 8.55213C17.4732 8.94262 17.3181 9.31711 17.042 9.59323C16.7659 9.86935 16.3914 10.0245 16.001 10.0245C15.6105 10.0245 15.2361 9.86935 14.96 9.59323C14.6839 9.31711 14.5288 8.94262 14.5288 8.55213C14.5288 8.16164 14.6839 7.78714 14.96 7.51103C15.2361 7.23491 15.6105 7.07979 16.001 7.07979ZM8.55489 24.9202C8.36142 24.9202 8.16984 24.8821 7.99109 24.8081C7.81235 24.734 7.64994 24.6255 7.51313 24.4887C7.37633 24.3518 7.26781 24.1894 7.19377 24.0106C7.11973 23.8319 7.08163 23.6403 7.08163 23.4468C7.08163 23.2533 7.11973 23.0617 7.19377 22.883C7.26781 22.7042 7.37633 22.5418 7.51313 22.4049C7.64994 22.2681 7.81235 22.1596 7.99109 22.0856C8.16984 22.0115 8.36142 21.9734 8.55489 21.9734C8.94562 21.9734 9.32035 22.1286 9.59664 22.4049C9.87293 22.6813 10.0281 23.056 10.0281 23.4468C10.0281 23.8376 9.87293 24.2123 9.59664 24.4887C9.32035 24.765 8.94562 24.9202 8.55489 24.9202ZM16.001 24.9202C15.8075 24.9202 15.6159 24.8821 15.4372 24.8081C15.2584 24.734 15.096 24.6255 14.9592 24.4887C14.8224 24.3518 14.7139 24.1894 14.6399 24.0106C14.5658 23.8319 14.5277 23.6403 14.5277 23.4468C14.5277 23.2533 14.5658 23.0617 14.6399 22.883C14.7139 22.7042 14.8224 22.5418 14.9592 22.4049C15.096 22.2681 15.2584 22.1596 15.4372 22.0856C15.6159 22.0115 15.8075 21.9734 16.001 21.9734C16.3917 21.9734 16.7664 22.1286 17.0427 22.4049C17.319 22.6813 17.4742 23.056 17.4742 23.4468C17.4742 23.8376 17.319 24.2123 17.0427 24.4887C16.7664 24.765 16.3917 24.9202 16.001 24.9202ZM23.4471 24.9202C23.2536 24.9202 23.062 24.8821 22.8833 24.8081C22.7045 24.734 22.5421 24.6255 22.4053 24.4887C22.2685 24.3518 22.16 24.1894 22.0859 24.0106C22.0119 23.8319 21.9738 23.6403 21.9738 23.4468C21.9738 23.2533 22.0119 23.0617 22.0859 22.883C22.16 22.7042 22.2685 22.5418 22.4053 22.4049C22.5421 22.2681 22.7045 22.1596 22.8833 22.0856C23.062 22.0115 23.2536 21.9734 23.4471 21.9734C23.8378 21.9734 24.2125 22.1286 24.4888 22.4049C24.7651 22.6813 24.9203 23.056 24.9203 23.4468C24.9203 23.8376 24.7651 24.2123 24.4888 24.4887C24.2125 24.765 23.8378 24.9202 23.4471 24.9202Z"
      fill={fill}
    />
  </svg>
)

export default FlowChart
