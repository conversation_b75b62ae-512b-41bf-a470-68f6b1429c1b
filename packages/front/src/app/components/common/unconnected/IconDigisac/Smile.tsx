import React from 'react'
import { IconProps } from './Types'

const IconSmile = ({ fill = '#000000', height = '21', width = '21', ...rest }: IconProps) => (
  <svg width={width} height={height} viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg" {...rest}>
    <g clipPath="url(#clip0_4642_626)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.0877 3.00001C5.94561 3.00001 2.58775 6.35787 2.58775 10.5C2.58775 14.6421 5.94561 18 10.0877 18C14.2299 18 17.5877 14.6421 17.5877 10.5C17.5877 6.35787 14.2299 3.00001 10.0877 3.00001ZM0.921082 10.5C0.921082 5.4374 5.02514 1.33334 10.0877 1.33334C15.1504 1.33334 19.2544 5.4374 19.2544 10.5C19.2544 15.5626 15.1504 19.6667 10.0877 19.6667C5.02514 19.6667 0.921082 15.5626 0.921082 10.5ZM6.75441 8.00001C6.75441 7.53977 7.12751 7.16668 7.58775 7.16668H7.59608C8.05632 7.16668 8.42941 7.53977 8.42941 8.00001C8.42941 8.46025 8.05632 8.83334 7.59608 8.83334H7.58775C7.12751 8.83334 6.75441 8.46025 6.75441 8.00001ZM11.7544 8.00001C11.7544 7.53977 12.1275 7.16668 12.5877 7.16668H12.5961C13.0563 7.16668 13.4294 7.53977 13.4294 8.00001C13.4294 8.46025 13.0563 8.83334 12.5961 8.83334H12.5877C12.1275 8.83334 11.7544 8.46025 11.7544 8.00001Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.41846 11.6632C7.14165 11.298 6.62144 11.2247 6.25441 11.5C5.88622 11.7762 5.81161 12.2985 6.08775 12.6667L6.75441 12.1667C6.08775 12.6667 6.08754 12.6664 6.08775 12.6667L6.08859 12.6678L6.08955 12.6691L6.09183 12.6721L6.09784 12.6799L6.11559 12.7026C6.12996 12.7208 6.14942 12.7449 6.17388 12.774C6.22275 12.8322 6.29191 12.9111 6.38062 13.0037C6.55735 13.1881 6.81561 13.431 7.14969 13.674C7.81464 14.1576 8.81647 14.6667 10.0877 14.6667C11.359 14.6667 12.3609 14.1576 13.0258 13.674C13.3599 13.431 13.6181 13.1881 13.7949 13.0037C13.8836 12.9111 13.9527 12.8322 14.0016 12.774C14.0261 12.7449 14.0455 12.7208 14.0599 12.7026L14.0777 12.6799L14.0837 12.6721L14.0859 12.6691L14.0869 12.6678C14.0871 12.6675 14.0877 12.6667 13.4211 12.1667L14.0877 12.6667C14.3639 12.2985 14.2893 11.7762 13.9211 11.5C13.5541 11.2247 13.0338 11.298 12.757 11.6632L12.7532 11.6681C12.7482 11.6744 12.7388 11.6861 12.725 11.7026C12.6974 11.7355 12.6526 11.7868 12.5916 11.8505C12.4688 11.9786 12.2844 12.1524 12.0455 12.3261C11.5646 12.6758 10.8998 13 10.0877 13C9.27569 13 8.61086 12.6758 8.12997 12.3261C7.89114 12.1524 7.70669 11.9786 7.58394 11.8505C7.5229 11.7868 7.47813 11.7355 7.45051 11.7026C7.43672 11.6861 7.42729 11.6744 7.42233 11.6681L7.41846 11.6632Z"
        fill={fill}
      />
    </g>
    <defs>
      <clipPath id="clip0_4642_626">
        <rect width={20} height={20} fill="white" transform="translate(0.0877686 0.5)" />
      </clipPath>
    </defs>
  </svg>
)

export default IconSmile
