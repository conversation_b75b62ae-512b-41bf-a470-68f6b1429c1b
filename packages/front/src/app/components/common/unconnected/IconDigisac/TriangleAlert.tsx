import React from 'react'
import { IconProps } from './Types'

const TriangleAlert = ({ fill, width, height }: IconProps) => (
  <svg width={width} height={height} viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M7.66646 4.99993V7.66659M7.66646 10.3333H7.67312M14.1531 10.9999L8.81979 1.66659C8.7035 1.4614 8.53486 1.29072 8.33107 1.17197C8.12729 1.05323 7.89565 0.990662 7.65979 0.990662C7.42393 0.990662 7.19229 1.05323 6.98851 1.17197C6.78472 1.29072 6.61608 1.4614 6.49979 1.66659L1.16646 10.9999C1.04891 11.2035 0.987276 11.4345 0.987796 11.6696C0.988317 11.9047 1.05098 12.1354 1.16942 12.3385C1.28787 12.5415 1.45789 12.7097 1.66225 12.8258C1.86662 12.942 2.09806 13.002 2.33312 12.9999H12.9998C13.2337 12.9997 13.4635 12.9379 13.666 12.8208C13.8685 12.7037 14.0366 12.5353 14.1535 12.3327C14.2703 12.13 14.3318 11.9002 14.3318 11.6663C14.3317 11.4323 14.2701 11.2025 14.1531 10.9999Z"
      stroke="#814F12"
      stroke-width="1.33333"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
)

export default TriangleAlert
