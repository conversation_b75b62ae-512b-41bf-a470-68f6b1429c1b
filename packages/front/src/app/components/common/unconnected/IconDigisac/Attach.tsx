import React from 'react'
import { IconProps } from './Types'

const Attach = ({ fill, width, height }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.6342 6.63447C20.9151 6.29125 22.2798 6.47093 23.4283 7.13397C24.5767 7.79701 25.4147 8.88911 25.7579 10.17C26.1011 11.4509 25.9214 12.8157 25.2584 13.9641L20.2584 22.6244C19.3301 24.2321 17.8012 25.4053 16.0079 25.8858C14.2147 26.3663 12.304 26.1148 10.6962 25.1865C9.08842 24.2583 7.91523 22.7293 7.43473 20.9361C6.95423 19.1428 7.20578 17.2321 8.13403 15.6244L12.179 8.61821C12.4552 8.13991 13.0668 7.97604 13.5451 8.25218C14.0234 8.52832 14.1872 9.13991 13.9111 9.61821L9.86608 16.6244C9.20304 17.7728 9.02337 19.1376 9.36658 20.4184C9.7098 21.6993 10.5478 22.7914 11.6962 23.4545C12.8446 24.1175 14.2094 24.2972 15.4903 23.954C16.7712 23.6108 17.8633 22.7728 18.5263 21.6244L23.5263 12.9641C23.9242 12.275 24.032 11.4562 23.826 10.6876C23.6201 9.9191 23.1173 9.26385 22.4283 8.86602C21.7392 8.4682 20.9203 8.36039 20.1518 8.56632C19.3833 8.77225 18.728 9.27505 18.3302 9.9641L13.7392 17.9159C13.6735 18.0297 13.6309 18.1552 13.6138 18.2854C13.5966 18.4156 13.6053 18.5479 13.6393 18.6748C13.6733 18.8016 13.7319 18.9205 13.8119 19.0247C13.8918 19.1289 13.9915 19.2163 14.1052 19.282C14.3349 19.4146 14.6079 19.4505 14.864 19.3819C15.1202 19.3132 15.3386 19.1456 15.4712 18.9159L19.0622 12.6961C19.3384 12.2179 19.95 12.054 20.4283 12.3301C20.9066 12.6063 21.0704 13.2179 20.7943 13.6961L17.2033 19.9159C16.8055 20.605 16.1502 21.1078 15.3817 21.3137C14.6131 21.5197 13.7943 21.4118 13.1052 21.014C12.764 20.817 12.465 20.5548 12.2252 20.2422C11.9853 19.9297 11.8094 19.5729 11.7074 19.1924C11.6055 18.8119 11.5795 18.415 11.6309 18.0244C11.6823 17.6338 11.8102 17.2571 12.0071 16.9159L16.5981 8.9641C17.2612 7.81568 18.3533 6.97769 19.6342 6.63447Z"
      fill={fill}
    />
  </svg>
)

export default Attach
