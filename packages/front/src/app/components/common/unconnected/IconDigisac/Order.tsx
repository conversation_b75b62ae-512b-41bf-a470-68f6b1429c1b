import React from 'react'
import { IconProps } from './Types'

const Order = ({ width = '14', height = '12', fill = '#324B7D', viewBox = '0 0 14 12' }: IconProps) => (
  <svg width={width} height={height} viewBox={viewBox} fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M0.52827 8.19526C0.788619 7.93491 1.21073 7.93491 1.47108 8.19526L3.66634 10.3905L5.8616 8.19526C6.12195 7.93491 6.54406 7.93491 6.80441 8.19526C7.06476 8.45561 7.06476 8.87772 6.80441 9.13807L4.13775 11.8047C3.8774 12.0651 3.45529 12.0651 3.19494 11.8047L0.52827 9.13807C0.26792 8.87772 0.26792 8.45561 0.52827 8.19526Z"
      fill={fill}
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M3.66634 0C4.03453 0 4.33301 0.298477 4.33301 0.666667V11.3333C4.33301 11.7015 4.03453 12 3.66634 12C3.29815 12 2.99967 11.7015 2.99967 11.3333V0.666667C2.99967 0.298477 3.29815 0 3.66634 0Z"
      fill={fill}
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M5.66634 0.666667C5.66634 0.298477 5.96482 0 6.33301 0H12.9997C13.3679 0 13.6663 0.298477 13.6663 0.666667C13.6663 1.03486 13.3679 1.33333 12.9997 1.33333H6.33301C5.96482 1.33333 5.66634 1.03486 5.66634 0.666667Z"
      fill={fill}
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M5.66634 3.33333C5.66634 2.96514 5.96482 2.66667 6.33301 2.66667H10.9997C11.3679 2.66667 11.6663 2.96514 11.6663 3.33333C11.6663 3.70152 11.3679 4 10.9997 4H6.33301C5.96482 4 5.66634 3.70152 5.66634 3.33333Z"
      fill={fill}
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M5.66634 6C5.66634 5.63181 5.96482 5.33333 6.33301 5.33333H8.99967C9.36786 5.33333 9.66634 5.63181 9.66634 6C9.66634 6.36819 9.36786 6.66667 8.99967 6.66667H6.33301C5.96482 6.66667 5.66634 6.36819 5.66634 6Z"
      fill={fill}
    />
  </svg>
)

export default Order
