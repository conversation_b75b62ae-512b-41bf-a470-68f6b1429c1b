import React from 'react'
import { IconProps } from './Types'

const Warning = ({ fill, width, height }: IconProps) => (
  <svg width={width || 22} height={height || 24} viewBox="9 10 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M29.375 12.7292C30.0242 12.7292 30.6469 12.9871 31.1059 13.4461C31.565 13.9052 31.8229 14.5279 31.8229 15.1771V34.7604C31.8229 35.4096 31.565 36.0323 31.1059 36.4914C30.6469 36.9504 30.0242 37.2083 29.375 37.2083H16.6458C15.9966 37.2083 15.374 36.9504 14.9149 36.4914C14.4558 36.0323 14.1979 35.4096 14.1979 34.7604V15.1771C14.1979 14.5279 14.4558 13.9052 14.9149 13.4461C15.374 12.9871 15.9966 12.7292 16.6458 12.7292H29.375ZM16.6458 10.2812C15.3474 10.2813 14.1021 10.7971 13.184 11.7152C12.2658 12.6334 11.75 13.8786 11.75 15.1771V34.7604C11.75 36.0589 12.2658 37.3041 13.184 38.2223C14.1021 39.1404 15.3474 39.6562 16.6458 39.6562H29.375C30.6735 39.6562 31.9187 39.1404 32.8369 38.2223C33.755 37.3041 34.2708 36.0589 34.2708 34.7604V15.1771C34.2708 13.8786 33.755 12.6334 32.8369 11.7152C31.9187 10.7971 30.6735 10.2813 29.375 10.2812H16.6458Z"
      fill={fill}
    />

    <path
      d="M24.4587 25.5379C24.4276 26.0843 23.9875 26.511 23.4551 26.511H22.1351C21.6027 26.511 21.1626 26.0843 21.1314 25.5379L20.7427 18.719C20.7088 18.1254 21.1679 17.625 21.7463 17.625H23.8438C24.4222 17.625 24.8813 18.1254 24.8475 18.719L24.4587 25.5379ZM20.5625 30.2656C20.5625 29.6681 20.7706 29.1796 21.1869 28.8001C21.611 28.4125 22.1373 28.2187 22.7656 28.2187C23.394 28.2187 23.9163 28.4125 24.3326 28.8001C24.7567 29.1796 24.9688 29.6681 24.9688 30.2656C24.9688 30.8631 24.7567 31.3557 24.3326 31.7432C23.9163 32.1227 23.394 32.3125 22.7656 32.3125C22.1373 32.3125 21.611 32.1227 21.1869 31.7432C20.7706 31.3557 20.5625 30.8631 20.5625 30.2656Z"
      fill={fill}
    />
  </svg>
)

export default Warning
