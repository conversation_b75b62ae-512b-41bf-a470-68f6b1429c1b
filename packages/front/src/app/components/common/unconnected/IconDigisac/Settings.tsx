import React from 'react'
import { IconProps } from './Types'

const Settings = ({ width, height, fill, className }: IconProps) => (
  <svg
    width={width || 28}
    height={height || 28}
    className={className}
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1 1H10.7222V10.7222H1V1ZM1 16.2778H10.7222V26H1V16.2778ZM16.2778 1H26V10.7222H16.2778V1Z"
      stroke="white"
      strokeWidth="1.78571"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={fill || 'fill'}
    />
    <path
      d="M16.9722 21.4861H25.9999"
      stroke="white"
      strokeWidth="1.38889"
      strokeLinecap="round"
      fill={fill || 'fill'}
    />
    <path
      d="M21.4861 16.9722L21.4861 26"
      stroke="white"
      strokeWidth="1.38889"
      strokeLinecap="round"
      fill={fill || 'fill'}
    />
  </svg>
)

export default Settings
