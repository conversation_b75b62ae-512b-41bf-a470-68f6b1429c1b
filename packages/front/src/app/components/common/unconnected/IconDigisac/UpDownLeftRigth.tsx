import React from 'react'
import { IconProps } from './Types'

const UpDownLeftRigth = ({ fill = '#24272D', width = '24', height = '24' }: IconProps) => (
  <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_4601_26349)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M11.5279 4.86201C11.7883 4.60166 12.2104 4.60166 12.4708 4.86201L14.4708 6.86201C14.7311 7.12236 14.7311 7.54447 14.4708 7.80482C14.2104 8.06517 13.7883 8.06517 13.5279 7.80482L12.666 6.94289V11.3334H17.0565L16.1946 10.4715C15.9343 10.2111 15.9343 9.78903 16.1946 9.52868C16.455 9.26833 16.8771 9.26833 17.1374 9.52868L19.1374 11.5287C19.3978 11.789 19.3978 12.2111 19.1374 12.4715L17.1374 14.4715C16.8771 14.7318 16.455 14.7318 16.1946 14.4715C15.9343 14.2111 15.9343 13.789 16.1946 13.5287L17.0565 12.6667H12.666V17.0573L13.5279 16.1953C13.7883 15.935 14.2104 15.935 14.4708 16.1953C14.7311 16.4557 14.7311 16.8778 14.4708 17.1382L12.4708 19.1382C12.2104 19.3985 11.7883 19.3985 11.5279 19.1382L9.52794 17.1382C9.26759 16.8778 9.26759 16.4557 9.52794 16.1953C9.78829 15.935 10.2104 15.935 10.4708 16.1953L11.3327 17.0573V12.6667H6.94216L7.80409 13.5287C8.06444 13.789 8.06444 14.2111 7.80409 14.4715C7.54374 14.7318 7.12163 14.7318 6.86128 14.4715L4.86128 12.4715C4.60093 12.2111 4.60093 11.789 4.86128 11.5287L6.86128 9.52868C7.12163 9.26833 7.54374 9.26833 7.80409 9.52868C8.06444 9.78903 8.06444 10.2111 7.80409 10.4715L6.94216 11.3334H11.3327V6.94289L10.4708 7.80482C10.2104 8.06517 9.78829 8.06517 9.52794 7.80482C9.26759 7.54447 9.26759 7.12236 9.52794 6.86201L11.5279 4.86201Z"
        fill={fill}
      />
    </g>
    <defs>
      <clipPath id="clip0_4601_26349">
        <rect width="16" height="16" fill="white" transform="translate(4 4)" />
      </clipPath>
    </defs>
  </svg>
)

export default UpDownLeftRigth
