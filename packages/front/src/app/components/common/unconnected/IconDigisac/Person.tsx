import React from 'react'
import { IconProps } from './Types'

const Person = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.9999 7.5C13.6652 7.5 11.7666 9.3985 11.7666 11.7333C11.7666 14.0682 13.6651 15.9667 15.9999 15.9667C18.3348 15.9667 20.2333 14.0681 20.2333 11.7333C20.2333 9.39856 18.3348 7.5 15.9999 7.5ZM13.8333 11.7333C13.8333 10.5396 14.8062 9.5667 15.9999 9.5667C17.1936 9.5667 18.1666 10.5396 18.1666 11.7333C18.1666 12.927 17.1937 13.9 15.9999 13.9C14.8062 13.9 13.8333 12.927 13.8333 11.7333ZM16 16.0333C11.9009 16.0333 8.56665 19.3676 8.56665 23.4666C8.56665 24.0372 9.02946 24.5 9.6 24.5C10.1705 24.5 10.6333 24.0372 10.6333 23.4666C10.6333 20.5076 13.0409 18.1 16 18.1C18.959 18.1 21.3666 20.5076 21.3666 23.4666C21.3666 24.0372 21.8294 24.5 22.3999 24.5C22.9705 24.5 23.4333 24.0372 23.4333 23.4666C23.4333 19.3676 20.0991 16.0333 16 16.0333Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.4666 11.7666C21.7206 11.7666 20.2999 13.1873 20.2999 14.9332C20.2999 16.6792 21.7206 18.0999 23.4666 18.0999C25.2126 18.0999 26.6332 16.6792 26.6332 14.9332C26.6332 13.1873 25.2126 11.7666 23.4666 11.7666ZM22.3666 14.9332C22.3666 14.3272 22.8606 13.8332 23.4666 13.8332C24.0726 13.8332 24.5666 14.3272 24.5666 14.9332C24.5666 15.5392 24.0725 16.0332 23.4666 16.0332C22.8606 16.0332 22.3666 15.5392 22.3666 14.9332ZM23.4666 18.1666C22.4976 18.1666 21.5482 18.4315 20.7212 18.9322L20.7212 18.9322L20.7189 18.9336C20.2358 19.2297 20.0769 19.8626 20.3734 20.3518L20.3734 20.3518L20.375 20.3544C20.6723 20.8385 21.3041 20.9958 21.7929 20.6995L21.7934 20.6993C22.2957 20.3942 22.8735 20.2332 23.4666 20.2332C25.2491 20.2332 26.6999 21.684 26.6999 23.4666C26.6999 24.0371 27.1627 24.4999 27.7333 24.4999C28.3038 24.4999 28.7666 24.0371 28.7666 23.4666C28.7666 20.544 26.3891 18.1666 23.4666 18.1666Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.30004 11.7666C10.046 11.7666 11.4667 13.1873 11.4667 14.9332C11.4667 16.6792 10.046 18.0999 8.30004 18.0999C6.55405 18.0999 5.1334 16.6792 5.1334 14.9332C5.1334 13.1873 6.55405 11.7666 8.30004 11.7666ZM9.40003 14.9332C9.40003 14.3272 8.90604 13.8332 8.30004 13.8332C7.69403 13.8332 7.20004 14.3272 7.20004 14.9332C7.20004 15.5392 7.69407 16.0332 8.30004 16.0332C8.90604 16.0332 9.40003 15.5392 9.40003 14.9332ZM8.30001 18.1666C9.26903 18.1666 10.2184 18.4315 11.0454 18.9322L11.0454 18.9322L11.0477 18.9336C11.5308 19.2297 11.6897 19.8626 11.3932 20.3518L11.3932 20.3518L11.3916 20.3544C11.0943 20.8385 10.4625 20.9958 9.97369 20.6995L9.97323 20.6993C9.47094 20.3942 8.89312 20.2332 8.30001 20.2332C6.51751 20.2332 5.06668 21.684 5.06668 23.4666C5.06668 24.0371 4.60387 24.4999 4.03333 24.4999C3.46278 24.4999 2.99998 24.0371 2.99998 23.4666C3.00004 20.544 5.37748 18.1666 8.30001 18.1666Z"
      fill={fill}
    />
  </svg>
)

export default Person
