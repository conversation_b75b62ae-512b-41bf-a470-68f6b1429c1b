import React from 'react'

import { IconProps } from './Types'

const Clips = ({ width = '24', height = '24', fill = '#FFFFFF' }: IconProps) => (
  <svg width={width} height={height} viewBox="0 0 33 34" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.545 8.21458C15.4091 4.98984 18.7237 3.07613 21.9485 3.9402C25.1732 4.80427 27.0869 8.1189 26.2228 11.3436L22.5249 25.1444C22.0177 27.0374 20.8314 28.6775 19.1924 29.7519C15.2087 32.3631 9.83926 30.9244 7.6949 26.6711C6.81262 24.9212 6.60534 22.9077 7.11257 21.0147L10.8875 6.9266C11.0015 6.501 11.439 6.24843 11.8646 6.36247C12.2902 6.47651 12.5427 6.91397 12.4287 7.33957L8.6538 21.4276C8.24802 22.9421 8.41385 24.5528 9.11967 25.9528C10.8352 29.3554 15.1307 30.5064 18.3177 28.4174C19.6289 27.5579 20.5779 26.2458 20.9837 24.7314L24.6816 10.9307C25.3176 8.55713 23.909 6.11742 21.5355 5.48144C19.1619 4.84545 16.7222 6.25401 16.0862 8.62755L12.6472 21.4622C12.4872 22.0593 12.5526 22.6943 12.8309 23.2462C13.5072 24.5876 15.2006 25.0414 16.457 24.2178C16.9739 23.879 17.348 23.3617 17.508 22.7647L21.0241 9.64269C21.1381 9.21709 21.5756 8.96452 22.0012 9.07856C22.4268 9.1926 22.6793 9.63006 22.5653 10.0557L19.0493 23.1777C18.7878 24.1533 18.1765 24.9986 17.3317 25.5523C15.2786 26.8981 12.5113 26.1566 11.4061 23.9645C10.9514 23.0626 10.8445 22.0249 11.106 21.0493L14.545 8.21458Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.2785 8.1433C15.1819 4.77185 18.6473 2.77108 22.0188 3.67446C25.3902 4.57784 27.391 8.04326 26.4876 11.4147L22.7897 25.2155C22.265 27.1737 21.0378 28.8703 19.3423 29.9818C15.2213 32.683 9.66676 31.1947 7.44848 26.7948C6.5358 24.9845 6.32136 22.9016 6.84608 20.9434L10.621 6.85531C10.7743 6.28301 11.3626 5.94338 11.9349 6.09673C12.5072 6.25008 12.8468 6.83833 12.6935 7.41064L8.91858 21.4987C8.53028 22.9479 8.68896 24.4892 9.36437 25.8289C11.0059 29.0849 15.1164 30.1863 18.166 28.1873C19.4208 27.3648 20.3289 26.1093 20.7172 24.6601L24.4151 10.8594C25.0118 8.63255 23.6903 6.34364 21.4634 5.74696C19.2366 5.15028 16.9477 6.47178 16.351 8.69862L12.912 21.5333C12.7695 22.0651 12.8277 22.6307 13.0756 23.1223C13.6779 24.3171 15.1863 24.7213 16.3054 23.9877C16.7658 23.6859 17.099 23.2252 17.2415 22.6934L20.7576 9.57141C20.9109 8.9991 21.4992 8.65947 22.0715 8.81282C22.6438 8.96617 22.9834 9.55443 22.8301 10.1267L19.314 23.2487C19.0351 24.2896 18.3829 25.1914 17.4816 25.7822C15.2912 27.218 12.3388 26.4269 11.1597 24.0882C10.6745 23.126 10.5606 22.0189 10.8395 20.978L14.2785 8.1433ZM21.8764 4.20572C18.7984 3.38096 15.6345 5.20761 14.8098 8.28565L11.3707 21.1203C11.1268 22.0307 11.2265 22.999 11.6508 23.8406C12.682 25.8861 15.2643 26.578 17.1801 25.3222C17.9683 24.8055 18.5388 24.0168 18.7828 23.1064L22.2988 9.98438C22.3735 9.70548 22.208 9.41881 21.9291 9.34408C21.6502 9.26935 21.3636 9.43486 21.2888 9.71376L17.7728 22.8358C17.5953 23.498 17.1803 24.0718 16.6069 24.4477C15.2132 25.3613 13.3347 24.8579 12.5844 23.3699C12.2758 22.7577 12.2033 22.0532 12.3807 21.391L15.8198 8.55627C16.4951 6.03602 19.0856 4.5404 21.6058 5.2157C24.126 5.89099 25.6217 8.48149 24.9464 11.0017L21.2485 24.8025C20.8252 26.3821 19.8353 27.7508 18.4676 28.6473C15.1433 30.8263 10.6627 29.6257 8.87325 26.0765C8.13702 24.6162 7.96405 22.936 8.38732 21.3564L12.1622 7.26829C12.2369 6.98939 12.0714 6.70272 11.7925 6.62799C11.5136 6.55326 11.227 6.71877 11.1522 6.99766L7.37734 21.0857C6.88759 22.9135 7.08773 24.8576 7.9396 26.5472C10.01 30.6539 15.1944 32.043 19.0408 29.5218C20.6233 28.4844 21.7687 26.9009 22.2584 25.0731L25.9564 11.2724C26.7811 8.19432 24.9545 5.03048 21.8764 4.20572Z"
      fill={fill}
    />
  </svg>
)

export default Clips
