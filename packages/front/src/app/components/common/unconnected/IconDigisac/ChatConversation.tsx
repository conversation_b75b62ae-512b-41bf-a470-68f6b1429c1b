import React from 'react'
import { IconProps } from './Types'

const ChatConversation = ({ fill, width, height }: IconProps) => (
  <svg width={width || 116} height={height || 116} viewBox="0 0 116 116" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M90.419 66.773L89.4136 67.2344C86.9174 68.3798 84.1382 69.0201 81.2 69.0201C70.3089 69.0201 61.48 60.1912 61.48 49.3001C61.48 38.409 70.3089 29.5801 81.2 29.5801C92.091 29.5801 100.92 38.409 100.92 49.3001C100.92 53.7441 99.4541 57.8358 96.9781 61.1315L95.9822 62.4572L97.3878 68.0797L90.419 66.773ZM90.8649 70.3973C87.9226 71.7474 84.6492 72.5001 81.2 72.5001C68.387 72.5001 58 62.1131 58 49.3001C58 36.4871 68.387 26.1001 81.2 26.1001C94.013 26.1001 104.4 36.4871 104.4 49.3001C104.4 54.5237 102.674 59.344 99.7604 63.2218L101.203 68.9905C101.613 70.631 100.186 72.145 98.5243 71.8334L90.8649 70.3973Z"
      fill={fill || '#868FA1'}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M57.93 36.7892C58.3421 35.9287 58.1416 34.8853 57.3635 34.3331C52.6302 30.9744 46.8457 29 40.6 29C24.5837 29 11.6 41.9837 11.6 58C11.6 64.5295 13.7579 70.5549 17.3995 75.4021L15.5968 82.613C15.0841 84.6636 16.867 86.5562 18.9446 86.1666L28.5189 84.3715C32.1967 86.0591 36.2885 87 40.6 87C50.9592 87 60.0497 81.5684 65.1791 73.3978C65.6868 72.589 65.4668 71.5429 64.7556 70.9057C63.7284 69.9855 62.054 70.2254 61.3043 71.3828C56.9124 78.1634 49.2801 82.65 40.6 82.65C36.9272 82.65 33.4533 81.8496 30.333 80.4178L29.0763 79.8411L20.3653 81.4745L22.1223 74.4464L20.8773 72.7893C17.7823 68.6696 15.95 63.555 15.95 58C15.95 44.3862 26.9862 33.35 40.6 33.35C45.7814 33.35 50.5894 34.9486 54.5577 37.6796C55.6936 38.4613 57.3344 38.0329 57.93 36.7892Z"
      fill={fill || '#868FA1'}
    />
  </svg>
)

export default ChatConversation
