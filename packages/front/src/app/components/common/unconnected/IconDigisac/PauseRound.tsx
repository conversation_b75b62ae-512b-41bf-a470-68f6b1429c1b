import React from 'react'
import { IconProps } from './Types'

const PauseRound = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 24}
    height={height || 24}
    className={className}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      stroke={fill}
      strokeWidth="1"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M10.0001 17.5C14.1422 17.5 17.5001 14.1422 17.5001 10C17.5001 5.85788 14.1422 2.50002 10.0001 2.50002C5.85795 2.50002 2.50008 5.85788 2.50008 10C2.50008 14.1422 5.85795 17.5 10.0001 17.5ZM10.0001 18.3334C14.6025 18.3334 18.3334 14.6024 18.3334 10C18.3334 5.39765 14.6025 1.66669 10.0001 1.66669C5.39771 1.66669 1.66675 5.39765 1.66675 10C1.66675 14.6024 5.39771 18.3334 10.0001 18.3334Z"
    />

    <path
      fill={fill}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M8.95 13.0001C8.95 13.1327 8.89469 13.2598 8.79623 13.3536C8.69777 13.4474 8.56424 13.5001 8.425 13.5001H7.9C7.76076 13.5001 7.62723 13.4474 7.52877 13.3536C7.43031 13.2598 7.375 13.1327 7.375 13.0001V7.00006C7.375 6.86745 7.43031 6.74028 7.52877 6.64651C7.62723 6.55274 7.76076 6.50006 7.9 6.50006H8.425C8.56424 6.50006 8.69777 6.55274 8.79623 6.64651C8.89469 6.74028 8.95 6.86745 8.95 7.00006V13.0001ZM12.625 13.0001C12.625 13.1327 12.5697 13.2598 12.4712 13.3536C12.3728 13.4474 12.2392 13.5001 12.1 13.5001H11.575C11.4358 13.5001 11.3022 13.4474 11.2038 13.3536C11.1053 13.2598 11.05 13.1327 11.05 13.0001V7.00006C11.05 6.86745 11.1053 6.74028 11.2038 6.64651C11.3022 6.55274 11.4358 6.50006 11.575 6.50006H12.1C12.2392 6.50006 12.3728 6.55274 12.4712 6.64651C12.5697 6.74028 12.625 6.86745 12.625 7.00006V13.0001Z"
    />
  </svg>
)

export default PauseRound
