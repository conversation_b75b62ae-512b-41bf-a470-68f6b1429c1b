import React from 'react'
import { IconProps } from './Types'

const ContactUnblock = ({ fill, width, height }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.5263 22.8421H8V19.6842C8 16.8421 13.5789 15.4737 16.4211 15.4737C18 15.4737 20.5263 15.8947 22.4211 16.8421C21.5789 17.1579 20.9474 17.5789 20.3158 18.1053C19.1579 17.6842 17.7895 17.4737 16.4211 17.4737C13.2632 17.4737 10 19.0526 10 19.6842V20.8421H18.7368C18.6316 21.2632 18.5263 21.7895 18.5263 22.3158V22.8421ZM28 22.3158C28 24.3158 26.3158 26 24.3158 26C22.3158 26 20.6316 24.3158 20.6316 22.3158C20.6316 20.3158 22.3158 18.6316 24.3158 18.6316C26.3158 18.6316 28 20.3158 28 22.3158ZM16.4211 8.10526C17.5789 8.10526 18.5263 9.05263 18.5263 10.2105C18.5263 11.3684 17.5789 12.3158 16.4211 12.3158C15.2632 12.3158 14.3158 11.3684 14.3158 10.2105C14.3158 9.05263 15.2632 8.10526 16.4211 8.10526ZM16.4211 6C14.1053 6 12.2105 7.89474 12.2105 10.2105C12.2105 12.5263 14.1053 14.4211 16.4211 14.4211C18.7368 14.4211 20.6316 12.5263 20.6316 10.2105C20.6316 7.89474 18.7368 6 16.4211 6Z"
      fill={fill}
    />
    <path
      d="M23.2825 23.1603L25.3991 21.0962C25.4649 21.0321 25.5486 21 25.6502 21C25.7519 21 25.8356 21.0321 25.9013 21.0962C25.9671 21.1603 26 21.242 26 21.3411C26 21.4402 25.9671 21.5219 25.9013 21.586L23.5336 23.895C23.4619 23.965 23.3782 24 23.2825 24C23.1868 24 23.1031 23.965 23.0314 23.895L22.0987 22.9854C22.0329 22.9213 22 22.8396 22 22.7405C22 22.6414 22.0329 22.5598 22.0987 22.4956C22.1644 22.4315 22.2481 22.3994 22.3498 22.3994C22.4514 22.3994 22.5351 22.4315 22.6009 22.4956L23.2825 23.1603Z"
      fill="white"
    />
  </svg>
)

export default ContactUnblock
