import React from 'react'
import { IconProps } from './Types'

const ArrowLeft = ({ fill, width, height, viewBox, className, style }: IconProps) => (
  <svg
    width={width || '24'}
    height={height || '24'}
    className={className}
    viewBox={viewBox}
    fill={fill || '#324B7D'}
    xmlns="http://www.w3.org/2000/svg"
    style={style}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.70711 0.292893C9.09763 0.683417 9.09763 1.31658 8.70711 1.70711L3.41421 7H15C15.5523 7 16 7.44772 16 8C16 8.55228 15.5523 9 15 9H3.41421L8.70711 14.2929C9.09763 14.6834 9.09763 15.3166 8.70711 15.7071C8.31658 16.0976 7.68342 16.0976 7.29289 15.7071L0.292893 8.70711C-0.0976311 8.31658 -0.0976311 7.68342 0.292893 7.29289L7.29289 0.292893C7.68342 -0.0976311 8.31658 -0.0976311 8.70711 0.292893Z"
      fill={fill}
    />
  </svg>
)

export default ArrowLeft
