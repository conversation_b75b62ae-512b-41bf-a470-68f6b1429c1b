import React from 'react'
import { IconProps } from './Types'

const XCircle = ({ fill = '#365497', width = '16', height = '16', viewBox = '0 0 16 16' }: IconProps) => (
  <svg width={width} height={height} viewBox={viewBox} fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.00033 1.99984C4.68662 1.99984 2.00033 4.68613 2.00033 7.99984C2.00033 11.3135 4.68662 13.9998 8.00033 13.9998C11.314 13.9998 14.0003 11.3135 14.0003 7.99984C14.0003 4.68613 11.314 1.99984 8.00033 1.99984ZM0.666992 7.99984C0.666992 3.94975 3.95024 0.666504 8.00033 0.666504C12.0504 0.666504 15.3337 3.94975 15.3337 7.99984C15.3337 12.0499 12.0504 15.3332 8.00033 15.3332C3.95024 15.3332 0.666992 12.0499 0.666992 7.99984ZM5.52892 5.52843C5.78927 5.26808 6.21138 5.26808 6.47173 5.52843L8.00033 7.05703L9.52892 5.52843C9.78927 5.26808 10.2114 5.26808 10.4717 5.52843C10.7321 5.78878 10.7321 6.21089 10.4717 6.47124L8.94313 7.99984L10.4717 9.52843C10.7321 9.78878 10.7321 10.2109 10.4717 10.4712C10.2114 10.7316 9.78927 10.7316 9.52892 10.4712L8.00033 8.94265L6.47173 10.4712C6.21138 10.7316 5.78927 10.7316 5.52892 10.4712C5.26857 10.2109 5.26857 9.78878 5.52892 9.52843L7.05752 7.99984L5.52892 6.47124C5.26857 6.21089 5.26857 5.78878 5.52892 5.52843Z"
      fill={fill}
    />
  </svg>
)

export default XCircle
