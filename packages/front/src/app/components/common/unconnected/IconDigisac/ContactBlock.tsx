import React from 'react'
import { IconProps } from './Types'

const ContactBlock = ({ fill, width, height }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16 7C14.9391 7 13.9217 7.42143 13.1716 8.17157C12.4214 8.92172 12 9.93913 12 11C12 12.0609 12.4214 13.0783 13.1716 13.8284C13.9217 14.5786 14.9391 15 16 15C17.0609 15 18.0783 14.5786 18.8284 13.8284C19.5786 13.0783 20 12.0609 20 11C20 9.93913 19.5786 8.92172 18.8284 8.17157C18.0783 7.42143 17.0609 7 16 7ZM16 9C16.5304 9 17.0391 9.21071 17.4142 9.58579C17.7893 9.96086 18 10.4696 18 11C18 11.5304 17.7893 12.0391 17.4142 12.4142C17.0391 12.7893 16.5304 13 16 13C15.4696 13 14.9609 12.7893 14.5858 12.4142C14.2107 12.0391 14 11.5304 14 11C14 10.4696 14.2107 9.96086 14.5858 9.58579C14.9609 9.21071 15.4696 9 16 9ZM16 16C13.33 16 8 17.33 8 20V23H17.5C17.2483 22.394 17.0899 21.7534 17.03 21.1H9.9V20C9.9 19.36 13.03 17.9 16 17.9C16.5 17.9 17 17.95 17.5 18.03C17.7566 17.3985 18.1109 16.8114 18.55 16.29C17.61 16.1 16.71 16 16 16ZM23.5 16C21 16 19 18 19 20.5C19 23 21 25 23.5 25C26 25 28 23 28 20.5C28 18 26 16 23.5 16ZM23.5 17.5C25.16 17.5 26.5 18.84 26.5 20.5C26.5 21.06 26.35 21.58 26.08 22L22 17.92C22.42 17.65 22.94 17.5 23.5 17.5ZM20.92 19L25 23.08C24.58 23.35 24.06 23.5 23.5 23.5C21.84 23.5 20.5 22.16 20.5 20.5C20.5 19.94 20.65 19.42 20.92 19Z"
      fill={fill}
    />
  </svg>
)

export default ContactBlock
