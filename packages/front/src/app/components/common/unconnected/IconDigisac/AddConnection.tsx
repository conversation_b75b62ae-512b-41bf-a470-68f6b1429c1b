import React from 'react'
import { IconProps } from './Types'

const AddConnection = ({ fill, width, height }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.1785 8.15482C19.8659 7.84226 19.442 7.66667 19 7.66667H12.3333C11.8913 7.66667 11.4674 7.84226 11.1548 8.15482C10.8423 8.46738 10.6667 8.89131 10.6667 9.33333V22.6667C10.6667 23.1087 10.8423 23.5326 11.1548 23.8452C11.4674 24.1577 11.8913 24.3333 12.3333 24.3333H19C19.442 24.3333 19.8659 24.1577 20.1785 23.8452C20.4911 23.5326 20.6667 23.1087 20.6667 22.6667V18.577C21.1616 18.9084 21.726 18.8863 22.3333 19V22.6667C22.3333 23.5507 21.9821 24.3986 21.357 25.0237C20.7319 25.6488 19.8841 26 19 26H12.3333C11.4493 26 10.6014 25.6488 9.97631 25.0237C9.35119 24.3986 9 23.5507 9 22.6667V9.33333C9 8.44928 9.35119 7.60143 9.97631 6.97631C10.6014 6.35119 11.4493 6 12.3333 6H19C19.8841 6 20.7319 6.35119 21.357 6.97631C21.9219 7.54117 22.2631 8.2879 22.3236 9.07886C21.72 9.19328 20.6667 10.0707 20.6667 9.33333C20.6667 8.89131 20.4911 8.46738 20.1785 8.15482Z"
      fill={fill}
    />
    <path
      d="M15.9999 22.6667C16.4419 22.6667 16.8659 22.4911 17.1784 22.1785C17.491 21.866 17.6666 21.442 17.6666 21C17.6666 20.558 17.491 20.1341 17.1784 19.8215C16.8659 19.5089 16.4419 19.3333 15.9999 19.3333C15.5579 19.3333 15.134 19.5089 14.8214 19.8215C14.5088 20.1341 14.3333 20.558 14.3333 21C14.3333 21.442 14.5088 21.866 14.8214 22.1785C15.134 22.4911 15.5579 22.6667 15.9999 22.6667Z"
      fill={fill}
    />
    <circle cx="21.5" cy="14.5" r="4.75" stroke={fill} strokeWidth="1.5" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M21.5 11.75H21.4914L21.4234 11.7547L21.415 11.7558C21.2654 11.7764 21.1282 11.8504 21.029 11.9642C20.9297 12.078 20.875 12.224 20.875 12.375V13.875H19.3664L19.2984 13.8797L19.29 13.8808C19.1404 13.9014 19.0032 13.9754 18.904 14.0892C18.8047 14.203 18.75 14.349 18.75 14.5V14.5086L18.7547 14.5766L18.7558 14.585C18.7764 14.7346 18.8504 14.8718 18.9642 14.971C19.078 15.0703 19.224 15.125 19.375 15.125H20.875V16.6336L20.8797 16.7016L20.8808 16.71C20.9014 16.8596 20.9754 16.9968 21.0892 17.096C21.203 17.1953 21.349 17.25 21.5 17.25L21.5086 17.25L21.5766 17.2453L21.585 17.2442C21.7346 17.2236 21.8718 17.1496 21.971 17.0358C22.0703 16.922 22.125 16.776 22.125 16.625V15.125H23.6336L23.7016 15.1203L23.71 15.1192C23.8596 15.0986 23.9968 15.0246 24.096 14.9108C24.1953 14.797 24.25 14.651 24.25 14.5L24.25 14.4914L24.2453 14.4234L24.2442 14.415C24.2236 14.2654 24.1496 14.1282 24.0358 14.029C23.922 13.9297 23.776 13.875 23.625 13.875H22.125V12.3664L22.1203 12.2984L22.1192 12.29C22.0986 12.1404 22.0246 12.0032 21.9108 11.904C21.797 11.8047 21.651 11.75 21.5 11.75Z"
      fill={fill}
    />
  </svg>
)

export default AddConnection
