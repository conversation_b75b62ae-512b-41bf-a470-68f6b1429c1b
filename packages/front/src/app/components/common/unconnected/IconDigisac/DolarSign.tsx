import React from 'react'
import { IconProps } from './Types'

const DolarSign = ({ fill = 'black', width = '12', height = '20' }: IconProps) => (
  <svg width={width} height={height} viewBox={`0 0 ${width} ${height}`} fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.99984 0C6.46007 0 6.83317 0.373096 6.83317 0.833333V3.33333H10.1665C10.6267 3.33333 10.9998 3.70643 10.9998 4.16667C10.9998 4.6269 10.6267 5 10.1665 5H6.83317V9.16667H8.08317C9.07773 9.16667 10.0316 9.56175 10.7348 10.265C11.4381 10.9683 11.8332 11.9221 11.8332 12.9167C11.8332 13.9112 11.4381 14.8651 10.7348 15.5683C10.0316 16.2716 9.07773 16.6667 8.08317 16.6667H6.83317V19.1667C6.83317 19.6269 6.46007 20 5.99984 20C5.5396 20 5.1665 19.6269 5.1665 19.1667V16.6667H0.999837C0.5396 16.6667 0.166504 16.2936 0.166504 15.8333C0.166504 15.3731 0.5396 15 0.999837 15H5.1665V10.8333H3.9165C2.92194 10.8333 1.96811 10.4382 1.26485 9.73498C0.561592 9.03172 0.166504 8.0779 0.166504 7.08333C0.166504 6.08877 0.561592 5.13494 1.26485 4.43168C1.96811 3.72842 2.92194 3.33333 3.9165 3.33333H5.1665V0.833333C5.1665 0.373096 5.5396 0 5.99984 0ZM5.1665 5H3.9165C3.36397 5 2.83407 5.21949 2.44336 5.61019C2.05266 6.0009 1.83317 6.5308 1.83317 7.08333C1.83317 7.63587 2.05266 8.16577 2.44336 8.55647C2.83407 8.94717 3.36397 9.16667 3.9165 9.16667H5.1665V5ZM6.83317 10.8333V15H8.08317C8.6357 15 9.16561 14.7805 9.55631 14.3898C9.94701 13.9991 10.1665 13.4692 10.1665 12.9167C10.1665 12.3641 9.94701 11.8342 9.55631 11.4435C9.16561 11.0528 8.6357 10.8333 8.08317 10.8333H6.83317Z"
      fill={fill}
    />
  </svg>
)

export default DolarSign
