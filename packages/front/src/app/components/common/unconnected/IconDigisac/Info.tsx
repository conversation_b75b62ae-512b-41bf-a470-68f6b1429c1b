import React from 'react'
import { IconProps } from './Types'

const Info = ({ id, fill, width, height, onClick }: IconProps) => (
  <svg
    id={id}
    width={width || 24}
    height={height || 24}
    onClick={onClick}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.95 1.1125C5.96461 1.1125 1.1125 5.96462 1.1125 11.95C1.1125 17.9354 5.96461 22.7875 11.95 22.7875C17.9354 22.7875 22.7875 17.9354 22.7875 11.95C22.7875 5.96462 17.9354 1.1125 11.95 1.1125ZM2.6 11.95C2.6 6.78614 6.78614 2.6 11.95 2.6C17.1138 2.6 21.3 6.78614 21.3 11.95C21.3 17.1138 17.1138 21.3 11.95 21.3C6.78614 21.3 2.6 17.1138 2.6 11.95Z"
      fill={fill}
    />

    <path
      d="M1.225 11.95C1.225 6.02675 6.02675 1.225 11.95 1.225V1C5.90248 1 1 5.90248 1 11.95H1.225ZM11.95 22.675C6.02675 22.675 1.225 17.8733 1.225 11.95H1C1 17.9975 5.90248 22.9 11.95 22.9V22.675ZM22.675 11.95C22.675 17.8733 17.8733 22.675 11.95 22.675V22.9C17.9975 22.9 22.9 17.9975 22.9 11.95H22.675ZM11.95 1.225C17.8733 1.225 22.675 6.02675 22.675 11.95H22.9C22.9 5.90248 17.9975 1 11.95 1V1.225ZM11.95 2.4875C6.72401 2.4875 2.4875 6.724 2.4875 11.95H2.7125C2.7125 6.84827 6.84827 2.7125 11.95 2.7125V2.4875ZM21.4125 11.95C21.4125 6.724 17.176 2.4875 11.95 2.4875V2.7125C17.0517 2.7125 21.1875 6.84827 21.1875 11.95H21.4125ZM11.95 21.4125C17.176 21.4125 21.4125 17.176 21.4125 11.95H21.1875C21.1875 17.0517 17.0517 21.1875 11.95 21.1875V21.4125ZM2.4875 11.95C2.4875 17.176 6.72401 21.4125 11.95 21.4125V21.1875C6.84827 21.1875 2.7125 17.0517 2.7125 11.95H2.4875Z"
      fill={fill}
    />

    <path
      d="M11.1221 9.76774H11.0096V9.88024V16.5036V16.6161H11.1221H12.7779H12.8904V16.5036V9.88024V9.76774H12.7779H11.1221ZM12.7708 8.43184C13.0056 8.21749 13.125 7.94799 13.125 7.63103C13.125 7.31482 13.0062 7.04965 12.7709 6.84413C12.546 6.63039 12.2651 6.52503 11.9362 6.52503C11.6048 6.52503 11.3226 6.63669 11.0977 6.86155C10.884 7.07526 10.775 7.33363 10.775 7.63103C10.775 7.92827 10.8838 8.19026 11.0959 8.41241L11.0996 8.41616C11.3248 8.63111 11.6064 8.73701 11.9362 8.73701C12.2644 8.73701 12.5454 8.63674 12.7708 8.43184Z"
      fill={fill}
      stroke={fill}
      strokeWidth="0.3"
    />
  </svg>
)

export default Info
