import React from 'react'
import { IconProps } from './Types'

const ChatStats = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M21.0428 9.80161C19.5989 8.34829 17.6932 7.44428 15.6542 7.24543C13.6152 7.04657 11.5707 7.56534 9.87326 8.71229C8.17577 9.85924 6.93164 11.5625 6.35535 13.5284C5.77906 15.4943 5.90674 17.5997 6.71636 19.4816C6.80074 19.6565 6.82843 19.8534 6.79556 20.0448L6.02116 23.7672C5.99132 23.9099 5.99742 24.0578 6.03889 24.1976C6.08036 24.3374 6.15591 24.4647 6.25876 24.568C6.34307 24.6517 6.44345 24.7175 6.55385 24.7613C6.66426 24.8052 6.7824 24.8262 6.90116 24.8232H7.07716L10.8436 24.0664C11.035 24.0434 11.2291 24.0707 11.4068 24.1456C13.2886 24.9552 15.394 25.0829 17.3599 24.5066C19.3259 23.9303 21.0291 22.6862 22.1761 20.9887C23.323 19.2912 23.8418 17.2468 23.6429 15.2078C23.4441 13.1688 22.5401 11.2631 21.0868 9.81921L21.0428 9.80161ZM21.7732 17.1584C21.6011 18.2091 21.193 19.2071 20.5795 20.0773C19.966 20.9475 19.1631 21.6672 18.2313 22.1822C17.2995 22.6973 16.2629 22.9942 15.1998 23.0508C14.1366 23.1074 13.0744 22.9221 12.0932 22.5088C11.7452 22.3608 11.3713 22.283 10.9932 22.28C10.828 22.2812 10.6631 22.2959 10.5004 22.324L8.01876 22.8256L8.52036 20.344C8.62026 19.8066 8.55586 19.2515 8.33556 18.7512C7.9223 17.77 7.73699 16.7078 7.79356 15.6446C7.85013 14.5814 8.14712 13.5449 8.66216 12.6131C9.17721 11.6813 9.89691 10.8784 10.7671 10.2649C11.6373 9.6514 12.6353 9.24327 13.686 9.07121C14.7888 8.89021 15.919 8.97439 16.9829 9.31679C18.0468 9.65918 19.0138 10.25 19.8041 11.0402C20.5944 11.8305 21.1852 12.7976 21.5276 13.8615C21.87 14.9254 21.9542 16.0555 21.7732 17.1584Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M21.6667 6.75C21.1444 6.75 20.75 7.19825 20.75 7.71429C20.75 8.23032 21.1444 8.67857 21.6667 8.67857H24.1486L19.6667 13.4806L16.9875 10.6101C16.6283 10.2252 16.0383 10.2252 15.6792 10.6101L11.0125 15.6101C10.6625 15.9851 10.6625 16.5864 11.0125 16.9614C11.3717 17.3462 11.9617 17.3462 12.3208 16.9614L16.3333 12.6623L19.0125 15.5328C19.3717 15.9176 19.9617 15.9176 20.3208 15.5328L25.4167 10.073V12.7143C25.4167 13.2303 25.8111 13.6786 26.3333 13.6786C26.8556 13.6786 27.25 13.2303 27.25 12.7143V7.71429C27.25 7.19825 26.8556 6.75 26.3333 6.75H21.6667Z"
      fill={fill}
    />
  </svg>
)

export default ChatStats
