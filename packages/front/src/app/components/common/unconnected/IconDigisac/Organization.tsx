import React from 'react'
import { IconProps } from './Types'

const Organization = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 10C12 10.2652 11.8946 10.5196 11.7071 10.7071C11.5196 10.8946 11.2652 11 11 11C10.7348 11 10.4804 10.8946 10.2929 10.7071C10.1054 10.5196 10 10.2652 10 10C10 9.73478 10.1054 9.48043 10.2929 9.29289C10.4804 9.10536 10.7348 9 11 9C11.2652 9 11.5196 9.10536 11.7071 9.29289C11.8946 9.48043 12 9.73478 12 10Z"
      fill={fill}
    />
    <path
      d="M11 14C11.2652 14 11.5196 13.8946 11.7071 13.7071C11.8946 13.5196 12 13.2652 12 13C12 12.7348 11.8946 12.4804 11.7071 12.2929C11.5196 12.1054 11.2652 12 11 12C10.7348 12 10.4804 12.1054 10.2929 12.2929C10.1054 12.4804 10 12.7348 10 13C10 13.2652 10.1054 13.5196 10.2929 13.7071C10.4804 13.8946 10.7348 14 11 14Z"
      fill={fill}
    />
    <path
      d="M12 16C12 16.2652 11.8946 16.5196 11.7071 16.7071C11.5196 16.8946 11.2652 17 11 17C10.7348 17 10.4804 16.8946 10.2929 16.7071C10.1054 16.5196 10 16.2652 10 16C10 15.7348 10.1054 15.4804 10.2929 15.2929C10.4804 15.1054 10.7348 15 11 15C11.2652 15 11.5196 15.1054 11.7071 15.2929C11.8946 15.4804 12 15.7348 12 16Z"
      fill={fill}
    />
    <path
      d="M12 19C12 19.2652 11.8946 19.5196 11.7071 19.7071C11.5196 19.8946 11.2652 20 11 20C10.7348 20 10.4804 19.8946 10.2929 19.7071C10.1054 19.5196 10 19.2652 10 19C10 18.7348 10.1054 18.4804 10.2929 18.2929C10.4804 18.1054 10.7348 18 11 18C11.2652 18 11.5196 18.1054 11.7071 18.2929C11.8946 18.4804 12 18.7348 12 19Z"
      fill={fill}
    />
    <path
      d="M11 23C11.2652 23 11.5196 22.8946 11.7071 22.7071C11.8946 22.5196 12 22.2652 12 22C12 21.7348 11.8946 21.4804 11.7071 21.2929C11.5196 21.1054 11.2652 21 11 21C10.7348 21 10.4804 21.1054 10.2929 21.2929C10.1054 21.4804 10 21.7348 10 22C10 22.2652 10.1054 22.5196 10.2929 22.7071C10.4804 22.8946 10.7348 23 11 23Z"
      fill={fill}
    />
    <path
      d="M18 20C18.2652 20 18.5196 19.8946 18.7071 19.7071C18.8946 19.5196 19 19.2652 19 19C19 18.7348 18.8946 18.4804 18.7071 18.2929C18.5196 18.1054 18.2652 18 18 18C17.7348 18 17.4804 18.1054 17.2929 18.2929C17.1054 18.4804 17 18.7348 17 19C17 19.2652 17.1054 19.5196 17.2929 19.7071C17.4804 19.8946 17.7348 20 18 20Z"
      fill={fill}
    />
    <path
      d="M21 20C21.2652 20 21.5196 19.8946 21.7071 19.7071C21.8946 19.5196 22 19.2652 22 19C22 18.7348 21.8946 18.4804 21.7071 18.2929C21.5196 18.1054 21.2652 18 21 18C20.7348 18 20.4804 18.1054 20.2929 18.2929C20.1054 18.4804 20 18.7348 20 19C20 19.2652 20.1054 19.5196 20.2929 19.7071C20.4804 19.8946 20.7348 20 21 20Z"
      fill={fill}
    />
    <path
      d="M21 17C21.2652 17 21.5196 16.8946 21.7071 16.7071C21.8946 16.5196 22 16.2652 22 16C22 15.7348 21.8946 15.4804 21.7071 15.2929C21.5196 15.1054 21.2652 15 21 15C20.7348 15 20.4804 15.1054 20.2929 15.2929C20.1054 15.4804 20 15.7348 20 16C20 16.2652 20.1054 16.5196 20.2929 16.7071C20.4804 16.8946 20.7348 17 21 17Z"
      fill={fill}
    />
    <path
      d="M19 16C19 16.2652 18.8946 16.5196 18.7071 16.7071C18.5196 16.8946 18.2652 17 18 17C17.7348 17 17.4804 16.8946 17.2929 16.7071C17.1054 16.5196 17 16.2652 17 16C17 15.7348 17.1054 15.4804 17.2929 15.2929C17.4804 15.1054 17.7348 15 18 15C18.2652 15 18.5196 15.1054 18.7071 15.2929C18.8946 15.4804 19 15.7348 19 16Z"
      fill={fill}
    />
    <path
      d="M21 14C21.2652 14 21.5196 13.8946 21.7071 13.7071C21.8946 13.5196 22 13.2652 22 13C22 12.7348 21.8946 12.4804 21.7071 12.2929C21.5196 12.1054 21.2652 12 21 12C20.7348 12 20.4804 12.1054 20.2929 12.2929C20.1054 12.4804 20 12.7348 20 13C20 13.2652 20.1054 13.5196 20.2929 13.7071C20.4804 13.8946 20.7348 14 21 14Z"
      fill={fill}
    />
    <path
      d="M19 13C19 13.2652 18.8946 13.5196 18.7071 13.7071C18.5196 13.8946 18.2652 14 18 14C17.7348 14 17.4804 13.8946 17.2929 13.7071C17.1054 13.5196 17 13.2652 17 13C17 12.7348 17.1054 12.4804 17.2929 12.2929C17.4804 12.1054 17.7348 12 18 12C18.2652 12 18.5196 12.1054 18.7071 12.2929C18.8946 12.4804 19 12.7348 19 13Z"
      fill={fill}
    />
    <path
      d="M18 9V8.25C18 7.65326 17.7629 7.08097 17.341 6.65901C16.919 6.23705 16.3467 6 15.75 6H9.25C8.65326 6 8.08097 6.23705 7.65901 6.65901C7.23705 7.08097 7 7.65326 7 8.25V24.75C7 25.164 7.336 25.5 7.75 25.5H24.25C24.4489 25.5 24.6397 25.421 24.7803 25.2803C24.921 25.1397 25 24.9489 25 24.75V11.25C25 10.6533 24.7629 10.081 24.341 9.65901C23.919 9.23705 23.3467 9 22.75 9H18ZM8.5 8.25C8.5 8.05109 8.57902 7.86032 8.71967 7.71967C8.86032 7.57902 9.05109 7.5 9.25 7.5H15.75C15.9489 7.5 16.1397 7.57902 16.2803 7.71967C16.421 7.86032 16.5 8.05109 16.5 8.25V9H16.25C15.6533 9 15.081 9.23705 14.659 9.65901C14.2371 10.081 14 10.6533 14 11.25V24H8.5V8.25ZM18.5 24V22.5H20.5V24H18.5ZM22 21.75C22 21.5511 21.921 21.3603 21.7803 21.2197C21.6397 21.079 21.4489 21 21.25 21H17.75C17.5511 21 17.3603 21.079 17.2197 21.2197C17.079 21.3603 17 21.5511 17 21.75V24H15.5V11.25C15.5 11.0511 15.579 10.8603 15.7197 10.7197C15.8603 10.579 16.0511 10.5 16.25 10.5H22.75C22.9489 10.5 23.1397 10.579 23.2803 10.7197C23.421 10.8603 23.5 11.0511 23.5 11.25V24H22V21.75Z"
      fill={fill}
    />
  </svg>
)

export default Organization
