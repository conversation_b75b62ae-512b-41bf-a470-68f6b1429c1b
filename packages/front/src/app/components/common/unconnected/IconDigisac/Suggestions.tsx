import React from 'react'
import { IconProps } from './Types'

const Suggestions = ({ width, height, fill }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M15.6843 6C11.9986 6 9 8.99862 9 12.6843C9 14.1927 9.4946 15.6179 10.4296 16.8052C10.6142 17.0402 10.8082 17.2652 11.0135 17.5034L11.0248 17.5165C11.6529 18.2453 12.3024 18.9991 12.545 19.8868C12.5998 20.0902 12.6076 20.4802 12.6045 20.7807V21.2913C12.6045 21.7586 12.9833 22.1374 13.4506 22.1374H17.8812C18.3485 22.1374 18.7274 21.7586 18.7274 21.2913V20.7747C18.7246 20.3823 18.7449 20.096 18.7858 19.9474C19.0297 19.0656 19.7495 18.2189 20.3903 17.4652L20.418 17.4327C20.6029 17.2155 20.7776 17.0102 20.9367 16.808C21.8731 15.62 22.368 14.194 22.368 12.6843C22.3679 8.99862 19.3697 6 15.6843 6ZM19.6069 15.7612C19.4676 15.9385 19.3033 16.1315 19.1293 16.3358L19.0953 16.3759C18.3467 17.2563 17.4983 18.2542 17.1544 19.4972C17.1058 19.6737 17.0518 19.9377 17.038 20.4451H14.2952C14.2834 19.9062 14.2285 19.6298 14.1783 19.4436C13.8342 18.1841 12.9873 17.2014 12.3066 16.4115L12.2952 16.3982C12.1029 16.1752 11.9212 15.9644 11.7598 15.7591C11.0614 14.8723 10.6923 13.809 10.6923 12.6843C10.6923 9.93174 12.9317 7.69229 15.6843 7.69229C18.4365 7.69229 20.6756 9.93174 20.6756 12.6843C20.6756 13.8102 20.3063 14.8739 19.6069 15.7612Z"
      fill={fill}
    />
    <path
      d="M17.8811 22.3777H13.4506C12.9833 22.3777 12.6044 22.7565 12.6044 23.2238C12.6044 23.6911 12.9833 24.07 13.4506 24.07H17.8812C18.3485 24.07 18.7273 23.6911 18.7273 23.2238C18.7273 22.7565 18.3484 22.3777 17.8811 22.3777Z"
      fill={fill}
    />
    <path
      d="M16.7735 24.3076H14.5583C14.091 24.3076 13.7122 24.6865 13.7122 25.1538C13.7122 25.6211 14.091 25.9999 14.5583 25.9999H16.7735C17.2408 25.9999 17.6196 25.6211 17.6196 25.1538C17.6196 24.6865 17.2408 24.3076 16.7735 24.3076Z"
      fill={fill}
    />
  </svg>
)

export default Suggestions
