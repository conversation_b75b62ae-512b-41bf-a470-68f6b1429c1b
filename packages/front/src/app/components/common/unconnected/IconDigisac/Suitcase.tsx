import React from 'react'
import { IconProps } from './Types'

const Suitcase = ({ width, height, fill, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24.1481 10.3529H20.8141V9.60784C20.8133 8.91643 20.5399 8.25356 20.0539 7.76466C19.5678 7.27576 18.9088 7.00076 18.2215 7H13.777C13.0897 7.00076 12.4307 7.27576 11.9446 7.76466C11.4586 8.25356 11.1852 8.91643 11.1844 9.60784V10.3529H7.85185C7.36088 10.3535 6.89016 10.5499 6.54299 10.8991C6.19582 11.2483 6.00054 11.7218 6 12.2157V24.1373C6.00054 24.6311 6.19582 25.1046 6.54299 25.4538C6.89016 25.803 7.36088 25.9995 7.85185 26H24.1481C24.6391 25.9995 25.1098 25.803 25.457 25.4538C25.8042 25.1046 25.9995 24.6311 26 24.1373V12.2157C25.9995 11.7218 25.8042 11.2483 25.457 10.8991C25.1098 10.5499 24.6391 10.3535 24.1481 10.3529ZM13.4067 9.60784C13.4068 9.50907 13.4458 9.41439 13.5153 9.34455C13.5847 9.27471 13.6788 9.23542 13.777 9.23529H18.2215C18.3197 9.23542 18.4138 9.27471 18.4832 9.34455C18.5527 9.41439 18.5917 9.50907 18.5919 9.60784V10.3529H13.4067V9.60784ZM23.7778 23.7647H8.22222V12.5882H11.1844V14.451C11.1844 14.7474 11.3015 15.0317 11.5099 15.2413C11.7183 15.4509 12.0009 15.5686 12.2956 15.5686C12.5902 15.5686 12.8729 15.4509 13.0812 15.2413C13.2896 15.0317 13.4067 14.7474 13.4067 14.451V12.5882H18.5919V14.451C18.5919 14.7474 18.7089 15.0317 18.9173 15.2413C19.1257 15.4509 19.4083 15.5686 19.703 15.5686C19.9976 15.5686 20.2803 15.4509 20.4886 15.2413C20.697 15.0317 20.8141 14.7474 20.8141 14.451V12.5882H23.7778V23.7647Z"
      fill={fill}
    />
  </svg>
)

export default Suitcase
