import React from 'react'
import { IconProps } from './Types'

const ChargedCBP = ({ width, height, fill }: IconProps) => (
  <svg width={width || 58} height={height || 58} viewBox="0 0 58 58" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M21.9241 57.7796C25.1025 57.42 28.2229 56.8516 31.1345 55.4596C31.7609 55.158 32.3525 54.7984 32.8977 54.3692C34.1621 53.3716 34.9045 52.1304 34.8233 50.4368C34.7537 49.1492 34.8233 47.85 34.8001 46.5508C34.7885 46.1564 34.8581 46.0288 35.2989 46.0868C40.2289 46.6784 45.1241 46.5044 49.9729 45.3792C51.5505 45.008 53.0933 44.5092 54.5317 43.7668C56.1325 42.9432 57.4665 41.8528 58.0001 40.0432C58.0001 28.826 58.0001 17.6204 58.0001 6.4032C57.2809 4.06 55.3901 2.9696 53.3253 2.1228C50.7037 1.0208 47.9313 0.522 45.1241 0.2204C44.4629 0.1508 43.7901 0.1972 43.1521 0C43.0013 0 42.8389 0 42.6881 0C42.2241 0.2088 41.7601 0.1624 41.2961 0C40.8321 0 40.3681 0 39.9041 0C39.4053 0.1508 38.8949 0.232 38.3961 0C38.2453 0 38.0829 0 37.9321 0C37.3289 0.2088 36.6909 0.1508 36.0761 0.2204C33.1645 0.5336 30.3109 1.0788 27.6081 2.2156C26.4481 2.7028 25.3577 3.3176 24.4645 4.234C23.5597 5.162 23.2117 6.2408 23.2233 7.5864C23.2349 9.8252 23.2349 12.064 23.2117 14.3028C23.2001 16.0428 23.1885 17.7712 23.2233 19.5112C23.2349 20.0216 23.1073 20.0912 22.6201 20.0332C17.1101 19.4184 11.6697 19.6504 6.32212 21.2396C4.89532 21.6688 3.52652 22.2372 2.29692 23.084C1.20652 23.838 0.394519 24.8124 0.0117188 26.1C0.0117188 34.6028 0.0117188 43.1172 0.0117188 51.62C0.788919 54.1024 2.81892 55.1696 5.01132 56.0164C7.93452 57.1532 10.9969 57.6172 14.0941 57.884C14.2681 57.8956 14.4885 57.7912 14.6277 58C14.8597 58 15.0917 58 15.3237 58C15.8225 57.768 16.3329 57.8492 16.8317 58C17.2609 58 17.6785 58 18.1077 58C18.5717 57.8492 19.0357 57.7912 19.4997 58C19.6969 58 19.8825 58 20.0797 58C20.1377 57.942 20.2073 57.942 20.2769 57.942C20.8105 57.7912 21.3673 57.8376 21.9241 57.7796ZM34.2201 24.8124C33.5705 23.78 32.6309 23.084 31.5869 22.5156C29.8121 21.5412 27.8865 20.996 25.9261 20.5784C25.5781 20.5088 25.4853 20.3812 25.5085 20.0564C25.5317 19.6388 25.5201 19.2096 25.5201 18.6992C26.4481 19.256 27.3529 19.7548 28.3157 20.1144C30.8097 21.054 33.3965 21.5412 36.0297 21.808C39.0805 22.1212 42.1197 22.1328 45.1705 21.808C48.4765 21.4484 51.7361 20.8568 54.7173 19.2676C55.0189 19.1052 55.3089 18.9196 55.6801 18.6992C55.6801 20.2304 55.6801 21.6804 55.6801 23.1304C55.6801 23.6408 55.4133 24.0004 55.0769 24.3368C54.1953 25.1836 53.1165 25.6824 51.9913 26.1C49.3697 27.0744 46.6205 27.5036 43.8481 27.724C40.9481 27.9444 38.0597 27.8748 35.1713 27.5152C34.8349 27.4688 34.6609 27.4688 34.7537 27.0164C34.9161 26.216 34.6493 25.4852 34.2201 24.8124ZM34.7885 30.4384C34.7769 29.9164 34.8813 29.7888 35.4381 29.8468C40.9249 30.45 46.3537 30.2296 51.6781 28.6404C53.0701 28.2228 54.3809 27.6544 55.6801 26.7728C55.6801 28.3504 55.6801 29.812 55.6801 31.2852C55.6801 31.7956 55.3785 32.1552 55.0421 32.4684C54.2417 33.2224 53.2789 33.698 52.2581 34.0924C49.5205 35.1596 46.6437 35.6004 43.7321 35.8324C40.9481 36.0528 38.1757 35.96 35.4033 35.612C34.9393 35.554 34.7189 35.5308 34.7653 34.8928C34.8697 33.4196 34.8233 31.9348 34.7885 30.4384ZM55.6685 39.4632C55.6569 40.0084 55.2973 40.3796 54.9145 40.716C53.9517 41.5512 52.8033 42.05 51.6201 42.4676C49.0913 43.3492 46.4581 43.7552 43.8017 43.964C41.0177 44.1844 38.2453 44.1032 35.4729 43.7552C34.9625 43.6856 34.7189 43.6856 34.7653 42.978C34.8813 41.4816 34.8233 39.962 34.7885 38.454C34.7769 37.9552 34.9393 37.9204 35.3685 37.9668C41.1105 38.5932 46.7829 38.3496 52.3161 36.5516C53.4761 36.1804 54.5665 35.6468 55.6685 34.916C55.6801 36.4936 55.6917 37.9784 55.6685 39.4632ZM25.9493 6.0088C26.3553 5.5332 26.8657 5.1852 27.4109 4.8836C29.3481 3.8164 31.4477 3.2828 33.6053 2.9116C37.4565 2.2272 41.3309 2.1344 45.2053 2.5752C48.1865 2.9116 51.1213 3.4568 53.8009 4.9068C54.3345 5.1968 54.8217 5.5332 55.2161 5.9856C55.8309 6.67 55.8309 7.25 55.2161 7.9344C54.6941 8.526 54.0329 8.9204 53.3369 9.2684C51.0401 10.3936 48.5693 10.9156 46.0637 11.252C44.2425 11.4956 42.4097 11.6116 39.9621 11.6116C36.8301 11.5652 33.1413 11.2172 29.5685 9.9992C28.6405 9.686 27.7357 9.28 26.9121 8.7464C26.5525 8.5144 26.2161 8.2476 25.9377 7.9344C25.3809 7.25 25.3693 6.6816 25.9493 6.0088ZM25.5085 14.558C25.5665 13.2588 25.5201 11.948 25.5201 10.556C27.2369 11.6928 29.0581 12.3308 30.9489 12.8064C34.8233 13.7692 38.7557 14.0476 42.7345 13.862C45.8897 13.7228 48.9985 13.282 52.0029 12.3076C53.2673 11.89 54.4737 11.3564 55.6801 10.556C55.6801 12.1336 55.6917 13.6184 55.6685 15.1032C55.6569 15.6484 55.2973 16.0196 54.9145 16.356C54.1373 17.0404 53.2209 17.4928 52.2697 17.864C49.6365 18.8964 46.8873 19.3488 44.0917 19.5808C40.4029 19.8824 36.7141 19.7316 33.0717 19.0356C31.1229 18.6528 29.2089 18.1424 27.4457 17.168C27.0977 16.9708 26.7613 16.7388 26.4365 16.4952C25.7985 16.008 25.4621 15.4048 25.5085 14.558ZM3.34092 25.1836C4.58212 24.2324 6.03212 23.6756 7.52852 23.2928C14.1289 21.5876 20.7293 21.5876 27.3297 23.3044C28.6985 23.664 30.0093 24.1744 31.1809 24.9748C32.8977 26.158 32.8977 27.2136 31.1577 28.3852C29.4641 29.522 27.5269 30.0672 25.5665 30.4964C22.8637 31.088 20.1145 31.3084 17.0521 31.32C13.4793 31.262 9.67452 30.8908 6.00892 29.5684C5.04612 29.2204 4.11812 28.7796 3.30612 28.1416C1.99532 27.1208 1.99532 26.216 3.34092 25.1836ZM2.29692 34.2432C2.36652 32.9556 2.32012 31.6564 2.32012 30.2992C5.63772 32.3524 9.26852 33.0136 12.9689 33.408C15.9965 33.7328 19.0241 33.7212 22.0517 33.3964C25.3461 33.0368 28.5709 32.4336 31.5405 30.856C31.8421 30.6936 32.1321 30.508 32.4801 30.2992C32.4801 31.8768 32.5033 33.3964 32.4685 34.916C32.4569 35.4844 31.9929 35.8672 31.5753 36.2152C30.6241 36.9692 29.5337 37.4564 28.3969 37.8392C24.9053 39.034 21.2861 39.4168 17.6205 39.44C13.8969 39.4632 10.2197 39.0804 6.65852 37.932C5.46372 37.5492 4.31532 37.0504 3.30612 36.2732C2.63332 35.7512 2.25052 35.148 2.29692 34.2432ZM2.29692 42.3632C2.36652 41.0756 2.32012 39.7764 2.32012 38.4192C5.64932 40.484 9.30332 41.1452 13.0153 41.528C16.5881 41.8992 20.1493 41.8296 23.6989 41.296C26.7613 40.8436 29.7541 40.1476 32.4685 38.396C32.4685 39.9852 32.4917 41.5048 32.4569 43.0244C32.4453 43.5928 31.9813 43.9756 31.5637 44.3236C30.6125 45.0776 29.5221 45.5648 28.3969 45.9476C24.9053 47.1424 21.2861 47.5252 17.6205 47.5484C13.8969 47.5716 10.2197 47.1888 6.65852 46.0404C5.46372 45.6576 4.31532 45.1588 3.30612 44.3816C2.63332 43.8828 2.25052 43.268 2.29692 42.3632ZM9.86012 54.984C8.18972 54.6592 6.55412 54.23 5.01132 53.4992C4.58212 53.2904 4.15292 53.0584 3.74692 52.8032C2.77252 52.2116 2.19252 51.446 2.29692 50.2048C2.40132 49.0216 2.32012 47.8152 2.32012 46.5276C2.65652 46.7248 2.93492 46.9104 3.22492 47.0728C5.35932 48.256 7.69092 48.8708 10.0689 49.2536C16.1009 50.2396 22.0981 50.1236 28.0373 48.488C29.5801 48.0588 31.0533 47.444 32.4801 46.5044C32.4801 48.0588 32.4801 49.5088 32.4801 50.9588C32.4801 51.6084 32.0741 52.026 31.6217 52.3972C30.6125 53.2324 29.4177 53.7312 28.1881 54.1488C25.8101 54.9492 23.3625 55.3436 20.8685 55.5408C17.1797 55.8424 13.5025 55.6916 9.86012 54.984Z"
      fill={fill}
    />
    <path d="M39.904 0C39.4052 0 38.8948 0 38.396 0C38.8948 0.0928 39.4052 0.0928 39.904 0Z" fill={fill} />
    <path d="M15.312 58C15.8108 58 16.3212 58 16.82 58C16.3212 57.9072 15.8108 57.9072 15.312 58Z" fill={fill} />
    <path d="M42.6881 0C42.2241 0 41.7601 0 41.2961 0C41.7601 0.1044 42.2241 0.1044 42.6881 0Z" fill={fill} />
    <path d="M18.0962 58C18.5602 58 19.0242 58 19.4882 58C19.0242 57.9072 18.5602 57.9072 18.0962 58Z" fill={fill} />
  </svg>
)

export default ChargedCBP
