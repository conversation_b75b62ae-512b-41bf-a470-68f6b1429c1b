import React from 'react'
import { IconProps } from './Types'

const Client = ({ width, height, fill }: IconProps) => (
  <svg width={width || 60} height={height || 61} viewBox="0 0 60 61" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M39.8608 23C39.8608 28.5228 35.3836 33 29.8608 33C24.3379 33 19.8608 28.5228 19.8608 23C19.8608 17.4772 24.3379 13 29.8608 13C35.3836 13 39.8608 17.4772 39.8608 23ZM35.9216 33.935C39.7619 31.8019 42.3608 27.7044 42.3608 23C42.3608 16.0964 36.7643 10.5 29.8608 10.5C22.9572 10.5 17.3608 16.0964 17.3608 23C17.3608 27.7043 19.9595 31.8016 23.7996 33.9348C16.4828 36.2587 10.9904 42.6779 10.015 50.5C9.84402 51.8715 11.1355 53 12.5176 53H47.4934C48.8861 53 49.8779 51.882 49.7056 50.5C48.7302 42.6782 43.2381 36.259 35.9216 33.935ZM29.8603 35.5C38.6765 35.5 45.97 42.0193 47.1831 50.5H12.5375C13.7506 42.0193 21.0441 35.5 29.8603 35.5Z"
      fill={fill}
    />
    <path
      d="M35.9216 33.935L35.6788 33.4979L34.6656 34.0606L35.7702 34.4115L35.9216 33.935ZM23.7996 33.9348L23.951 34.4113L25.0556 34.0605L24.0424 33.4977L23.7996 33.9348ZM10.015 50.5L9.51888 50.4381L10.015 50.5ZM49.7056 50.5L49.2094 50.5619L49.2094 50.5619L49.7056 50.5ZM47.1831 50.5V51H47.7597L47.678 50.4292L47.1831 50.5ZM12.5375 50.5L12.0426 50.4292L11.9609 51H12.5375V50.5ZM29.8608 33.5C35.6598 33.5 40.3608 28.799 40.3608 23H39.3608C39.3608 28.2467 35.1075 32.5 29.8608 32.5V33.5ZM19.3608 23C19.3608 28.799 24.0618 33.5 29.8608 33.5V32.5C24.6141 32.5 20.3608 28.2467 20.3608 23H19.3608ZM29.8608 12.5C24.0618 12.5 19.3608 17.201 19.3608 23H20.3608C20.3608 17.7533 24.6141 13.5 29.8608 13.5V12.5ZM40.3608 23C40.3608 17.201 35.6598 12.5 29.8608 12.5V13.5C35.1075 13.5 39.3608 17.7533 39.3608 23H40.3608ZM36.1644 34.3721C40.157 32.1544 42.8608 27.8933 42.8608 23H41.8608C41.8608 27.5156 39.3668 31.4494 35.6788 33.4979L36.1644 34.3721ZM42.8608 23C42.8608 15.8203 37.0405 10 29.8608 10V11C36.4882 11 41.8608 16.3726 41.8608 23H42.8608ZM29.8608 10C22.6811 10 16.8608 15.8203 16.8608 23H17.8608C17.8608 16.3726 23.2333 11 29.8608 11V10ZM16.8608 23C16.8608 27.8931 19.5643 32.1541 23.5568 34.3719L24.0424 33.4977C20.3546 31.4491 17.8608 27.5155 17.8608 23H16.8608ZM23.6482 33.4582C16.1487 35.8402 10.5188 42.4191 9.51888 50.4381L10.5112 50.5619C11.462 42.9368 16.817 36.6771 23.951 34.4113L23.6482 33.4582ZM9.51888 50.4381C9.30055 52.189 10.9227 53.5 12.5176 53.5V52.5C11.3482 52.5 10.3875 51.5539 10.5112 50.5619L9.51888 50.4381ZM12.5176 53.5H47.4934V52.5H12.5176V53.5ZM47.4934 53.5C49.1853 53.5 50.4114 52.1199 50.2017 50.4381L49.2094 50.5619C49.3444 51.644 48.5868 52.5 47.4934 52.5V53.5ZM50.2017 50.4381C49.2018 42.4193 43.5722 35.8405 36.073 33.4584L35.7702 34.4115C42.9039 36.6775 48.2586 42.937 49.2094 50.5619L50.2017 50.4381ZM47.678 50.4292C46.4302 41.7057 38.9288 35 29.8603 35V36C38.4242 36 45.5098 42.3328 46.6881 50.5708L47.678 50.4292ZM12.5375 51H47.1831V50H12.5375V51ZM29.8603 35C20.7918 35 13.2904 41.7057 12.0426 50.4292L13.0325 50.5708C14.2108 42.3328 21.2964 36 29.8603 36V35Z"
      fill={fill}
    />
  </svg>
)

export default Client
