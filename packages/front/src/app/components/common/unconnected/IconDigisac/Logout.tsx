import React from 'react'
import { IconProps } from './Types'

const Logout = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.1919 5.33473C7.40622 5.12041 7.69692 5 8.00002 5C8.30312 5 8.59381 5.12041 8.80814 5.33473C9.02247 5.54906 9.14288 5.83975 9.14288 6.14286V25.8571C9.14288 26.1602 9.02247 26.4509 8.80814 26.6653C8.59381 26.8796 8.30312 27 8.00002 27C7.69692 27 7.40622 26.8796 7.1919 26.6653C6.97757 26.4509 6.85716 26.1602 6.85716 25.8571V6.14286C6.85716 5.83975 6.97757 5.54906 7.1919 5.33473ZM20.3349 18.6206C20.1267 18.8361 20.0115 19.1248 20.0141 19.4245C20.0167 19.7241 20.1369 20.0108 20.3488 20.2227C20.5607 20.4345 20.8473 20.5547 21.147 20.5573C21.4466 20.5599 21.7353 20.4448 21.9509 20.2366L25.3794 16.808C25.5937 16.5937 25.714 16.303 25.714 16C25.714 15.697 25.5937 15.4063 25.3794 15.192L21.9509 11.7634C21.8454 11.6543 21.7193 11.5672 21.5799 11.5073C21.4405 11.4474 21.2905 11.4159 21.1387 11.4146C20.987 11.4133 20.8365 11.4422 20.6961 11.4996C20.5556 11.5571 20.428 11.642 20.3207 11.7493C20.2134 11.8566 20.1285 11.9842 20.0711 12.1246C20.0136 12.2651 19.9847 12.4156 19.986 12.5673C19.9873 12.7191 20.0188 12.869 20.0787 13.0085C20.1386 13.1479 20.2257 13.274 20.3349 13.3794L21.8126 14.8571H13.1429C12.8398 14.8571 12.5491 14.9776 12.3347 15.1919C12.1204 15.4062 12 15.6969 12 16C12 16.3031 12.1204 16.5938 12.3347 16.8081C12.5491 17.0224 12.8398 17.1429 13.1429 17.1429H21.8126L20.3349 18.6206Z"
      fill={fill}
    />
  </svg>
)

export default Logout
