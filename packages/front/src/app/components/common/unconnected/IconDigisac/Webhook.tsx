import React from 'react'
import { IconProps } from './Types'

const Webhook = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 28}
    height={height || 28}
    className={className}
    viewBox="0 0 24 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_3877_13792)">
      <path
        d="M3.39907 17.2019C3.57767 18.1228 4.31442 18.8038 5.29116 18.9601C6.14511 19.0996 7.06046 18.6475 7.50697 17.8717C7.97581 17.0512 7.90325 16.0521 7.30046 15.3489C7.17767 15.2038 7.21116 15.1201 7.2893 14.9917C8.28279 13.3675 9.27069 11.7433 10.2586 10.1191C10.4986 9.72285 10.4986 9.72285 10.0856 9.48285C8.07069 8.32192 7.26139 5.87727 8.19907 3.7675C9.12558 1.67448 11.4921 0.625175 13.68 1.34517C15.8902 2.06517 17.1684 4.38703 16.6046 6.66982C16.56 6.84285 16.5712 6.92099 16.7553 6.96006C16.9786 7.01029 17.2074 7.07727 17.4195 7.16657C17.5758 7.22796 17.626 7.18889 17.6651 7.0382C17.7544 6.68657 17.8158 6.32936 17.8437 5.96657C18.0781 2.76285 15.5107 0.0112215 12.2456 0.00564009C11.9888 -0.0111041 11.6819 0.0168029 11.3805 0.0781982C9.06418 0.541454 7.50139 1.89773 6.8986 4.17494C6.29581 6.45215 7.01581 8.38331 8.81302 9.90703C8.95814 10.0298 8.96372 10.108 8.86883 10.2587C8.02604 11.6373 7.18883 13.0159 6.35721 14.4001C6.25674 14.5675 6.16186 14.6066 5.96651 14.5731C4.38139 14.2996 3.09209 15.6335 3.39907 17.2019ZM5.59814 15.628C6.20093 15.6335 6.69767 16.147 6.69767 16.761C6.69767 17.3638 6.18418 17.8605 5.57023 17.8605C4.96744 17.8605 4.47069 17.3415 4.47069 16.7275C4.47069 16.1303 4.98976 15.6224 5.59814 15.628Z"
        fill={fill}
      />
      <path
        d="M16.3591 17.5423C16.7386 18.5581 17.7656 19.133 18.8037 18.9377C19.8865 18.7312 20.6512 17.8214 20.6512 16.7386C20.6512 15.6502 19.8754 14.7461 18.7702 14.5451C17.76 14.3609 16.7274 14.9581 16.3591 15.9628C16.2865 16.1637 16.1861 16.1916 16.0019 16.1916C14.1767 16.1861 12.3572 16.1861 10.5321 16.1861C10.0465 16.1861 10.0465 16.1861 10.0465 16.6772C10.0409 19.4679 7.71907 21.5163 4.9507 21.1591C1.99814 20.7795 0.245586 17.52 1.54605 14.8409C2.11535 13.6688 3.02512 12.8819 4.26977 12.4968C4.4428 12.4409 4.51535 12.3795 4.44838 12.1898C4.37582 11.9888 4.32559 11.7823 4.2921 11.5702C4.25861 11.3414 4.15814 11.3302 3.95721 11.3916C1.62977 12.0893 4.95085e-06 14.294 4.95085e-06 16.7944C-0.00557644 16.9786 0.0223305 17.2074 0.0446561 17.4419C0.424191 20.7628 3.80094 23.0623 7.01582 22.1302C9.30419 21.4661 10.6047 19.8642 11.0288 17.5368C11.0623 17.3581 11.107 17.3023 11.2967 17.3023C12.8707 17.3135 14.4391 17.3135 16.013 17.3023C16.2028 17.3023 16.2865 17.3526 16.3591 17.5423ZM18.4242 15.6335C19.0437 15.6391 19.5349 16.1302 19.5349 16.7498C19.5349 17.3805 19.027 17.8772 18.3963 17.8661C17.7823 17.8549 17.2967 17.3526 17.3023 16.733C17.3079 16.1191 17.8047 15.6279 18.4242 15.6335Z"
        fill={fill}
      />
      <path
        d="M16.4819 11.5702C16.3088 11.6428 16.2307 11.6204 16.1414 11.4586C15.3991 10.0967 14.6512 8.74044 13.9033 7.38974C13.8251 7.24463 13.8307 7.1609 13.9479 7.03253C14.6009 6.29579 14.6958 5.3023 14.2158 4.47625C13.7247 3.63904 12.7814 3.21486 11.8326 3.40463C10.6772 3.63346 9.8735 4.79439 10.08 5.933C10.3088 7.18323 11.4251 7.99253 12.6363 7.77486C12.8037 7.74695 12.8651 7.81393 12.9321 7.93672C13.8028 9.52183 14.6791 11.107 15.5498 12.6921C15.7786 13.1051 15.7786 13.1163 16.2028 12.8818C17.2186 12.3125 18.2902 12.1451 19.4288 12.3963C21.5609 12.8595 23.0847 14.9581 22.8726 17.1237C22.6493 19.4121 20.8019 21.1646 18.5693 21.2093C17.3972 21.2316 16.3591 20.8632 15.4772 20.093C15.3488 19.9814 15.2819 19.9702 15.1702 20.1042C15.0195 20.2828 14.8633 20.4558 14.6902 20.6065C14.5507 20.7293 14.5786 20.7907 14.707 20.9023C15.773 21.8344 17.0121 22.3032 18.1842 22.3256C21.3544 22.3256 23.8214 20.0316 23.9944 17.1014C24.2288 12.9544 20.0986 10.08 16.4819 11.5702ZM12.2847 6.70323C11.6651 6.70881 11.1684 6.21765 11.1628 5.6037C11.1572 4.973 11.654 4.47067 12.2847 4.47625C12.9042 4.48184 13.3954 4.973 13.3954 5.59253C13.3954 6.2009 12.9042 6.69765 12.2847 6.70323Z"
        fill={fill}
      />
    </g>
    <defs>
      <clipPath id="clip0_3877_13792">
        <rect width="24" height="22.3367" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export default Webhook
