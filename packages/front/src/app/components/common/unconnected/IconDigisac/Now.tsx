import React from 'react'

const Now = ({ fill, width, height, className }) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.3445 9.29329C10.4375 9.38616 10.5112 9.49645 10.5616 9.61785C10.6119 9.73924 10.6378 9.86937 10.6378 10.0008C10.6378 10.1322 10.6119 10.2623 10.5616 10.3837C10.5112 10.5051 10.4375 10.6154 10.3445 10.7083C8.84394 12.2091 8.00095 14.2445 8.00095 16.3668C8.00095 18.4891 8.84394 20.5245 10.3445 22.0253C10.4373 22.1182 10.511 22.2285 10.5612 22.3499C10.6114 22.4712 10.6372 22.6013 10.6372 22.7326C10.6372 22.864 10.6112 22.994 10.5609 23.1154C10.5106 23.2367 10.4369 23.3469 10.344 23.4398C10.2511 23.5326 10.1408 23.6063 10.0194 23.6565C9.89807 23.7067 9.76799 23.7325 9.63665 23.7325C9.5053 23.7324 9.37524 23.7065 9.25391 23.6562C9.13258 23.6059 9.02234 23.5322 8.9295 23.4393C5.0235 19.5333 5.0235 13.1993 8.9295 9.29329C9.02237 9.20031 9.13266 9.12655 9.25406 9.07623C9.37546 9.0259 9.50558 9 9.637 9C9.76842 9 9.89854 9.0259 10.0199 9.07623C10.1413 9.12655 10.2516 9.20031 10.3445 9.29329ZM23.0755 9.29329C26.9815 13.2003 26.9815 19.5333 23.0755 23.4393C22.8869 23.6214 22.6343 23.7222 22.3721 23.72C22.1099 23.7177 21.8591 23.6125 21.6737 23.4271C21.4883 23.2417 21.3831 22.9909 21.3808 22.7287C21.3785 22.4665 21.4793 22.2139 21.6615 22.0253C23.1621 20.5245 24.005 18.4891 24.005 16.3668C24.005 14.2445 23.1621 12.2091 21.6615 10.7083C21.4739 10.5208 21.3684 10.2664 21.3683 10.0011C21.3682 9.73587 21.4735 9.48143 21.661 9.29379C21.8485 9.10615 22.1029 9.00068 22.3681 9.00058C22.6334 9.00049 22.8879 9.10578 23.0755 9.29329ZM13.3115 12.1673C13.499 12.3548 13.6043 12.6091 13.6043 12.8743C13.6043 13.1395 13.499 13.3938 13.3115 13.5813C12.9475 13.9453 12.6587 14.3775 12.4616 14.8531C12.2646 15.3287 12.1632 15.8385 12.1632 16.3533C12.1632 16.8681 12.2646 17.3779 12.4616 17.8535C12.6587 18.3291 12.9475 18.7613 13.3115 19.1253C13.407 19.2175 13.4832 19.3279 13.5356 19.4499C13.588 19.5719 13.6156 19.7031 13.6168 19.8359C13.6179 19.9687 13.5926 20.1003 13.5423 20.2232C13.492 20.3461 13.4178 20.4578 13.3239 20.5517C13.23 20.6456 13.1184 20.7198 12.9955 20.7701C12.8726 20.8204 12.7409 20.8457 12.6081 20.8445C12.4753 20.8434 12.3441 20.8158 12.2221 20.7634C12.1001 20.711 11.9897 20.6348 11.8975 20.5393C10.7873 19.4291 10.1636 17.9233 10.1636 16.3533C10.1636 14.7832 10.7873 13.2775 11.8975 12.1673C12.085 11.9798 12.3393 11.8745 12.6045 11.8745C12.8697 11.8745 13.124 11.9798 13.3115 12.1673ZM20.2705 12.1673C21.3807 13.2775 22.0044 14.7832 22.0044 16.3533C22.0044 17.9233 21.3807 19.4291 20.2705 20.5393C20.1777 20.6322 20.0674 20.7059 19.9461 20.7562C19.8248 20.8065 19.6947 20.8324 19.5634 20.8325C19.432 20.8325 19.3019 20.8067 19.1806 20.7565C19.0592 20.7063 18.9489 20.6326 18.856 20.5398C18.7631 20.4469 18.6894 20.3367 18.6391 20.2154C18.5888 20.094 18.5628 19.964 18.5628 19.8326C18.5628 19.7013 18.5886 19.5712 18.6388 19.4499C18.689 19.3285 18.7627 19.2182 18.8555 19.1253C19.2195 18.7613 19.5083 18.3291 19.7054 17.8535C19.9024 17.3779 20.0038 16.8681 20.0038 16.3533C20.0038 15.8385 19.9024 15.3287 19.7054 14.8531C19.5083 14.3775 19.2195 13.9453 18.8555 13.5813C18.668 13.3936 18.5627 13.1392 18.5628 12.8739C18.5629 12.6087 18.6684 12.3543 18.856 12.1668C19.0436 11.9793 19.2981 11.874 19.5634 11.8741C19.8286 11.8742 20.083 11.9796 20.2705 12.1673ZM16.0835 14.9373C16.2805 14.9373 16.4755 14.9761 16.6575 15.0515C16.8395 15.1268 17.0049 15.2373 17.1442 15.3766C17.2834 15.5159 17.3939 15.6813 17.4693 15.8633C17.5447 16.0452 17.5835 16.2403 17.5835 16.4373C17.5835 16.6343 17.5447 16.8293 17.4693 17.0113C17.3939 17.1933 17.2834 17.3587 17.1442 17.4979C17.0049 17.6372 16.8395 17.7477 16.6575 17.8231C16.4755 17.8985 16.2805 17.9373 16.0835 17.9373C15.6857 17.9373 15.3041 17.7793 15.0228 17.4979C14.7415 17.2166 14.5835 16.8351 14.5835 16.4373C14.5835 16.0395 14.7415 15.6579 15.0228 15.3766C15.3041 15.0953 15.6857 14.9373 16.0835 14.9373Z"
      fill={fill}
    />
  </svg>
)

export default Now
