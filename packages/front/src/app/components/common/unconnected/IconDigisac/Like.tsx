import React from 'react'
import { IconProps } from './Types'

const Like = ({ fill = '#324B7D', width = '24', height = '24' }: IconProps) => (
  <svg width={width} height={height} viewBox="0 0 37 37" fill={fill} xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10.6673 16.8333L17.334 1.83325C18.6601 1.83325 19.9318 2.36004 20.8695 3.29772C21.8072 4.2354 22.334 5.50717 22.334 6.83325V13.4999H31.7673C32.2505 13.4944 32.7291 13.5941 33.1699 13.7919C33.6108 13.9898 34.0033 14.2811 34.3204 14.6458C34.6375 15.0104 34.8715 15.4396 35.0062 15.9037C35.1409 16.3677 35.1732 16.8555 35.1007 17.3333L32.8007 32.3333C32.6801 33.1281 32.2764 33.8526 31.6638 34.3733C31.0513 34.8939 30.2712 35.1757 29.4673 35.1666H10.6673M10.6673 16.8333V35.1666M10.6673 16.8333H5.66732C4.78326 16.8333 3.93542 17.1844 3.31029 17.8096C2.68517 18.4347 2.33398 19.2825 2.33398 20.1666V31.8333C2.33398 32.7173 2.68517 33.5652 3.31029 34.1903C3.93542 34.8154 4.78326 35.1666 5.66732 35.1666H10.6673"
      stroke="#324B7D"
      stroke-width="3.33333"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
)

export default Like
