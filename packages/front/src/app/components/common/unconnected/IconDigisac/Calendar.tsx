import React from 'react'
import { IconProps } from './Types'

const Calendar = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.76694 6C8.76788 6 7.80975 6.39687 7.10331 7.10331C6.39687 7.80975 6 8.76788 6 9.76694V22.2331C6 23.2321 6.39687 24.1903 7.10331 24.8967C7.80975 25.6031 8.76788 26 9.76694 26H22.2331C23.2321 26 24.1903 25.6031 24.8967 24.8967C25.6031 24.1903 26 23.2321 26 22.2331V9.76694C26 8.76788 25.6031 7.80975 24.8967 7.10331C24.1903 6.39687 23.2321 6 22.2331 6H9.76694ZM8.11382 9.76694C8.11382 8.85449 8.85449 8.11382 9.76694 8.11382H22.2331C23.1455 8.11382 23.8862 8.85449 23.8862 9.76694V10.336H8.11382V9.76694ZM8.11382 22.2331V12.4499H23.8862V22.2331C23.8862 22.6715 23.712 23.092 23.402 23.402C23.092 23.712 22.6715 23.8862 22.2331 23.8862H9.76694C9.3285 23.8862 8.90803 23.712 8.59801 23.402C8.28799 23.092 8.11382 22.6715 8.11382 22.2331ZM11.393 14.1301C10.9689 14.1301 10.5622 14.2985 10.2623 14.5984C9.9625 14.8982 9.79404 15.3049 9.79404 15.729C9.79404 16.1531 9.9625 16.5597 10.2623 16.8596C10.5622 17.1595 10.9689 17.3279 11.393 17.3279C11.817 17.3279 12.2237 17.1595 12.5236 16.8596C12.8234 16.5597 12.9919 16.1531 12.9919 15.729C12.9919 15.3049 12.8234 14.8982 12.5236 14.5984C12.2237 14.2985 11.817 14.1301 11.393 14.1301ZM16 14.1301C15.5759 14.1301 15.1693 14.2985 14.8694 14.5984C14.5695 14.8982 14.4011 15.3049 14.4011 15.729C14.4011 16.1531 14.5695 16.5597 14.8694 16.8596C15.1693 17.1595 15.5759 17.3279 16 17.3279C16.4241 17.3279 16.8308 17.1595 17.1306 16.8596C17.4305 16.5597 17.5989 16.1531 17.5989 15.729C17.5989 15.3049 17.4305 14.8982 17.1306 14.5984C16.8308 14.2985 16.4241 14.1301 16 14.1301ZM20.607 14.1301C20.183 14.1301 19.7763 14.2985 19.4764 14.5984C19.1766 14.8982 19.0081 15.3049 19.0081 15.729C19.0081 16.1531 19.1766 16.5597 19.4764 16.8596C19.7763 17.1595 20.183 17.3279 20.607 17.3279C21.0311 17.3279 21.4378 17.1595 21.7377 16.8596C22.0375 16.5597 22.206 16.1531 22.206 15.729C22.206 15.3049 22.0375 14.8982 21.7377 14.5984C21.4378 14.2985 21.0311 14.1301 20.607 14.1301ZM16 18.4661C15.5759 18.4661 15.1693 18.6346 14.8694 18.9344C14.5695 19.2343 14.4011 19.641 14.4011 20.065C14.4011 20.4891 14.5695 20.8958 14.8694 21.1956C15.1693 21.4955 15.5759 21.664 16 21.664C16.4241 21.664 16.8308 21.4955 17.1306 21.1956C17.4305 20.8958 17.5989 20.4891 17.5989 20.065C17.5989 19.641 17.4305 19.2343 17.1306 18.9344C16.8308 18.6346 16.4241 18.4661 16 18.4661ZM20.607 18.4661C20.183 18.4661 19.7763 18.6346 19.4764 18.9344C19.1766 19.2343 19.0081 19.641 19.0081 20.065C19.0081 20.4891 19.1766 20.8958 19.4764 21.1956C19.7763 21.4955 20.183 21.664 20.607 21.664C21.0311 21.664 21.4378 21.4955 21.7377 21.1956C22.0375 20.8958 22.206 20.4891 22.206 20.065C22.206 19.641 22.0375 19.2343 21.7377 18.9344C21.4378 18.6346 21.0311 18.4661 20.607 18.4661Z"
      fill={fill}
    />
  </svg>
)

export default Calendar
