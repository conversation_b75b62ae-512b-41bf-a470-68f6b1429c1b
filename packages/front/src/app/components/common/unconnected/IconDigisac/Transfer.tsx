import React from 'react'
import { IconProps } from './Types'

const Transfer = ({ fill, width, height }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.6301 6.07593C12.7508 6.12601 12.8604 6.19939 12.9527 6.29187C13.0452 6.38419 13.1186 6.49384 13.1687 6.61455C13.2188 6.73535 13.2446 6.86484 13.2446 6.99563C13.2446 7.12641 13.2188 7.2559 13.1687 7.3767C13.1186 7.49742 13.045 7.60726 12.9525 7.69958L10.1474 10.5036H24.2551C24.5188 10.5036 24.7717 10.6084 24.9582 10.7948C25.1446 10.9813 25.2494 11.2342 25.2494 11.4979C25.2494 11.7616 25.1446 12.0145 24.9582 12.201C24.7717 12.3874 24.5188 12.4922 24.2551 12.4922H10.1474L12.9527 15.2965C13.0452 15.3889 13.1185 15.4986 13.1685 15.6193C13.2185 15.7401 13.2442 15.8695 13.2442 16.0002C13.2442 16.1309 13.2185 16.2603 13.1685 16.3811C13.1185 16.5018 13.0452 16.6115 12.9527 16.7039C12.8603 16.7963 12.7506 16.8697 12.6299 16.9197C12.5091 16.9697 12.3797 16.9954 12.249 16.9954C12.1183 16.9954 11.9889 16.9697 11.8682 16.9197C11.7474 16.8697 11.6377 16.7963 11.5453 16.7039L7.04299 12.2016C6.9505 12.1093 6.87711 11.9997 6.82702 11.879C6.7769 11.7582 6.7511 11.6287 6.7511 11.4979C6.7511 11.3671 6.7769 11.2376 6.82702 11.1168C6.87715 10.996 6.95061 10.8863 7.04321 10.794L11.5451 6.29211C11.6374 6.19951 11.7471 6.12605 11.8679 6.07593C11.9887 6.0258 12.1182 6 12.249 6C12.3798 6 12.5093 6.0258 12.6301 6.07593ZM7.74672 19.5082C7.48302 19.5082 7.23012 19.613 7.04366 19.7994C6.85719 19.9859 6.75244 20.2388 6.75244 20.5025C6.75244 20.7662 6.85719 21.0191 7.04366 21.2056C7.23012 21.392 7.48302 21.4968 7.74672 21.4968H21.8545L19.0492 24.301C18.8625 24.4877 18.7577 24.7408 18.7577 25.0048C18.7577 25.2687 18.8625 25.5219 19.0492 25.7085C19.2358 25.8951 19.4889 26 19.7529 26C20.0168 26 20.27 25.8951 20.4566 25.7085L24.9587 21.2064C25.0513 21.1141 25.1248 21.0044 25.1749 20.8836C25.225 20.7628 25.2508 20.6333 25.2508 20.5025C25.2508 20.3717 25.225 20.2422 25.1749 20.1214C25.1248 20.0007 25.0512 19.8908 24.9587 19.7985L20.4567 15.2966C20.3644 15.204 20.2547 15.1306 20.134 15.0805C20.0132 15.0304 19.8837 15.0046 19.7529 15.0046C19.6221 15.0046 19.4926 15.0304 19.3718 15.0805C19.2511 15.1306 19.1415 15.204 19.0492 15.2965C18.9567 15.3888 18.8833 15.4984 18.8332 15.6191C18.7831 15.7399 18.7573 15.8694 18.7573 16.0002C18.7573 16.131 18.7831 16.2605 18.8332 16.3813C18.8833 16.502 18.9568 16.6118 19.0494 16.7041L21.8545 19.5082H7.74672Z"
      fill={fill}
    />
  </svg>
)

export default Transfer
