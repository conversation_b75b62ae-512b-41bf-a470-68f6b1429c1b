import React from 'react'
import { IconProps } from './Types'

const Envelope = ({ fill, width, height }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.5 9.25C8.15625 9.25 7.875 9.5875 7.875 10V11.0359L14.6133 17.6734C15.4219 18.4703 16.582 18.4703 17.3906 17.6734L24.125 11.0359V10C24.125 9.5875 23.8438 9.25 23.5 9.25H8.5ZM7.875 13.9469V22C7.875 22.4125 8.15625 22.75 8.5 22.75H23.5C23.8438 22.75 24.125 22.4125 24.125 22V13.9469L18.5781 19.4125C17.0781 20.8891 14.918 20.8891 13.4219 19.4125L7.875 13.9469ZM6 10C6 8.34531 7.12109 7 8.5 7H23.5C24.8789 7 26 8.34531 26 10V22C26 23.6547 24.8789 25 23.5 25H8.5C7.12109 25 6 23.6547 6 22V10Z"
      fill={fill}
    />
  </svg>
)

export default Envelope
