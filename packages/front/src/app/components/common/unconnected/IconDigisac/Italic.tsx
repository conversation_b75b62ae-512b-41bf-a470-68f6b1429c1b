import React from 'react'
import { IconProps } from './Types'

const Italic = ({ fill = '#324B7D', width = '24', height = '24' }: IconProps) => (
  <svg width="6" height="14" viewBox="0 0 6 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M2.75957 4H4.75957L2.55957 14H0.55957L2.75957 4ZM4.43957 0C4.24179 0 4.04845 0.0586491 3.884 0.16853C3.71955 0.278412 3.59138 0.434591 3.51569 0.617317C3.44 0.800043 3.4202 1.00111 3.45879 1.19509C3.49737 1.38907 3.59261 1.56725 3.73246 1.70711C3.87232 1.84696 4.0505 1.9422 4.24448 1.98079C4.43846 2.01937 4.63953 1.99957 4.82225 1.92388C5.00498 1.84819 5.16116 1.72002 5.27104 1.55557C5.38092 1.39112 5.43957 1.19778 5.43957 1C5.43957 0.734784 5.33421 0.48043 5.14668 0.292893C4.95914 0.105357 4.70479 0 4.43957 0Z"
      fill="#24272D"
    />
  </svg>
)

export default Italic
