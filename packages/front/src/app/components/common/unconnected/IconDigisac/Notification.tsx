import React from 'react'
import { IconProps } from './Types'

const Notification = ({ width, height, fill }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M24.4392 21.0373C22.3937 18.9913 22.0858 17.9646 22.0858 14.6956C22.0858 11.3338 19.3612 8.60864 16 8.60864C12.6389 8.60864 9.91424 11.3339 9.91424 14.6956C9.91424 16.4582 9.8673 17.1162 9.62467 17.924C9.32293 18.9304 8.7035 19.8939 7.5606 21.0373C7.01308 21.5852 7.40092 22.5217 8.17541 22.5217H13.0006L12.9568 22.9565C12.9568 24.6373 14.3192 25.9999 15.9997 25.9999C17.6803 25.9999 19.0426 24.6373 19.0426 22.9565L18.9988 22.5217H23.8244C24.5992 22.5217 24.987 21.5852 24.4392 21.0373ZM16.0003 25.1304C14.8 25.1304 13.8267 24.1568 13.8267 22.9565L13.8705 22.5217H18.1296L18.174 22.9565C18.174 24.1568 17.2006 25.1304 16.0003 25.1304ZM8.17569 21.6521C10.7838 19.0434 10.7838 17.3043 10.7838 14.6956C10.7838 11.8145 13.1192 9.47821 16 9.47821C18.8809 9.47821 21.2163 11.8145 21.2165 14.6956C21.2165 17.3043 21.2165 19.0434 23.8247 21.6521H8.17569Z"
      fill={fill || 'white'}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.79052 14.8198C9.79052 11.3896 12.5705 8.60864 16 8.60864C19.4296 8.60864 22.2095 11.3895 22.2095 14.8198C22.2095 16.3757 22.2851 17.3333 22.5749 18.1358C22.8607 18.927 23.3696 19.6067 24.3293 20.5668C25.1117 21.3494 24.5578 22.6873 23.451 22.6873H19.3115C19.3115 24.5168 17.8289 25.9999 15.9997 25.9999C14.1706 25.9999 12.688 24.5168 12.688 22.6873H8.5487C7.44209 22.6873 6.88843 21.3493 7.67037 20.5669L7.67038 20.5669C8.72953 19.5071 9.2711 18.6465 9.53221 17.7756L9.53227 17.7754C9.7441 17.07 9.79052 16.4981 9.79052 14.8198ZM13.5161 22.7073C13.5269 24.0703 14.6347 25.1718 15.9997 25.1718C14.634 25.1715 13.5257 24.0683 13.5164 22.7046L13.5161 22.7073ZM16 9.4368C18.1058 9.4368 19.929 10.6466 20.813 12.4092C19.9289 10.6467 18.1058 9.4368 16 9.4368ZM21.6083 17.7728C21.3955 16.9283 21.3825 16.062 21.3817 14.9922C21.3859 16.1268 21.4336 17.0077 21.6083 17.7728ZM23.451 21.8592H23.4513H23.451ZM16 10.265C13.4854 10.265 11.4467 12.3045 11.4467 14.8198L11.4467 14.8528C11.4467 16.07 11.4467 17.1578 11.1188 18.2514C10.8471 19.1574 10.3582 20.0467 9.503 21.031H22.4973C21.6421 20.0467 21.1532 19.1574 20.8815 18.2514C20.5536 17.1578 20.5536 16.07 20.5536 14.8528V14.8199C20.5536 14.8198 20.5536 14.8198 20.5536 14.8198C20.5533 12.3044 18.5147 10.265 16 10.265ZM10.6186 14.8198C10.6186 15.3871 10.618 15.8968 10.5905 16.3745C10.6123 15.9596 10.6186 15.4629 10.6186 14.8198ZM10.3517 17.9236C10.3432 17.9534 10.3344 17.9833 10.3254 18.0134C10.2039 18.4188 10.0336 18.8135 9.80546 19.21C10.0431 18.7976 10.2087 18.4034 10.3256 18.0135C10.3346 17.9835 10.3433 17.9536 10.3517 17.9236ZM8.58908 20.8081C8.48313 20.9215 8.37219 21.0361 8.25606 21.1523L7.96336 20.8597L8.25607 21.1523C8.25607 21.1523 8.25607 21.1523 8.25606 21.1523C8.25418 21.1542 8.25231 21.1561 8.25047 21.158L7.54953 21.8592H8.5487C8.1829 21.8592 7.9979 21.4198 8.25047 21.158L8.25615 21.1523C8.37293 21.0355 8.4838 20.9208 8.58908 20.8081ZM14.3465 22.6873L14.3445 22.707C14.3551 23.6125 15.0925 24.3436 16.0003 24.3436C16.9079 24.3436 17.6454 23.6126 17.6561 22.7072L17.6541 22.6873H14.3465Z"
      fill={fill || 'white'}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.3913 6.86957C13.3913 6.38932 13.7806 6 14.2609 6L17.7391 6C18.2194 6 18.6087 6.38932 18.6087 6.86957C18.6087 7.34981 18.2194 7.73913 17.7391 7.73913L14.2609 7.73913C13.7806 7.73913 13.3913 7.34981 13.3913 6.86957Z"
      fill={fill || 'white'}
    />
  </svg>
)

export default Notification
