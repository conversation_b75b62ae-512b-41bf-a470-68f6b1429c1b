import React from 'react'
import { IconProps } from './Types'

const IconFilePlus = ({ fill, width, height, stroke }: IconProps) => (
  <svg
    width={width || '19'}
    height={height || '23'}
    viewBox="0 0 19 23"
    fill={fill || 'white'}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.4833 1.80005H3.14998C2.59745 1.80005 2.06755 2.01076 1.67684 2.38584C1.28614 2.76091 1.06665 3.26962 1.06665 3.80005V19.8C1.06665 20.3305 1.28614 20.8392 1.67684 21.2143C2.06755 21.5893 2.59745 21.8 3.14998 21.8H15.65C16.2025 21.8 16.7324 21.5893 17.1231 21.2143C17.5138 20.8392 17.7333 20.3305 17.7333 19.8V7.80005M11.4833 1.80005L17.7333 7.80005M11.4833 1.80005V7.80005H17.7333M9.39998 17.8V11.8M6.27498 14.8H12.525"
      stroke={stroke || '#324B7D'}
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
)

export default IconFilePlus
