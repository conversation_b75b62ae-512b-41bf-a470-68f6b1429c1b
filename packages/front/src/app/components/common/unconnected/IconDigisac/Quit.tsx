import React from 'react'
import { IconProps } from './Types'

const Quit = ({ width, height, className, fill }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 29 29"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.3418 4.75349C10.3418 5.21423 9.96829 5.58774 9.50754 5.58774L8.31575 5.58774C6.14367 5.58774 4.38285 7.34856 4.38285 9.52065L4.38285 19.055C4.38285 21.2271 6.14367 22.9879 8.31575 22.9879L9.50754 22.9879C9.96829 22.9879 10.3418 23.3614 10.3418 23.8221C10.3418 24.2829 9.96829 24.6564 9.50754 24.6564L8.31575 24.6564C5.22218 24.6564 2.71434 22.1485 2.71434 19.055L2.71434 9.52065C2.71434 6.42707 5.22218 3.91924 8.31575 3.91924L9.50754 3.91924C9.96829 3.91924 10.3418 4.29274 10.3418 4.75349Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.0307 10.1225C16.7049 9.79672 16.1766 9.79672 15.8508 10.1225C15.5251 10.4483 15.5251 10.9765 15.8508 11.3023L18.2189 13.6704L7.12332 13.6704C6.66257 13.6704 6.28906 14.0439 6.28906 14.5047C6.28906 14.9654 6.66257 15.3389 7.12332 15.3389L18.2189 15.3389L15.8508 17.707C15.5251 18.0328 15.5251 18.561 15.8508 18.8868C16.1766 19.2126 16.7049 19.2126 17.0307 18.8868L20.8229 15.0946C21.1487 14.7688 21.1487 14.2406 20.8229 13.9148L17.0307 10.1225Z"
      fill={fill}
    />
  </svg>
)

export default Quit
