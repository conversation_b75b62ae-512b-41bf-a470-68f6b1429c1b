import React from 'react'
import { IconProps } from './Types'

const Smartphone = ({ width, height, fill, ...props }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M19.3333 7.66667C19.7753 7.66667 20.1992 7.84226 20.5118 8.15482C20.8243 8.46738 20.9999 8.89131 20.9999 9.33333V22.6667C20.9999 23.1087 20.8243 23.5326 20.5118 23.8452C20.1992 24.1577 19.7753 24.3333 19.3333 24.3333H12.6666C12.2246 24.3333 11.8006 24.1577 11.4881 23.8452C11.1755 23.5326 10.9999 23.1087 10.9999 22.6667V9.33333C10.9999 8.89131 11.1755 8.46738 11.4881 8.15482C11.8006 7.84226 12.2246 7.66667 12.6666 7.66667H19.3333ZM12.6666 6C11.7825 6 10.9347 6.35119 10.3096 6.97631C9.68444 7.60143 9.33325 8.44928 9.33325 9.33333V22.6667C9.33325 23.5507 9.68444 24.3986 10.3096 25.0237C10.9347 25.6488 11.7825 26 12.6666 26H19.3333C20.2173 26 21.0652 25.6488 21.6903 25.0237C22.3154 24.3986 22.6666 23.5507 22.6666 22.6667V9.33333C22.6666 8.44928 22.3154 7.60143 21.6903 6.97631C21.0652 6.35119 20.2173 6 19.3333 6H12.6666Z"
      fill={fill}
    />
    <path
      d="M17.1785 22.1785C16.8659 22.4911 16.442 22.6667 16 22.6667C15.558 22.6667 15.134 22.4911 14.8215 22.1785C14.5089 21.866 14.3333 21.442 14.3333 21C14.3333 20.558 14.5089 20.1341 14.8215 19.8215C15.134 19.5089 15.558 19.3333 16 19.3333C16.442 19.3333 16.8659 19.5089 17.1785 19.8215C17.4911 20.1341 17.6666 20.558 17.6666 21C17.6666 21.442 17.4911 21.866 17.1785 22.1785Z"
      fill={fill}
    />
  </svg>
)

export default Smartphone
