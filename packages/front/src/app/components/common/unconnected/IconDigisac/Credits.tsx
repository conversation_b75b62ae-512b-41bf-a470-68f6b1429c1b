import React from 'react'
import { IconProps } from './Types'

const Credits = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.997 6C19.827 5.99913 21.602 6.62567 23.0259 7.77513C24.4498 8.92458 25.4366 10.5275 25.8217 12.3165C26.2068 14.1055 25.9669 15.9724 25.1421 17.6059C24.3172 19.2395 22.9573 20.5408 21.289 21.293C20.7558 22.4719 19.9446 23.5039 18.9249 24.3004C17.9052 25.097 16.7076 25.6343 15.4347 25.8663C14.1617 26.0984 12.8515 26.0182 11.6164 25.6327C10.3813 25.2472 9.25807 24.5678 8.34315 23.6529C7.42822 22.738 6.74887 21.6148 6.36335 20.3796C5.97783 19.1445 5.89765 17.8343 6.12969 16.5614C6.36173 15.2884 6.89906 14.0908 7.69561 13.0711C8.49215 12.0515 9.52412 11.2402 10.703 10.707C11.3372 9.30389 12.3626 8.1135 13.6564 7.27861C14.9501 6.44371 16.4573 5.99976 17.997 6ZM13.997 12C13.2091 12 12.4289 12.1552 11.7009 12.4567C10.973 12.7583 10.3115 13.2002 9.75439 13.7574C9.19724 14.3145 8.75529 14.9759 8.45376 15.7039C8.15223 16.4319 7.99703 17.2121 7.99703 18C7.99703 18.7879 8.15223 19.5681 8.45376 20.2961C8.75529 21.0241 9.19724 21.6855 9.75439 22.2426C10.3115 22.7998 10.973 23.2417 11.7009 23.5433C12.4289 23.8448 13.2091 24 13.997 24C15.5883 24 17.1145 23.3679 18.2397 22.2426C19.3649 21.1174 19.997 19.5913 19.997 18C19.997 16.4087 19.3649 14.8826 18.2397 13.7574C17.1145 12.6321 15.5883 12 13.997 12ZM13.997 12.5C14.5493 12.5 14.997 12.9477 14.997 13.5V14H15.997C16.5493 14 16.997 14.4477 16.997 15C16.997 15.5523 16.5493 16 15.997 16H12.997C12.8721 15.9998 12.7516 16.0463 12.6593 16.1305C12.5669 16.2147 12.5094 16.3304 12.4982 16.4548C12.4869 16.5793 12.5226 16.7034 12.5983 16.8028C12.6739 16.9023 12.7841 16.9697 12.907 16.992L12.997 17H14.997C15.6601 17 16.296 17.2634 16.7648 17.7322C17.2336 18.2011 17.497 18.837 17.497 19.5C17.497 20.163 17.2336 20.7989 16.7648 21.2678C16.296 21.7366 15.6601 22 14.997 22V22.5C14.997 23.0523 14.5493 23.5 13.997 23.5C13.4447 23.5 12.997 23.0523 12.997 22.5V22H11.997C11.4447 22 10.997 21.5523 10.997 21C10.997 20.4477 11.4447 20 11.997 20H14.997C15.122 20.0002 15.2425 19.9537 15.3348 19.8695C15.4271 19.7853 15.4846 19.6696 15.4959 19.5452C15.5072 19.4207 15.4715 19.2966 15.3958 19.1972C15.3201 19.0977 15.21 19.0303 15.087 19.008L14.997 19H12.997C12.334 19 11.6981 18.7366 11.2293 18.2678C10.7604 17.7989 10.497 17.163 10.497 16.5C10.497 15.837 10.7604 15.2011 11.2293 14.7322C11.6981 14.2634 12.334 14 12.997 14V13.5C12.997 12.9477 13.4447 12.5 13.997 12.5ZM17.997 8C17.1497 7.99901 16.3119 8.17794 15.5389 8.52496C14.7659 8.87198 14.0754 9.37918 13.513 10.013C14.6433 9.94439 15.7753 10.1165 16.834 10.5179C17.8928 10.9194 18.8543 11.541 19.655 12.3418C20.4556 13.1425 21.0772 14.1041 21.4785 15.1629C21.8798 16.2218 22.0518 17.3537 21.983 18.484C22.8922 17.6756 23.5342 16.6099 23.8239 15.4283C24.1135 14.2467 24.037 13.0049 23.6047 11.8677C23.1723 10.7305 22.4045 9.75164 21.403 9.06088C20.4015 8.37013 19.2136 8.00014 17.997 8Z"
      fill={fill}
    />
  </svg>
)

export default Credits
