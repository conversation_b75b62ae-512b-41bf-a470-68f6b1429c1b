import React from 'react'
import { IconProps } from './Types'

const <PERSON><PERSON><PERSON><PERSON><PERSON> = ({ width, height, fillRule }: IconProps) => (
  <svg width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_2027_760)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.33334 0.833336C8.79358 0.833336 9.16667 1.20643 9.16667 1.66667C9.62691 1.66667 10 2.03977 10 2.5C10 2.96024 9.62691 3.33334 9.16667 3.33334C9.16667 3.79357 8.79358 4.16667 8.33334 4.16667C7.8731 4.16667 7.50001 3.79357 7.50001 3.33334C7.03977 3.33334 6.66667 2.96024 6.66667 2.5C6.66667 2.03977 7.03977 1.66667 7.50001 1.66667C7.50001 1.20643 7.8731 0.833336 8.33334 0.833336ZM15.5408 0.976327C15.7655 0.882584 16.0066 0.834316 16.25 0.834316C16.4935 0.834316 16.7345 0.882584 16.9592 0.976327C17.183 1.06973 17.3862 1.20643 17.5571 1.37857L17.559 1.38046L18.6192 2.44067C18.7925 2.61058 18.9305 2.81324 19.0249 3.0369C19.1202 3.26257 19.1693 3.50505 19.1693 3.75C19.1693 3.99496 19.1202 4.23743 19.0249 4.4631C18.9305 4.68677 18.7925 4.88942 18.6192 5.05933L5.05931 18.6192C4.88941 18.7926 4.68676 18.9305 4.46311 19.0249C4.23744 19.1202 3.99496 19.1693 3.75001 19.1693C3.50505 19.1693 3.26258 19.1202 3.03691 19.0249C2.81324 18.9304 2.61058 18.7925 2.44068 18.6192L1.38046 17.559L1.37858 17.5571C1.20643 17.3862 1.06973 17.183 0.976331 16.9592C0.882588 16.7345 0.83432 16.4935 0.83432 16.25C0.83432 16.0065 0.882588 15.7655 0.976331 15.5408C1.06976 15.3169 1.20652 15.1137 1.37875 14.9428L1.38046 14.941L14.9411 1.38046L14.9428 1.37874C15.1137 1.20652 15.3169 1.06976 15.5408 0.976327ZM11.6667 7.01185L2.55593 16.1226L2.55288 16.1256C2.53644 16.1419 2.52339 16.1613 2.51448 16.1826C2.50557 16.204 2.50099 16.2269 2.50099 16.25C2.50099 16.2731 2.50557 16.296 2.51448 16.3174C2.52339 16.3387 2.53644 16.3581 2.55288 16.3744L2.55594 16.3774L3.6226 17.4441L3.63056 17.4522C3.6461 17.4681 3.66467 17.4808 3.68518 17.4895C3.7057 17.4981 3.72774 17.5026 3.75001 17.5026C3.77228 17.5026 3.79432 17.4981 3.81483 17.4895C3.83535 17.4808 3.85392 17.4681 3.86945 17.4522L3.87742 17.4441L12.9882 8.33333L11.6667 7.01185ZM14.1667 7.15482L12.8452 5.83334L16.1226 2.55592L16.1256 2.55288C16.1419 2.53644 16.1613 2.52339 16.1826 2.51448C16.204 2.50557 16.2269 2.50098 16.25 2.50098C16.2731 2.50098 16.296 2.50557 16.3174 2.51448C16.3387 2.52339 16.3581 2.53644 16.3744 2.55288L16.3774 2.55592L17.4441 3.62259L17.4522 3.63056C17.4681 3.64609 17.4808 3.66466 17.4895 3.68518C17.4981 3.70569 17.5026 3.72773 17.5026 3.75C17.5026 3.77227 17.4981 3.79431 17.4895 3.81483C17.4808 3.83534 17.4681 3.85391 17.4522 3.86945L17.4441 3.87741L14.1667 7.15482ZM4.16667 4.16667C4.62691 4.16667 5.00001 4.53976 5.00001 5V5.83334H5.83334C6.29358 5.83334 6.66667 6.20643 6.66667 6.66667C6.66667 7.12691 6.29358 7.5 5.83334 7.5H5.00001V8.33333C5.00001 8.79357 4.62691 9.16667 4.16667 9.16667C3.70644 9.16667 3.33334 8.79357 3.33334 8.33333V7.5H2.50001C2.03977 7.5 1.66667 7.12691 1.66667 6.66667C1.66667 6.20643 2.03977 5.83334 2.50001 5.83334H3.33334V5C3.33334 4.53976 3.70644 4.16667 4.16667 4.16667ZM15.8333 10.8333C16.2936 10.8333 16.6667 11.2064 16.6667 11.6667V12.5H17.5C17.9602 12.5 18.3333 12.8731 18.3333 13.3333C18.3333 13.7936 17.9602 14.1667 17.5 14.1667H16.6667V15C16.6667 15.4602 16.2936 15.8333 15.8333 15.8333C15.3731 15.8333 15 15.4602 15 15V14.1667H14.1667C13.7064 14.1667 13.3333 13.7936 13.3333 13.3333C13.3333 12.8731 13.7064 12.5 14.1667 12.5H15V11.6667C15 11.2064 15.3731 10.8333 15.8333 10.8333Z"
        fill={fillRule || '#324B7D'}
      />
    </g>
    <defs>
      <clipPath id="clip0_2027_760">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export default WandSparkles
