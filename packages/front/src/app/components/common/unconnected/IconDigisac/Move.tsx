import React from 'react'
import { IconProps } from './Types'

const Move = ({ fill, width = 16, height = 16 }: IconProps) => (
  <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_4863_3987)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M7.52794 0.86201C7.78829 0.601661 8.2104 0.601661 8.47075 0.86201L10.4708 2.86201C10.7311 3.12236 10.7311 3.54447 10.4708 3.80482C10.2104 4.06517 9.78829 4.06517 9.52794 3.80482L8.66602 2.94289V7.33341H13.0565L12.1946 6.47149C11.9343 6.21114 11.9343 5.78903 12.1946 5.52868C12.455 5.26833 12.8771 5.26833 13.1374 5.52868L15.1374 7.52868C15.3978 7.78903 15.3978 8.21114 15.1374 8.47149L13.1374 10.4715C12.8771 10.7318 12.455 10.7318 12.1946 10.4715C11.9343 10.2111 11.9343 9.78903 12.1946 9.52868L13.0565 8.66675H8.66602V13.0573L9.52794 12.1953C9.78829 11.935 10.2104 11.935 10.4708 12.1953C10.7311 12.4557 10.7311 12.8778 10.4708 13.1382L8.47075 15.1382C8.2104 15.3985 7.78829 15.3985 7.52794 15.1382L5.52794 13.1382C5.26759 12.8778 5.26759 12.4557 5.52794 12.1953C5.78829 11.935 6.2104 11.935 6.47075 12.1953L7.33268 13.0573V8.66675H2.94216L3.80409 9.52868C4.06444 9.78903 4.06444 10.2111 3.80409 10.4715C3.54374 10.7318 3.12163 10.7318 2.86128 10.4715L0.861278 8.47149C0.600928 8.21114 0.600928 7.78903 0.861278 7.52868L2.86128 5.52868C3.12163 5.26833 3.54374 5.26833 3.80409 5.52868C4.06444 5.78903 4.06444 6.21114 3.80409 6.47149L2.94216 7.33341H7.33268V2.94289L6.47075 3.80482C6.2104 4.06517 5.78829 4.06517 5.52794 3.80482C5.26759 3.54447 5.26759 3.12236 5.52794 2.86201L7.52794 0.86201Z"
        fill={fill}
      />
    </g>
    <defs>
      <clipPath id="clip0_4863_3987">
        <rect width={width} height={height} fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export default Move
