import React from 'react'
import { IconProps } from './Types'

const Disconnect = ({ fill, width, height }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.0011 10.3295C7.9641 12.7789 8.85948 15.5628 10.6604 18.6817C12.9825 22.7033 15.5743 25.143 18.4654 25.9502C19.1202 26.1331 19.8112 26.1432 20.4711 25.9797C21.131 25.8161 21.7373 25.4845 22.2309 25.017L22.4658 24.7943L21.3519 23.6803L21.1475 23.8738C20.8515 24.1542 20.488 24.3532 20.0923 24.4514C19.6966 24.5496 19.2823 24.5438 18.8895 24.4344C16.4561 23.7541 14.1582 21.5916 12.024 17.8943C10.7739 15.7287 10.0076 13.7779 9.71319 12.0416L8.0011 10.3295ZM14.5534 11.2249L14.8458 10.9523C15.0463 10.7654 15.1833 10.5205 15.2376 10.2518C15.2918 9.98315 15.2605 9.70424 15.1481 9.45425L14.2013 7.34843C14.0706 7.05807 13.8388 6.82513 13.5491 6.69303C13.2594 6.56092 12.9315 6.53866 12.6266 6.63039L11.4005 6.99991C11.1426 7.07762 10.9008 7.19858 10.6851 7.35671L9.56359 6.23516C9.96863 5.8979 10.4385 5.64443 10.9459 5.4914L12.171 5.12293C12.8421 4.92056 13.5639 4.96928 14.2018 5.25999C14.8396 5.5507 15.3498 6.06354 15.6373 6.70282L16.5842 8.80865C16.8314 9.35852 16.9002 9.97197 16.7811 10.5629C16.6619 11.1539 16.3607 11.6928 15.9197 12.1039L15.6675 12.339L14.5534 11.2249ZM13.047 15.3755C13.2283 15.8295 13.4777 16.3271 13.7929 16.8729C14.9661 18.9054 16.0606 19.8947 17.2603 19.5887L13.047 15.3755ZM20.7771 17.4486L23.0967 19.7683L22.1721 18.4864C21.8247 18.0052 21.3375 17.6429 20.7771 17.4486Z"
      fill={fill}
    />
    <path
      d="M10.6604 18.6817L10.9202 18.5317L10.9202 18.5317L10.6604 18.6817ZM8.0011 10.3295L8.21323 10.1174L7.71185 9.61601L7.70114 10.325L8.0011 10.3295ZM18.4654 25.9502L18.3847 26.2392L18.3847 26.2392L18.4654 25.9502ZM20.4711 25.9797L20.5433 26.2708V26.2708L20.4711 25.9797ZM22.2309 25.017L22.4372 25.2348L22.4373 25.2347L22.2309 25.017ZM22.4658 24.7943L22.6722 25.012L22.8958 24.8L22.678 24.5821L22.4658 24.7943ZM21.3519 23.6803L21.564 23.4682L21.3576 23.2617L21.1456 23.4625L21.3519 23.6803ZM21.1475 23.8738L20.9413 23.656L20.9412 23.656L21.1475 23.8738ZM20.0923 24.4514L20.02 24.1602H20.02L20.0923 24.4514ZM18.8895 24.4344L18.8087 24.7233L18.809 24.7234L18.8895 24.4344ZM12.024 17.8943L12.2839 17.7444L12.2839 17.7444L12.024 17.8943ZM9.71319 12.0416L10.009 11.9915L9.99297 11.8971L9.92532 11.8295L9.71319 12.0416ZM14.5534 11.2249L14.3488 11.0055L14.1216 11.2174L14.3412 11.4371L14.5534 11.2249ZM14.8458 10.9523L14.6413 10.7328L14.6412 10.7328L14.8458 10.9523ZM15.2376 10.2518L15.5316 10.3112V10.3112L15.2376 10.2518ZM15.1481 9.45425L15.4218 9.33126L15.4218 9.33122L15.1481 9.45425ZM14.2013 7.34843L14.4749 7.2254L14.4748 7.22531L14.2013 7.34843ZM13.5491 6.69303L13.4246 6.96599V6.96599L13.5491 6.69303ZM12.6266 6.63039L12.5402 6.34311L12.54 6.34315L12.6266 6.63039ZM11.4005 6.99991L11.487 7.28715L11.4871 7.28715L11.4005 6.99991ZM10.6851 7.35671L10.473 7.56885L10.655 7.75083L10.8625 7.59864L10.6851 7.35671ZM9.56359 6.23516L9.37163 6.00462L9.11907 6.21491L9.35146 6.44729L9.56359 6.23516ZM10.9459 5.4914L10.8595 5.20411L10.8593 5.20417L10.9459 5.4914ZM12.171 5.12293L12.2574 5.41022L12.2576 5.41016L12.171 5.12293ZM14.2018 5.25999L14.0773 5.53297V5.53297L14.2018 5.25999ZM15.6373 6.70282L15.9109 6.57979L15.9109 6.57978L15.6373 6.70282ZM16.5842 8.80865L16.8578 8.68565L16.8578 8.68562L16.5842 8.80865ZM16.7811 10.5629L17.0751 10.6222V10.6222L16.7811 10.5629ZM15.9197 12.1039L15.7151 11.8844L15.7151 11.8844L15.9197 12.1039ZM15.6675 12.339L15.4553 12.5512L15.6602 12.756L15.8721 12.5585L15.6675 12.339ZM13.7929 16.8729L14.0527 16.723L14.0527 16.7229L13.7929 16.8729ZM13.047 15.3755L13.2592 15.1633L12.7684 15.4867L13.047 15.3755ZM17.2603 19.5887L17.3344 19.8794L17.845 19.7492L17.4724 19.3766L17.2603 19.5887ZM20.7771 17.4486L20.8753 17.1652L20.5649 17.6608L20.7771 17.4486ZM23.0967 19.7683L22.8845 19.9804L23.34 19.5928L23.0967 19.7683ZM22.1721 18.4864L22.4154 18.3109L22.4153 18.3108L22.1721 18.4864ZM10.9202 18.5317C9.13391 15.4381 8.26519 12.7088 8.30107 10.3341L7.70114 10.325C7.66301 12.849 8.58504 15.6874 10.4006 18.8317L10.9202 18.5317ZM18.5461 25.6613C15.7643 24.8845 13.225 22.5233 10.9202 18.5317L10.4006 18.8317C12.74 22.8833 15.3844 25.4014 18.3847 26.2392L18.5461 25.6613ZM20.3989 25.6885C19.7893 25.8396 19.151 25.8302 18.5461 25.6613L18.3847 26.2392C19.0894 26.4359 19.8331 26.4469 20.5433 26.2708L20.3989 25.6885ZM22.0246 24.7992C21.5686 25.231 21.0085 25.5374 20.3989 25.6885L20.5433 26.2708C21.2534 26.0948 21.9059 25.7379 22.4372 25.2348L22.0246 24.7992ZM22.2594 24.5765L22.0245 24.7993L22.4373 25.2347L22.6722 25.012L22.2594 24.5765ZM22.678 24.5821L21.564 23.4682L21.1397 23.8924L22.2537 25.0064L22.678 24.5821ZM21.3538 24.0916L21.5582 23.8981L21.1456 23.4625L20.9413 23.656L21.3538 24.0916ZM20.1646 24.7426C20.6106 24.6318 21.0203 24.4076 21.3538 24.0916L20.9412 23.656C20.6828 23.9008 20.3655 24.0745 20.02 24.1602L20.1646 24.7426ZM18.809 24.7234C19.2516 24.8467 19.7187 24.8533 20.1646 24.7426L20.02 24.1602C19.6746 24.246 19.3129 24.2409 18.97 24.1454L18.809 24.7234ZM11.7642 18.0443C13.9135 21.7678 16.2635 24.0118 18.8087 24.7233L18.9703 24.1454C16.6488 23.4965 14.4029 21.4155 12.2839 17.7444L11.7642 18.0443ZM9.41741 12.0918C9.71961 13.8742 10.5029 15.8592 11.7642 18.0443L12.2839 17.7444C11.045 15.5983 10.2955 13.6816 10.009 11.9915L9.41741 12.0918ZM9.92532 11.8295L8.21323 10.1174L7.78897 10.5417L9.50106 12.2537L9.92532 11.8295ZM14.758 11.4444L15.0504 11.1717L14.6412 10.7328L14.3488 11.0055L14.758 11.4444ZM15.0503 11.1718C15.2967 10.9422 15.465 10.6413 15.5316 10.3112L14.9435 10.1925C14.9017 10.3997 14.796 10.5886 14.6413 10.7328L15.0503 11.1718ZM15.5316 10.3112C15.5982 9.98107 15.5598 9.63841 15.4218 9.33126L14.8745 9.57724C14.9612 9.77008 14.9853 9.98522 14.9435 10.1925L15.5316 10.3112ZM15.4218 9.33122L14.4749 7.2254L13.9276 7.47146L14.8745 9.57728L15.4218 9.33122ZM14.4748 7.22531C14.3143 6.86857 14.0295 6.58238 13.6736 6.42007L13.4246 6.96599C13.6481 7.06789 13.8269 7.24757 13.9277 7.47155L14.4748 7.22531ZM13.6736 6.42007C13.3176 6.25776 12.9148 6.23041 12.5402 6.34311L12.713 6.91767C12.9482 6.84691 13.2011 6.86408 13.4246 6.96599L13.6736 6.42007ZM12.54 6.34315L11.3139 6.71267L11.4871 7.28715L12.7132 6.91763L12.54 6.34315ZM11.3139 6.71266C11.0232 6.80024 10.7508 6.93657 10.5077 7.11479L10.8625 7.59864C11.0508 7.46059 11.2619 7.35499 11.487 7.28715L11.3139 6.71266ZM9.35146 6.44729L10.473 7.56885L10.8973 7.14458L9.77572 6.02303L9.35146 6.44729ZM9.75555 6.4657C10.1297 6.15415 10.5638 5.91999 11.0326 5.77862L10.8593 5.20417C10.3132 5.36887 9.80753 5.64166 9.37163 6.00462L9.75555 6.4657ZM11.0324 5.77868L12.2574 5.41022L12.0846 4.83564L10.8595 5.20411L11.0324 5.77868ZM12.2576 5.41016C12.859 5.22882 13.5058 5.27247 14.0773 5.53297L14.3262 4.98701C13.6221 4.66608 12.8252 4.61231 12.0844 4.83571L12.2576 5.41016ZM14.0773 5.53297C14.6489 5.79347 15.1061 6.25302 15.3637 6.82587L15.9109 6.57978C15.5936 5.87407 15.0303 5.30793 14.3262 4.987L14.0773 5.53297ZM15.3637 6.82585L16.3106 8.93168L16.8578 8.68562L15.9109 6.57979L15.3637 6.82585ZM16.3106 8.93165C16.5321 9.42438 16.5938 9.97408 16.487 10.5036L17.0751 10.6222C17.2067 9.96986 17.1307 9.29265 16.8578 8.68565L16.3106 8.93165ZM16.487 10.5036C16.3802 11.0332 16.1103 11.516 15.7151 11.8844L16.1243 12.3233C16.6111 11.8695 16.9436 11.2746 17.0751 10.6222L16.487 10.5036ZM15.7151 11.8844L15.4629 12.1196L15.8721 12.5585L16.1243 12.3233L15.7151 11.8844ZM14.3412 11.4371L15.4553 12.5512L15.8796 12.1269L14.7655 11.0128L14.3412 11.4371ZM14.0527 16.7229C13.7424 16.1855 13.5001 15.7011 13.3256 15.2642L12.7684 15.4867C12.9566 15.9578 13.2131 16.4688 13.5331 17.0229L14.0527 16.7229ZM17.1862 19.298C16.7019 19.4215 16.2236 19.2955 15.7058 18.8749C15.1781 18.4464 14.6332 17.7285 14.0527 16.723L13.5331 17.0229C14.1258 18.0498 14.7147 18.8429 15.3275 19.3406C15.9501 19.8463 16.6189 20.0619 17.3344 19.8794L17.1862 19.298ZM17.4724 19.3766L13.2592 15.1633L12.8349 15.5876L17.0482 19.8009L17.4724 19.3766ZM20.5649 17.6608L22.8845 19.9804L23.3088 19.5561L20.9892 17.2365L20.5649 17.6608ZM21.9288 18.6619L22.8534 19.9437L23.34 19.5928L22.4154 18.3109L21.9288 18.6619ZM20.6788 17.7321C21.181 17.9062 21.6175 18.2308 21.9289 18.662L22.4153 18.3108C22.0318 17.7796 21.494 17.3796 20.8753 17.1652L20.6788 17.7321Z"
      fill={fill}
    />
    <path d="M5 5L26.5 26.5" stroke={fill} strokeWidth="2" strokeLinecap="round" />
  </svg>
)

export default Disconnect
