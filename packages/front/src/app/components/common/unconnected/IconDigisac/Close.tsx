import React from 'react'
import { IconProps } from './Types'

const Close = ({ fill, width, height, onClick, viewBox = '0 0 32 32', ...rest }: IconProps) => (
  <svg
    data-testid="svg-button-close"
    width={width || 32}
    height={height || 32}
    onClick={onClick}
    viewBox={viewBox}
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...{ ...rest }}
  >
    <path
      d="M17.6434 15.999L22.6552 10.9981C22.8747 10.7786 22.998 10.4809 22.998 10.1705C22.998 9.86005 22.8747 9.56233 22.6552 9.34282C22.4358 9.12332 22.1381 9 21.8277 9C21.5173 9 21.2196 9.12332 21.0002 9.34282L16 14.3554L10.9998 9.34282C10.7804 9.12332 10.4827 9 10.1723 9C9.86191 9 9.56424 9.12332 9.34476 9.34282C9.12529 9.56233 9.00199 9.86005 9.00199 10.1705C9.00199 10.4809 9.12529 10.7786 9.34476 10.9981L14.3566 15.999L9.34476 20.9999C9.23552 21.1083 9.14881 21.2372 9.08964 21.3792C9.03047 21.5213 9 21.6736 9 21.8275C9 21.9814 9.03047 22.1338 9.08964 22.2758C9.14881 22.4179 9.23552 22.5468 9.34476 22.6552C9.45312 22.7644 9.58203 22.8512 9.72406 22.9103C9.86609 22.9695 10.0184 23 10.1723 23C10.3262 23 10.4785 22.9695 10.6205 22.9103C10.7626 22.8512 10.8915 22.7644 10.9998 22.6552L16 17.6427L21.0002 22.6552C21.1085 22.7644 21.2374 22.8512 21.3795 22.9103C21.5215 22.9695 21.6738 23 21.8277 23C21.9816 23 22.1339 22.9695 22.2759 22.9103C22.418 22.8512 22.5469 22.7644 22.6552 22.6552C22.7645 22.5468 22.8512 22.4179 22.9104 22.2758C22.9695 22.1338 23 21.9814 23 21.8275C23 21.6736 22.9695 21.5213 22.9104 21.3792C22.8512 21.2372 22.7645 21.1083 22.6552 20.9999L17.6434 15.999Z"
      fill={fill}
    />
  </svg>
)

export default Close
