import React from 'react'
import { IconProps } from './Types'

const Chart = ({ fill, width, height }: IconProps) => (
  <svg width={width || 32} height={height || 32} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20 11C19.4477 11 19 10.5523 19 10C19 9.44772 19.4477 9 20 9H27C27.5523 9 28 9.44772 28 10V17C28 17.5523 27.5523 18 27 18C26.4477 18 26 17.5523 26 17V12.4142L17.7071 20.7071C17.3166 21.0976 16.6834 21.0976 16.2929 20.7071L12 16.4142L5.70711 22.7071C5.31658 23.0976 4.68342 23.0976 4.29289 22.7071C3.90237 22.3166 3.90237 21.6834 4.29289 21.2929L11.2929 14.2929C11.6834 13.9024 12.3166 13.9024 12.7071 14.2929L17 18.5858L24.5858 11H20Z"
      fill={fill}
    />
  </svg>
)

export default Chart
