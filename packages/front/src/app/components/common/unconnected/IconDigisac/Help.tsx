import React from 'react'
import { IconProps } from './Types'

const Help = ({ width, height, fill, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16 8C11.5817 8 8 11.5817 8 16C8 20.4183 11.5817 24 16 24C20.4183 24 24 20.4183 24 16C24 11.5817 20.4183 8 16 8ZM6 16C6 10.4772 10.4772 6 16 6C21.5228 6 26 10.4772 26 16C26 21.5228 21.5228 26 16 26C10.4772 26 6 21.5228 6 16Z"
      fill={fill}
    />
    <path
      d="M13.381 13.3001C13.5926 12.6986 14.0102 12.1914 14.5599 11.8683C15.1097 11.5453 15.756 11.4272 16.3844 11.535C17.0129 11.6428 17.5829 11.9695 17.9936 12.4573C18.4042 12.9451 18.6289 13.5625 18.628 14.2001C18.628 16.0001 15.928 16.9001 15.928 16.9001"
      stroke={fill}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path d="M16 20.5H16.009" stroke={fill} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export default Help
