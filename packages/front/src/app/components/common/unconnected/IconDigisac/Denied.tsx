import React from 'react'
import { IconProps } from './Types'

const Denied = ({ width, height, className, fill }: IconProps) => (
  <svg
    width={width || 63}
    height={height || 62}
    className={className}
    viewBox="0 0 63 62"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M31.235 4.64801C16.6887 4.64801 4.89648 16.4402 4.89648 30.9866C4.89648 45.533 16.6887 57.3251 31.235 57.3251C45.7814 57.3251 57.5736 45.533 57.5736 30.9866C57.5736 16.4402 45.7814 4.64801 31.235 4.64801ZM8.51158 30.9866C8.51158 18.4367 18.6852 8.26311 31.235 8.26311C43.7849 8.26311 53.9585 18.4367 53.9585 30.9866C53.9585 43.5364 43.7849 53.71 31.235 53.71C18.6852 53.71 8.51158 43.5364 8.51158 30.9866Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M40.5253 20.3328C40.9022 19.9559 41.5132 19.9559 41.89 20.3328C42.2669 20.7096 42.2669 21.3206 41.89 21.6975L32.6009 30.9866L41.89 40.2757C42.2669 40.6525 42.2669 41.2635 41.89 41.6404C41.5132 42.0172 40.9022 42.0172 40.5253 41.6404L31.2362 32.3513L21.9471 41.6404C21.5703 42.0172 20.9593 42.0172 20.5824 41.6404C20.2056 41.2635 20.2056 40.6525 20.5824 40.2757L29.8715 30.9866L20.5824 21.6975C20.2056 21.3206 20.2056 20.7096 20.5824 20.3328C20.9593 19.9559 21.5703 19.9559 21.9471 20.3328L31.2362 29.6219L40.5253 20.3328Z"
      fill={fill}
    />
    <path
      d="M41.89 20.3328L42.5345 19.6883L42.5345 19.6883L41.89 20.3328ZM40.5253 20.3328L39.8809 19.6883L39.8809 19.6883L40.5253 20.3328ZM41.89 21.6975L42.5345 22.3419L42.5345 22.3419L41.89 21.6975ZM32.6009 30.9866L31.9565 30.3421L31.312 30.9866L31.9565 31.631L32.6009 30.9866ZM41.89 40.2757L41.2456 40.9201L41.2456 40.9201L41.89 40.2757ZM40.5253 41.6404L39.8809 42.2848L39.8809 42.2848L40.5253 41.6404ZM31.2362 32.3513L31.8807 31.7068L31.2362 31.0624L30.5918 31.7068L31.2362 32.3513ZM21.9471 41.6404L21.3027 40.9959L21.3027 40.9959L21.9471 41.6404ZM20.5824 41.6404L19.938 42.2848L19.938 42.2848L20.5824 41.6404ZM20.5824 40.2757L19.938 39.6312L19.938 39.6312L20.5824 40.2757ZM29.8715 30.9866L30.516 31.631L31.1604 30.9866L30.516 30.3421L29.8715 30.9866ZM20.5824 21.6975L21.2269 21.053L21.2269 21.053L20.5824 21.6975ZM20.5824 20.3328L21.2269 20.9772L21.2269 20.9772L20.5824 20.3328ZM21.9471 20.3328L22.5916 19.6883L22.5916 19.6883L21.9471 20.3328ZM31.2362 29.6219L30.5918 30.2663L31.2362 30.9108L31.8807 30.2663L31.2362 29.6219ZM42.5345 19.6883C41.8017 18.9556 40.6137 18.9556 39.8809 19.6883L41.1698 20.9772C41.1907 20.9563 41.2247 20.9563 41.2456 20.9772L42.5345 19.6883ZM42.5345 22.3419C43.2672 21.6091 43.2672 20.4211 42.5345 19.6883L41.2456 20.9772C41.2665 20.9981 41.2665 21.0321 41.2456 21.053L42.5345 22.3419ZM33.2454 31.631L42.5345 22.3419L41.2456 21.053L31.9565 30.3421L33.2454 31.631ZM31.9565 31.631L41.2456 40.9201L42.5345 39.6312L33.2454 30.3421L31.9565 31.631ZM41.2456 40.9201C41.2665 40.941 41.2665 40.975 41.2456 40.9959L42.5345 42.2848C43.2672 41.552 43.2672 40.364 42.5345 39.6312L41.2456 40.9201ZM41.2456 40.9959C41.2247 41.0169 41.1907 41.0169 41.1698 40.9959L39.8809 42.2848C40.6137 43.0176 41.8017 43.0176 42.5345 42.2848L41.2456 40.9959ZM41.1698 40.9959L31.8807 31.7068L30.5918 32.9957L39.8809 42.2848L41.1698 40.9959ZM22.5916 42.2848L31.8807 32.9957L30.5918 31.7068L21.3027 40.9959L22.5916 42.2848ZM19.938 42.2848C20.6708 43.0176 21.8588 43.0176 22.5916 42.2848L21.3027 40.9959C21.2818 41.0169 21.2478 41.0169 21.2269 40.9959L19.938 42.2848ZM19.938 39.6312C19.2052 40.364 19.2052 41.552 19.938 42.2848L21.2269 40.9959C21.2059 40.975 21.2059 40.941 21.2269 40.9201L19.938 39.6312ZM29.2271 30.3421L19.938 39.6312L21.2269 40.9201L30.516 31.631L29.2271 30.3421ZM30.516 30.3421L21.2269 21.053L19.938 22.3419L29.2271 31.631L30.516 30.3421ZM21.2269 21.053C21.2059 21.0321 21.2059 20.9981 21.2269 20.9772L19.938 19.6883C19.2052 20.4211 19.2052 21.6091 19.938 22.3419L21.2269 21.053ZM21.2269 20.9772C21.2478 20.9563 21.2818 20.9563 21.3027 20.9772L22.5916 19.6883C21.8588 18.9556 20.6708 18.9556 19.938 19.6883L21.2269 20.9772ZM21.3027 20.9772L30.5918 30.2663L31.8807 28.9774L22.5916 19.6883L21.3027 20.9772ZM39.8809 19.6883L30.5918 28.9774L31.8807 30.2663L41.1698 20.9772L39.8809 19.6883Z"
      fill={fill}
    />
  </svg>
)

export default Denied
