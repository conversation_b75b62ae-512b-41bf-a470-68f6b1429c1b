import React from 'react'
import { IconProps } from './Types'

const Instagram = ({ fill, width, height }: IconProps) => (
  <svg width={width || 28} height={height || 28} viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M26.996 8.58806C26.996 8.10171 26.9439 7.61784 26.8422 7.14141C26.4973 5.51858 25.7208 4.13645 24.4952 3.0223C23.0315 1.6898 21.3023 1.01238 19.3275 1.00493C15.7823 0.995007 12.2395 1.00245 8.6943 1.00245C8.18075 1.00245 7.67216 1.04712 7.17101 1.15382C5.38475 1.52851 3.90365 2.41684 2.74506 3.82875C1.60632 5.21833 1.02579 6.81883 1.00842 8.61287C0.991052 10.4094 1.00594 12.2034 1.00594 14C1.00594 15.7593 1.00594 17.5161 1.00594 19.2754C1.00594 19.9975 1.09525 20.7072 1.2962 21.402C1.74525 22.9652 2.59869 24.263 3.86395 25.2853C5.26815 26.4193 6.88075 26.9925 8.68437 26.995C12.2544 27 15.822 26.9975 19.392 26.995C20.1636 26.995 20.9202 26.8709 21.6546 26.6327C22.9422 26.2159 24.0536 25.5111 24.9641 24.5111C26.2815 23.067 26.9786 21.3623 26.9886 19.4069C27.0084 15.8015 26.996 12.1935 26.996 8.58806ZM25.0981 22.2903C24.2174 23.9578 22.8678 25.0595 21.0517 25.5831C20.4588 25.7543 19.851 25.8188 19.2357 25.8188C15.7451 25.8188 12.2519 25.8188 8.76128 25.8188C7.38933 25.8188 6.12903 25.4541 5.00021 24.6699C3.71014 23.7742 2.85174 22.5632 2.42254 21.0521C2.26128 20.4789 2.18933 19.8958 2.18933 19.3002C2.19182 15.7245 2.17445 12.1464 2.19678 8.57069C2.20918 6.73942 2.92369 5.18607 4.25346 3.92801C5.17884 3.05456 6.27788 2.5161 7.52827 2.28533C7.91281 2.21337 8.30231 2.18359 8.6943 2.18359C12.2296 2.18359 15.7649 2.18111 19.3002 2.18607C21.1262 2.18856 22.6867 2.84612 23.9693 4.14141C24.8773 5.05952 25.4479 6.16622 25.696 7.43421C25.7853 7.88335 25.8176 8.33496 25.8176 8.79153C25.8176 10.5285 25.8176 12.2655 25.8176 14.0025C25.8176 15.7494 25.8151 17.4938 25.8176 19.2407C25.8176 20.3176 25.5993 21.3424 25.0981 22.2903Z"
      fill={fill}
    />
    <path
      d="M26.996 8.58806C26.996 8.10171 26.9439 7.61784 26.8422 7.14141C26.4973 5.51858 25.7208 4.13645 24.4952 3.0223C23.0315 1.6898 21.3023 1.01238 19.3275 1.00493C15.7823 0.995007 12.2395 1.00245 8.6943 1.00245C8.18075 1.00245 7.67216 1.04712 7.17101 1.15382C5.38475 1.52851 3.90365 2.41684 2.74506 3.82875C1.60632 5.21833 1.02579 6.81883 1.00842 8.61287C0.991052 10.4094 1.00594 12.2034 1.00594 14C1.00594 15.7593 1.00594 17.5161 1.00594 19.2754C1.00594 19.9975 1.09525 20.7072 1.2962 21.402C1.74525 22.9652 2.59869 24.263 3.86395 25.2853C5.26815 26.4193 6.88075 26.9925 8.68437 26.995C12.2544 27 15.822 26.9975 19.392 26.995C20.1636 26.995 20.9202 26.8709 21.6546 26.6327C22.9422 26.2159 24.0536 25.5111 24.9641 24.5111C26.2815 23.067 26.9786 21.3623 26.9886 19.4069C27.0084 15.8015 26.996 12.1935 26.996 8.58806ZM25.0981 22.2903C24.2174 23.9578 22.8678 25.0595 21.0517 25.5831C20.4588 25.7543 19.851 25.8188 19.2357 25.8188C15.7451 25.8188 12.2519 25.8188 8.76128 25.8188C7.38933 25.8188 6.12903 25.4541 5.00021 24.6699C3.71014 23.7742 2.85174 22.5632 2.42254 21.0521C2.26128 20.4789 2.18933 19.8958 2.18933 19.3002C2.19182 15.7245 2.17445 12.1464 2.19678 8.57069C2.20918 6.73942 2.92369 5.18607 4.25346 3.92801C5.17884 3.05456 6.27788 2.5161 7.52827 2.28533C7.91281 2.21337 8.30231 2.18359 8.6943 2.18359C12.2296 2.18359 15.7649 2.18111 19.3002 2.18607C21.1262 2.18856 22.6867 2.84612 23.9693 4.14141C24.8773 5.05952 25.4479 6.16622 25.696 7.43421C25.7853 7.88335 25.8176 8.33496 25.8176 8.79153C25.8176 10.5285 25.8176 12.2655 25.8176 14.0025C25.8176 15.7494 25.8151 17.4938 25.8176 19.2407C25.8176 20.3176 25.5993 21.3424 25.0981 22.2903Z"
      stroke={fill}
      strokeWidth="0.6"
      mask="url(#path-2-outside-1_3_161)"
    />
    <path
      d="M14.0135 7.50123C10.4435 7.49379 7.50859 10.4119 7.50611 13.9727C7.50362 17.5757 10.4113 20.4963 14.0011 20.4988C17.5737 20.5012 20.4987 17.5806 20.5011 14.005C20.5036 10.4318 17.591 7.50868 14.0135 7.50123ZM13.9912 19.3176C11.0513 19.3151 8.68454 16.9404 8.68702 13.9926C8.6895 11.0521 11.0662 8.68486 14.0111 8.68734C16.9485 8.68982 19.3177 11.062 19.3177 14.0025C19.3153 16.9504 16.9435 19.3176 13.9912 19.3176Z"
      fill={fill}
    />
    <path
      d="M14.0135 7.50123C10.4435 7.49379 7.50859 10.4119 7.50611 13.9727C7.50362 17.5757 10.4113 20.4963 14.0011 20.4988C17.5737 20.5012 20.4987 17.5806 20.5011 14.005C20.5036 10.4318 17.591 7.50868 14.0135 7.50123ZM13.9912 19.3176C11.0513 19.3151 8.68454 16.9404 8.68702 13.9926C8.6895 11.0521 11.0662 8.68486 14.0111 8.68734C16.9485 8.68982 19.3177 11.062 19.3177 14.0025C19.3153 16.9504 16.9435 19.3176 13.9912 19.3176Z"
      stroke={fill}
      strokeWidth="0.6"
      mask="url(#path-3-outside-2_3_161)"
    />
    <path
      d="M21.0991 5.72953C20.4466 5.72456 19.9082 6.25806 19.9082 6.91067C19.9057 7.56079 20.4366 8.09181 21.0866 8.09181C21.7317 8.09429 22.27 7.57072 22.27 6.93796C22.2725 6.26799 21.754 5.73201 21.0991 5.72953Z"
      fill={fill}
    />
    <path
      d="M21.0991 5.72953C20.4466 5.72456 19.9082 6.25806 19.9082 6.91067C19.9057 7.56079 20.4366 8.09181 21.0866 8.09181C21.7317 8.09429 22.27 7.57072 22.27 6.93796C22.2725 6.26799 21.754 5.73201 21.0991 5.72953Z"
      stroke={fill}
      strokeWidth="0.6"
      mask="url(#path-4-outside-3_3_161)"
    />
  </svg>
)

export default Instagram
