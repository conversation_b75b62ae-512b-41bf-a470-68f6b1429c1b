import React from 'react'
import { IconProps } from './Types'

const Plug = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.6189 8.15779C20.7414 8.0437 20.8396 7.90613 20.9077 7.75328C20.9758 7.60042 21.0124 7.43541 21.0153 7.2681C21.0183 7.10078 20.9875 6.93458 20.9248 6.77942C20.8622 6.62426 20.7689 6.48331 20.6506 6.36498C20.5322 6.24665 20.3913 6.15337 20.2361 6.09069C20.081 6.02802 19.9148 5.99724 19.7474 6.00019C19.5801 6.00315 19.4151 6.03977 19.2623 6.10788C19.1094 6.17598 18.9718 6.27418 18.8578 6.39661L16 9.25437L15.2191 8.47347C14.9855 8.24011 14.6687 8.10904 14.3385 8.10904C14.0083 8.10904 13.6916 8.24011 13.4579 8.47347L10.5088 11.4226C9.32999 12.6014 8.60616 14.1592 8.46537 15.8204C8.32459 17.4815 8.77592 19.1389 9.73952 20.4993L6.39661 23.8422C6.27418 23.9563 6.17598 24.0939 6.10788 24.2467C6.03977 24.3996 6.00315 24.5646 6.00019 24.7319C5.99724 24.8992 6.02802 25.0654 6.09069 25.2206C6.15337 25.3757 6.24665 25.5167 6.36498 25.635C6.48331 25.7534 6.62426 25.8466 6.77942 25.9093C6.93458 25.972 7.10078 26.0028 7.2681 25.9998C7.43541 25.9969 7.60042 25.9602 7.75328 25.8921C7.90613 25.824 8.0437 25.7258 8.15779 25.6034L11.5007 22.2605C12.8611 23.2241 14.5185 23.6754 16.1796 23.5346C17.8408 23.3938 19.3986 22.67 20.5774 21.4912L23.5265 18.5421C23.7599 18.3084 23.891 17.9917 23.891 17.6615C23.891 17.3313 23.7599 17.0145 23.5265 16.7809L22.7456 16L25.6034 13.1422C25.7258 13.0282 25.824 12.8906 25.8921 12.7377C25.9602 12.5849 25.9969 12.4199 25.9998 12.2526C26.0028 12.0852 25.972 11.919 25.9093 11.7639C25.8466 11.6087 25.7534 11.4678 25.635 11.3494C25.5167 11.2311 25.3757 11.1378 25.2206 11.0752C25.0654 11.0125 24.8992 10.9817 24.7319 10.9847C24.5646 10.9876 24.3996 11.0242 24.2467 11.0923C24.0939 11.1604 23.9563 11.2586 23.8422 11.3811L20.9845 14.2388L17.7612 11.0155L20.6189 8.15779ZM15.1128 11.8911L15.1194 11.8961L15.1244 11.9028L20.0972 16.8756L20.1039 16.8806L20.1089 16.8872L20.8831 17.6615L18.8162 19.73C18.3919 20.1545 17.8881 20.4913 17.3336 20.721C16.7792 20.9508 16.1848 21.069 15.5846 21.069C14.9844 21.069 14.3901 20.9508 13.8356 20.721C13.2811 20.4913 12.7773 20.1545 12.353 19.73L12.27 19.647C11.8455 19.2227 11.5087 18.7189 11.279 18.1644C11.0492 17.6099 10.931 17.0156 10.931 16.4154C10.931 15.8152 11.0492 15.2209 11.279 14.6664C11.5087 14.1119 11.8455 13.6081 12.27 13.1838L14.3385 11.1186L15.1128 11.8928V11.8911Z"
      fill={fill}
    />
  </svg>
)

export default Plug
