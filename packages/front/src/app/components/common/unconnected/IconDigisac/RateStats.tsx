import React from 'react'
import { IconProps } from './Types'

const RateStats = ({ width, height, fill, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.08824 5.15894C5.42832 5.15894 4.79543 5.42109 4.32879 5.88772C3.86216 6.35436 3.60001 6.98725 3.60001 7.64717V20.1766C3.60001 20.8365 3.86216 21.4694 4.32879 21.936C4.79543 22.4027 5.42832 22.6648 6.08824 22.6648H12.904C13.5093 22.6648 14 22.1741 14 21.5687C14 20.9634 13.5093 20.4727 12.904 20.4727H6.08824C6.00972 20.4727 5.93441 20.4415 5.87888 20.3859C5.82336 20.3304 5.79216 20.2551 5.79216 20.1766V7.64717C5.79216 7.56865 5.82336 7.49334 5.87888 7.43781C5.93441 7.38229 6.00972 7.35109 6.08824 7.35109H24.1863C24.2648 7.35109 24.3401 7.38229 24.3956 7.43781C24.4512 7.49334 24.4824 7.56865 24.4824 7.64717V12.5038C24.4824 13.1091 24.9731 13.5998 25.5784 13.5998C26.1838 13.5998 26.6745 13.1091 26.6745 12.5038V7.64717C26.6745 6.98725 26.4124 6.35436 25.9457 5.88772C25.4791 5.42109 24.8462 5.15894 24.1863 5.15894H6.08824Z"
      fill={fill}
    />
    <path
      d="M28.5294 21.2647C28.5294 22.9262 27.8694 24.5197 26.6946 25.6945C25.5197 26.8694 23.9262 27.5294 22.2647 27.5294C20.6032 27.5294 19.0098 26.8694 17.8349 25.6945C16.6601 24.5197 16 22.9262 16 21.2647C16 19.6032 16.6601 18.0098 17.8349 16.8349C19.0098 15.66 20.6032 15 22.2647 15C23.9262 15 25.5197 15.66 26.6946 16.8349C27.8694 18.0098 28.5294 19.6032 28.5294 21.2647ZM22.9274 17.5894C22.8872 17.4446 22.8006 17.3169 22.6809 17.2259C22.5613 17.1349 22.4151 17.0856 22.2647 17.0856C22.1144 17.0856 21.9682 17.1349 21.8485 17.2259C21.7289 17.3169 21.6423 17.4446 21.6021 17.5894L20.938 19.7222H18.7843C18.1105 19.7222 17.8293 20.6229 18.3751 21.0364L20.1152 22.3548L19.4512 24.4875C19.2424 25.1558 19.9774 25.7126 20.5231 25.2992L22.2633 23.9808L24.0035 25.2992C24.5493 25.7126 25.2843 25.1558 25.0755 24.4875L24.4114 22.3548L26.1516 21.0364C26.6974 20.6229 26.4161 19.7222 25.7423 19.7222H23.5915L22.926 17.5908L22.9274 17.5894Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.9799 9C16.5104 9 16.1557 9.42152 16.1557 9.90677C16.1557 10.392 16.5104 10.8135 16.9799 10.8135H19.2114L15.1817 15.3292L12.7728 12.6298C12.4499 12.2679 11.9194 12.2679 11.5965 12.6298L8.23602 16.4579C7.92133 16.8105 7.92133 17.376 8.23602 17.7286C8.55896 18.0905 9.08941 18.0905 9.41236 17.7286L12.1846 14.5596L14.5935 17.259C14.9165 17.6209 15.4469 17.6209 15.7699 17.259L20.3516 12.1248V14.6086C20.3516 15.0938 20.7062 15.5153 21.1758 15.5153C21.6453 15.5153 22 15.0938 22 14.6086V9.90677C22 9.42152 21.6453 9 21.1758 9H16.9799Z"
      fill={fill}
    />
  </svg>
)

export default RateStats
