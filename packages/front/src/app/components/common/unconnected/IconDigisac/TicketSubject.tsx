import React from 'react'
import { IconProps } from './Types'

const TicketSubject = ({ width, height, fill, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M26.5028 12.5263C26.3178 12.3077 26.0461 12.1824 25.7575 12.1824H6.96907C6.68072 12.1824 6.40929 12.3077 6.22429 12.526C6.05164 12.7298 5.97363 12.995 6.00792 13.2582L7.11711 24.8337C7.11808 24.8426 7.11905 24.8515 7.12026 24.8602C7.21782 25.5905 7.838 26.1411 8.56298 26.1411H24.1637C24.9259 26.1411 25.5615 25.5574 25.6095 24.8337L26.7187 13.2585C26.753 12.9953 26.6752 12.7301 26.5028 12.5263ZM24.1675 24.6936C24.1668 24.6924 24.1632 24.6917 24.1637 24.6922L8.57577 24.6944C8.5695 24.6914 8.56031 24.6806 8.55743 24.6721L7.49913 13.6316H25.2274L24.1675 24.6936Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.03356 12.3644C6.26642 12.0896 6.60781 11.9324 6.96907 11.9324H25.7575C26.1189 11.9324 26.4607 12.0895 26.6936 12.3647C26.9105 12.6211 27.0091 12.9548 26.9672 13.2863L25.8587 24.854C25.7996 25.714 25.0495 26.3911 24.1637 26.3911H8.56298C7.7096 26.3911 6.9862 25.7447 6.87246 24.8933C6.87074 24.8807 6.86944 24.8687 6.86857 24.8606L6.86823 24.8575L5.75944 13.2861C5.7175 12.9544 5.81648 12.6207 6.03356 12.3644ZM6.41502 12.6876C6.28779 12.8379 6.23064 13.0326 6.25582 13.226L6.25692 13.2344L7.36576 24.8076L7.36786 24.8257L7.36807 24.8271C7.44945 25.4363 7.96639 25.8911 8.56298 25.8911H24.1637C24.801 25.8911 25.3212 25.4029 25.3601 24.8171L25.3606 24.8098L26.4707 13.2262C26.4958 13.0328 26.4389 12.8379 26.312 12.6878C26.1749 12.5259 25.9733 12.4324 25.7575 12.4324H6.96907C6.75364 12.4324 6.55215 12.5258 6.41502 12.6876ZM7.22402 13.3816H25.5025L24.4274 24.6024L24.7671 24.9421L24.3948 24.9421L24.3439 25.4736L24.0251 24.9422L8.52056 24.9444L8.47045 24.9211C8.41743 24.8965 8.38509 24.8596 8.37318 24.8453C8.35808 24.8271 8.33534 24.7956 8.32072 24.7525L8.31135 24.725L7.22402 13.3816ZM23.9404 24.4422L24.9523 13.8816H7.77424L8.78674 24.4443L23.9404 24.4422Z"
      fill={fill}
    />
    <path
      d="M24.8958 9.39945C24.7123 9.18814 24.4454 9.06689 24.1633 9.06689H8.58684C8.30501 9.06689 8.03694 9.19271 7.85125 9.412C7.66459 9.63274 7.58391 9.92012 7.62933 10.1913L8.06402 13.0168L9.49634 12.7966L9.14544 10.5159H23.6051L23.2544 12.7966L24.6865 13.0168L25.1222 10.1831C25.1627 9.89769 25.0803 9.61198 24.8958 9.39945Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.66046 9.25044C7.89255 8.97636 8.22981 8.81689 8.58684 8.81689H24.1633C24.5173 8.81689 24.8532 8.9691 25.0846 9.23551C25.318 9.50437 25.4201 9.86329 25.3697 10.2182L25.3693 10.2211L24.8956 13.3019L22.9693 13.0057L23.3137 10.7659H9.43685L9.78145 13.0057L7.85494 13.3019L7.38248 10.2309C7.32505 9.88471 7.42877 9.52445 7.66046 9.25044ZM8.04215 9.57342C7.90092 9.74044 7.84312 9.95432 7.8759 10.15L7.87645 10.1533L8.2731 12.7317L9.21122 12.5875L8.85404 10.2659H23.8964L23.5395 12.5875L24.4774 12.7317L24.8746 10.1479C24.8747 10.1475 24.8748 10.147 24.8748 10.1465C24.905 9.93121 24.8424 9.71924 24.7071 9.56339M8.04215 9.57342C8.18144 9.40901 8.38026 9.31689 8.58684 9.31689H24.1633C24.3736 9.31689 24.5714 9.40719 24.7071 9.56339"
      fill={fill}
    />
    <path
      d="M22.7475 6.33304C22.5639 6.12148 22.2968 6 22.0143 6H10.7364C10.4546 6 10.1865 6.12582 10.0008 6.3451C9.8139 6.56585 9.73346 6.85323 9.77912 7.12635L10.2138 9.90358L11.6454 9.67947L11.2962 7.44899H21.4495L21.0821 9.6734L22.5118 9.90957L22.973 7.11737C23.0141 6.83149 22.9317 6.54581 22.7475 6.33304Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.81001 6.18355C10.0421 5.90947 10.3794 5.75 10.7364 5.75H22.0143C22.3687 5.75 22.7049 5.90248 22.9363 6.16921C23.1693 6.43824 23.2715 6.79724 23.2205 7.1529L23.2197 7.15812L22.7177 10.197L20.7947 9.87931L21.1548 7.69899H11.5884L11.9311 9.8878L10.0055 10.1892L9.53232 7.16622C9.4746 6.81847 9.5776 6.45804 9.81001 6.18355ZM10.1916 6.50666C10.0505 6.6733 9.99258 6.88705 10.0257 7.08513L10.0261 7.08769L10.4221 9.61792L11.3597 9.47115L11.004 7.19899H21.7441L21.3695 9.46749L22.3059 9.62217L22.7259 7.07941C22.7561 6.86414 22.6935 6.65263 22.5585 6.49671C22.4228 6.34042 22.2249 6.25 22.0143 6.25H10.7364C10.5298 6.25 10.3309 6.34217 10.1916 6.50666Z"
      fill={fill}
    />
    <path
      d="M19.4062 16.7466H13.3204C12.9203 16.7466 12.5959 17.0709 12.5959 17.4711C12.5959 17.8712 12.9203 18.1956 13.3204 18.1956H19.4062C19.8063 18.1956 20.1307 17.8712 20.1307 17.4711C20.1306 17.0709 19.8063 16.7466 19.4062 16.7466Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.3459 17.4711C12.3459 16.9328 12.7822 16.4966 13.3204 16.4966H19.4062C19.9444 16.4966 20.3806 16.9329 20.3807 17.4711V17.4711C20.3807 18.0093 19.9444 18.4456 19.4062 18.4456H13.3204C12.7822 18.4456 12.3459 18.0093 12.3459 17.4711ZM13.3204 16.9966C13.0584 16.9966 12.8459 17.209 12.8459 17.4711C12.8459 17.7332 13.0584 17.9456 13.3204 17.9456H19.4062C19.6682 17.9456 19.8807 17.7332 19.8807 17.4711M13.3204 16.9966H19.4062H13.3204ZM19.4062 16.9966C19.6682 16.9966 19.8806 17.209 19.8807 17.4711L19.4062 16.9966Z"
      fill={fill}
    />
  </svg>
)

export default TicketSubject
