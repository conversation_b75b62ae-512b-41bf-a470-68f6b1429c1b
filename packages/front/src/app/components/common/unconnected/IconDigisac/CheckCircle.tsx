import React from 'react'
import { IconProps } from './Types'

const CheckCircle = ({ fill = '#324B7D', width = '24', height = '24' }: IconProps) => (
  <svg width={width} height={height} viewBox="0 0 12 12" fill={fill} xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_5451_383)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.8315 1.88665C6.94512 1.4917 5.95482 1.39386 5.00829 1.60771C4.06175 1.82157 3.20971 2.33567 2.57923 3.07333C1.94874 3.81099 1.5736 4.7327 1.50976 5.70099C1.44591 6.66928 1.69677 7.63226 2.22493 8.44633C2.7531 9.26039 3.53026 9.88192 4.44051 10.2182C5.35077 10.5545 6.34534 10.5875 7.27591 10.3124C8.20648 10.0373 9.02318 9.46869 9.6042 8.69147C10.1852 7.91425 10.4994 6.97004 10.5 5.99965V5.53993C10.5 5.26379 10.7239 5.03993 11 5.03993C11.2761 5.03993 11.5 5.26379 11.5 5.53993V5.99993C11.4993 7.18597 11.1153 8.34029 10.4051 9.29022C9.69499 10.2402 8.69681 10.9351 7.55945 11.2714C6.42209 11.6076 5.20649 11.5673 4.09396 11.1562C2.98142 10.7452 2.03156 9.98557 1.38603 8.99061C0.740496 7.99564 0.433885 6.81865 0.511922 5.63519C0.589959 4.45173 1.04846 3.3252 1.81905 2.42361C2.58964 1.52202 3.63103 0.893679 4.7879 0.632299C5.94478 0.370919 7.15515 0.490504 8.2385 0.973219C8.49074 1.08561 8.60411 1.3812 8.49172 1.63343C8.37933 1.88567 8.08374 1.99904 7.8315 1.88665ZM11.3534 1.6462C11.5487 1.84137 11.5489 2.15795 11.3537 2.35331L6.35373 7.35831C6.25998 7.45216 6.13278 7.5049 6.00013 7.50493C5.86748 7.50497 5.74025 7.45229 5.64645 7.35849L4.14645 5.85849C3.95119 5.66322 3.95119 5.34664 4.14645 5.15138C4.34171 4.95612 4.65829 4.95612 4.85356 5.15138L5.99983 6.29765L10.6463 1.64656C10.8414 1.4512 11.158 1.45104 11.3534 1.6462Z"
        fill="#324B7D"
      />
    </g>
    <defs>
      <clipPath id="clip0_5451_383">
        <rect width={width} height={height} fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export default CheckCircle
