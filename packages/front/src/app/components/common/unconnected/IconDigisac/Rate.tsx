import React from 'react'
import { IconProps } from './Types'

const Rate = ({ width, height, fill, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M23.7778 6C24.3671 6 24.9324 6.23413 25.3491 6.65087C25.7659 7.06762 26 7.63285 26 8.22222V23.7778C26 24.3671 25.7659 24.9324 25.3491 25.3491C24.9324 25.7659 24.3671 26 23.7778 26H8.22222C7.63285 26 7.06762 25.7659 6.65087 25.3491C6.23413 24.9324 6 24.3671 6 23.7778V8.22222C6 7.63285 6.23413 7.06762 6.65087 6.65087C7.06762 6.23413 7.63285 6 8.22222 6H23.7778ZM8.22222 8.22222V23.7778H23.7778V8.22222H8.22222Z"
      fill={fill}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16 17.1715L17.3303 17.938C17.4828 18.0258 17.6662 17.8882 17.6245 17.7173L17.2751 16.2844L18.2905 15.4467C18.429 15.3325 18.3586 15.1079 18.1797 15.0931L16.8176 14.9808L16.1855 13.5539C16.1152 13.3951 15.8898 13.3952 15.8197 13.554L15.1837 14.9935L13.8203 15.1059C13.6414 15.1206 13.571 15.3453 13.7095 15.4595L14.7255 16.2977L14.379 17.7152C14.3373 17.8861 14.5207 18.0238 14.6732 17.936L16 17.1715ZM12.4847 17.0419L11.863 19.5856C11.6211 20.5751 12.7448 21.3552 13.6559 20.8303L16 19.4797L18.346 20.8314C19.2568 21.3561 20.3803 20.5766 20.139 19.5873L19.5153 17.0291L21.5818 15.3242C22.387 14.66 21.959 13.3979 20.8989 13.3105L18.1653 13.0852L17.1094 10.7012C16.695 9.76578 15.3065 9.7664 14.8931 10.7022L13.8347 13.0979L11.1011 13.3233C10.041 13.4107 9.61301 14.6727 10.4182 15.337L12.4847 17.0419Z"
      fill={fill}
    />
  </svg>
)

export default Rate
