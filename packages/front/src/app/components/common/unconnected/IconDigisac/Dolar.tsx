import React from 'react'
import { IconProps } from './Types'

const Dolar = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.1677 15.1172L13.9177 13.8828C13.31 13.7422 12.8871 13.2852 12.8871 12.7773C12.8871 12.1406 13.5288 11.625 14.3211 11.625H17.5441C18.1371 11.625 18.7205 11.7695 19.2066 12.0352C19.5031 12.1953 19.9017 12.1562 20.1545 11.957L21.8461 10.6289C22.1913 10.3594 22.1427 9.91016 21.7586 9.67188C20.5677 8.92187 19.0802 8.50391 17.5538 8.5V6.625C17.5538 6.28125 17.2038 6 16.776 6H15.2205C14.7927 6 14.4427 6.28125 14.4427 6.625V8.5H14.3211C11.2246 8.5 8.73573 10.6367 9.02253 13.1719C9.2267 14.9727 10.9378 16.4375 13.0961 16.9453L18.0788 18.1172C18.6864 18.2617 19.1093 18.7148 19.1093 19.2227C19.1093 19.8594 18.4677 20.375 17.6753 20.375H14.4524C13.8593 20.375 13.276 20.2305 12.7899 19.9648C12.4934 19.8047 12.0948 19.8438 11.842 20.043L10.1503 21.3711C9.80517 21.6406 9.85378 22.0898 10.2378 22.3281C11.4288 23.0781 12.9163 23.4961 14.4427 23.5V25.375C14.4427 25.7188 14.7927 26 15.2205 26H16.776C17.2038 26 17.5538 25.7188 17.5538 25.375V23.4922C19.8191 23.457 21.9434 22.375 22.692 20.6523C23.7371 18.2461 21.9823 15.7773 19.1677 15.1172V15.1172Z"
      fill={fill}
    />
  </svg>
)

export default Dolar
