import React from 'react'
import { IconProps } from './Types'

const User = ({ fill, width, height }: IconProps) => (
  <svg width={width || 24} height={height || 24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.9442 9C15.9442 11.2091 14.1533 13 11.9442 13C9.73506 13 7.9442 11.2091 7.9442 9C7.9442 6.79086 9.73506 5 11.9442 5C14.1533 5 15.9442 6.79086 15.9442 9ZM14.3686 13.3739C15.9047 12.5207 16.9442 10.8817 16.9442 9C16.9442 6.23858 14.7056 4 11.9442 4C9.18277 4 6.9442 6.23858 6.9442 9C6.9442 10.8817 7.98371 12.5207 9.51981 13.374C6.59317 14.3035 4.39627 16.8712 4.00611 20C3.93771 20.5486 4.45428 21 5.00712 21H18.9975C19.5545 21 19.9513 20.5528 19.8823 20C19.4922 16.8712 17.2953 14.3035 14.3686 13.3739ZM11.9442 14C15.4707 14 18.3881 16.6077 18.8733 20H5.01511C5.50034 16.6077 8.41775 14 11.9442 14Z"
      fill={fill}
    />
  </svg>
)

export default User
