import React from 'react'
import { IconProps } from './Types'

const GitMerge = ({ width, height, fill, className }: IconProps) => (
  <svg
    width={width || '14'}
    height={height || '14'}
    className={className}
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.99992 1.66666C2.26354 1.66666 1.66659 2.26362 1.66659 2.99999C1.66659 3.73637 2.26354 4.33333 2.99992 4.33333C3.7363 4.33333 4.33325 3.73637 4.33325 2.99999C4.33325 2.26362 3.7363 1.66666 2.99992 1.66666ZM0.333252 2.99999C0.333252 1.52724 1.52716 0.333328 2.99992 0.333328C4.47268 0.333328 5.66659 1.52724 5.66659 2.99999C5.66659 4.23136 4.83198 5.26779 3.6976 5.57447C3.82793 6.77771 4.36483 7.90738 5.22868 8.77123C6.09253 9.63508 7.22221 10.172 8.42545 10.3023C8.73212 9.16793 9.76855 8.33333 10.9999 8.33333C12.4727 8.33333 13.6666 9.52724 13.6666 11C13.6666 12.4728 12.4727 13.6667 10.9999 13.6667C9.74799 13.6667 8.69756 12.8039 8.4107 11.6406C6.85857 11.5029 5.39647 10.8246 4.28587 9.71404C4.0615 9.48967 3.85477 9.25095 3.66659 9.00004V13C3.66659 13.3682 3.36811 13.6667 2.99992 13.6667C2.63173 13.6667 2.33325 13.3682 2.33325 13V5.58265C1.1831 5.28662 0.333252 4.24255 0.333252 2.99999ZM10.9999 9.66666C10.2635 9.66666 9.66659 10.2636 9.66659 11C9.66659 11.7364 10.2635 12.3333 10.9999 12.3333C11.7363 12.3333 12.3333 11.7364 12.3333 11C12.3333 10.2636 11.7363 9.66666 10.9999 9.66666Z"
      fill={fill}
    />
  </svg>
)

export default GitMerge
