import React from 'react'
import { IconProps } from './Types'

const Handshake = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.3013 8H17.08C16.78 8 16.4913 8.12495 16.27 8.34987L12.5838 12.0985C12.58 12.1027 12.5763 12.111 12.5725 12.1152C11.95 12.7649 11.9613 13.8021 12.4938 14.4477C12.97 15.0266 13.9713 15.1807 14.5975 14.5601C14.6013 14.556 14.6088 14.556 14.6125 14.5518L17.6088 11.5029C17.8525 11.2572 18.235 11.2738 18.4563 11.5445C18.6813 11.8153 18.6625 12.236 18.4188 12.4859L17.44 13.4813L22.9 18.4046C23.0088 18.5045 23.1063 18.6128 23.1963 18.7253V10.6657L21.1488 8.39152C20.9275 8.14162 20.62 8 20.3013 8V8ZM24.4 10.674V19.9998C24.4 20.737 24.9363 21.3327 25.6 21.3327H28V10.674H24.4ZM26.2 19.9998C25.87 19.9998 25.6 19.6999 25.6 19.3334C25.6 18.9669 25.87 18.667 26.2 18.667C26.53 18.667 26.8 18.9669 26.8 19.3334C26.8 19.6999 26.53 19.9998 26.2 19.9998ZM4 21.3285H6.4C7.06375 21.3285 7.6 20.7329 7.6 19.9956V10.674H4V21.3285ZM5.8 18.667C6.13 18.667 6.4 18.9669 6.4 19.3334C6.4 19.6999 6.13 19.9998 5.8 19.9998C5.47 19.9998 5.2 19.6999 5.2 19.3334C5.2 18.9627 5.47 18.667 5.8 18.667ZM22.1463 19.4417L16.5475 14.3935L15.4225 15.5389C14.3088 16.6677 12.6025 16.5594 11.6088 15.3557C10.6 14.1311 10.6788 12.236 11.7738 11.1197L14.8413 8H11.6988C11.38 8 11.0763 8.14162 10.8513 8.39152L8.8 10.6657V19.9915H9.48625L12.88 23.4027C13.9075 24.3316 15.4188 24.1566 16.255 23.0154L16.2625 23.0071L16.9338 23.6527C17.53 24.1941 18.4113 24.09 18.895 23.4277L20.0725 21.82L20.275 22.0033C20.7888 22.4656 21.5463 22.3823 21.9625 21.8075L22.3188 21.3202C22.7388 20.7454 22.66 19.9082 22.1463 19.4417V19.4417Z"
      fill={fill}
    />
  </svg>
)

export default Handshake
