import React from 'react'
import { IconProps } from './Types'

const Terms = ({ fill, width, height, className }: IconProps) => (
  <svg
    width={width || 32}
    height={height || 32}
    className={className}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M24.1234 9.01243L20.6252 5.80524C20.0594 5.2897 19.2953 5 18.5031 5L10 5C8.34297 5 7 6.23105 7 7.75L7.0003 24.25C7.0003 25.7685 8.34327 27 10.0003 27H22C23.65 27 25 25.7625 25 24.25V10.9555C25 10.2293 24.6859 9.52891 24.1234 9.01243ZM22.75 24.25C22.75 24.6297 22.4142 24.9375 22 24.9375H10.0009C9.58666 24.9375 9.25094 24.6297 9.25094 24.25L9.25 7.75559C9.25 7.37592 9.58581 7.06809 10 7.06809H17.5V10.5C17.5 11.2593 18.1717 11.875 19 11.875H22.7078V24.25H22.75ZM11.5 17.0313C11.5 17.6027 12.0063 18.0625 12.625 18.0625H19.375C19.9984 18.0625 20.5 17.6027 20.5 17.0313C20.5 16.4598 19.9984 16 19.375 16H12.625C12.0063 16 11.5 16.4641 11.5 17.0313ZM19.375 20.125H12.625C12.0063 20.125 11.5 20.5891 11.5 21.1563C11.5 21.7234 12.0039 22.1875 12.625 22.1875H19.375C19.9961 22.1875 20.5 21.7256 20.5 21.1563C20.5 20.5869 19.9984 20.125 19.375 20.125Z"
      fill={fill}
    />
  </svg>
)

export default Terms
