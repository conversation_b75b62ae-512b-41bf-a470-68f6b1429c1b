import React from 'react'
import { IconProps } from './Types'

const Gift = ({ width, height, fill, className }: IconProps) => (
  <svg
    width={width || 45}
    height={height || 50}
    fill="none"
    className={className}
    viewBox="0 0 56 62"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M29 16.8125C29 16.8125 25.375 0.500014 14.5 5.93751C3.625 11.375 29 16.8125 29 16.8125ZM29 16.8125C29 16.8125 32.625 0.500014 43.5 5.93751C54.375 11.375 29 16.8125 29 16.8125ZM29 16.8125V54.875M7.25 25.875V54.875H50.75V25.875H7.25ZM3.625 16.8125V25.875H54.375V16.8125H3.625Z"
      stroke={fill}
      strokeWidth="3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export default Gift
