import React from 'react'
import { But<PERSON> as <PERSON>tra<PERSON><PERSON><PERSON><PERSON> } from 'reactstrap'
import Icon from '../Icon/index'
import { But<PERSON> } from '../ui/button'

const BootstrapLoadingButton = ({ loadIcon = 'spinner', withMargin = false, isLoading, children, ...rest }) => (
  <BootstrapButton disabled={isLoading} {...rest}>
    {isLoading ? (
      <span className={withMargin ? 'mx-4' : ''}>
        <Icon name={loadIcon} spin fixedWidth />
      </span>
    ) : (
      children
    )}
  </BootstrapButton>
)

export const LoadingButton = ({ loadIcon = 'spinner', withMargin = false, isLoading, children, ...rest }) => (
  <Button disabled={isLoading} {...rest}>
    {isLoading ? (
      <span className={withMargin ? 'mx-4' : ''}>
        <Icon name={loadIcon} spin fixedWidth />
      </span>
    ) : (
      children
    )}
  </Button>
)

export default BootstrapLoadingButton
