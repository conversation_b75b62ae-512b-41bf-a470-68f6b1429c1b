import React, { CSSProperties, useCallback } from 'react'
import Select, { components as reactSelectComponents, MultiValueRemoveProps } from 'react-select'
import CreatableSelect from 'react-select/creatable'
import AsyncSelect from 'react-select/async'
import { withTranslation } from 'react-i18next'
import { CheckIcon } from 'primereact/icons/check'
import { CircleXIcon } from 'lucide-react'

const MultiValueRemove = (props: MultiValueRemoveProps) => {
  return (
    <reactSelectComponents.MultiValueRemove {...props}>
      <CircleXIcon size={14} />
    </reactSelectComponents.MultiValueRemove>
  )
}

export const selectStyles = {
  control:
    (styles) =>
    (...styles) => ({
      ...styles,
      backgroundColor: 'transparent',
      border: 'none',
      padding: 0,
      borderColor: 'transparent',
      display: 'flex',
      ':hover': {
        borderColor: 'transparent',
      },
    }),
  valueContainer: (styles) => ({ ...styles, padding: 0 }),
  indicatorSeparator: (styles) => ({ ...styles, display: 'none' }),
  indicatorContainer: (styles) => ({ ...styles, color: '#868FA1' }),
}

const styles: Record<string, CSSProperties> = {
  IconContainer: {
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  OptionContainer: {
    display: 'grid',
    gridTemplateColumns: '1fr 20px',
    columnGap: '4px',
    width: '100%',
  },
  OptionLabel: {
    fontSize: '14px',
    fontWeight: 500,
    color: '#313439',
  },
  OptionDescription: {
    fontSize: '12px',
    fontWeight: 500,
    color: '#5b5d61',
  },
}
const Control = ({ children, ...props }) => {
  const { icon, fill } = props.selectProps

  return (
    <reactSelectComponents.Control {...props}>
      {icon && <span style={styles.IconContainer}>{icon}</span>}
      {children}
    </reactSelectComponents.Control>
  )
}

const components = {
  Control,
  MultiValueRemove,
  Option: (props: any) => {
    const { data, isSelected, label } = props

    return (
      <reactSelectComponents.Option {...props}>
        <div style={styles.OptionContainer}>
          <div>
            <span style={styles.OptionLabel}>{label}</span>
            {data.description && (
              <>
                <br />
                <span style={styles.OptionDescription}>{data.description}</span>
              </>
            )}
          </div>

          <div>{isSelected && <CheckIcon />}</div>
        </div>
      </reactSelectComponents.Option>
    )
  },
}

function SelectWrapper({ isAsync = false, isCreatable = false, id, options = [], valueIdMode, t, ...rest }) {
  const defaultProps = {
    isClearable: true,
    getOptionValue: (o) => o.id,
    getOptionLabel: (o) => o.name,
    placeholder: t('SELECT_LOADING_PLACEHOLDER'),
    noOptionsMessage: () => t('NO_RESULTS_FOUND'),
    searchPromptText: t('SELECT_SEARCH_PROMPT_TEXT'),
    loadingPlaceholder: t('TABLE_LOADING'),
    noResultsText: t('NO_RESULTS_FOUND'),
    clearValueText: t('MESSAGE_CLEAR'),
    clearAllText: t('MESSAGE_CLEAR_ALL'),
    classNamePrefix: 'react_select',
    loadingMessage: () => t('TABLE_LOADING'),
  }

  const getOptionValue = rest.getOptionValue || defaultProps.getOptionValue
  const { onChange } = rest

  const value = valueIdMode && rest.value ? options.find((option) => getOptionValue(option) === rest.value) : rest.value

  const handleChange = useCallback(
    (value) => {
      if (onChange) {
        onChange(valueIdMode ? getOptionValue(value) : value)
      }
    },
    [valueIdMode, onChange, getOptionValue],
  )

  const props = {
    ...defaultProps,
    instanceId: id,
    isDisabled: rest.isDisabled || rest.disabled,
    ...rest,
    options: options || [],
    value,
    onChange: handleChange,
  }

  function bindCustomOption(...args: any) {
    const { option } = props?.styles ?? {}

    if (typeof option === 'function') {
      return option(...args)
    }

    return option
  }

  const baseStyles = {
    selectStyles,
    ...(props.styles || {}),
    option: (defaultStyles: CSSProperties, { data, isDisabled, isFocused, isSelected, ...restOption }: any) => {
      return {
        ...defaultStyles,
        paddingBlock: 6,
        paddingBlockStart: data.divider ? 12 : 6,
        marginBlockStart: data.divider ? 6 : 0,
        borderTop: data.divider ? '1px solid #E3E5E8' : 'none',
        ...bindCustomOption(defaultStyles, { ...restOption, data, isDisabled, isFocused, isSelected }),
        color: isDisabled ? '#E3E5E8' : '#495057',
        backgroundColor: isSelected ? '#f2f8fc' : isFocused ? '#e8f4ff' : '#fff',
      }
    },
  }

  if (isAsync) {
    return <AsyncSelect components={components} {...props} styles={baseStyles} />
  }

  if (isCreatable) {
    return <CreatableSelect components={components} {...props} styles={baseStyles} />
  }

  return <Select components={components} {...props} styles={baseStyles} />
}

export default withTranslation(['common'])(React.memo(SelectWrapper))
