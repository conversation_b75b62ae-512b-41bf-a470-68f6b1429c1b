import React, { memo } from 'react'
import Emoji from './Emoji'
import GroupBadge from './GroupBadge'
import BroadcastBadge from './BroadcastBadge'
import isEqual from '../../../utils/logic/isEqual'
import { IconLock, IconUnLock, IconStar, IconPerson } from './IconDigisac/index'

const styles = {
  wrapNamesTitle: {
    fontSize: '12px',
    fontWeight: '600',
    color: '#222222',
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    wordBreak: 'break-word',
    maxWidth: '100%',
    whiteSpace: 'normal',
  },
  wrapNames: {
    color: '#222222',
    fontSize: '12px',
    fontWeight: '300',
    maxWidth: 450,
    margin: '0px 2px 4px',
  },
  wrapNamesPDF: {
    color: '#222222',
    fontSize: '20px',
    lineHeight: '25px',
    fontWeight: '300',
    wordBreak: 'break-all',
    maxWidth: 450,
    margin: '0px 2px 4px',
    whiteSpace: 'nowrap',
  },
}

const ContactName = ({ contact, wrapNames, isRecAqui = false, RAInteractionTypeId = null, pdf = false }) => {
  const alternativeName = ((contact && (contact.alternativeName || contact.name)) || '').trim()
  const name = ((contact && contact.internalName) || contact.name || '').trim()

  return (
    <span
      className={`${wrapNames}`}
      style={wrapNames ? (styles.wrapNamesTitle as React.CSSProperties) : {}}
      title={name}
      data-testid={`message-person-${contact.internalName === null ? contact.name : contact.internalName}`}
    >
      {contact.isBroadcast && <BroadcastBadge />}
      <Emoji children={name.length > 150 ? `${name.replace(name.substring(115), '')}...` : name} />

      {name !== alternativeName && !!alternativeName && !isRecAqui && (
        <span style={styles.wrapNames}>
          {!wrapNames && ' '}(
          <Emoji
            children={
              alternativeName.length > 150
                ? `${alternativeName.replace(alternativeName.substring(115), '')}...`
                : alternativeName
            }
          />
          )
        </span>
      )}

      {isRecAqui && RAInteractionTypeId && (
        <span>
          {[1, 2, 4, 5, 7].includes(RAInteractionTypeId) ? (
            <IconUnLock width="22" height="22" fill="#05cd1e" />
          ) : [3, 6].includes(RAInteractionTypeId) ? (
            <IconLock width="18" height="18" className="mb-1" fill="#ff0606" />
          ) : [8, 9].includes(RAInteractionTypeId) ? (
            <IconPerson width="18" height="18" className="mb-1 mx-1" fill="#05cd1e" />
          ) : (
            [11].includes(RAInteractionTypeId) && (
              <IconStar width="18" height="18" className="mb-1 mx-1" fill="#ffc107" />
            )
          )}
        </span>
      )}
    </span>
  )
}

const propsAreEqual = (props, nextProps) =>
  isEqual(props.contact, nextProps.contact, ['name', 'alternativeName', 'internalName', 'isBroadcast', 'isGroup'])

export default memo(ContactName, propsAreEqual)
