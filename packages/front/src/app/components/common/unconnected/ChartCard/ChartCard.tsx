import React from 'react'
import { Col } from 'reactstrap'

const ChartCard = ({ className, head, body, headTitle, bodyTitle, id, children, ...rest }) => (
  <Col className={`my-3 ${className || ''}`} {...rest}>
    <div className="shadow-sm rounded p-4 bg-white">
      {head && (
        <div className="w-100 pb-3 border-bottom mb-4">
          {head && (
            <div
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              <b className="text-dark" title={headTitle || head}>
                {head}
              </b>
            </div>
          )}

          {body && (
            <div
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
              data-testid={`contact-chat-name${id}`}
            >
              <p className="mb-0 text-dark" title={bodyTitle || body}>
                {body}
              </p>
            </div>
          )}
        </div>
      )}

      {children}
    </div>
  </Col>
)

export default ChartCard
