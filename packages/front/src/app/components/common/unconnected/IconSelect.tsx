import React, { useCallback, useMemo } from 'react'
import { components } from 'react-select'
import Select from './Select'
import * as IconDigisac from './IconDigisac'
import { PrimaryColor } from '../../App/styles/colors'

const icons = { ...IconDigisac }

export const options = Object.entries(icons).map(([name, icon]) => ({
  id: name.split('Icon')[1],
  name: name.split('Icon')[1],
  icon,
}))

const handleSizeIcon = (iconName: string) => {
  const iconSizeOptions = {
    FilterOutline: '18',
    FlagEs: '20',
    FlagEn: '20',
    FlagBr: '20',
  }

  return iconSizeOptions[iconName] ? iconSizeOptions[iconName] : '25'
}

const Option = (props) => {
  const { data } = props

  return (
    <components.Option {...props} className="d-flex align-items-center">
      <div className="d-flex align-items-center justify-content-center" style={{ width: '30px', height: '30px' }}>
        <data.icon fill={PrimaryColor} width={handleSizeIcon(data.name)} height={handleSizeIcon(data.name)} />
      </div>
      <span className="ml-2">{data.name}</span>
    </components.Option>
  )
}

const selectComponents = { Option }

function IconSelect(props) {
  const { onChange } = props

  const handleOnChange = useCallback(
    (icon) => {
      if (onChange) {
        const id = icon ? icon.id : ''
        onChange(id)
      }
    },
    [onChange],
  )

  const value = useMemo(() => (props.value ? { id: props.value, name: props.value } : null), [props.value])

  return (
    <Select
      isSearchable
      components={selectComponents}
      defaultValue={null}
      options={options}
      {...props}
      onChange={handleOnChange}
      value={value}
    />
  )
}

export default IconSelect
