import * as React from 'react'
import * as CheckboxPrimitive from '@radix-ui/react-checkbox'
import { Check } from 'lucide-react'
import styled from 'styled-components'

// Estilizando o Checkbox com styled-components
const StyledCheckbox = styled(CheckboxPrimitive.Root)`
  padding: 0; /* p-0 */
  height: 16px; /* h-4 */
  width: 16px; /* w-4 */
  flex-shrink: 0; /* shrink-0 */
  border-radius: 4px; /* rounded-sm */
  border: 1px solid #b4bbc5; /* border-primary */
  background-color: white;
  transition:
    border 0.2s,
    background-color 0.2s;
  &:focus-visible {
    outline: 2px solid #a5cbeb;
  }
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  &[data-state='checked'] {
    background-color: #324b7d; /* bg-primary */
    border-color: #324b7d; /* border-primary */
    color: white; /* text-primary-foreground */
  }
`

const StyledIndicator = styled(CheckboxPrimitive.Indicator)`
  display: flex;
  align-items: center;
  justify-content: center;
  color: currentColor; /* text-current */
`

// Componente Checkbox
const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>
>(({ className, ...props }, ref) => (
  <StyledCheckbox ref={ref} className={className} {...props}>
    <StyledIndicator>
      <Check width="14px" height="14px" /> {/* h-4 w-4 */}
    </StyledIndicator>
  </StyledCheckbox>
))
Checkbox.displayName = CheckboxPrimitive.Root.displayName

export { Checkbox }
