import React, { ComponentProps } from 'react'
import styled, { css } from 'styled-components'

const variantStyles = {
  outline: css`
    border: 1px solid #365497;
    color: #365497;
    background-color: transparent;
  `,
  info: css`
    background-color: #a5cbeb;
    color: #212e4a;
  `,
  'info-soft': css`
    background-color: #e1edf8;
    color: #365497;
  `,
  error: css`
    background-color: #fba6a6;
    color: #450a0a;
  `,
  'error-soft': css`
    background-color: #fee2e2;
    color: #981c1c;
  `,
  neutral: css`
    background-color: #b4bbc5;
    color: #24272d;
  `,
  'neutral-soft': css`
    background-color: #edeef1;
    color: #3e444e;
  `,
  success: css`
    background-color: #63fe58;
    color: #013204;
  `,
  'success-soft': css`
    background-color: #cdffc7;
    color: #0b690b;
  `,
  warning: css`
    background-color: #f8df4c;
    color: #402108;
  `,
  'warning-soft': css`
    background-color: #fcf9c5;
    color: #814f12;
  `,
  'new-feature': css`
    font-weight: 700;
    background-color: #d62d24;
    color: #ffffff;
  `,
}

const StyledBadge = styled.span<{ variant: BadgeProps['variant'] }>`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 9999px;
  font-weight: 500;
  font-size: 12px;
  line-height: 14px;
  text-transform: uppercase;

  ${({ variant }) => variant && variantStyles[variant]}
`

interface BadgeProps extends ComponentProps<'span'> {
  variant?: keyof typeof variantStyles
}

export function Badge({ className, variant = 'info', ...props }: BadgeProps) {
  return <StyledBadge className={className} variant={variant} {...props} ref={null} />
}
