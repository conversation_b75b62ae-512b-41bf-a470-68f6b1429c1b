import React from 'react'
import styled from 'styled-components'

const StyledSkeleton = styled.div<{ height?: string; width?: string; borderRadius?: string }>`
  animation: pulse 1.5s ease-in-out infinite;
  background-color: #f1f1f1;
  border-radius: ${({ borderRadius }) => borderRadius || '8px'};
  height: ${({ height }) => height || '100%'};
  width: ${({ width }) => width || '100%'};

  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }
`

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  height?: string
  width?: string
  borderRadius?: string
}

function Skeleton({ className, ...props }: SkeletonProps) {
  return <StyledSkeleton className={className} {...props} />
}

export { Skeleton }
