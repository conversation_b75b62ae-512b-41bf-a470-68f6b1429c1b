import React from 'react'
import * as SelectPrimitive from '@radix-ui/react-select'
import { Check, ChevronDown, ChevronUp } from 'lucide-react'
import styled, { keyframes, css } from 'styled-components'

const Select = SelectPrimitive.Root

const SelectGroup = SelectPrimitive.Group

const SelectValue = SelectPrimitive.Value

const fadeIn = keyframes`
  from { opacity: 0; }
  to { opacity: 1; }
`

const fadeOut = keyframes`
  from { opacity: 1; }
  to { opacity: 0; }
`

const zoomIn = keyframes`
  from { transform: scale(0.95); }
  to { transform: scale(1); }
`

const zoomOut = keyframes`
  from { transform: scale(1); }
  to { transform: scale(0.95); }
`

const StyledTrigger = styled(SelectPrimitive.Trigger)<{ hasError?: boolean }>`
  display: flex;
  height: 40px;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  border-radius: 20px;
  border: 1px solid ${({ hasError }) => (hasError ? '#db2727' : '#b4bbc5')};
  background-color: white;
  padding: 12px 16px;
  font-size: 14px;
  color: ${({ hasError }) => (hasError ? '#db2727' : '#24272d')};
  cursor: pointer;
  position: relative;
  transition: box-shadow 0.2s;

  &::placeholder {
    color: #6e7a89;
  }

  &:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px #3c66b9;
  }

  &:disabled {
    background-color: #edeef1;
    border: 1px solid #b4bbc5;
    color: #586171;
    cursor: initial;
    color: #24272d;
  }

  & > span {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    width: calc(100% - 20px);
    overflow: hidden;
    text-align: left;
  }
`

const StyledIcon = styled(ChevronDown)`
  height: 20px;
  width: 20px;
  position: absolute;
  right: 16px;
`

const SelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & { hasError?: boolean }
>(({ children, hasError, ...props }, ref) => (
  <StyledTrigger ref={ref} {...props} hasError={hasError}>
    {children}
    <SelectPrimitive.Icon asChild>
      <StyledIcon />
    </SelectPrimitive.Icon>
  </StyledTrigger>
))
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName

const StyledScrollButton = styled.div`
  display: flex;
  cursor: default;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0;
`

const StyledChevronUp = styled(ChevronUp)`
  height: 1rem;
  width: 1rem;
`

const StyledChevronDown = styled(ChevronDown)`
  height: 1rem;
  width: 1rem;
`

const SelectScrollUpButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>
>((props, ref) => (
  <SelectPrimitive.ScrollUpButton ref={ref} asChild>
    <StyledScrollButton {...props}>
      <StyledChevronUp />
    </StyledScrollButton>
  </SelectPrimitive.ScrollUpButton>
))
SelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName

const SelectScrollDownButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>
>((props, ref) => (
  <SelectPrimitive.ScrollDownButton ref={ref} asChild>
    <StyledScrollButton {...props}>
      <StyledChevronDown />
    </StyledScrollButton>
  </SelectPrimitive.ScrollDownButton>
))
SelectScrollDownButton.displayName = SelectPrimitive.ScrollDownButton.displayName

const StyledContent = styled(SelectPrimitive.Content)<{ position?: string }>`
  position: relative;
  z-index: 999999;
  max-height: 24rem;
  min-width: 8rem;
  overflow: hidden;
  border-radius: 20px;
  border: 1px solid #b4bbc5;
  background-color: white;
  color: #586171;
  margin: 8px 0;

  &[data-state='open'] {
    animation:
      ${fadeIn} 0.15s forwards,
      ${zoomIn} 0.15s forwards;
  }

  &[data-state='closed'] {
    animation:
      ${fadeOut} 0.15s forwards,
      ${zoomOut} 0.15s forwards;
  }

  ${({ position }) =>
    position === 'popper' &&
    css`
      &[data-side='bottom'] {
        transform: translateY(0.25rem);
      }
      &[data-side='top'] {
        transform: translateY(-0.25rem);
      }
      &[data-side='left'] {
        transform: translateX(-0.25rem);
      }
      &[data-side='right'] {
        transform: translateX(0.25rem);
      }
    `}
`

const StyledViewport = styled(SelectPrimitive.Viewport)<{ position?: string }>`
  ${({ position }) =>
    position === 'popper' &&
    css`
      height: var(--radix-select-trigger-height);
      width: 100%;
      min-width: var(--radix-select-trigger-width);
    `}
`

const SelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content> & { position?: string }
>(({ children, position = 'popper', ...props }, ref) => (
  <SelectPrimitive.Portal>
    <StyledContent ref={ref} position={position} {...props}>
      <SelectScrollUpButton />
      <StyledViewport position={position}>{children}</StyledViewport>
      <SelectScrollDownButton />
    </StyledContent>
  </SelectPrimitive.Portal>
))
SelectContent.displayName = SelectPrimitive.Content.displayName

const StyledLabel = styled(SelectPrimitive.Label)`
  padding: 6px 8px;
  padding-left: 32px;
  font-size: 14px;
  font-weight: 600;
`

const SelectLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>((props, ref) => <StyledLabel ref={ref} {...props} />)
SelectLabel.displayName = SelectPrimitive.Label.displayName

const StyledItem = styled(SelectPrimitive.Item)`
  position: relative;
  display: flex;
  width: 100%;
  cursor: default;
  user-select: none;
  align-items: center;
  padding: 8px 16px;
  padding-left: 32px;
  font-size: 14px;
  outline: none;

  &:focus {
    background-color: #f7f8f8;
  }

  &[data-state='checked'] {
    font-weight: 600;
    color: #24272d;
  }

  &[data-disabled] {
    pointer-events: none;
    opacity: 0.5;
  }
`

const StyledItemIndicator = styled(SelectPrimitive.ItemIndicator)`
  position: absolute;
  left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
`

const StyledCheckIcon = styled(Check)`
  height: 1rem;
  width: 1rem;
`

const SelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ children, ...props }, ref) => (
  <StyledItem ref={ref} {...props}>
    <StyledItemIndicator>
      <StyledCheckIcon />
    </StyledItemIndicator>
    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
  </StyledItem>
))
SelectItem.displayName = SelectPrimitive.Item.displayName

const StyledSeparator = styled(SelectPrimitive.Separator)`
  margin: 4px 0;
  height: 1px;
  background-color: #b4bbc5;
`

const SelectSeparator = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>((props, ref) => <StyledSeparator ref={ref} {...props} />)
SelectSeparator.displayName = SelectPrimitive.Separator.displayName

export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
  SelectScrollUpButton,
  SelectScrollDownButton,
}
