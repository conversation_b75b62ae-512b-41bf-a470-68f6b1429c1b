import * as React from 'react'
import styled from 'styled-components'

// Estilos para a Table usando styled-components
const TableContainer = styled.div`
  width: 100%;
  overflow: auto;
  position: relative;
`

const StyledTable = styled.table`
  width: 100%;
  caption-side: bottom;
`

const StyledTableHeader = styled.thead`
  tr {
    border-bottom: 1px solid #d7dbe0;
  }
`

const StyledTableBody = styled.tbody`
  tr:last-child {
    border: none;
  }
`

const StyledTableFooter = styled.tfoot`
  background-color: rgba(0, 0, 0, 0.05); /* bg-muted/50 */
  border-top: 1px solid;
  font-weight: 500;

  tr:last-child {
    border-bottom: none;
  }
`

const StyledTableRow = styled.tr`
  border-bottom: 1px solid #d7dbe0; /* Custom color */
  transition: color 0.2s;
`

const StyledTableHead = styled.th`
  height: 2.75rem; /* h-11 */
  padding-left: 1rem; /* px-4 */
  padding-right: 1rem; /* px-4 */
  text-align: left;
  vertical-align: middle;
  font-size: 0.875rem; /* text-sm */
  font-weight: 500; /* font-medium */
  color: #324b7d; /* Custom color */

  &:has([role='checkbox']) {
    padding-right: 0;
  }
`

const StyledTableCell = styled.td`
  height: 3.5rem; /* h-14 */
  padding-left: 1rem; /* px-4 */
  padding-right: 1rem; /* px-4 */
  vertical-align: middle;

  &:has([role='checkbox']) {
    padding-right: 0;
  }
`

const StyledTableCaption = styled.caption`
  margin-top: 1rem; /* mt-4 */
  font-size: 0.875rem; /* text-sm */
  color: rgba(0, 0, 0, 0.5); /* text-muted-foreground */
`

// Componente Table
const Table = React.forwardRef<HTMLTableElement, React.HTMLAttributes<HTMLTableElement>>(
  ({ className, ...props }, ref) => (
    <TableContainer>
      <StyledTable ref={ref} className={className} {...props} />
    </TableContainer>
  ),
)
Table.displayName = 'Table'

// Demais componentes utilizando styled-components
const TableHeader = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(
  ({ className, ...props }, ref) => <StyledTableHeader ref={ref} className={className} {...props} />,
)
TableHeader.displayName = 'TableHeader'

const TableBody = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(
  ({ className, ...props }, ref) => <StyledTableBody ref={ref} className={className} {...props} />,
)
TableBody.displayName = 'TableBody'

const TableFooter = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(
  ({ className, ...props }, ref) => <StyledTableFooter ref={ref} className={className} {...props} />,
)
TableFooter.displayName = 'TableFooter'

const TableRow = React.forwardRef<HTMLTableRowElement, React.HTMLAttributes<HTMLTableRowElement>>(
  ({ className, ...props }, ref) => <StyledTableRow ref={ref} className={className} {...props} />,
)
TableRow.displayName = 'TableRow'

const TableHead = React.forwardRef<HTMLTableCellElement, React.ThHTMLAttributes<HTMLTableCellElement>>(
  ({ className, ...props }, ref) => <StyledTableHead ref={ref} className={className} {...props} />,
)
TableHead.displayName = 'TableHead'

const TableCell = React.forwardRef<HTMLTableCellElement, React.TdHTMLAttributes<HTMLTableCellElement>>(
  ({ className, ...props }, ref) => <StyledTableCell ref={ref} className={className} {...props} />,
)
TableCell.displayName = 'TableCell'

const TableCaption = React.forwardRef<HTMLTableCaptionElement, React.HTMLAttributes<HTMLTableCaptionElement>>(
  ({ className, ...props }, ref) => <StyledTableCaption ref={ref} className={className} {...props} />,
)
TableCaption.displayName = 'TableCaption'

export { Table, TableHeader, TableBody, TableFooter, TableHead, TableRow, TableCell, TableCaption }
