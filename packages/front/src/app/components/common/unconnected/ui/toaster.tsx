import React from 'react'
import { useToast } from '../../../../hooks/useToast'
import { Toast, ToastClose, ToastDescription, ToastProvider, ToastTitle, ToastViewport } from './toast'
import styled from 'styled-components'

const StyledToastContent = styled.div`
  display: grid;
  gap: 4px;
`

export function Toaster() {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(({ id, title, description, action, ...rest }) => {
        return (
          <Toast key={id} {...rest}>
            <StyledToastContent>
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && <ToastDescription>{description}</ToastDescription>}
              {action}
            </StyledToastContent>
            <ToastClose />
          </Toast>
        )
      })}
      <ToastViewport />
    </ToastProvider>
  )
}
