import * as React from 'react'
import * as ContextMenuPrimitive from '@radix-ui/react-context-menu'
import styled from 'styled-components'

// Componentes estilizados
const StyledSubTrigger = styled(ContextMenuPrimitive.SubTrigger)<{ inset?: boolean }>`
  display: flex;
  cursor: default;
  select: none;
  align-items: center;
  padding: 0.375rem 0.5rem;
  font-size: 0.875rem;
  outline: none;
  border-radius: 0.125rem;
  transition:
    background-color 0.2s,
    color 0.2s;
  background-color: ${({ inset }) => (inset ? 'var(--color-accent)' : 'transparent')};
  color: ${({ inset }) => (inset ? 'var(--color-accent-foreground)' : 'inherit')};
  &:focus {
    background-color: var(--color-accent);
    color: var(--color-accent-foreground);
  }
`

const StyledSubContent = styled(ContextMenuPrimitive.SubContent)`
  z-index: 50;
  min-width: 8rem;
  overflow: hidden;
  border-radius: 0.25rem;
  background-color: var(--color-popover);
  color: var(--color-popover-foreground);
  box-shadow:
    0px 10px 15px -3px rgba(0, 0, 0, 0.1),
    0px 4px 6px -4px rgba(0, 0, 0, 0.1);
`

const StyledContent = styled(ContextMenuPrimitive.Content)`
  z-index: 50;
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 1rem;
  overflow: hidden;
  min-width: 8rem;
  box-shadow: 0px 4px 8px 0px #a6b8e652;
`

const StyledItem = styled(ContextMenuPrimitive.Item)`
  padding: 0 16px;
  height: 48px;
  font-size: 0.875rem;
  color: #586171;
  cursor: default;

  display: flex;
  align-items: center;
  outline: none;
  border-radius: 0.125rem;
  transition: background-color 0.2s;
  &:focus {
    background-color: #f6f6f9;
  }
`

const StyledCheckboxItem = styled(ContextMenuPrimitive.CheckboxItem)`
  display: flex;
  cursor: default;
  select: none;
  align-items: center;
  padding: 0.375rem 0.5rem;
  font-size: 0.875rem;
  outline: none;
  border-radius: 0.125rem;
  transition: background-color 0.2s;
  position: relative;
  padding-left: 2rem;
`

const StyledRadioItem = styled(ContextMenuPrimitive.RadioItem)`
  display: flex;
  cursor: default;
  select: none;
  align-items: center;
  padding: 0.375rem 0.5rem;
  font-size: 0.875rem;
  outline: none;
  border-radius: 0.125rem;
  position: relative;
  padding-left: 2rem;
`

const StyledLabel = styled(ContextMenuPrimitive.Label)<{ inset?: boolean }>`
  padding: 0.375rem 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-foreground);
  padding-left: ${({ inset }) => (inset ? '2rem' : 'inherit')};
`

const StyledSeparator = styled(ContextMenuPrimitive.Separator)`
  height: 1px;
  background-color: var(--color-border);
  margin: 0.25rem 0;
`

const Shortcut = styled.span`
  margin-left: auto;
  font-size: 0.75rem;
  letter-spacing: 0.05rem;
  color: var(--color-muted-foreground);
`

// Componentes principais
const ContextMenu = ContextMenuPrimitive.Root
const ContextMenuTrigger = ContextMenuPrimitive.Trigger
const ContextMenuGroup = ContextMenuPrimitive.Group
const ContextMenuPortal = ContextMenuPrimitive.Portal
const ContextMenuSub = ContextMenuPrimitive.Sub
const ContextMenuRadioGroup = ContextMenuPrimitive.RadioGroup
const ContextMenuSubTrigger = React.forwardRef<typeof StyledSubTrigger, any>(({ inset, ...props }, ref) => (
  <StyledSubTrigger inset={inset} ref={ref} {...props} />
))
ContextMenuSubTrigger.displayName = 'ContextMenuSubTrigger'

const ContextMenuSubContent = React.forwardRef<typeof StyledSubContent, any>((props, ref) => (
  <StyledSubContent ref={ref} {...props} />
))
ContextMenuSubContent.displayName = 'ContextMenuSubContent'

const ContextMenuContent = React.forwardRef<typeof StyledContent, any>((props, ref) => (
  <ContextMenuPrimitive.Portal>
    <StyledContent ref={ref} {...props} />
  </ContextMenuPrimitive.Portal>
))
ContextMenuContent.displayName = 'ContextMenuContent'

const ContextMenuItem = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Item>
>(({ ...props }, ref) => <StyledItem ref={ref} {...props} />)
ContextMenuItem.displayName = 'ContextMenuItem'

const ContextMenuCheckboxItem = React.forwardRef<typeof StyledCheckboxItem, any>((props, ref) => (
  <StyledCheckboxItem ref={ref} {...props} />
))
ContextMenuCheckboxItem.displayName = 'ContextMenuCheckboxItem'

const ContextMenuRadioItem = React.forwardRef<typeof StyledRadioItem, any>((props, ref) => (
  <StyledRadioItem ref={ref} {...props} />
))
ContextMenuRadioItem.displayName = 'ContextMenuRadioItem'

const ContextMenuLabel = React.forwardRef<typeof StyledLabel, any>(({ inset, ...props }, ref) => (
  <StyledLabel inset={inset} ref={ref} {...props} />
))
ContextMenuLabel.displayName = 'ContextMenuLabel'

const ContextMenuSeparator = React.forwardRef<typeof StyledSeparator, any>((props, ref) => (
  <StyledSeparator ref={ref} {...props} />
))
ContextMenuSeparator.displayName = 'ContextMenuSeparator'

const ContextMenuShortcut = ({ className, ...props }: React.HTMLAttributes<HTMLSpanElement>) => (
  <Shortcut className={className} {...props} />
)

export {
  ContextMenu,
  ContextMenuTrigger,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuCheckboxItem,
  ContextMenuRadioItem,
  ContextMenuLabel,
  ContextMenuSeparator,
  ContextMenuShortcut,
  ContextMenuGroup,
  ContextMenuPortal,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuRadioGroup,
}
