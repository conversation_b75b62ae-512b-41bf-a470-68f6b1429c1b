import * as React from 'react'
import * as PopoverPrimitive from '@radix-ui/react-popover'
import styled, { css } from 'styled-components'

const StyledPopoverContent = styled(PopoverPrimitive.Content)<{
  align?: 'start' | 'center' | 'end'
  sideOffset?: number
}>`
  z-index: 9999991;
  width: 18rem;
  border-radius: 1rem;
  border: transparent;
  background-color: white;
  color: black;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  outline: none;

  ${({ theme }) => css`
    &[data-state='open'] {
      animation:
        fadeIn 0.15s ease,
        zoomIn 0.15s ease;
    }
    &[data-state='closed'] {
      animation:
        fadeOut 0.15s ease,
        zoomOut 0.15s ease;
    }
  `}

  // Estilos de animação para cada lado
  &[data-side="bottom"] {
    transform-origin: top center;
    animation: slideInFromTop 0.2s ease;
  }
  &[data-side='left'] {
    transform-origin: right center;
    animation: slideInFromRight 0.2s ease;
  }
  &[data-side='right'] {
    transform-origin: left center;
    animation: slideInFromLeft 0.2s ease;
  }
  &[data-side='top'] {
    transform-origin: bottom center;
    animation: slideInFromBottom 0.2s ease;
  }

  // Definindo keyframes para animações
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }

  @keyframes zoomIn {
    from {
      transform: scale(0.95);
    }
    to {
      transform: scale(1);
    }
  }

  @keyframes zoomOut {
    from {
      transform: scale(1);
    }
    to {
      transform: scale(0.95);
    }
  }

  @keyframes slideInFromTop {
    from {
      transform: translateY(-8px);
    }
    to {
      transform: translateY(0);
    }
  }

  @keyframes slideInFromBottom {
    from {
      transform: translateY(8px);
    }
    to {
      transform: translateY(0);
    }
  }

  @keyframes slideInFromLeft {
    from {
      transform: translateX(-8px);
    }
    to {
      transform: translateX(0);
    }
  }

  @keyframes slideInFromRight {
    from {
      transform: translateX(8px);
    }
    to {
      transform: translateX(0);
    }
  }
`

const Popover = PopoverPrimitive.Root
const PopoverTrigger = PopoverPrimitive.Trigger

const PopoverContent = React.forwardRef<
  React.ElementRef<typeof PopoverPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>
>(({ align = 'center', sideOffset = 4, ...props }, ref) => (
  <PopoverPrimitive.Portal>
    <StyledPopoverContent ref={ref} align={align} sideOffset={sideOffset} {...props} />
  </PopoverPrimitive.Portal>
))
PopoverContent.displayName = PopoverPrimitive.Content.displayName

export { Popover, PopoverTrigger, PopoverContent }
