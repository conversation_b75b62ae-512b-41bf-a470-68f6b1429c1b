import * as React from 'react'
import styled from 'styled-components'

import { Calendar, CalendarProps } from './calendar'

import { Popover, PopoverContent, PopoverTrigger } from './popover'
import { ReactNode, useMemo } from 'react'

import Select from '../Select'
import { GroupInput } from '../../../App/styles/common'
import CalendarIcon from '../IconDigisac/Calendar'
import { PrimaryColor } from '../../../App/styles/colors'
import formatDate from 'date-fns/format'

const DatePickerWrapper = styled.div`
  display: grid;
  gap: 0.5rem;
`

export type DatePickerProps = Omit<CalendarProps, 'mode'> & {
  date: Date
  setDate: (date: Date) => void
  triggerComponent?: ReactNode
  onOpenChange?: (isOpen: boolean) => void
}

function DatePicker({ date, setDate, triggerComponent, ...props }: DatePickerProps) {
  const placeholder = useMemo(() => {
    if (date) {
      return `${formatDate(date, 'dd/MM/yyyy')}`
    }

    return 'dd/mm/aaaa'
  }, [date])

  const defaultTriggerComponent = (
    <div>
      <GroupInput style={{ pointerEvents: 'none' }} withIcon>
        <Select placeholder={placeholder} icon={<CalendarIcon fill={PrimaryColor} width="25" height="25" />} />
      </GroupInput>
    </div>
  )

  return (
    <DatePickerWrapper>
      <Popover onOpenChange={props.onOpenChange}>
        <PopoverTrigger asChild>{triggerComponent || defaultTriggerComponent}</PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            selected={date}
            onSelect={setDate}
            mode="single"
            numberOfMonths={1}
            defaultMonth={date || new Date()}
            required={true}
            {...props}
          />
        </PopoverContent>
      </Popover>
    </DatePickerWrapper>
  )
}

export { DatePicker }
