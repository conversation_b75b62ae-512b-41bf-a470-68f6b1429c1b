import * as React from 'react'
import styled from 'styled-components'
import { type DialogProps } from '@radix-ui/react-dialog'
import { Command as CommandPrimitive } from 'cmdk'
import { Search } from 'lucide-react'

import { Dialog, DialogContent } from './dialog'

/**
 * Styled components to replace Tailwind classes.
 */
const StyledCommand = styled(CommandPrimitive)`
  display: flex;
  height: 100%;
  width: 100%;
  flex-direction: column;
  overflow: hidden;
  border-radius: 0.375rem;
  background: var(--popover);
  color: var(--popover-foreground);
`

const StyledDialogContent = styled(DialogContent)`
  overflow: hidden;
  padding: 0;
  box-shadow: var(--shadow-lg, 0 10px 15px rgba(0, 0, 0, 0.1));
`

/**
 * We apply the equivalent of the complex Tailwind selectors directly here.
 * Adjust spacing and values as needed for fidelity.
 */
const StyledCommandInDialog = styled(StyledCommand)`
  & [cmdk-group-heading] {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    font-weight: 500;
    color: var(--muted-foreground);
  }

  & [cmdk-group]:not([hidden]) ~ [cmdk-group] {
    padding-top: 0;
  }

  & [cmdk-group] {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  & [cmdk-input-wrapper] svg {
    height: 1.25rem;
    width: 1.25rem;
  }

  & [cmdk-input] {
    height: 3rem;
  }

  & [cmdk-item] {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  & [cmdk-item] svg {
    height: 1.25rem;
    width: 1.25rem;
  }
`

const InputWrapper = styled.div`
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border);
  padding: 0 0.75rem;
`

const StyledCommandInput = styled(CommandPrimitive.Input)`
  flex: 1;
  height: 2.75rem;
  width: 100%;
  border-radius: 0.375rem;
  background: transparent;
  padding: 0.75rem 0;
  font-size: 0.875rem;
  outline: none;
  &::placeholder {
    color: var(--muted-foreground);
  }
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
`

const StyledCommandList = styled(CommandPrimitive.List)`
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
`

const StyledCommandEmpty = styled(CommandPrimitive.Empty)`
  padding: 1.5rem 0;
  text-align: center;
  font-size: 0.875rem;
`

const StyledCommandGroup = styled(CommandPrimitive.Group)`
  overflow: hidden;
  padding: 0.25rem;
  color: var(--foreground);

  & [cmdk-group-heading] {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--muted-foreground);
  }
`

const StyledCommandSeparator = styled(CommandPrimitive.Separator)`
  margin-left: -0.25rem;
  margin-right: -0.25rem;
  height: 1px;
  background: var(--border);
`

const StyledCommandItem = styled(CommandPrimitive.Item)`
  position: relative;
  display: flex;
  cursor: default;
  gap: 0.5rem;
  user-select: none;
  align-items: center;
  border-radius: 0.125rem;
  padding: 0.375rem 0.5rem;
  font-size: 0.875rem;
  outline: none;

  &[data-disabled='true'] {
    pointer-events: none;
    opacity: 0.5;
  }

  &[data-selected='true'] {
    background: var(--accent);
    color: var(--accent-foreground);
  }

  & svg {
    pointer-events: none;
    flex-shrink: 0;
    width: 1rem;
    height: 1rem;
  }
`

const StyledCommandShortcut = styled.span`
  margin-left: auto;
  font-size: 0.75rem;
  letter-spacing: 0.1em;
  color: var(--muted-foreground);
`

/**
 * Components with ref and typing.
 */

type CommandProps = React.ComponentPropsWithoutRef<typeof CommandPrimitive>
const Command = React.forwardRef<React.ElementRef<typeof CommandPrimitive>, CommandProps>(
  ({ className, ...props }, ref) => <StyledCommand ref={ref} {...props} />,
)
Command.displayName = CommandPrimitive.displayName

const CommandDialog: React.FC<DialogProps> = ({ children, ...props }) => {
  return (
    <Dialog {...props}>
      <StyledDialogContent>
        <StyledCommandInDialog>{children}</StyledCommandInDialog>
      </StyledDialogContent>
    </Dialog>
  )
}

type CommandInputProps = React.ComponentPropsWithoutRef<typeof CommandPrimitive.Input>
const CommandInput = React.forwardRef<React.ElementRef<typeof CommandPrimitive.Input>, CommandInputProps>(
  ({ className, ...props }, ref) => (
    <InputWrapper cmdk-input-wrapper="">
      <Search style={{ marginRight: '0.5rem', opacity: 0.5, width: '1rem', height: '1rem' }} />
      <StyledCommandInput ref={ref} {...props} />
    </InputWrapper>
  ),
)
CommandInput.displayName = CommandPrimitive.Input.displayName

type CommandListProps = React.ComponentPropsWithoutRef<typeof CommandPrimitive.List>
const CommandList = React.forwardRef<React.ElementRef<typeof CommandPrimitive.List>, CommandListProps>(
  ({ className, ...props }, ref) => <StyledCommandList ref={ref} {...props} />,
)
CommandList.displayName = CommandPrimitive.List.displayName

type CommandEmptyProps = React.ComponentPropsWithoutRef<typeof CommandPrimitive.Empty>
const CommandEmpty = React.forwardRef<React.ElementRef<typeof CommandPrimitive.Empty>, CommandEmptyProps>(
  (props, ref) => <StyledCommandEmpty ref={ref} {...props} />,
)
CommandEmpty.displayName = CommandPrimitive.Empty.displayName

type CommandGroupProps = React.ComponentPropsWithoutRef<typeof CommandPrimitive.Group>
const CommandGroup = React.forwardRef<React.ElementRef<typeof CommandPrimitive.Group>, CommandGroupProps>(
  ({ className, ...props }, ref) => <StyledCommandGroup ref={ref} {...props} />,
)
CommandGroup.displayName = CommandPrimitive.Group.displayName

type CommandSeparatorProps = React.ComponentPropsWithoutRef<typeof CommandPrimitive.Separator>
const CommandSeparator = React.forwardRef<React.ElementRef<typeof CommandPrimitive.Separator>, CommandSeparatorProps>(
  ({ className, ...props }, ref) => <StyledCommandSeparator ref={ref} {...props} />,
)
CommandSeparator.displayName = CommandPrimitive.Separator.displayName

type CommandItemProps = React.ComponentPropsWithoutRef<typeof CommandPrimitive.Item>
const CommandItem = React.forwardRef<React.ElementRef<typeof CommandPrimitive.Item>, CommandItemProps>(
  ({ className, ...props }, ref) => <StyledCommandItem ref={ref} {...props} />,
)
CommandItem.displayName = CommandPrimitive.Item.displayName

const CommandShortcut: React.FC<React.HTMLAttributes<HTMLSpanElement>> = ({ className, ...props }) => {
  return <StyledCommandShortcut {...props} />
}
CommandShortcut.displayName = 'CommandShortcut'

export {
  Command,
  CommandDialog,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandShortcut,
  CommandSeparator,
}
