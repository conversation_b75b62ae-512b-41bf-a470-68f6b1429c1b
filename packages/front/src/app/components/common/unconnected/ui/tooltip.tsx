import * as React from 'react'
import * as TooltipPrimitive from '@radix-ui/react-tooltip'
import styled from 'styled-components'

const StyledTooltipContent = styled(TooltipPrimitive.Content)`
  z-index: 9999991;
  max-width: 240px;
  background-color: #324b7d;
  overflow: hidden;
  border-radius: 8px;
  text-align: center;
  padding: 8px;
  font-weight: 400;
  line-height: 1.4;
  font-size: 12px;
  color: white;
  animation:
    fadeIn 0.2s,
    zoomIn 0.2s;

  &[data-state='closed'] {
    animation:
      fadeOut 0.2s,
      zoomOut 0.2s;
  }

  &[data-side='bottom'] {
    animation: slideInFromTop 0.2s;
  }

  &[data-side='left'] {
    animation: slideInFromRight 0.2s;
  }

  &[data-side='right'] {
    animation: slideInFromLeft 0.2s;
  }

  &[data-side='top'] {
    animation: slideInFromBottom 0.2s;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }

  @keyframes zoomIn {
    from {
      transform: scale(0.95);
    }
    to {
      transform: scale(1);
    }
  }

  @keyframes zoomOut {
    from {
      transform: scale(1);
    }
    to {
      transform: scale(0.95);
    }
  }

  @keyframes slideInFromTop {
    from {
      transform: translateY(-0.5rem);
    }
    to {
      transform: translateY(0);
    }
  }

  @keyframes slideInFromBottom {
    from {
      transform: translateY(0.5rem);
    }
    to {
      transform: translateY(0);
    }
  }

  @keyframes slideInFromLeft {
    from {
      transform: translateX(-0.5rem);
    }
    to {
      transform: translateX(0);
    }
  }

  @keyframes slideInFromRight {
    from {
      transform: translateX(0.5rem);
    }
    to {
      transform: translateX(0);
    }
  }
`

const TooltipProvider = TooltipPrimitive.Provider

const Tooltip = TooltipPrimitive.Root

const TooltipTrigger = TooltipPrimitive.Trigger

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Portal>
    <StyledTooltipContent ref={ref} sideOffset={sideOffset} {...props} />
  </TooltipPrimitive.Portal>
))
TooltipContent.displayName = TooltipPrimitive.Content.displayName

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }
