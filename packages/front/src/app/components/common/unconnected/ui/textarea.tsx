import * as React from 'react'
import styled from 'styled-components'

const StyledTextarea = styled.textarea`
  display: flex;
  min-height: 80px;
  max-height: 80px;
  border: none;
  width: 100%;
  border-radius: 20px;
  color: #24272d;
  background-color: white;
  padding: 8px 16px;
  font-size: 14px;
  outline: none;
  resize: none;
  transition:
    box-shadow 0.2s,
    border-color 0.2s;

  &::placeholder {
    color: #6e7a89;
  }

  &:focus-visible {
    box-shadow: 0 0 0 2px var(--color-ring);
    border-color: #24272d;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
`

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  hasError?: boolean
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(({ className, hasError, ...props }, ref) => {
  const adjustHeight = (textarea: HTMLTextAreaElement) => {
    textarea.style.height = 'auto'
    textarea.style.height = `${textarea.scrollHeight}px`
  }

  const handleInput = (event: React.FormEvent<HTMLTextAreaElement>) => {
    const textarea = event.currentTarget
    adjustHeight(textarea)
  }

  const setRef = React.useCallback((element: HTMLTextAreaElement | null) => {
    if (element) {
      adjustHeight(element)
    }
    if (typeof ref === 'function') {
      ref(element)
    } else if (ref) {
      ref.current = element
    }
  }, [])

  return (
    <div
      style={{
        borderRadius: '20px',
        border: '1px solid #b4bbc5',
        overflow: 'hidden',
        ...(hasError && {
          borderColor: '#DB2727',
        }),
      }}
    >
      <StyledTextarea className={className} ref={setRef} {...props} onInput={handleInput} />
    </div>
  )
})

Textarea.displayName = 'Textarea'

export { Textarea }
