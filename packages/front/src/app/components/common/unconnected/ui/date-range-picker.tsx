/* eslint-disable jsx-a11y/no-autofocus */
import * as React from 'react'
import styled from 'styled-components'

import { Calendar, CalendarProps } from './calendar'

import { Popover, PopoverContent, PopoverTrigger } from './popover'
import { ReactNode, useMemo } from 'react'

import Select from '../Select'
import { GroupInput } from '../../../App/styles/common'
import CalendarIcon from '../IconDigisac/Calendar'
import { PrimaryColor } from '../../../App/styles/colors'
import formatDate from 'date-fns/format'
import { DateRange } from 'react-day-picker'
import { customStyles } from '../GenericResourceSelect'

const DatePicker = styled.div`
  display: grid;
  gap: 0.5rem;
`

export const DATE_RANGE_PICKER_DATA_TESTID = 'date-range-picker-trigger'

export type DateRangePickerProps = Omit<CalendarProps, 'mode' | 'onSelect'> & {
  dateRange: DateRange
  setDateRange: (date: DateRange) => void
  triggerComponent?: ReactNode
  onOpenChange?: (isOpen: boolean) => void
  disabledAfterToday?: boolean
  required?: boolean
  side?: 'top' | 'right' | 'bottom' | 'left'
  avoidCollisions?: boolean
  twoDigitsYear?: boolean
}

function DateRangePicker({
  dateRange,
  setDateRange,
  triggerComponent,
  side = 'bottom',
  avoidCollisions = true,
  twoDigitsYear,
  required,
  ...props
}: DateRangePickerProps) {
  const placeholder = useMemo(() => {
    const dateFormat = twoDigitsYear ? 'dd/MM/yy' : 'dd/MM/yyyy'
    const placeholderText = twoDigitsYear ? 'dd/mm/aa' : 'dd/mm/aaaa'

    if (dateRange?.to && dateRange?.from) {
      return `${formatDate(dateRange.from, dateFormat)}  -  ${formatDate(dateRange.to, dateFormat)}`
    }

    return `${placeholderText}  -  ${placeholderText}`
  }, [dateRange])

  const defaultTriggerComponent = (
    <div data-testid={DATE_RANGE_PICKER_DATA_TESTID} id={props.id}>
      <GroupInput style={{ pointerEvents: 'none' }} withIcon>
        <Select
          placeholder={placeholder}
          icon={<CalendarIcon fill={PrimaryColor} width="25" height="25" />}
          styles={customStyles}
        />
      </GroupInput>
    </div>
  )

  return (
    <DatePicker>
      <Popover onOpenChange={props.onOpenChange}>
        <PopoverTrigger asChild>{triggerComponent || defaultTriggerComponent}</PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start" side={side} avoidCollisions={avoidCollisions}>
          <Calendar
            autoFocus
            mode="range"
            defaultMonth={dateRange?.from}
            selected={dateRange}
            onSelect={setDateRange}
            numberOfMonths={2}
            endMonth={props.disabledAfterToday ? new Date() : null}
            required={required}
            {...props}
          />
        </PopoverContent>
      </Popover>
    </DatePicker>
  )
}

export { DateRangePicker }
