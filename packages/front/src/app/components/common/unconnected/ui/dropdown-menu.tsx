import * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'
import { Check, ChevronRight, Circle } from 'lucide-react'
import * as React from 'react'
import styled from 'styled-components'

const StyledDropdownMenuSubTrigger = styled(DropdownMenuPrimitive.SubTrigger)<{ inset?: boolean }>`
  display: flex;
  align-items: center;
  padding: 0.375rem 0.5rem;
  font-size: 0.875rem;
  ${(props) => props.inset && 'padding-left: 2rem'};
  &:focus {
    background-color: #f3f4f6;
  }
`

const StyledDropdownMenuSubContent = styled(DropdownMenuPrimitive.SubContent)`
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
`

const StyledDropdownMenuContent = styled(DropdownMenuPrimitive.Content)`
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 1rem;
  overflow: hidden;
  min-width: 8rem;
  box-shadow: 0px 4px 8px 0px #a6b8e652;
  z-index: 10;
`

const StyledDropdownMenuItem = styled(DropdownMenuPrimitive.Item)<{ inset?: boolean }>`
  padding: 0 16px;
  height: 48px;
  font-size: 0.875rem;
  color: #586171;
  cursor: pointer;

  display: flex;
  align-items: center;

  ${(props) => props.inset && 'padding-left: 2rem'};
  &:hover {
    background-color: #f6f6f9;
  }
  &:focus-visible {
    outline: 2px solid #a5cbeb;
    outline-offset: -2px;
    border-radius: 16px;
  }
`

const StyledDropdownMenuCheckboxItem = styled(DropdownMenuPrimitive.CheckboxItem)`
  position: relative;
  padding-left: 2rem;
  font-size: 0.875rem;
  &:hover {
    background-color: #f6f6f9;
  }
`

const StyledDropdownMenuRadioItem = styled(DropdownMenuPrimitive.RadioItem)`
  position: relative;
  padding-left: 2rem;
  font-size: 0.875rem;
  &:hover {
    background-color: #f6f6f9;
  }
`

const StyledDropdownMenuLabel = styled(DropdownMenuPrimitive.Label)<{ inset?: boolean }>`
  padding: 0 16px;
  height: 40px;
  font-size: 0.875rem;
  color: #586171;
  font-weight: bold;

  display: flex;
  align-items: center;
  ${(props) => props.inset && 'padding-left: 2rem'};
`

const StyledDropdownMenuSeparator = styled(DropdownMenuPrimitive.Separator)`
  height: 1px;
  background-color: #e5e7eb;
`

const StyledDropdownMenuShortcut = styled.span`
  margin-left: auto;
  font-size: 0.75rem;
  opacity: 0.6;
  text-transform: uppercase;
`

const DropdownMenu = DropdownMenuPrimitive.Root
const DropdownMenuTrigger = DropdownMenuPrimitive.Trigger
const DropdownMenuGroup = DropdownMenuPrimitive.Group
const DropdownMenuPortal = DropdownMenuPrimitive.Portal
const DropdownMenuSub = DropdownMenuPrimitive.Sub
const DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup

const DropdownMenuSubTrigger = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & { inset?: boolean }
>(({ className, inset, children, ...props }, ref) => (
  <StyledDropdownMenuSubTrigger ref={ref} inset={inset} {...props}>
    {children}
    <ChevronRight className="ml-auto" />
  </StyledDropdownMenuSubTrigger>
))
DropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName

const DropdownMenuSubContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>
>(({ className, ...props }, ref) => <StyledDropdownMenuSubContent ref={ref} {...props} />)
DropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName

const DropdownMenuContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <DropdownMenuPortal>
    <StyledDropdownMenuContent ref={ref} sideOffset={sideOffset} {...props} />
  </DropdownMenuPortal>
))
DropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName

const DropdownMenuItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & { inset?: boolean }
>(({ className, inset, ...props }, ref) => <StyledDropdownMenuItem ref={ref} inset={inset} {...props} />)
DropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName

const DropdownMenuCheckboxItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>
>(({ className, children, checked, ...props }, ref) => (
  <StyledDropdownMenuCheckboxItem ref={ref} checked={checked} {...props}>
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <DropdownMenuPrimitive.ItemIndicator>
        <Check className="h-4 w-4" />
      </DropdownMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </StyledDropdownMenuCheckboxItem>
))
DropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName

const DropdownMenuRadioItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>
>(({ className, children, ...props }, ref) => (
  <StyledDropdownMenuRadioItem ref={ref} {...props}>
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <DropdownMenuPrimitive.ItemIndicator>
        <Circle className="h-2 w-2 fill-current" />
      </DropdownMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </StyledDropdownMenuRadioItem>
))
DropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName

const DropdownMenuLabel = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & { inset?: boolean }
>(({ className, inset, ...props }, ref) => <StyledDropdownMenuLabel ref={ref} inset={inset} {...props} />)
DropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName

const DropdownMenuSeparator = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>
>(({ className, ...props }, ref) => <StyledDropdownMenuSeparator ref={ref} {...props} />)
DropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName

const DropdownMenuShortcut = ({ className, ...props }: React.HTMLAttributes<HTMLSpanElement>) => (
  <StyledDropdownMenuShortcut className={className} {...props} />
)
DropdownMenuShortcut.displayName = 'DropdownMenuShortcut'

export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuGroup,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuRadioGroup,
}
