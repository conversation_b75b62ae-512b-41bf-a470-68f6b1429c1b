import * as React from 'react'
import * as SeparatorPrimitive from '@radix-ui/react-separator'
import styled, { css } from 'styled-components'

const horizontalStyles = css`
  height: 1px;
  width: 100%;
`

const verticalStyles = css`
  height: 100%;
  width: 1px;
`

const StyledSeparator = styled(SeparatorPrimitive.Root)<{ orientation: string }>`
  flex-shrink: 0;
  background-color: #edeef1;

  ${({ orientation }) => (orientation === 'horizontal' ? horizontalStyles : verticalStyles)}
`

const Separator = React.forwardRef<
  React.ElementRef<typeof SeparatorPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>
>(({ orientation = 'horizontal', decorative = true, ...props }, ref) => (
  <StyledSeparator ref={ref} decorative={decorative} orientation={orientation} {...props} />
))
Separator.displayName = SeparatorPrimitive.Root.displayName

export { Separator }
