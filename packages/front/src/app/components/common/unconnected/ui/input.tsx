import * as React from 'react'
import styled from 'styled-components'

const InputWrapper = styled.div<Pick<InputProps, 'iconPosition'>>`
  position: relative;
  width: 100%;

  svg {
    position: absolute;
    width: 20px;
    height: 20px;
    left: ${({ iconPosition = 'left' }) => (iconPosition === 'left' ? '16px' : 'calc(100% - 32px)')};
    top: 10px;
  }
`

const StyledInput = styled.input<{ icon: boolean }>`
  display: flex;
  height: 40px;
  width: 100%;
  border-radius: 40px;
  border: 1px solid #b4bbc5;
  color: #24272d;
  background-color: white;
  padding: ${({ icon }) => (icon ? '12px 16px 12px 44px' : '12px 16px')};
  font-size: 14px;
  color: var(--color-text);
  transition: box-shadow 0.2s;

  &::file-selector-button {
    border: 0;
    background: transparent;
    font-size: 16px;
    font-weight: 500;
  }

  &::placeholder {
    color: #6e7a89;
  }

  &:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-ring);
    border-color: #24272d;
  }

  &:disabled {
    background-color: #edeef1;
    border: 1px solid #b4bbc5;
    color: #586171;
    cursor: initial;
  }
`

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  icon?: React.ReactNode
  iconPosition?: 'right' | 'left'
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(({ icon, type, iconPosition = 'left', ...props }, ref) => {
  return (
    <InputWrapper iconPosition={iconPosition}>
      {icon}
      <StyledInput icon={!!icon} ref={ref} type={type || 'text'} {...props} />
    </InputWrapper>
  )
})
Input.displayName = 'Input'

export { Input }
