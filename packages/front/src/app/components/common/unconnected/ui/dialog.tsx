import * as React from 'react'
import * as DialogPrimitive from '@radix-ui/react-dialog'
import { X } from 'lucide-react'
import styled from 'styled-components'
import { Button } from './button'

const StyledDialogOverlay = styled(DialogPrimitive.Overlay)`
  position: fixed;
  inset: 0;
  z-index: 999999;
  background-color: rgba(0, 0, 0, 0.6);
`

const StyledDialogContent = styled(DialogPrimitive.Content)`
  position: fixed;
  left: 50%;
  top: 50%;
  width: 100%;
  max-width: 552px;
  max-height: 90vh;
  transform: translate(-50%, -50%);
  z-index: 999999;
  max-height: 90vh;
  overflow: auto;
  display: flex;
  flex-direction: column;
  background-color: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 0 0.5rem rgba(0, 0, 0, 0.1);
  transition:
    transform 0.2s,
    opacity 0.2s;
`

const StyledDialogClose = styled(DialogPrimitive.Close)`
  position: absolute;
  right: 24px;
  top: 24px;
  transform: translateY(-25%);
`

const StyledDialogHeader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 8px;
  margin-bottom: 40px;

  @media (min-width: 640px) {
    text-align: left;
  }
`

const StyledDialogFooter = styled.div`
  width: 100%;
  gap: 16rem;
  display: flex;
  flex-direction: column-reverse;

  @media (min-width: 640px) {
    flex-direction: row;
    justify-content: center;
    gap: 1rem;
  }
`

const StyledDialogTitle = styled(DialogPrimitive.Title)`
  color: #24272d;
  font-size: 20px;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  margin: 0;
`

const StyledDialogDescription = styled(DialogPrimitive.Description)`
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
  text-align: center;
  color: #586171;
`

const Dialog = DialogPrimitive.Root

const DialogTrigger = DialogPrimitive.Trigger

const DialogPortal = DialogPrimitive.Portal

const DialogClose = DialogPrimitive.Close

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ ...props }, ref) => <StyledDialogOverlay ref={ref} {...props} />)
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

interface DialogContentProps {
  portal?: boolean
  closeButton?: boolean
}

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & DialogContentProps
>(({ children, portal = true, closeButton = true, ...props }, ref) => {
  const Comp = portal ? DialogPortal : React.Fragment

  return (
    <Comp>
      <DialogOverlay />
      <StyledDialogContent ref={ref} {...props}>
        {children}
        {closeButton && (
          <StyledDialogClose asChild>
            <Button size="icon" variant="ghost">
              <X
                color="#000000"
                style={{
                  width: '24px',
                  height: '24px',
                }}
              />
              <span className="sr-only">Close</span>
            </Button>
          </StyledDialogClose>
        )}
      </StyledDialogContent>
    </Comp>
  )
})
DialogContent.displayName = DialogPrimitive.Content.displayName

const DialogHeader: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({ ...props }) => <StyledDialogHeader {...props} />
DialogHeader.displayName = 'DialogHeader'

const DialogFooter: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({ ...props }) => <StyledDialogFooter {...props} />
DialogFooter.displayName = 'DialogFooter'

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ ...props }, ref) => <StyledDialogTitle ref={ref} {...props} />)
DialogTitle.displayName = DialogPrimitive.Title.displayName

const DialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ ...props }, ref) => <StyledDialogDescription ref={ref} {...props} />)
DialogDescription.displayName = DialogPrimitive.Description.displayName

export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogClose,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
}
