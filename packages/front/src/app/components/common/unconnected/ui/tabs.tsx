import * as TabsPrimitive from '@radix-ui/react-tabs'
import * as React from 'react'
import styled from 'styled-components'

const StyledTabsList = styled(TabsPrimitive.List)`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-md);
`

const StyledTabsTrigger = styled(TabsPrimitive.Trigger)`
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-bottom: 2px solid #b4bbc5;
  padding: 1rem;
  font-size: 1rem;
  font-weight: 600;
  color: #586171;
  transition: all 0.2s ease;
  background-color: transparent;

  &:focus-visible {
    outline: none;
  }

  &:disabled {
    pointer-events: none;
    opacity: 0.5;
  }

  &[data-state='active'] {
    border-color: #3c66b9;
    color: #24272d;
  }
`

const StyledTabsContent = styled(TabsPrimitive.Content)`
  &:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-ring);
  }
`

const Tabs = TabsPrimitive.Root

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => <StyledTabsList ref={ref} className={className} {...props} />)
TabsList.displayName = TabsPrimitive.List.displayName

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => <StyledTabsTrigger ref={ref} className={className} {...props} />)
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => <StyledTabsContent ref={ref} className={className} {...props} />)
TabsContent.displayName = TabsPrimitive.Content.displayName

export { Tabs, TabsList, TabsTrigger, TabsContent }
