import * as React from 'react'
import * as HoverCardPrimitive from '@radix-ui/react-hover-card'
import styled, { css } from 'styled-components'

// Estilização usando styled-components
const StyledHoverCardContent = styled(HoverCardPrimitive.Content)`
  z-index: 50;
  width: 16rem;
  border-radius: 16px;
  border: 1px solid #e5e5e5;
  background: white;
  color: black;
  padding: 1rem;
  box-shadow: 0px 4px 8px 0px #a6b8e652;
  outline: none;
  transition:
    transform 0.2s,
    opacity 0.2s;

  &[data-state='open'] {
    opacity: 1;
    transform: scale(1);
  }
  &[data-state='closed'] {
    opacity: 0;
    transform: scale(0.95);
  }
  &[data-side='top'] {
    animation: slide-in-from-bottom 0.2s ease-out;
  }
  &[data-side='right'] {
    animation: slide-in-from-left 0.2s ease-out;
  }
  &[data-side='bottom'] {
    animation: slide-in-from-top 0.2s ease-out;
  }
  &[data-side='left'] {
    animation: slide-in-from-right 0.2s ease-out;
  }

  @keyframes slide-in-from-bottom {
    from {
      transform: translateY(10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-from-left {
    from {
      transform: translateX(-10px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-from-top {
    from {
      transform: translateY(-10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-from-right {
    from {
      transform: translateX(10px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
`

// Componentes principais
const HoverCard = HoverCardPrimitive.Root

const HoverCardTrigger = HoverCardPrimitive.Trigger

const HoverCardContent = React.forwardRef<
  React.ElementRef<typeof HoverCardPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof HoverCardPrimitive.Content>
>(({ align = 'center', sideOffset = 4, ...props }, ref) => (
  <StyledHoverCardContent ref={ref} align={align} sideOffset={sideOffset} {...props} />
))
HoverCardContent.displayName = HoverCardPrimitive.Content.displayName

export { HoverCard, HoverCardTrigger, HoverCardContent }
