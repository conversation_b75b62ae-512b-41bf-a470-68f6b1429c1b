import * as React from 'react'
import * as Dialog from '@radix-ui/react-dialog'
import styled, { keyframes } from 'styled-components'
import { Button } from './button'
import { X } from 'lucide-react'

// Animações usando keyframes
const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`

const fadeOut = keyframes`
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
`

const slideInRight = keyframes`
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
`

const slideOutRight = keyframes`
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
`

const slideInLeft = keyframes`
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
`

const slideOutLeft = keyframes`
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
`

const Drawer = ({ ...props }: React.ComponentProps<typeof Dialog.Root>) => <Dialog.Root {...props} />
Drawer.displayName = 'Drawer'

const DrawerTrigger = Dialog.Trigger
const DrawerPortal = Dialog.Portal
const DrawerClose = Dialog.Close

const DrawerMain = styled.main`
  padding: 16px;
`

// Estilizando o Overlay
const StyledOverlay = styled(Dialog.Overlay)`
  position: fixed;
  inset: 0;
  z-index: 99999;
  background-color: rgba(0, 0, 0, 0.8);
  animation: ${fadeIn} 0.3s cubic-bezier(0.16, 1, 0.3, 1);

  &[data-state='closed'] {
    animation: ${fadeOut} 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }
`
const DrawerOverlay = React.forwardRef<
  React.ElementRef<typeof Dialog.Overlay>,
  React.ComponentPropsWithoutRef<typeof Dialog.Overlay>
>((props, ref) => <StyledOverlay ref={ref} {...props} />)
DrawerOverlay.displayName = Dialog.Overlay.displayName

// Estilizando o Content
const StyledContent = styled(Dialog.Content)`
  position: fixed;
  left: 0;
  bottom: 0;
  top: 0;
  width: 480px;
  z-index: 999999;
  height: auto;
  display: flex;
  flex-direction: column;
  background-color: white;

  animation: ${slideInLeft} 0.3s;

  &[data-state='closed'] {
    animation: ${slideOutLeft} 0.3s;
  }
`

interface DrawerContentProps {
  portal?: boolean
  closeButton?: boolean
}

const DrawerContent = React.forwardRef<
  React.ElementRef<typeof Dialog.Content>,
  React.ComponentPropsWithoutRef<typeof Dialog.Content> & DrawerContentProps
>(({ children, portal = true, ...props }, ref) => {
  const Comp = portal ? DrawerPortal : React.Fragment

  return (
    <Comp>
      <DrawerOverlay />
      <StyledContent ref={ref} {...props}>
        {children}
      </StyledContent>
    </Comp>
  )
})
DrawerContent.displayName = 'DrawerContent'

// Estilizando o Header
const StyledHeader = styled.div`
  height: 72px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;

  border-bottom: 1px solid rgba(82, 101, 140, 0.15);

  @media (min-width: 640px) {
    text-align: left;
  }
`
const DrawerHeader = ({ children, className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <StyledHeader className={className} {...props}>
    <div>{children}</div>
    <DrawerClose asChild>
      <Button variant="ghost" size="icon">
        <X
          color="#000000"
          style={{
            width: '24px',
            height: '24px',
          }}
        />
      </Button>
    </DrawerClose>
  </StyledHeader>
)
DrawerHeader.displayName = 'DrawerHeader'

// Estilizando o Footer
const StyledFooter = styled.div`
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
`
const DrawerFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <StyledFooter className={className} {...props} />
)
DrawerFooter.displayName = 'DrawerFooter'

// Estilizando o Title
const StyledTitle = styled(Dialog.Title)`
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.25;
  letter-spacing: -0.02em;
  margin: 0;
`
const DrawerTitle = React.forwardRef<
  React.ElementRef<typeof Dialog.Title>,
  React.ComponentPropsWithoutRef<typeof Dialog.Title>
>((props, ref) => <StyledTitle ref={ref} {...props} />)
DrawerTitle.displayName = Dialog.Title.displayName

// Estilizando o Description
const StyledDescription = styled(Dialog.Description)`
  font-size: 0.875rem;
  color: var(--muted-foreground);
`
const DrawerDescription = React.forwardRef<
  React.ElementRef<typeof Dialog.Description>,
  React.ComponentPropsWithoutRef<typeof Dialog.Description>
>((props, ref) => <StyledDescription ref={ref} {...props} />)
DrawerDescription.displayName = Dialog.Description.displayName

export {
  Drawer,
  DrawerPortal,
  DrawerOverlay,
  DrawerTrigger,
  DrawerClose,
  DrawerContent,
  DrawerHeader,
  DrawerFooter,
  DrawerTitle,
  DrawerDescription,
  DrawerMain,
}
