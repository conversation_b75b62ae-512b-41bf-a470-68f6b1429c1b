import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import styled, { css } from 'styled-components'

const buttonVariants = {
  default: css`
    background-color: #324b7d;
    color: white;
    &:hover {
      background-color: #405a8e;
    }
  `,
  muted: css`
    background-color: #95a7cc;
    color: white;
    &:hover {
      background-color: #7d91b9;
    }
  `,
  outline: css`
    border: 1px solid #324b7d;
    background-color: transparent;
    color: #324b7d;
    &:hover {
      background-color: #e1edf8;
    }
  `,
  ghost: css`
    background-color: transparent;
    color: #324b7d;
    border: none;
    &:hover {
      background-color: #e1edf8;
    }
  `,
  link: css`
    color: #324b7d;
    text-decoration: none;
    text-underline-offset: 4px;
    background-color: transparent;

    &:hover {
      text-decoration: underline;
    }
  `,
  destructive: css`
    background-color: #b81d1d;
    color: white;
    &:hover {
      background-color: #aa1a1a;
    }
    &:focus-visible {
      outline: 4px solid #fba6a6;
    }
  `,
}

const buttonSizes = {
  default: css`
    height: 40px;
    padding: 0 16px;
    font-size: 16px;

    > svg {
      height: 16px;
      width: 16px;
    }
  `,
  sm: css`
    height: 32px;
    padding: 0 8px;
    font-size: 14px;

    > svg {
      height: 14px;
      width: 14px;
    }
  `,
  xs: css`
    height: 28px;
    padding: 0 12px;
    font-size: 12px;

    > svg {
      height: 12px;
      width: 12px;
    }
  `,
  lg: css`
    height: 48px;
    padding: 0 24px;
    font-size: 20px;

    > svg {
      height: 20px;
      width: 20px;
    }
  `,
  icon: css`
    min-height: 40px;
    min-width: 40px;

    > svg {
      height: 16px;
      width: 16px;
    }
  `,
  'icon-sm': css`
    min-height: 32px;
    min-width: 32px;

    > svg {
      height: 12px;
      width: 12px;
    }
  `,
  'icon-lg': css`
    min-height: 48px;
    min-width: 48px;

    > svg {
      height: 20px;
      width: 20px;
    }
  `,
}

interface StyledButtonProps {
  variant?: keyof typeof buttonVariants
  size?: keyof typeof buttonSizes
}

const StyledButton = styled.button<StyledButtonProps>`
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border: none;
  border-radius: 99999px;
  font-weight: 600;
  line-height: 1;
  transition:
    color 0.2s,
    background-color 0.2s;
  &:disabled {
    pointer-events: none;
    opacity: 0.5;

    color: #6e7a89;
    background-color: #d7dbe0;
    border: none;

    > svg {
      stroke: #6e7a89;
    }
  }
  &:focus-visible {
    outline: 4px solid #a5cbeb;
  }
  ${({ variant }) => buttonVariants[variant || 'default']}
  ${({ size }) => buttonSizes[size || 'default']}
`

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  asChild?: boolean
  variant?: keyof typeof buttonVariants
  size?: keyof typeof buttonSizes
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant = 'default', size = 'default', asChild = false, type = 'button', ...props }, ref) => {
    const Comp = asChild ? Slot : StyledButton
    return <Comp ref={ref} variant={variant} size={size} type={type} {...props} />
  },
)
Button.displayName = 'Button'

export { Button }
