import React from 'react'
import styled, { css } from 'styled-components'

const variantStyles = {
  default: css`
    background-color: #f2f7fc;
    border-color: #4679ca;
    svg {
      color: #3c66b9;
    }
  `,
  destructive: css`
    background-color: #fef2f2;
    border-color: #db2727;
    svg {
      color: #b81d1d;
    }
  `,
  warning: css`
    background-color: #fdfbe9;
    border-color: #c48b0a;
    svg {
      color: #814f12;
    }
  `,
}

const StyledAlert = styled.div<{ variant: keyof typeof variantStyles }>`
  min-height: 48px;
  width: 100%;
  border-radius: 0.5rem;
  border: 1px solid;
  padding: 8px 16px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 8px;

  ${({ variant }) => variantStyles[variant]}

  svg {
    height: 24px;
    width: 24px;
  }
`

const StyledAlertTitle = styled.h5`
  margin-bottom: 0.25rem;
  font-weight: 500;
  line-height: 1;
  letter-spacing: -0.015em;
`

const StyledAlertDescription = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: #24272d;

  & p {
    line-height: 1.6;
  }
`

const StyledAlertLink = styled.div`
  flex: 1;
  text-align: end;
  line-height: 14px;
  font-weight: 600;
`

// Componentes principais
const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { variant?: keyof typeof variantStyles }
>(({ variant = 'default', ...props }, ref) => <StyledAlert ref={ref} role="alert" variant={variant} {...props} />)
Alert.displayName = 'Alert'

const AlertTitle = React.forwardRef<HTMLHeadingElement, React.HTMLAttributes<HTMLHeadingElement>>((props, ref) => (
  <StyledAlertTitle ref={ref} {...props} />
))
AlertTitle.displayName = 'AlertTitle'

const AlertDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
  (props, ref) => <StyledAlertDescription ref={ref} {...props} />,
)
AlertDescription.displayName = 'AlertDescription'

const AlertLink = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>((props, ref) => (
  <StyledAlertLink ref={ref} {...props} />
))
AlertLink.displayName = 'AlertLink'

export { Alert, AlertTitle, AlertDescription, AlertLink }
