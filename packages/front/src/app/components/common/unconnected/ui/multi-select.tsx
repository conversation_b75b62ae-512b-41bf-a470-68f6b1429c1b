import * as React from 'react'
import styled, { css, keyframes } from 'styled-components'
import { CheckIcon, XCircle, ChevronDown, XIcon, WandSparkles } from 'lucide-react'

import { Separator } from './separator'
import { Button } from './button'
import { Badge } from './badge'
import { Popover, PopoverContent, PopoverTrigger } from './popover'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from './command'

const bounce = keyframes`
  0%, 100% { transform: translateY(0%); }
  50% { transform: translateY(-5%); }
`

/**
 * Variants for styling the badge.
 */
type Variant = 'default' | 'secondary' | 'destructive' | 'inverted'

interface StyledBadgeProps {
  $variant?: Variant
  $isAnimating?: boolean
  $animation?: number
}

const StyledBadge = styled(Badge)<StyledBadgeProps>`
  margin: 0.25rem;
  transition:
    transform 0.3s ease-in-out,
    background 0.3s ease-in-out;
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;

  &:hover {
    transform: translateY(-0.25rem) scale(1.1);
  }

  ${({ $variant }) => {
    switch ($variant) {
      case 'secondary':
        return css`
          border-color: rgba(0, 0, 0, 0.1);
          background: var(--secondary);
          color: var(--secondary-foreground);
          &:hover {
            background: var(--secondary-hover);
          }
        `
      case 'destructive':
        return css`
          border-color: transparent;
          background: var(--destructive);
          color: var(--destructive-foreground);
          &:hover {
            background: var(--destructive-hover);
          }
        `
      case 'inverted':
        return css`
          /* Customize 'inverted' variant as needed */
          background: var(--inverted-bg, #000);
          color: var(--inverted-fg, #fff);
        `
      default:
        return css`
          border-color: rgba(0, 0, 0, 0.1);
          background: var(--card);
          color: var(--foreground);
          &:hover {
            background: var(--card-hover);
          }
        `
    }
  }}

  ${({ $isAnimating, $animation }) =>
    $isAnimating &&
    $animation &&
    css`
      animation: ${bounce} ${$animation}s infinite;
    `}
`

const StyledMoreBadge = styled(StyledBadge)`
  background: transparent;
  color: var(--foreground);
  border: 1px solid var(--foreground);
  &:hover {
    background: transparent;
  }
`

const IconButton = styled.div`
  display: inline-flex;
  margin-left: 0.5rem;
  width: 1rem;
  height: 1rem;
  cursor: pointer;
`

const StyledXCircleIcon = styled(XCircle)`
  width: 1rem;
  height: 1rem;
  cursor: pointer;
  margin-left: 0.5rem;
`

const StyledCheckIcon = styled(CheckIcon)`
  width: 1rem;
  height: 1rem;
`

const StyledXIcon = styled(XIcon)`
  width: 1rem;
  height: 1rem;
  margin: 0 0.5rem;
  cursor: pointer;
  color: var(--muted-foreground);
`

const StyledChevronDownIcon = styled(ChevronDown)`
  width: 1rem;
  height: 1rem;
  margin: 0 0.5rem;
  cursor: pointer;
  color: var(--muted-foreground);
`

interface StyledWandSparklesIconProps {
  $isAnimating?: boolean
}

const StyledWandSparklesIcon = styled(WandSparkles)<StyledWandSparklesIconProps>`
  margin: 0.5rem 0;
  width: 0.75rem;
  height: 0.75rem;
  cursor: pointer;
  color: var(--foreground);
  background: var(--background);
  ${({ $isAnimating }) =>
    !$isAnimating &&
    css`
      color: var(--muted-foreground);
    `}
`

const StyledButton = styled(Button)`
  display: flex;
  width: 100%;
  padding: 0.25rem;
  border-radius: 0.375rem;
  border: 1px solid;
  min-height: 2.5rem;
  height: auto;
  align-items: center;
  justify-content: space-between;
  background: inherit;

  &:hover {
    background: inherit;
  }

  svg {
    pointer-events: auto;
  }
`

const Wrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
`

const FlexWrap = styled.div`
  display: flex;
  flex-wrap: wrap;
  align-items: center;
`

const PlaceholderText = styled.span`
  font-size: 0.875rem;
  color: var(--muted-foreground);
  margin: 0 0.75rem;
`

const VerticalSeparator = styled(Separator)`
  min-height: 1.5rem;
  height: 100%;
`

const StyledCommandItem = styled(CommandItem)`
  cursor: pointer;
  display: flex;
  align-items: center;
`

const CenteredCommandItem = styled(StyledCommandItem)`
  flex: 1;
  justify-content: center;
`

const CheckBoxContainer = styled.div<{ $selected: boolean }>`
  margin-right: 0.5rem;
  display: flex;
  height: 1rem;
  width: 1rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.125rem;
  border: 1px solid var(--primary);

  ${({ $selected }) =>
    $selected
      ? css`
          background: var(--primary);
          color: var(--primary-foreground);
          svg {
            visibility: visible;
          }
        `
      : css`
          opacity: 0.5;
          svg {
            visibility: hidden;
          }
        `}
`

/**
 * Props for MultiSelect component
 */
interface MultiSelectOption {
  label: string
  value: string
  icon?: React.ComponentType<{ className?: string }>
}

interface MultiSelectProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  options: MultiSelectOption[]
  onValueChange: (value: string[]) => void
  defaultValue?: string[]
  placeholder?: string
  animation?: number
  maxCount?: number
  modalPopover?: boolean
  asChild?: boolean
  className?: string
  variant?: Variant
}

export const MultiSelect = React.forwardRef<HTMLButtonElement, MultiSelectProps>(
  (
    {
      options,
      onValueChange,
      variant = 'default',
      defaultValue = [],
      placeholder = 'Select options',
      animation = 0,
      maxCount = 3,
      modalPopover = false,
      asChild = false,
      className,
      ...props
    },
    ref,
  ) => {
    const [selectedValues, setSelectedValues] = React.useState<string[]>(defaultValue)
    const [isPopoverOpen, setIsPopoverOpen] = React.useState(false)
    const [isAnimating, setIsAnimating] = React.useState(false)

    const handleInputKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter') {
        setIsPopoverOpen(true)
      } else if (event.key === 'Backspace' && !event.currentTarget.value) {
        const newSelectedValues = [...selectedValues]
        newSelectedValues.pop()
        setSelectedValues(newSelectedValues)
        onValueChange(newSelectedValues)
      }
    }

    const toggleOption = (option: string) => {
      const newSelectedValues = selectedValues.includes(option)
        ? selectedValues.filter((value) => value !== option)
        : [...selectedValues, option]
      setSelectedValues(newSelectedValues)
      onValueChange(newSelectedValues)
    }

    const handleClear = () => {
      setSelectedValues([])
      onValueChange([])
    }

    const handleTogglePopover = () => {
      setIsPopoverOpen((prev) => !prev)
    }

    const clearExtraOptions = () => {
      const newSelectedValues = selectedValues.slice(0, maxCount)
      setSelectedValues(newSelectedValues)
      onValueChange(newSelectedValues)
    }

    const toggleAll = () => {
      if (selectedValues.length === options.length) {
        handleClear()
      } else {
        const allValues = options.map((option) => option.value)
        setSelectedValues(allValues)
        onValueChange(allValues)
      }
    }

    return (
      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen} modal={modalPopover}>
        <PopoverTrigger asChild={asChild}>
          <StyledButton ref={ref} {...props} onClick={handleTogglePopover} className={className}>
            {selectedValues.length > 0 ? (
              <Wrapper>
                <FlexWrap>
                  {selectedValues.slice(0, maxCount).map((value) => {
                    const option = options.find((o) => o.value === value)
                    const IconComponent = option?.icon
                    return (
                      <StyledBadge key={value} $isAnimating={isAnimating} $animation={animation} $variant={variant}>
                        {IconComponent && <IconComponent className="icon" />}
                        {option?.label}
                        <StyledXCircleIcon
                          onClick={(event) => {
                            event.stopPropagation()
                            toggleOption(value)
                          }}
                        />
                      </StyledBadge>
                    )
                  })}
                  {selectedValues.length > maxCount && (
                    <StyledMoreBadge $isAnimating={isAnimating} $animation={animation} $variant={variant}>
                      {`+ ${selectedValues.length - maxCount} more`}
                      <StyledXCircleIcon
                        onClick={(event) => {
                          event.stopPropagation()
                          clearExtraOptions()
                        }}
                      />
                    </StyledMoreBadge>
                  )}
                </FlexWrap>
                <FlexWrap style={{ justifyContent: 'flex-end' }}>
                  <StyledXIcon
                    onClick={(event) => {
                      event.stopPropagation()
                      handleClear()
                    }}
                  />
                  <VerticalSeparator orientation="vertical" />
                  <StyledChevronDownIcon />
                </FlexWrap>
              </Wrapper>
            ) : (
              <Wrapper>
                <PlaceholderText>{placeholder}</PlaceholderText>
                <StyledChevronDownIcon />
              </Wrapper>
            )}
          </StyledButton>
        </PopoverTrigger>
        <PopoverContent align="start" onEscapeKeyDown={() => setIsPopoverOpen(false)}>
          <Command>
            <CommandInput placeholder="Search..." onKeyDown={handleInputKeyDown} />
            <CommandList>
              <CommandEmpty>No results found.</CommandEmpty>
              <CommandGroup>
                <StyledCommandItem key="all" onSelect={toggleAll}>
                  <CheckBoxContainer $selected={selectedValues.length === options.length}>
                    <StyledCheckIcon />
                  </CheckBoxContainer>
                  <span>(Select All)</span>
                </StyledCommandItem>
                {options.map((option) => {
                  const isSelected = selectedValues.includes(option.value)
                  const IconComponent = option.icon
                  return (
                    <StyledCommandItem key={option.value} onSelect={() => toggleOption(option.value)}>
                      <CheckBoxContainer $selected={isSelected}>
                        <StyledCheckIcon />
                      </CheckBoxContainer>
                      {IconComponent && <IconComponent className="icon" />}
                      <span>{option.label}</span>
                    </StyledCommandItem>
                  )
                })}
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  {selectedValues.length > 0 && (
                    <>
                      <CenteredCommandItem onSelect={handleClear}>Clear</CenteredCommandItem>
                      <VerticalSeparator orientation="vertical" />
                    </>
                  )}
                  <CenteredCommandItem onSelect={() => setIsPopoverOpen(false)}>Close</CenteredCommandItem>
                </div>
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
        {animation > 0 && selectedValues.length > 0 && (
          <StyledWandSparklesIcon $isAnimating={isAnimating} onClick={() => setIsAnimating(!isAnimating)} />
        )}
      </Popover>
    )
  },
)

MultiSelect.displayName = 'MultiSelect'
