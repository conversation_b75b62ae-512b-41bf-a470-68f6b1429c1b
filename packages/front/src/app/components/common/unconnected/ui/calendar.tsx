import * as React from 'react'
import { DayPicker, Nav, Months, MonthCaption, CaptionLabel } from 'react-day-picker'
import { ptBR, enUS, es } from 'date-fns/locale'
import i18n from '../../../../../client/i18n'
import styled from 'styled-components'
import { format, startOfToday } from 'date-fns'
import { ChevronLeft, ChevronRight } from 'lucide-react'

const getLanguage = () => {
  switch (i18n.language) {
    case 'pt-BR':
      return ptBR
    case 'en-US':
      return enUS
    case 'es':
      return es
  }
}

const theme = {
  accentColor: 'blue',
  accentBackgroundColor: '#e0e0ff',
  dayHeight: '2.75rem',
  dayWidth: '2.75rem',
  dayButtonBorderRadius: '100%',
  dayButtonBorder: '2px solid transparent',
  dayButtonHeight: '2.75rem',
  dayButtonWidth: '2.75rem',
  selectedBorder: '2px solid darkblue',
  disabledOpacity: 0.5,
  outsideOpacity: 0.75,
  todayColor: 'darkblue',
  dropdownGap: '0.5rem',
  monthsGap: '2rem',
  navButtonDisabledOpacity: 0.5,
  navButtonHeight: '2.25rem',
  navButtonWidth: '2.25rem',
  navHeight: '2.75rem',
  rangeMiddleBackgroundColor: '#E1EDF8',
  rangeStartDateBackgroundColor: '#3C66B9',
  rangeStartDateFontColor: 'white',
  rangeStartBackgroundColor: 'linear-gradient(90deg, transparent 50%, #E1EDF8 50%)',
  rangeEndDateBackgroundColor: '#3C66B9',
  rangeEndDateFontColor: 'white',
  rangeEndBackgroundColor: 'linear-gradient(90deg, #E1EDF8 50%, transparent 50%)',
  weekdayOpacity: 0.75,
  weekdayPadding: '0.5rem 0rem',
  weekdayTextAlign: 'center',
}

const CalendarWrapper = styled.div`
  padding: 16px;

  .rdp-root {
    --rdp-accent-color: ${theme.accentColor};
    --rdp-accent-background-color: ${theme.accentBackgroundColor};
    --rdp-day-height: ${theme.dayHeight};
    --rdp-day-width: ${theme.dayWidth};
    --rdp-day_button-border-radius: ${theme.dayButtonBorderRadius};
    --rdp-day_button-border: ${theme.dayButtonBorder};
    --rdp-day_button-height: ${theme.dayButtonHeight};
    --rdp-day_button-width: ${theme.dayButtonWidth};
    --rdp-selected-border: ${theme.selectedBorder};
    --rdp-disabled-opacity: ${theme.disabledOpacity};
    --rdp-outside-opacity: ${theme.outsideOpacity};
    --rdp-today-color: ${theme.todayColor};
    --rdp-dropdown-gap: ${theme.dropdownGap};
    --rdp-months-gap: ${theme.monthsGap};
    --rdp-nav_button-disabled-opacity: ${theme.navButtonDisabledOpacity};
    --rdp-nav_button-height: ${theme.navButtonHeight};
    --rdp-nav_button-width: ${theme.navButtonWidth};
    --rdp-nav-height: ${theme.navHeight};
    --rdp-range_middle-background-color: ${theme.rangeMiddleBackgroundColor};
    --rdp-range_start-date-background-color: ${theme.rangeStartDateBackgroundColor};
    --rdp-range_start-date-font-color: ${theme.rangeStartDateFontColor};
    --rdp-range_start-background: ${theme.rangeStartBackgroundColor};
    --rdp-range_end-date-background-color: ${theme.rangeEndDateBackgroundColor};
    --rdp-range_end-date-font-color: ${theme.rangeEndDateFontColor};
    --rdp-range_end-background: ${theme.rangeEndBackgroundColor};
    --rdp-weekday-opacity: ${theme.weekdayOpacity};
    --rdp-weekday-padding: ${theme.weekdayPadding};
    --rdp-weekday-text-align: ${theme.weekdayTextAlign};

    .rdp-hidden {
      visibility: hidden;
    }

    .rdp-outside {
      opacity: var(--rdp-outside-opacity);
    }

    .rdp-range_start {
      background: var(--rdp-range_start-background);

      .rdp-day_button {
        background: var(--rdp-range_start-date-background-color);
        color: var(--rdp-range_start-date-font-color);
      }
    }

    .rdp-range_middle {
      background-color: var(--rdp-range_middle-background-color);
    }

    .rdp-range_end {
      background: var(--rdp-range_end-background);
      color: var(--rdp-range_end-color);

      .rdp-day_button {
        background: var(--rdp-range_start-date-background-color);
        color: var(--rdp-range_start-date-font-color);
      }
    }

    .rdp-range_start.rdp-range_end {
      background: revert;
    }

    .rdp-selected.rdp-focused .rdp-day_button,
    .rdp-selected:not(.rdp-range_middle) .rdp-day_button {
      background: var(--rdp-range_start-date-background-color);
    }
  }
`

const StyledNav = styled(Nav)`
  align-items: center;
  display: flex;
  height: var(--rdp-nav-height);
  position: absolute;
  right: 0;
  top: 0;

  .rdp-button_next,
  .rdp-button_previous {
    background: none;
    border: none;
    color: inherit;
    display: inline-flex;
    height: var(--rdp-nav_button-height);
    width: var(--rdp-nav_button-width);
  }
`

const StyledMonths = styled(Months)`
  display: flex;
  flex-wrap: wrap;
  gap: var(--rdp-months-gap);
  max-width: -moz-fit-content;
  max-width: fit-content;
  position: relative;
`

const StyledMonthCaption = styled(MonthCaption)`
  height: var(--rdp-nav-height);
`

const StyledCaptionLabel = styled(CaptionLabel)`
  font-size: 16px;
  font-weight: bold;
  text-transform: capitalize;
  color: #324b7d;
`

const StyledWeekday = styled.th`
  opacity: var(--rdp-weekday-opacity);
  padding: var(--rdp-weekday-padding);
  text-align: var(--rdp-weekday-text-align);
  font-weight: 500;
  font-size: smaller;
  text-transform: capitalize;
  display: table-cell;
  width: var(--rdp-day-width);
`

const StyledDayButton = styled.button`
  font-size: 0.9rem;
  font-weight: 500;
  background-color: transparent;
  border-radius: var(--rdp-day_button-border-radius);
  border: var(--rdp-day_button-border);
  width: var(--rdp-day_button-width);
  height: var(--rdp-day_button-height);
  padding: 0.5rem;
  cursor: pointer;
  color: black;

  :disabled {
    color: #cdcdcd;
  }
`

export type CalendarProps = React.ComponentProps<typeof DayPicker> & {
  disabledAfterToday?: boolean
}

function Calendar({
  className,
  classNames,
  showOutsideDays = false,
  disabledAfterToday = false,
  ...props
}: CalendarProps) {
  const today = startOfToday()

  return (
    <CalendarWrapper>
      <DayPicker
        showOutsideDays={showOutsideDays}
        weekStartsOn={1}
        locale={getLanguage()}
        disabled={{
          after: disabledAfterToday ? today : undefined,
        }}
        components={{
          Chevron: ({ orientation }) => {
            switch (orientation) {
              case 'left':
                return <ChevronLeft style={{ width: '16px', height: '16px' }} />
              case 'right':
                return <ChevronRight style={{ width: '16px', height: '16px' }} />
              default:
                return <></>
            }
          },
          Nav: StyledNav,
          Months: StyledMonths,
          MonthCaption: StyledMonthCaption,
          CaptionLabel: StyledCaptionLabel,
          Weekday: StyledWeekday,
          DayButton: (dayButtonProps: React.ComponentProps<typeof StyledDayButton>) => {
            const { day, modifiers, ...rest } = dayButtonProps
            return <StyledDayButton {...rest} data-testid={`calendar-day-element-${format(day.date, 'yyyy-MM-dd')}`} />
          },
        }}
        {...props}
      />
    </CalendarWrapper>
  )
}

Calendar.displayName = 'Calendar'

export { Calendar }
