import * as React from 'react'
import * as SliderPrimitive from '@radix-ui/react-slider'
import styled, { css } from 'styled-components'

// Estilização usando styled-components
const SliderRoot = styled(SliderPrimitive.Root)`
  position: relative;
  display: flex;
  width: 100%;
  touch-action: none;
  user-select: none;
  align-items: center;
`

const SliderTrack = styled(SliderPrimitive.Track)`
  position: relative;
  height: 4px;
  width: 100%;
  flex-grow: 1;
  overflow: hidden;
  border-radius: 9999px;
  background: #b4bbc5;
`

const SliderRange = styled(SliderPrimitive.Range)`
  position: absolute;
  height: 100%;
  background: #6e7a89;
`

const SliderThumb = styled(SliderPrimitive.Thumb)`
  ${({ theme }) => css`
    display: block;
    height: 12px;
    width: 12px;
    border-radius: 50%;
    border: 2px solid #6e7a89;
    background: #6e7a89;
    transition:
      background-color 0.2s,
      border-color 0.2s;
    &:focus-visible {
      outline: none;
      box-shadow:
        0 0 0 2px black,
        0 0 0 4px green;
    }
    &:disabled {
      pointer-events: none;
      opacity: 0.5;
    }
  `}
`

const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>
>(({ className, ...props }, ref) => (
  <SliderRoot ref={ref} className={className} {...props}>
    <SliderTrack>
      <SliderRange />
    </SliderTrack>
    <SliderThumb />
  </SliderRoot>
))
Slider.displayName = SliderPrimitive.Root.displayName

export { Slider }
