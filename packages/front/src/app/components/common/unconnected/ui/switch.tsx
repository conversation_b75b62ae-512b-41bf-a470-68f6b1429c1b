import * as React from 'react'
import * as SwitchPrimitives from '@radix-ui/react-switch'
import styled from 'styled-components'

// Estilizando o Switch com styled-components
const StyledSwitchRoot = styled(SwitchPrimitives.Root)`
  display: inline-flex;
  height: 24px;
  width: 40px;
  align-items: center;
  border-radius: 9999px;
  border: 2px solid;
  cursor: pointer;
  padding: 0 2px;

  transition:
    background-color 0.2s,
    border-color 0.2s;
  &:focus-visible {
    outline: none;
  }
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  &[data-state='checked'] {
    border-color: transparent;
    background-color: #365497;
  }
  &[data-state='unchecked'] {
    border-color: #6e7a89;
    background-color: white;
  }
`

const StyledSwitchThumb = styled(SwitchPrimitives.Thumb)`
  display: block;
  height: 1rem;
  width: 1rem;
  border-radius: 9999px;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
  &[data-state='checked'] {
    background-color: white;
    transform: translateX(1rem); /* translate-x-5 */
  }
  &[data-state='unchecked'] {
    background-color: #6e7a89;
    transform: translateX(0);
  }
`

// Componente Switch
const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <StyledSwitchRoot ref={ref} className={className} {...props}>
    <StyledSwitchThumb />
  </StyledSwitchRoot>
))
Switch.displayName = SwitchPrimitives.Root.displayName

export { Switch }
