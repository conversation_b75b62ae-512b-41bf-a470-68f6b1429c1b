import React from 'react'

class CharacterLimit extends React.Component {
  constructor(props) {
    super(props)
    const { message, limit, label, t } = props
    this.state = {
      message,
      limit,
      label,
    }
  }

  getQuantity = (message, limit) => {
    const len = message.length - 1
    return len > 0 ? parseInt(len / limit) + 1 : 1
  }

  getLeftChar = (message, limit) => {
    const len = limit * this.getQuantity(message, limit) - message.length
    return len
  }

  UNSAFE_componentWillReceiveProps(newProps) {
    this.setState({
      ...this.state,
      ...newProps,
    })
  }

  render() {
    const { t } = this.props
    const { message, limit, label } = this.state
    return (
      <div
        data-testid="chat-label-limit_message"
        style={{ color: 'gray', float: 'right', width: '50px' }}
        title={
          label !== 'max.'
            ? `${t('common:LABEL_SMS_SIZE')} ${this.getLeftChar(message, limit)} ${t(
                'common:LABEL_SMS_CONSUMPTION',
              )} ${this.getQuantity(message, limit)} ${t('common:LABEL_SMS_CREDITS')}`
            : `${t('common:LABEL_SMS_TEXT')} ${message.length} ${t('common:LABEL_SMS_CHARACTERS')} ${this.getQuantity(
                message,
                limit,
              )} ${t('common:LABEL_SMS_MESSAGES')}`
        }
      >
        <label
          style={{
            whiteSpace: 'nowrap',
            margin: 0,
          }}
        >{`${this.getLeftChar(message, limit)} / ${this.getQuantity(message, limit)}`}</label>
      </div>
    )
  }
}

export default CharacterLimit
