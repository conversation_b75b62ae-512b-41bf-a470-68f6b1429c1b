import React, { memo } from 'react'
import { Badge } from 'reactstrap'
import { TextColor } from '../../../App/styles/colors'
import { fontFamily } from '../../../App/styles/common'
import { isEqual } from '../../../../utils/logic'

const style = {
  departmentBadge: {
    padding: '5px 20px',
    background: `${TextColor}`,
    color: '#FFFFFF',
    fontFamily: `${fontFamily}`,
    borderRadius: '16px',
    fontSize: '12px',
    fontWeight: '400',
  },
}

const DepartmentBadge = ({ department, ...rest } = {}) => {
  if (!department) return null

  const { name } = department

  return (
    <Badge style={style.departmentBadge} title="Departamento do chamado" {...rest}>
      {name}
    </Badge>
  )
}

const propsAreEqual = (props, nextProps) => isEqual(props.department, nextProps.department, ['name'])

export default memo(DepartmentBadge, propsAreEqual)
