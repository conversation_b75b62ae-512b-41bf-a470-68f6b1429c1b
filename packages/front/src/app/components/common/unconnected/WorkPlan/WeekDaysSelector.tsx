import React, { memo } from 'react'
import { ButtonGroup } from 'reactstrap'
import without from 'lodash/without'
import { useTranslation } from 'react-i18next'
import * as S from './styles'
import useEventCallback from '../../../../hooks/useEventCallback'

const styles = {
  button: {
    zIndex: 0,
  },
}

function WeekdaysSelector(props) {
  const { t } = useTranslation('workPlan')
  const weekMap = {
    sun: t('LABEL_SUNDAY'),
    mon: t('LABEL_MONDAY'),
    tue: t('LABEL_TUESDAY'),
    wed: t('LABEL_WEDNESDAY'),
    thu: t('LABEL_THURSDAY'),
    fri: t('LABEL_FRIDAY'),
    sat: t('LABEL_SATURDAY'),
  }

  const { onChange, value = [], ...rest } = props

  const handleClick = useEventCallback((weekday) => {
    if (!onChange) return

    if (value.includes(weekday)) {
      onChange(without(value, weekday))
      return
    }

    onChange([...value, weekday])
  })

  return (
    <>
      <S.ButtonGroup>
        {Object.entries(weekMap).map(([weekday, label]) => (
          <S.ButtonWeekDays
            key={weekday}
            color={value.includes(weekday) ? 'primary' : '#fff'}
            onClick={() => handleClick(weekday)}
            active={value.includes(weekday)}
            style={styles.button}
            {...rest}
          >
            {label}
          </S.ButtonWeekDays>
        ))}
      </S.ButtonGroup>
    </>
  )
}

export default memo(WeekdaysSelector)
