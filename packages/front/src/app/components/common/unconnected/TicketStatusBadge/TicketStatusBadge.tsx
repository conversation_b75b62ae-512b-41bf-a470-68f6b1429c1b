import React, { memo } from 'react'
import { Badge } from 'reactstrap'
import cns from 'classnames'
import './TicketStatusBadge.scss'

export default memo(({ ticket, ...rest } = {}) => {
  if (!ticket) return null

  const { isOpen } = ticket

  return (
    <Badge className={cns(['mr-2'], isOpen ? ['bg-opened'] : ['bg-closed'])} title="Status do chamado">
      {isOpen ? 'Aberto' : 'Fechado'}
    </Badge>
  )
})
