import { memo, useMemo } from 'react'
import useEventCallback from '../../../../hooks/useEventCallback'

export type ArrayHelpers = {
  push(item: any): void
  remove(index: number): void
  set(index: number, item: any): void
  get(index: number): any
}

function FieldArray2(props) {
  const { render, children, value = [], onChange } = props

  const push = useEventCallback((item) => {
    if (!onChange) return

    onChange([...value, item])
  })

  const remove = useEventCallback((index) => {
    if (!onChange) return

    onChange([...value.slice(0, index), ...value.slice(index + 1)])
  })

  const set = useEventCallback((index, item) => {
    if (!onChange) return

    onChange([...value.slice(0, index), item, ...value.slice(index + 1)])
  })

  const get = useEventCallback((index) => value[index])

  const arrayHelpers: ArrayHelpers = useMemo(
    () => ({
      push,
      remove,
      set,
      get,
    }),
    [push, remove, set],
  )

  return (render || children)(arrayHelpers)
}

export default memo(FieldArray2)
