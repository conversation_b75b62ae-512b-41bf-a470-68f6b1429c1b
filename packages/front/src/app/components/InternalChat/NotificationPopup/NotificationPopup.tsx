import React from 'react'
import { useTranslation } from 'react-i18next'
import { Container, Header, Title, CloseButton, Subtitle } from './styles'

interface NotificationPopupProps {
  contactNames: string
  messageCount: string
  onClose: () => void
  position: 'top-right' | 'top-left' | 'bottom-left' | 'bottom-right'
  iconPosition: { x: number; y: number }
}

const NotificationPopup: React.FC<NotificationPopupProps> = ({
  contactNames,
  messageCount,
  onClose,
  position,
  iconPosition,
}) => {
  const { t } = useTranslation(['internalChat'])

  return (
    <Container position={position} iconPosition={iconPosition}>
      <Header>
        <Title>{contactNames}</Title>
        <CloseButton onClick={onClose} title={t('CLOSE_NOTIFICATION')}>
          ×
        </CloseButton>
      </Header>
      <Subtitle>{messageCount}</Subtitle>
    </Container>
  )
}

export default NotificationPopup
