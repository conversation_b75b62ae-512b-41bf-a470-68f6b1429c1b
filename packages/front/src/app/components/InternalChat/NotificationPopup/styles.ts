import styled from 'styled-components'

export const Container = styled.div<{ position: string; iconPosition: { x: number; y: number } }>`
  position: absolute;
  background: #ffffff;
  width: 208px;
  opacity: 1;
  gap: 4px;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 4px;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.15);
  padding: 12px;
  z-index: 999998;
  border: 1px solid #e5e5e5;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;

  ${({ position, iconPosition }) => {
    switch (position) {
      case 'top-right':
        return `
          top: 70px;
          right: 5px;
          border-top-right-radius: 4px;
          border-top-left-radius: 20px;
          border-bottom-left-radius: 20px;
          border-bottom-right-radius: 20px;
        `
      case 'top-left':
        return `
          top: 70px;
          left: 5px;
          border-top-left-radius: 4px;
          border-top-right-radius: 20px;
          border-bottom-right-radius: 20px;
          border-bottom-left-radius: 20px;
        `
      case 'bottom-left':
        return `
          bottom: 70px;
          left: 5px;
          border-bottom-left-radius: 4px;
          border-top-right-radius: 20px;
          border-top-left-radius: 20px;
          border-bottom-right-radius: 20px;
        `
      case 'bottom-right':
      default:
        return `
          bottom: 70px;
          right: 5px;
          border-bottom-right-radius: 4px;
          border-top-left-radius: 20px;
          border-top-right-radius: 20px;
          border-bottom-left-radius: 20px;
        `
    }
  }}
`

export const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
`

export const Title = styled.p`
  font-size: 14px;
  font-weight: 600;
  font-style: normal;
  line-height: 20px;
  letter-spacing: 0%;
  color: var(--Text-Default---ds-color-text, #323439);
  margin: 0;
  flex: 1;
`

export const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
`

export const Subtitle = styled.p`
  font-size: 14px;
  font-weight: 500;
  font-style: normal;
  letter-spacing: 0%;
  color: #323439;
  margin: 0;
  margin-top: 0;
  margin-bottom: 0;
`
