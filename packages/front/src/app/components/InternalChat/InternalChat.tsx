import React, { useState, useEffect } from 'react'
import Draggable from 'react-draggable'
import { useSelector } from 'react-redux'
import { useTranslation } from 'react-i18next'
import internalChatIcon from '../../assets/icons/internal-chat/icon.svg'
import internalChatIconDisable from '../../assets/icons/internal-chat/icon-disable.svg'
import config from '../../../../config'
import { getUser, getUserAccount } from '../../modules/auth/selectors'
import { getIsImpersonating } from '../../modules/admin/modules/users/modules/impersonate/selectors'
import * as accountsFlags from '../App/Dashboard/Admin/accounts/AccountsFlags'
import NotificationPopup from './NotificationPopup'

const styles = {
  container: {
    zIndex: '999999',
    position: 'fixed' as const,
    bottom: '5px',
    right: '5px',
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'flex-end',
  },
  button: {
    width: '64px',
    height: '64px',
    outline: 'none',
    border: 'none',
    background: 'transparent',
    backgroundSize: 'contain',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
  },
  buttonClose: {
    outline: 'none',
    border: 'none',
    background: 'transparent',
    marginBottom: '5px',
  },
  image: { width: 50, height: 50 },
  iframe: {
    boxShadow: '0px 2px 12px rgba(82, 101, 140, 0.3)',
    borderRadius: '16px',
    transition: '0.5s',
    position: 'fixed' as const,
    bottom: '80px',
    right: '32px',
    zIndex: '99999',
    maxHeight: '720px',
  },
  unread: {
    position: 'absolute' as const,
    bottom: '45px',

    background: '#DC3545',
    color: '#FAFAFA',
    boxShadow: '0px 6.26462px 6.26462px rgba(0, 0, 0, 0.25)',

    fontSize: '11px',
    fontWeight: 'bold',

    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',

    minWidth: '24px',
    width: 'auto',
    height: '24px',
    minHeight: '24px',

    borderRadius: 25,

    padding: '0 5px',
    marginBottom: '0',
  },
}

const InternalChat = () => {
  const { t } = useTranslation(['internalChat'])
  const [toggle, setToggle] = useState(false)
  const [unread, setUnread] = useState(0)
  const [showNotification, setShowNotification] = useState(false)
  const [unreadDetails, setUnreadDetails] = useState([])
  const [popupPosition, setPopupPosition] = useState<'top-right' | 'top-left' | 'bottom-left' | 'bottom-right'>(
    'bottom-right',
  )
  const [iconPosition, setIconPosition] = useState({ x: 0, y: 0 })
  const [windowSize, setWindowSize] = useState({
    innerHeight: -(window.innerHeight - 70),
    innerWidth: -(window.innerWidth - 70),
  })

  const userAccount = useSelector(getUserAccount)
  const user = useSelector(getUser)
  const impersonate = useSelector(getIsImpersonating)
  const canAccessInternalChat = user.roles.some((role) => role.isAdmin) || user.isActiveInternalChat

  const calculatePopupPosition = () => {
    const popupHeight = 64
    const popupWidth = 208
    const margin = 80

    const screenHeight = window.innerHeight
    const screenWidth = window.innerWidth

    const absoluteIconX = Math.abs(iconPosition.x)
    const absoluteIconY = Math.abs(iconPosition.y)

    const hasSpaceBelow = screenHeight - absoluteIconY - margin - popupHeight >= 0
    const hasSpaceRight = screenWidth - absoluteIconX - margin - popupWidth >= 0

    if (hasSpaceBelow && hasSpaceRight) {
      return 'bottom-right'
    }

    if (!hasSpaceBelow && hasSpaceRight) {
      return 'top-right'
    }

    if (hasSpaceBelow && !hasSpaceRight) {
      return 'bottom-left'
    }

    return 'top-left'
  }

  useEffect(() => {
    const handleMessage = (event) => {
      if (typeof event.data.unread === 'number') {
        setUnread(event.data.unread)
      }

      const unreadDetailsData = event.data.unreadDetails
      const hasDetailsData = unreadDetailsData?.length > 0
      if (hasDetailsData) {
        setUnreadDetails(unreadDetailsData)
      }
    }

    window.addEventListener('message', handleMessage)

    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [])

  useEffect(() => {
    const updateWindowDimensions = () => {
      const newHeight = window.innerHeight
      const newWidth = window.innerWidth

      setWindowSize({
        innerHeight: -(newHeight - 70),
        innerWidth: -(newWidth - 70),
      })

      if (showNotification) {
        const idealPosition = calculatePopupPosition()
        setPopupPosition(idealPosition)
      }
    }

    window.addEventListener('resize', updateWindowDimensions)

    return () => window.removeEventListener('resize', updateWindowDimensions)
  }, [showNotification, windowSize])

  useEffect(() => {
    if (showNotification) {
      const idealPosition = calculatePopupPosition()
      setPopupPosition(idealPosition)
    }
  }, [windowSize, showNotification])

  useEffect(() => {
    if (!toggle && unread > 0 && unreadDetails.length > 0) {
      setShowNotification(true)
    }
  }, [toggle, unread, unreadDetails])

  const handleChangeToggle = () => {
    const newToggle = !toggle

    if (newToggle) {
      setShowNotification(false)
    } else {
      if (unread > 0 && unreadDetails.length > 0) {
        setShowNotification(true)
      }
    }

    setToggle(newToggle)
  }

  const handleCloseNotification = () => {
    setShowNotification(false)
  }

  const formatContactNames = () => {
    if (unreadDetails.length === 0) return ''

    const allContacts = new Set(
      unreadDetails
        .sort((a, b) => {
          const dateA = a.lastMessage?.createdAt ? new Date(a.lastMessage.createdAt).getTime() : 0
          const dateB = b.lastMessage?.createdAt ? new Date(b.lastMessage.createdAt).getTime() : 0
          return dateB - dateA
        })
        .flatMap((chat) => {
          if (chat.isGroup) {
            return [chat.chatName || t('GROUP')]
          } else {
            return chat.contacts.map((c) => c.name)
          }
        }),
    )

    const uniqueContacts = [...allContacts]

    if (uniqueContacts.length === 1) {
      return uniqueContacts[0]
    }

    if (uniqueContacts.length === 2) {
      return `${uniqueContacts[0]}, +1 ${t('CONVERSATION')}`
    }

    return `${uniqueContacts[0]}, +${uniqueContacts.length - 1} ${t('CONVERSATIONS')}`
  }

  const formatMessageCount = () => {
    if (unread === 1) {
      return `1 ${t('NEW_MESSAGE')}`
    }
    return `${unread} ${t('NEW_MESSAGES')}`
  }

  if (!accountsFlags.isEnable(userAccount?.settings?.flags || {}, 'internal-chat')) {
    return null
  }

  if (user.isSuperAdmin || impersonate) return null

  return (
    <div>
      <Draggable
        bounds={{
          left: windowSize.innerWidth,
          top: windowSize.innerHeight,
          right: 0,
          bottom: 0,
        }}
        onDrag={(e, data) => {
          const newPosition = { x: data.x, y: data.y }
          setIconPosition(newPosition)

          if (showNotification) {
            const idealPosition = calculatePopupPosition()
            setPopupPosition(idealPosition)
          }
        }}
      >
        <div style={styles.container}>
          {showNotification && unread > 0 && unreadDetails.length > 0 && !toggle && (
            <NotificationPopup
              onClose={handleCloseNotification}
              contactNames={formatContactNames()}
              messageCount={formatMessageCount()}
              position={popupPosition}
              iconPosition={iconPosition}
            />
          )}

          {unread > 0 && !toggle && <span style={styles.unread}> {unread} </span>}
          {canAccessInternalChat ? (
            <button
              type="button"
              onDoubleClick={handleChangeToggle}
              style={{
                ...styles.button,
              }}
            >
              <img
                alt="Chat Interno"
                src={internalChatIcon}
                style={{
                  width: '100%',
                  height: '100%',
                  pointerEvents: 'none',
                }}
              />
            </button>
          ) : (
            <button
              type="button"
              style={{
                ...styles.button,
              }}
            >
              <img
                alt="Chat Interno"
                src={internalChatIconDisable}
                style={{
                  width: '100%',
                  height: '100%',
                  pointerEvents: 'none',
                }}
              />
            </button>
          )}
        </div>
      </Draggable>

      <iframe
        title="internalchat"
        width={toggle ? '405px' : '0'}
        height={toggle ? '75%' : '0'}
        src={`${config('internalChatUrl')}/onboard/${userAccount.id}/${user?.internalChatToken}/?language=${user?.language}`}
        frameBorder="0"
        allow="microphone; camera; accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture; web-share; clipboard-read; clipboard-write"
        allowFullScreen
        style={{
          ...styles.iframe,
          opacity: toggle ? 1 : 0,
          pointerEvents: toggle ? 'auto' : 'none',
          transition: 'opacity 0.3s ease-in-out',
        }}
      />
    </div>
  )
}

export default InternalChat
