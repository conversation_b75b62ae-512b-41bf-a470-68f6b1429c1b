import React, { useEffect, useState, useRef } from 'react'
import { ChevronRightIcon, ChevronLeftIcon } from 'lucide-react'
import * as S from './styles'

const Slider = ({ children }) => {
  const scrollRef = useRef<HTMLDivElement>(null)
  const [showButtons, setShowButtons] = useState(false)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(false)

  useEffect(() => {
    const element = scrollRef.current

    const handleScroll = () => {
      if (element) {
        const { scrollLeft, scrollWidth, clientWidth } = element
        setShowButtons(scrollWidth > clientWidth)
        setCanScrollLeft(scrollLeft > 0)
        setCanScrollRight(scrollLeft < scrollWidth - clientWidth)
      }
    }

    if (element) {
      handleScroll()
      element.addEventListener('scroll', handleScroll)
      window.addEventListener('resize', handleScroll)

      return () => {
        element.removeEventListener('scroll', handleScroll)
        window.removeEventListener('resize', handleScroll)
      }
    }
  }, [scrollRef.current])

  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = 300
      const newScrollLeft =
        direction === 'left' ? scrollRef.current.scrollLeft - scrollAmount : scrollRef.current.scrollLeft + scrollAmount

      scrollRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth',
      })

      setTimeout(() => {
        if (scrollRef.current) {
          const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current
          setCanScrollLeft(scrollLeft > 0)
          setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1) // -1 to avoid float precision issues
        }
      }, 300)
    }
  }

  return (
    <S.ScrollContainer>
      <S.ScrollButton
        onClick={() => scroll('left')}
        data-position="left"
        show={showButtons && canScrollLeft}
        style={{ marginLeft: '-20px' }}
        marginLeft="-3px"
      >
        <ChevronLeftIcon size={16} />
      </S.ScrollButton>

      <S.BoxesWrapper ref={scrollRef}>{children}</S.BoxesWrapper>

      <S.ScrollButton
        onClick={() => scroll('right')}
        data-position="right"
        show={showButtons && canScrollRight}
        style={{ marginRight: '-20px' }}
        marginRight="-3px"
      >
        <ChevronRightIcon size={16} />
      </S.ScrollButton>
    </S.ScrollContainer>
  )
}

export default Slider
