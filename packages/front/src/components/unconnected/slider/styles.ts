import styled from 'styled-components'

export const ScrollContainer = styled.div`
  position: relative;
  width: 100%;
`

export const BoxesWrapper = styled.div`
  display: flex;
  gap: 16px;
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
`

export const ScrollButton = styled.button`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  border: 1px solid #d7dbe0;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  z-index: 1;
  display: ${(props) => (props.show ? 'inline-flex' : 'none')};
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;

  svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    margin-left: ${(props) => (props.marginLeft ? props.marginLeft : '0px')};
    margin-right: ${(props) => (props.marginRight ? props.marginRight : '0px')};
  }

  &[data-position='left'] {
    left: 0;
  }

  &[data-position='right'] {
    right: 0;
  }
`
