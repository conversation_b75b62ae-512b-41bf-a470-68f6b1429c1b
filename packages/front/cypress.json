{"baseUrl": "http://127.0.0.1:1337", "projectId": "rcp789", "fixturesFolder": "tests/cypress/fixtures", "pluginsFile": "tests/cypress/plugins", "screenshotsFolder": "tests/cypress/screenshots", "supportFile": "tests/cypress/support", "videosFolder": "tests/cypress/videos", "integrationFolder": "src/app/__tests__/e2e", "screenshotOnRunFailure": false, "video": false, "chromeWebSecurity": false, "watchForFileChanges": false, "env": {"MEDIA_HOST": "127.0.0.1:8080", "API_URL": "http://127.0.0.1:8080/v1", "SOCKET_URL": "http://127.0.0.1:8080", "CLIENT_ID": "api", "CLIENT_SECRET": "secret", "API_PROJECT_PATH": "../mandeumzap-api"}}