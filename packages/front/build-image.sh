#!/usr/bin/env bash

set -e

COMMIT_SHA=$(git rev-parse HEAD)
TAG=${1:-latest}
TAG2=${2:-${COMMIT_SHA}}

AWS_REPOSITORY=${3:-digisac-front}
AWS_REGISTRY=${4:-617751658601.dkr.ecr.us-east-1.amazonaws.com}
AWS_REGION=${5:-us-east-1}
AWS_PROFILE=${6:-default}

export FULL_AWS_REGISTRY_REPOSITORY_NAME="${AWS_REGISTRY}/${AWS_REPOSITORY}"

OCI_REGION=sa-vinhedo-1.ocir.io
OCI_NAMESPACE=axvaplbwrlcl
OCI_REPOSITORY=digisac-isolated

export FULL_OCI_REGISTRY_REPOSITORY_NAME="${OCI_REGION}/${OCI_NAMESPACE}/${OCI_REPOSITORY}/front"

echo 'TAG' $TAG
echo 'TAG2' $TAG2
echo 'AWS_REPOSITORY' $AWS_REPOSITORY
echo 'AWS_REGISTRY' $AWS_REGISTRY
echo 'AWS_REGION' $AWS_REGION
echo 'AWS_PROFILE' $AWS_PROFILE

eval "$(aws ecr get-login --no-include-email --region ${AWS_REGION} --profile ${AWS_PROFILE})"
echo '=> logged-in to ecr'

if aws ecr describe-repositories --region ${AWS_REGION} --repository-names ${AWS_REPOSITORY} --profile ${AWS_PROFILE}; then
    echo '=> repository exists'
else
    aws ecr create-repository --region ${AWS_REGION} --repository-name ${AWS_REPOSITORY} --profile ${AWS_PROFILE}
    echo '=> created repository'
fi

export DOCKER_BUILDKIT=1

docker pull ${FULL_AWS_REGISTRY_REPOSITORY_NAME}:${TAG} || true

# remove version from package.json so it does not invalidate build kit cache
cp package.json package.no-version.json
sed -i 's/\"version\":.*/\"version\": \"1.0.0\",/g' ./package.no-version.json


#if docker build --cache-from ${AWS_REGISTRY}/${AWS_REPOSITORY}:${TAG} --build-arg BUILDKIT_INLINE_CACHE=1 -t ${AWS_REPOSITORY} -f Dockerfile .; then
if docker build --build-arg BUILDKIT_INLINE_CACHE=1 -t ${AWS_REPOSITORY} -f Dockerfile .; then
    rm ./package.no-version.json

    echo '=> image built'

    docker tag ${AWS_REPOSITORY}:latest ${FULL_AWS_REGISTRY_REPOSITORY_NAME}:${TAG}
    docker tag ${AWS_REPOSITORY}:latest ${FULL_OCI_REGISTRY_REPOSITORY_NAME}:${TAG}

    if [ ${TAG} != ${TAG2} ]; then
        docker tag ${AWS_REPOSITORY}:latest ${FULL_AWS_REGISTRY_REPOSITORY_NAME}:${TAG2}
        docker tag ${AWS_REPOSITORY}:latest ${FULL_OCI_REGISTRY_REPOSITORY_NAME}:${TAG2}
    fi

    echo '=> image tagged'

    docker push ${FULL_AWS_REGISTRY_REPOSITORY_NAME}:${TAG}
    docker push ${FULL_OCI_REGISTRY_REPOSITORY_NAME}:${TAG}

    if [ ${TAG} != ${TAG2} ]; then
        docker push ${FULL_AWS_REGISTRY_REPOSITORY_NAME}:${TAG2}
        docker push ${FULL_OCI_REGISTRY_REPOSITORY_NAME}:${TAG2}
    fi

    echo '=> image pushed'
else
    rm ./package.no-version.json
    echo '=> image build failed'
    exit 1
fi
