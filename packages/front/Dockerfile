# build step
FROM node:20-alpine3.18 as build

WORKDIR /
RUN apk add --no-cache curl
RUN curl https://raw.githubusercontent.com/eficode/wait-for/master/wait-for -o wait-for-it -s

WORKDIR /app

COPY yarn.lock     yarn.lock
COPY package.no-version.json package.json
RUN rm -rf node_modules && \
    yarn --pure-lockfile  --ignore-engines

COPY .babelrc                     .babelrc
COPY .env.example                 .env
COPY tsconfig.json                tsconfig.json
COPY public                       public
COPY internal                     internal
COPY config                       config
COPY src                          src
COPY tailwind.config.js           tailwind.config.js
COPY postcss.config.mjs           postcss.config.mjs

RUN  NODE_OPTIONS=--openssl-legacy-provider yarn build
RUN yarn install --pure-lockfile --production --ignore-engines

RUN rm -rf dist/__tests__ src

# release step
FROM node:20-alpine3.18 as release

WORKDIR /
COPY --from=build /wait-for-it /usr/local/bin/wait-for-it
RUN chmod +x /usr/local/bin/wait-for-it

WORKDIR /app

COPY --from=build /app ./
COPY package.json package.json
