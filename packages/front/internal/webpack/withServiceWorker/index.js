import path from 'path'
import HtmlWebpackPlugin from 'html-webpack-plugin'
import config from '../../../config'
import ClientConfig from '../../components/ClientConfig'

export default function withServiceWorker(webpackConfig, bundleConfig) {
  webpackConfig.plugins.push(
    new HtmlWebpackPlugin({
      filename: config('serviceWorker.offlinePageFileName'),
      template: `swc-loader!${path.resolve(__dirname, './offlinePageTemplate.tsx')}`,
      production: true,
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeRedundantAttributes: true,
        useShortDoctype: true,
        removeEmptyAttributes: true, // Updated for Webpack 5
        removeStyleLinkTypeAttributes: true,
        keepClosingSlash: true,
        minifyJS: true,
        minifyCSS: true,
        minifyURLs: true,
      },
      inject: true,
      custom: {
        config,
        ClientConfig,
      },
    }),
  )

  return webpackConfig
}
