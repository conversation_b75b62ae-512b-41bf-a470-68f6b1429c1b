import express from 'express'
import webpackDevMiddleware from 'webpack-dev-middleware'
import webpackHotMiddleware from 'webpack-hot-middleware'
import ListenerManager from './listenerManager'
import { log } from '../utils'

class HotClientServer {
  constructor(port, compiler) {
    const app = express()

    const httpPathRegex = /^https?:\/\/(.*):([\d]{1,5})/i
    const httpPath = compiler.options.output.publicPath
    if (!httpPath.startsWith('http') && !httpPathRegex.test(httpPath)) {
      throw new Error(
        'You must supply an absolute public path to a development build of a web target bundle as it will be hosted on a separate development server to any node target bundles.',
      )
    }

    this.webpackDevMiddleware = webpackDevMiddleware(compiler, {
      headers: {
        'Access-Control-Allow-Origin': '*',
      },
      publicPath: compiler.options.output.publicPath,
    })

    app.use(this.webpackDevMiddleware)
    app.use(webpackHotMiddleware(compiler))

    const listener = app.listen(port)

    this.listenerManager = new ListenerManager(listener, 'client')

    compiler.hooks.compile.tap('compile', () => {
      log({
        title: 'client',
        level: 'info',
        message: 'Building new bundle...',
      })
    })

    compiler.hooks.done.tap('done', (stats) => {
      if (stats.hasErrors()) {
        log({
          title: 'client',
          level: 'error',
          message: 'Build failed, please check the console for more information.',
          notify: true,
        })
        console.error(stats.toString())
      } else {
        log({
          title: 'client',
          level: 'info',
          message: 'Running with latest changes.',
          notify: true,
        })
      }
    })
  }

  dispose() {
    this.webpackDevMiddleware.close()

    return this.listenerManager ? this.listenerManager.dispose() : Promise.resolve()
  }
}

export default HotClientServer
