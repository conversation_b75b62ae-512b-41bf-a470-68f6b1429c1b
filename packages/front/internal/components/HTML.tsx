/* eslint-disable jsx-a11y/html-has-lang */
import React from 'react'
import PropTypes from 'prop-types'

function HTML(props) {
  const {
    htmlAttributes = null,
    headerElements = null,
    bodyElements = null,
    appBodyString = '',
    children = null,
  } = props

  return (
    <html {...htmlAttributes}>
      <head>{headerElements}</head>
      <body>
        {appBodyString ? (
          <div id="app" dangerouslySetInnerHTML={{ __html: appBodyString }} />
        ) : (
          <div id="app">{children}</div>
        )}
        {bodyElements}
      </body>
    </html>
  )
}

HTML.propTypes = {
  htmlAttributes: PropTypes.object,
  headerElements: PropTypes.node,
  bodyElements: PropTypes.node,
  appBodyString: PropTypes.string,
  children: PropTypes.node,
}

export default HTML
