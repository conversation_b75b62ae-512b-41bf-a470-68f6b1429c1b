import * as cls from 'cls-hooked'
import { v4 as uuid } from 'uuid'

export default class Cls {
  protected namespace: cls.Namespace

  constructor(name: string = uuid()) {
    this.namespace = cls.createNamespace(name)
  }

  run(fn: (...args: any[]) => void) {
    this.namespace.run(fn)
  }

  set<T>(key: string, value: T): T {
    return this.namespace.set(key, value)
  }

  get(key: string, defaultValue = undefined): any {
    const val = this.namespace.get(key)

    if (val === undefined) return defaultValue

    return val
  }
}
