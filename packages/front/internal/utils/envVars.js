import appRootDir from 'app-root-dir'
import dotenv from 'dotenv'
import fs from 'fs'
import path from 'path'
import ifElse from './logic/ifElse'
import clean from './arrays/clean'

function registerEnvFile() {
  const NODE_ENV = process.env.NODE_ENV
  const envFile = '.env'

  const envFileResolutionOrder = clean([
    ifElse(NODE_ENV)(path.resolve(appRootDir.get(), `${envFile}.${NODE_ENV}`)),
    path.resolve(appRootDir.get(), envFile),
  ])

  const envFilePath = envFileResolutionOrder.find(filePath => fs.existsSync(filePath))

  if (envFilePath) {
    console.log(`==> Registering environment variables from: ${envFilePath}`)
    dotenv.config({ path: envFilePath })
  }
}

registerEnvFile()

export function string(name, defaultVal) {
  return process.env[name] || defaultVal
}

export function number(name, defaultVal) {
  return process.env[name] ? parseInt(process.env[name], 10) : defaultVal
}

export function bool(name, defaultVal) {
  return process.env[name]
    ? process.env[name] === 'true' || process.env[name] === '1'
    : defaultVal
}

export default {
  string,
  number,
  bool,
}
