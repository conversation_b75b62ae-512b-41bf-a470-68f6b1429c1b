FROM bitnami/minideb:stretch

RUN apt update && \
    apt install apt-utils apt-transport-https ca-certificates gnupg2 curl \
        fish openssh-server dumb-init wait-for-it \
        build-essential software-properties-common \
        -y --no-install-recommends && \
    curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | apt-key add - && \
    echo "deb https://dl.yarnpkg.com/debian/ stable main" | tee /etc/apt/sources.list.d/yarn.list && \
    curl -sL https://deb.nodesource.com/setup_10.x | bash - && \
    apt update && \
    apt install yarn nodejs -y --no-install-recommends && \
    apt clean && \
    apt autoremove && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*
