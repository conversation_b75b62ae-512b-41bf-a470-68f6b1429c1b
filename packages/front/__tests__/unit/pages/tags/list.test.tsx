import '@testing-library/jest-dom'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useQuery } from '@tanstack/react-query'
import { useHistory } from 'react-router'
import { useTranslation } from 'react-i18next'
import React from 'react'
import { ListTags } from '../../../../src/pages/tags/list'

// Mock dos hooks e módulos
jest.mock('@tanstack/react-query')
jest.mock('react-router')
jest.mock('react-i18next')
jest.mock('../../api/tags')
jest.mock('../../hooks/use-search-params')
jest.mock('../../app/components/common/connected/IfUserCan', () => ({
  __esModule: true,
  default: ({ children }) => children,
}))

const mockHistory = {
  push: jest.fn(),
}

const mockTags = {
  data: [
    {
      id: '1',
      label: 'Tag 1',
      backgroundColor: '#FF0000',
      linkedContacts: 5,
    },
    {
      id: '2',
      label: 'Tag 2',
      backgroundColor: '#00FF00',
      linkedContacts: 3,
    },
  ],
  currentPage: 1,
  limit: 10,
  total: 2,
}

describe('ListTags', () => {
  beforeEach(() => {
    jest.clearAllMocks()

    useQuery.mockReturnValue({
      data: mockTags,
      refetch: jest.fn(),
    })
    useHistory.mockReturnValue(mockHistory)
    useTranslation.mockReturnValue({
      t: (key: string) => key,
    })
  })

  it('should render the component with tags list', () => {
    render(<ListTags />)

    expect(screen.getByText('tagsPage:TITLE_TAGS')).toBeInTheDocument()
    expect(screen.getByText('Tag 1')).toBeInTheDocument()
    expect(screen.getByText('Tag 2')).toBeInTheDocument()
  })

  it('should navigate to create page when clicking create button', () => {
    render(<ListTags />)

    const createButton = screen.getByTestId('tag-button-create')
    fireEvent.click(createButton)

    expect(mockHistory.push).toHaveBeenCalledWith('/tags/create')
  })

  it('should navigate to view page when clicking view button', async () => {
    render(<ListTags />)

    const viewButton = screen.getByTestId('view-create-tag')
    fireEvent.click(viewButton)

    expect(mockHistory.push).toHaveBeenCalledWith('/tags/1')
  })

  it('should navigate to edit page when clicking edit button', () => {
    render(<ListTags />)

    const editButton = screen.getByTestId('edit-button-tag')
    fireEvent.click(editButton)

    expect(mockHistory.push).toHaveBeenCalledWith('/tags/1/edit')
  })

  it('should navigate to delete page when clicking delete button', () => {
    render(<ListTags />)

    const deleteButton = screen.getByTestId('delete-button-tag')
    fireEvent.click(deleteButton)

    expect(mockHistory.push).toHaveBeenCalledWith('/tags/1/delete')
  })

  it('should filter tags when searching', async () => {
    const mockSetValues = jest.fn()
    jest.spyOn(require('../../hooks/use-search-params'), 'useSearchParams').mockReturnValue({
      params: {},
      setValues: mockSetValues,
      set: jest.fn(),
    })

    render(<ListTags />)

    const searchInput = screen.getByTestId('input-search-tag')
    fireEvent.change(searchInput, { target: { value: 'Test' } })
    fireEvent.submit(searchInput.closest('form'))

    expect(mockSetValues).toHaveBeenCalledWith({ label: 'Test' })
  })

  it('should show bulk actions when selecting tags', () => {
    render(<ListTags />)

    const checkbox = screen.getAllByRole('checkbox')[1] // First tag checkbox
    fireEvent.click(checkbox)

    expect(screen.getByTestId('tags-button-bulk-actions')).toBeInTheDocument()
  })

  it('should handle select all tags', () => {
    render(<ListTags />)

    const selectAllCheckbox = screen.getAllByRole('checkbox')[0]
    fireEvent.click(selectAllCheckbox)

    const bulkActionsButton = screen.getByTestId('tags-button-bulk-actions')
    expect(bulkActionsButton).toBeInTheDocument()
  })

  it('should show no results message when there are no tags', () => {
    ;(useQuery as jest.Mock).mockReturnValue({
      data: { data: [], currentPage: 1, limit: 10, total: 0 },
      refetch: jest.fn(),
    })

    render(<ListTags />)

    expect(screen.getByText('common:NO_RESULTS_FOUND')).toBeInTheDocument()
  })
})
