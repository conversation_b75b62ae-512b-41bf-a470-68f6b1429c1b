deploy-qa1:
  stage: deploy
  environment:
    name: qa1-v3
    url: https://qa1-v3.digisac.biz
  script:
    - BACK_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/back/package.json)
    - echo $BACK_VERSION

    - FRONT_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/front/package.json)
    - echo $FRONT_VERSION

    - |
     curl --request POST \
      --url https://awx-public.ikatec.cloud/api/v2/job_templates/17/launch/ \
      --header 'Authorization: Bearer C4snoZluuc6wZqsdoND6ZNVfUj7MrY' \
      --header 'Content-Type: application/json' \
      --data '{
        "extra_vars": {
          "hosts_group": "qa1-v3",
          "infra_version": "v3",
          "back_version": "'"${BACK_VERSION}"'",
          "front_version": "'"${FRONT_VERSION}"'",
          "api_replicas": 1,
          "workers_replicas": 1
        }
      }'
  rules:
    - if: '$CI_PIPELINE_SOURCE =~ "merge_request_event"'
      when: manual

deploy-qa2:
  stage: deploy
  environment:
    name: qa2-v3
    url: https://qa2-v3.digisac.biz
  script:
    - BACK_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/back/package.json)
    - echo $BACK_VERSION

    - FRONT_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/front/package.json)
    - echo $FRONT_VERSION

    - |
     curl --request POST \
      --url https://awx-public.ikatec.cloud/api/v2/job_templates/17/launch/ \
      --header 'Authorization: Bearer C4snoZluuc6wZqsdoND6ZNVfUj7MrY' \
      --header 'Content-Type: application/json' \
      --data '{
        "extra_vars": {
          "hosts_group": "qa2-v3",
          "infra_version": "v3",
          "back_version": "'"${BACK_VERSION}"'",
          "front_version": "'"${FRONT_VERSION}"'",
          "api_replicas": 1,
          "workers_replicas": 1
        }
      }'
  rules:
    - if: '$CI_PIPELINE_SOURCE =~ "merge_request_event"'
      when: manual

deploy-qa3:
  stage: deploy
  environment:
    name: qa3-v3
    url: https://qa3-v3.digisac.biz
  script:
    - BACK_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/back/package.json)
    - echo $BACK_VERSION

    - FRONT_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/front/package.json)
    - echo $FRONT_VERSION

    - |
     curl --request POST \
      --url https://awx-public.ikatec.cloud/api/v2/job_templates/17/launch/ \
      --header 'Authorization: Bearer C4snoZluuc6wZqsdoND6ZNVfUj7MrY' \
      --header 'Content-Type: application/json' \
      --data '{
        "extra_vars": {
          "hosts_group": "qa3-v3",
          "infra_version": "v3",
          "back_version": "'"${BACK_VERSION}"'",
          "front_version": "'"${FRONT_VERSION}"'",
          "api_replicas": 1,
          "workers_replicas": 1
        }
      }'
  rules:
    - if: '$CI_PIPELINE_SOURCE =~ "merge_request_event"'
      when: manual

deploy-qa4:
  stage: deploy
  environment:
    name: qa4-v3
    url: https://qa4-v3.digisac.biz
  script:
    - BACK_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/back/package.json)
    - echo $BACK_VERSION

    - FRONT_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/front/package.json)
    - echo $FRONT_VERSION

    - |
     curl --request POST \
      --url https://awx-public.ikatec.cloud/api/v2/job_templates/17/launch/ \
      --header 'Authorization: Bearer C4snoZluuc6wZqsdoND6ZNVfUj7MrY' \
      --header 'Content-Type: application/json' \
      --data '{
        "extra_vars": {
          "hosts_group": "qa4-v3",
          "infra_version": "v3",
          "back_version": "'"${BACK_VERSION}"'",
          "front_version": "'"${FRONT_VERSION}"'",
          "api_replicas": 1,
          "workers_replicas": 1
        }
      }'
  rules:
    - if: '$CI_PIPELINE_SOURCE =~ "merge_request_event"'
      when: manual

deploy-qa5:
  stage: deploy
  environment:
    name: qa5-v3
    url: https://qa5-v3.digisac.app
  script:
    - BACK_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/back/package.json)
    - echo $BACK_VERSION

    - FRONT_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/front/package.json)
    - echo $FRONT_VERSION

    - |
     curl --request POST \
      --url https://awx-public.ikatec.cloud/api/v2/job_templates/17/launch/ \
      --header 'Authorization: Bearer C4snoZluuc6wZqsdoND6ZNVfUj7MrY' \
      --header 'Content-Type: application/json' \
      --data '{
        "extra_vars": {
          "hosts_group": "qa5-v3",
          "infra_version": "v3",
          "back_version": "'"${BACK_VERSION}"'",
          "front_version": "'"${FRONT_VERSION}"'",
          "api_replicas": 1,
          "workers_replicas": 1
        }
      }'
  rules:
    - if: '$CI_PIPELINE_SOURCE =~ "merge_request_event"'
      when: manual

deploy-qa6:
  stage: deploy
  environment:
    name: qa6-v3
    url: https://qa6-v3.digisac.app
  script:
    - BACK_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/back/package.json)
    - echo $BACK_VERSION

    - FRONT_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/front/package.json)
    - echo $FRONT_VERSION

    - |
     curl --request POST \
      --url https://awx-public.ikatec.cloud/api/v2/job_templates/17/launch/ \
      --header 'Authorization: Bearer C4snoZluuc6wZqsdoND6ZNVfUj7MrY' \
      --header 'Content-Type: application/json' \
      --data '{
        "extra_vars": {
          "hosts_group": "qa6-v3",
          "infra_version": "v3",
          "back_version": "'"${BACK_VERSION}"'",
          "front_version": "'"${FRONT_VERSION}"'",
          "api_replicas": 1,
          "workers_replicas": 1
        }
      }'
  rules:
    - if: '$CI_PIPELINE_SOURCE =~ "merge_request_event"'
      when: manual

deploy-qa7:
  stage: deploy
  environment:
    name: qa7-v3
    url: https://qa7-v3.digisac.app
  script:
    - BACK_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/back/package.json)
    - echo $BACK_VERSION

    - FRONT_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/front/package.json)
    - echo $FRONT_VERSION

    - |
     curl --request POST \
      --url https://awx-public.ikatec.cloud/api/v2/job_templates/17/launch/ \
      --header 'Authorization: Bearer C4snoZluuc6wZqsdoND6ZNVfUj7MrY' \
      --header 'Content-Type: application/json' \
      --data '{
        "extra_vars": {
          "hosts_group": "qa7-v3",
          "infra_version": "v3",
          "back_version": "'"${BACK_VERSION}"'",
          "front_version": "'"${FRONT_VERSION}"'",
          "api_replicas": 1,
          "workers_replicas": 1
        }
      }'
  rules:
    - if: '$CI_PIPELINE_SOURCE =~ "merge_request_event"'
      when: manual

deploy-qa8:
  stage: deploy
  environment:
    name: qa8-v3
    url: https://qa8-v3.digisac.app
  script:
    - BACK_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/back/package.json)
    - echo $BACK_VERSION

    - FRONT_VERSION=$(awk -F\" '/"version":/ {print $4}' packages/front/package.json)
    - echo $FRONT_VERSION

    - |
     curl --request POST \
      --url https://awx-public.ikatec.cloud/api/v2/job_templates/17/launch/ \
      --header 'Authorization: Bearer C4snoZluuc6wZqsdoND6ZNVfUj7MrY' \
      --header 'Content-Type: application/json' \
      --data '{
        "extra_vars": {
          "hosts_group": "qa8-v3",
          "infra_version": "v3",
          "back_version": "'"${BACK_VERSION}"'",
          "front_version": "'"${FRONT_VERSION}"'",
          "api_replicas": 1,
          "workers_replicas": 1
        }
      }'
  rules:
    - if: '$CI_PIPELINE_SOURCE =~ "merge_request_event"'
      when: manual
